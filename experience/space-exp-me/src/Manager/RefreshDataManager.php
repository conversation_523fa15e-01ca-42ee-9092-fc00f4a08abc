<?php

namespace App\Manager;

use App\Service\RefreshQueueService;
use App\Trait\LoggerTrait;
use App\Helper\SuccessResponse;
use App\Helper\ErrorResponse;
use App\Helper\ResponseArrayFormat;
use App\Model\ObjectsListToRefreshModel;
use App\Model\ObjectToRefreshModel;

/**
 * refreshDataManager
 */
class RefreshDataManager
{
    use LoggerTrait;

    public function __construct(
        private RefreshQueueService $refreshQueueService
    ) {
    }

    public function enqueueObjectsToRefresh(
        ObjectsListToRefreshModel $objectsList
    ): ResponseArrayFormat {
        try {
            $faileds = [];
            foreach ($objectsList->getObjects() as $objectName => $object) {

                if (!$this->refreshQueueService->isObjectValid($objectName, $object)) {

                    continue;
                }

                try {
                    $enqueued = $this->refreshQueueService->enqueueObject(
                        new ObjectToRefreshModel(
                            name: $objectName,
                            userId: $objectsList->getUserId(),
                            source: $objectsList->getSource(),
                            object: [$objectName => $object]
                        )
                    );
                    if ($enqueued === true) {
                        $this->logger->debug("$objectName successfully enqueued !");
                    } else {
                        $this->logger->error("enqueuing failed for $objectName !");
                    }
                } catch (\Exception $e) {
                    $faileds[] = $objectName;
                    $this->logger->error(
                        "error occured while enqueing $objectName" .
                            $e->getMessage()
                    );
                }
            }

            if ($faileds === []) {
                return new SuccessResponse(
                    "objects pushed to be refreshed successfully"
                );
            }

            throw new \Exception(
                "Failed to refresh objects : " . json_encode($faileds)
            );
        } catch (\Exception $e) {
            $this->logger->error("Error occured while enqueing objects :", [
                "exception" => $e,
            ]);

            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }
}
