<?php

namespace App\Model;

use Symfony\Component\Serializer\Annotation\Groups;

/**
 * VehicleModel
 */
class VehicleModel
{
    /**
     * @var string
     */
    #[Groups(['list'])]
    private $id;

    /**
     * @var string
     */
    #[Groups(['list'])]
    private $vin;

    /**
     * @var string
     */
    #[Groups(['list'])]
    private $label;

    /**
     * @var string
     */
    #[Groups(['list'])]
    private $versionId;

    /**
     * @var string
     */
    #[Groups(['list'])]
    private $brand;

    /**
     * @var string
     */
    #[Groups(['list'])]
    private $visual;

    /**
     * @var bool
     */
    #[Groups(['list'])]
    private $isOrder;

    /**
     * @var VehicleOrder
     */
    #[Groups(['list'])]
    private $vehicleOrder;

    /**
     * @return string
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param string $id
     *
     * @return self
     */
    public function setId($id)
    {
        $this->id = $id;

        return $this;
    }

    /**
     * @return string
     */
    public function getVin()
    {
        return $this->vin;
    }

    /**
     * @param string $vin
     *
     * @return self
     */
    public function setVin($vin)
    {
        $this->vin = $vin;

        return $this;
    }

    /**
     * @return string
     */
    public function getLabel()
    {
        return $this->label;
    }

    /**
     * @param string $label
     *
     * @return self
     */
    public function setLabel($label)
    {
        $this->label = $label;

        return $this;
    }

    /**
     * @return string
     */
    public function getVersionId()
    {
        return $this->versionId;
    }

    /**
     * @param string $versionId
     *
     * @return self
     */
    public function setVersionId($versionId)
    {
        $this->versionId = $versionId;

        return $this;
    }

    /**
     * @return string
     */
    public function getBrand()
    {
        return $this->brand;
    }

    /**
     * @param string $brand
     *
     * @return self
     */
    public function setBrand($brand)
    {
        $this->brand = $brand;

        return $this;
    }

    /**
     * @return string
     */
    public function getVisual()
    {
        return $this->visual;
    }

    /**
     * @param string $visual
     *
     * @return self
     */
    public function setVisual($visual)
    {
        $this->visual = $visual;

        return $this;
    }

    /**
     * @return bool
     */
    public function isIsOrder()
    {
        return $this->isOrder;
    }

    /**
     * @param bool $isOrder
     *
     * @return self
     */
    public function setIsOrder($isOrder)
    {
        $this->isOrder = $isOrder;

        return $this;
    }

    /**
     * @return VehicleOrder
     */
    public function getVehicleOrder()
    {
        return $this->vehicleOrder;
    }

    /**
     * @param VehicleOrder $vehicleOrder
     *
     * @return self
     */
    public function setVehicleOrder(VehicleOrder $vehicleOrder)
    {
        $this->vehicleOrder = $vehicleOrder;

        return $this;
    }
}
