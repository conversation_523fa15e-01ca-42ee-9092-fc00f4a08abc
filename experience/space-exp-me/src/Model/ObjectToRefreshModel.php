<?php

namespace App\Model;

use Symfony\Component\Uid\Uuid;
use Symfony\Component\Serializer\Attribute\Ignore;

/**
 * ObjectToRefreshModel the model of the object to send to the queue tobe processed an refreshed in process layer
 */

class ObjectToRefreshModel
{
    private ?string $messageId;

    public function __construct(
        #[Ignore] private ?string $name = null,
        private ?string $userId = null,
        private ?string $source = null,
        private ?array $object = null
    ) {
        $this->messageId = Uuid::v4();
    }

    public function getMessageId(): ?string
    {
        return $this->messageId;
    }

    public function setMessageId(?string $messageId): self
    {
        $this->messageId = $messageId;

        return $this;
    }

    public function getUserId(): ?string
    {
        return $this->userId;
    }

    public function setUserId(?string $userId): self
    {
        $this->userId = $userId;

        return $this;
    }

    public function getObject(): ?array
    {
        return $this->object;
    }

    public function setObject(?array $object): self
    {
        $this->object = $object;

        return $this;
    }

    public function getSource(): ?string
    {
        return $this->source;
    }

    public function setSource(?string $source): self
    {
        $this->source = $source;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }
}
