<?php

namespace App\Model;

use Symfony\Component\Serializer\Annotation\Groups;

/**
 * Vehicle on order
 */
class VehicleOrder
{
    /**
     * @var string
     */
    #[Groups(['list'])]
    private $mopId;

    /**
     * @var string
     */
    #[Groups(['list'])]
    private $orderFormId;

    /**
     * @var string
     */
    #[Groups(['list'])]
    private $trackingStatus;

    /**
     * @var bool
     */
    #[Groups(['list'])]
    private $isUpdated;

    /**
     * @var string
     */
    #[Groups(['list'])]
    private $orderFormStatus;
 
    /**
     * @return string
     */
    public function getMopId()
    {
        return $this->mopId;
    }

    /**
     * @param string $mopId
     *
     * @return self
     */
    public function setMopId($mopId)
    {
        $this->mopId = $mopId;

        return $this;
    }

    /**
     * @return string
     */
    public function getOrderFormId()
    {
        return $this->orderFormId;
    }

    /**
     * @param string $orderFormId
     *
     * @return self
     */
    public function setOrderFormId($orderFormId)
    {
        $this->orderFormId = $orderFormId;

        return $this;
    }

    /**
     * @return string
     */
    public function getTrackingStatus()
    {
        return $this->trackingStatus;
    }

    /**
     * @param string $trackingStatus
     *
     * @return self
     */
    public function setTrackingStatus($trackingStatus)
    {
        $this->trackingStatus = $trackingStatus;

        return $this;
    }

    /**
     * @return bool
     */
    public function isIsUpdated()
    {
        return $this->isUpdated;
    }

    /**
     * @param bool $isUpdated
     *
     * @return self
     */
    public function setIsUpdated($isUpdated)
    {
        $this->isUpdated = $isUpdated;

        return $this;
    }

    /**
     * @return string
     */
    public function getOrderFormStatus()
    {
        return $this->orderFormStatus;
    }

    /**
     * @param string $orderFormStatus
     *
     * @return self
     */
    public function setOrderFormStatus($orderFormStatus)
    {
        $this->orderFormStatus = $orderFormStatus;

        return $this;
    }
}