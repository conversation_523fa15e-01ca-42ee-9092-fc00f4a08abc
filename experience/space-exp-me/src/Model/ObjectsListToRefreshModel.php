<?php
namespace App\Model;

use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Serializer\Attribute\Ignore;

class ObjectsListToRefreshModel
{
    public function __construct(
        #[Assert\NotBlank]
        public array $objects,

        #[Assert\NotBlank]
        public string $source,

        #[Assert\NotBlank]
        #[Ignore]
        public string $userId,
    ) {
    }

    public function setUserId(string $userId): self
    {
        $this->userId = $userId;
        
        return $this;
    }

    public function getUserId(): string
    {
        return $this->userId;
    }

    public function getSource(): string
    {
        return $this->source;
    }

    public function setSource(string $source): self
    {
        $this->source = $source;
        return $this;
    }

    public function getObjects(): array
    {
        return $this->objects;
    }

    public function setObjects(array $objects): self
    {
        $this->objects = $objects;
        return $this;
    }
}
