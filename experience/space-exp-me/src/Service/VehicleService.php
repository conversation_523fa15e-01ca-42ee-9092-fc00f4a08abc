<?php

namespace App\Service;

use App\Trait\LoggerTrait;
use App\Helper\WSResponse;
use Exception;
use Symfony\Component\HttpFoundation\Request;

class VehicleService
{
    use LoggerTrait;

    /**
     * @var ProcUserVehicleConnector
     */
    private $procUserVehicleConnector;

    /**
     * @param ProcUserVehicleConnector $connector
     */
    public function __construct(ProcUserVehicleConnector $connector)
    {
        $this->procUserVehicleConnector = $connector;
    }

    /**
     * @param string $userId 
     * @return WSResponse
     */
    public function getVehicles(string $userId): WSResponse
    {
        try {
            $this->logger->info("VehicleService::getVehicles for userid " . $userId);
            return $this->procUserVehicleConnector->call(Request::METHOD_GET, "/v1/vehicles", [
                                'headers' => ['userId' => $userId]
                            ]);
        } catch (Exception $e) {
            $this->logger->error("VehicleService::getVehicles : Cached Exception " . $e->getMessage());
            return new WSResponse($e->getCode(), $e->getMessage());
        }
    }
    
    /**
     * Get all vehicles summary
     * @param string $userId
     * @param string $vehicleId
     * @return WSResponse
     */
    public function getVehicleSummary(string $userId, string $vehicleId)
    { 
        try
         {
            $this->logger->info("VehicleService::getVehicleSummary for userId $userId and vehicleId $vehicleId");
            
            return $this->procUserVehicleConnector->call(
                    Request::METHOD_GET, 
                    "/v1/vehicles/{$vehicleId}/summary", 
                    [
                        'headers' => ['userId' => $userId]
                    ]);
                } catch (Exception $e) {
                    $this->logger->error("VehicleService:getVehicleSummary : Cached Exception " . $e->getMessage());
                    return new WSResponse($e->getCode(), $e->getMessage());
                }
        }  

    /**
     * Add Vehicles Data
     * @param string $brand
     * @param string $country
     * @param string $language
     * @param string $source
     * @param string $vin
     * @return WSResponse
     */
    public function addVehicle(string $brand, string $country, string $language, string $source,string $vin, string $mileage)
    {
        try
         {
            $this->logger->info("VehicleService::addVehicle for brand and country,source,language". $brand.$country. $language. $source.$vin.$mileage);
            
            return $this->procUserVehicleConnector->call(
                Request::METHOD_POST,
                "/v1/vehicles/add", 
                [
                    'query' => ['brand' => $brand, 'country'=> $country, 'language'=> $language, 'source'=> $source ],
                    'json' => ['vin' => $vin, 'mileage'=> $mileage]
                    
                ]);    
            }catch (Exception $e) {
                $this->logger->error("VehicleService:addVehicle : Cached Exception " . $e->getMessage());            
                return new WSResponse($e->getCode(), $e->getMessage());
            }
        }
    }
