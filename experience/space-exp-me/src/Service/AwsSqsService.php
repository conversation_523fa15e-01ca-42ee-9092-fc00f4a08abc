<?php

namespace App\Service;

use Aws\Sqs\SqsClient;
use Aws\Result;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

class AwsSqsService
{
    private string $queueUrl;

    public function __construct(
        private SqsClient $sqsClient,
        private string $queueBaseUrl
    ) {
    }

    /**
     * Sends a message to the specified SQS queue.
     *
     * @param string $messageBody The body of the message.
     *
     * @return Aws\Result The result of the sendMessage operation.
     * @throws \Exception If an error occurs during the sendMessage operation.
     */
    public function sendMessage(string $messageBody): Result
    {
        try {
            return $this->sqsClient->sendMessage([
                'QueueUrl' => $this->queueUrl,
                'MessageBody' => $messageBody,
            ]);
        } catch (\Exception $e) {
            throw $e;
        }
    }

    public function setQueue(string $queueName): string
    {
        return $this->queueUrl = $this->queueBaseUrl . '/' . $queueName;
    }
}
