<?php

namespace App\Service;

use Symfony\Component\Serializer\SerializerInterface;
use App\Model\ObjectToRefreshModel;
use App\Trait\LoggerTrait;

/**
 * Manage the process of enqueuing objects to refresh for asynchronous processing in an Amazon SQS queue.
 */
class RefreshQueueService
{
    use LoggerTrait;

    function __construct(
        private AwsSqsService $awsSqsService,
        private array $queuesConfig,
        private SerializerInterface $serializer
    ) {
    }

    /**
     * @throws \Exception When errors occur during enqueueing.
     *
     */
    public function enqueueObject(ObjectToRefreshModel $objectToRefresh): bool
    {
        try {
            $queueName = $this->getQueue($objectToRefresh->getName());
            $this->awsSqsService->setQueue($queueName);

            $refreshMessage = $this->serializer->serialize(
                $objectToRefresh,
                "json"
            );
            
            return (bool)$this->awsSqsService->sendMessage($refreshMessage);
        } catch (\Exception $e) {
            $this->logger->error("Failed to enqueue object:", [
                "exception" => $e,
            ]);

            throw $e;
        }
    }

    /**
     * @throws \Exception
     */
    private function getQueue(string $queueName): string
    {
        try {
            if (!isset($this->queuesConfig[$queueName])) {
                throw new \Exception("The queue $queueName is not configured ");
            }
            return $this->queuesConfig[$queueName]["queue_name"];
        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * check if the current object is ready to send it as a message
     * @param string $objectName
     * @param mixed $object
     * @return bool
     */
    public function isObjectValid(string $objectName, mixed $object): bool
    {

        return ($objectName === "vehicle" && $object !== true) ? false : true;
    }

}
