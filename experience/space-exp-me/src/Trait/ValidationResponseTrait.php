<?php

namespace App\Trait;

use App\Helper\ErrorResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\ConstraintViolationListInterface;

trait ValidationResponseTrait
{
    /**
     * Get the validation error messages.
     *
     * @param  ConstraintViolationListInterface  $errors
     *
     * @return array
     */
    protected static function getValidationMessages(ConstraintViolationListInterface $errors): array
    {
        $messages = [];

        if ($errors->count() === 0) {
            return $messages;
        }

        foreach ($errors as $error) {

            /** @var  ConstraintViolation  $error */
            $name            = str_replace(['[', ']'], '', $error->getPropertyPath() ?? '');
            $messages[$name] = $error->getMessage();
        }

        return $messages;
    }

    /**
     * Get the validation error response.
     *
     * @param  array  $messages
     * @param  int    $statusCode
     *
     * @return JsonResponse
     */
    protected static function getValidationErrorResponse(array $messages): ErrorResponse
    {
        return (new ErrorResponse('validation_failed', Response::HTTP_UNPROCESSABLE_ENTITY))->setErrors($messages);
    }
}