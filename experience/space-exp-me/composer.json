{"type": "project", "license": "proprietary", "minimum-stability": "stable", "prefer-stable": true, "require": {"php": "^8.0", "ext-ctype": "*", "ext-iconv": "*", "aws/aws-sdk-php-symfony": "^2.6", "doctrine/annotations": "^2.0", "nelmio/api-doc-bundle": "^4.11", "phpstan/phpdoc-parser": "^1.25", "symfony/asset": "6.4.*", "symfony/console": "6.4.*", "symfony/dotenv": "6.4.*", "symfony/flex": "^1.17|^2", "symfony/framework-bundle": "6.4.*", "symfony/http-client": "6.4.*", "symfony/monolog-bundle": "^3.8", "symfony/property-access": "6.4.*", "symfony/runtime": "6.4.*", "symfony/serializer": "6.4.*", "symfony/twig-bundle": "6.4.*", "symfony/uid": "6.4.*", "symfony/validator": "6.4.*", "symfony/yaml": "6.4.*", "twig/extra-bundle": "^2.12|^3.0", "twig/twig": "^2.12|^3.0"}, "config": {"allow-plugins": {"composer/package-versions-deprecated": true, "php-http/discovery": true, "symfony/flex": true, "symfony/runtime": true}, "optimize-autoloader": true, "preferred-install": {"*": "dist"}, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "6.4.*"}}, "require-dev": {"phpunit/phpunit": "^9.5", "rector/rector": "^0.19", "symfony/browser-kit": "6.4.*", "symfony/css-selector": "6.4.*", "symfony/maker-bundle": "^1.48", "symfony/phpunit-bridge": "^6.2"}}