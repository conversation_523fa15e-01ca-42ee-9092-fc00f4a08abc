<?php

namespace App\Tests\Model;

use App\Model\VehicleModel;
use App\Model\VehicleOrder;
use PHPUnit\Framework\TestCase;

class VehicleModelTest extends TestCase
{
    public function testGettersAndSetters()
    {
        $vehicleOrder = new VehicleOrder();
        $vehicleModel = new VehicleModel();
        $vehicleOrder->setMopId('123');
        $vehicleOrder->setOrderFormId('ABC123');
        $vehicleOrder->setTrackingStatus('My Tracking Status');
        $vehicleOrder->setIsUpdated(true);
        $vehicleOrder->setOrderFormStatus('My Order Form Status');

        $vehicleModel->setId('123');
        $vehicleModel->setVin('ABC123');
        $vehicleModel->setLabel('My Car');
        $vehicleModel->setVersionId('456');
        $vehicleModel->setBrand('My Brand');
        $vehicleModel->setVisual('My Visual');
        $vehicleModel->setIsOrder(true);
        $vehicleModel->setVehicleOrder($vehicleOrder);

        $this->assertEquals('123', $vehicleOrder->getMopId());
        $this->assertEquals('ABC123', $vehicleOrder->getOrderFormId());
        $this->assertEquals('My Tracking Status', $vehicleOrder->getTrackingStatus());
        $this->assertTrue($vehicleOrder->isIsUpdated());
        $this->assertEquals('My Order Form Status', $vehicleOrder->getOrderFormStatus());

        $this->assertEquals('123', $vehicleModel->getId());
        $this->assertEquals('ABC123', $vehicleModel->getVin());
        $this->assertEquals('My Car', $vehicleModel->getLabel());
        $this->assertEquals('456', $vehicleModel->getVersionId());
        $this->assertEquals('My Brand', $vehicleModel->getBrand());
        $this->assertEquals('My Visual', $vehicleModel->getVisual());
        $this->assertTrue($vehicleModel->isIsOrder());
        $this->assertSame($vehicleOrder, $vehicleModel->getVehicleOrder());
    }
}