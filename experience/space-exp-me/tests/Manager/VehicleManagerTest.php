<?php

namespace App\Tests\Manager;

use App\Helper\ErrorResponse;
use App\Helper\SuccessResponse;
use App\Helper\WSResponse;
use App\Manager\VehicleManager;
use App\Service\VehicleService;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;

class VehicleManagerTest extends TestCase
{
    /**
     * @var VehicleManager
     */
    private $vehicleManager;
    /**
     * @var VehicleService
     */
    private $vehicleService;

    public function setUp(): void
    {
        $this->vehicleService = $this->createMock(VehicleService::class);
        $this->vehicleManager = new VehicleManager($this->vehicleService);

        $logger = $this->createMock(LoggerInterface::class);
        $logger->expects($this->any())
            ->method('info')
            ;
        $logger->expects($this->any())
            ->method('error')
            ;

        $this->vehicleManager->setLogger($logger);
    }

    public function testResponseIsSuccessResponse()
    {
        $userId = '815ef5d49f0b4dce874859657ae98249';
        $this->vehicleService->expects($this->once())
            ->method('getVehicles')
            ->with($userId)
            ->willReturn(new WSResponse(Response::HTTP_OK, ["success" => []]));

        $response = $this->vehicleManager->getVehicles($userId);
        $this->assertInstanceOf(SuccessResponse::class, $response);
    }
    public function testResponseIsErrorResponse()
    {
        $userId = '815ef5d49f0b4dce874859657ae98249';
        $this->vehicleService->expects($this->once())
            ->method('getVehicles')
            ->with($userId)
            ->willReturn(new WSResponse(404, ['Not found']));

        $response = $this->vehicleManager->getVehicles($userId);
        $this->assertInstanceOf(ErrorResponse::class, $response);
    }

    public function testGetVehiclesThrowsException()
    {
        $this->vehicleService->expects($this->once())
            ->method('getVehicles')
            ->with('userId')
            ->willThrowException(new \Exception('', 500));

        $result = $this->vehicleManager->getVehicles('userId');
        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals(500, $result->toArray()['code']);
    }

    public function testGetVehicleSummaryWithSuccedResponse(): void
    {
        // create a response mock
        $response = $this->CreateMock(WSResponse::class);
        $response->expects($this->any())
            ->method("getCode")
            ->willReturn(Response::HTTP_OK);
        $response->expects($this->any())
            ->method("getData")
            ->willReturn(['success' => 'Success result']);

        // create a mock for VehicleService that return response mock
        $vehicleService = $this->CreateMock(VehicleService::class);
        $vehicleService->expects($this->any())
            ->method('getVehicleSummary')
            ->willReturn($response);

        // Run the method of manager
        $manager = new VehicleManager($vehicleService);
        $userId = '1111';
        $vehicleId = '2222';
        $managerResponse = $manager->getVehicleSummary($userId, $vehicleId);

        // check if result is correct
        $this->assertInstanceOf(SuccessResponse::class, $managerResponse);
        $this->assertSame('Success result', $managerResponse->getData());
    }

    public function testGetVehicleSummaryWithErrorResponse(): void
    {
        $response = $this->createMock(WSResponse::class);
        $response->expects($this->any())
            ->method("getcode")
            ->WillReturn(Response::HTTP_INTERNAL_SERVER_ERROR);
        $response->expects($this->any())
            ->method("getdata")
            ->WillReturn('Error result');

        $vehicleService = $this->createMock(VehicleService::class);
        $vehicleService->expects($this->any())
            ->method('getVehicleSummary')
            ->WillReturn($response);

        $manager = new VehicleManager($vehicleService);
        $userId = '1111';
        $vehicleId = '2222';
        $managerResponse = $manager->getVehicleSummary($userId, $vehicleId);

        $this->assertInstanceOf(ErrorResponse::class, $managerResponse);
        $result = $managerResponse->toArray();
        $code = $result['code'] ?? null;
        $error = $result['content']['error']['message'];
        $this->assertSame(Response::HTTP_INTERNAL_SERVER_ERROR, $code);
        $this->assertSame('Error result', $error);
    }

    public function testGetVehicleSummaryWithExeception(): void
    {
        $logger = $this->CreateMock(LoggerInterface::class);
        $logger->expects($this->once())
            ->method('error');

        // create a mock for VehicleService that return response mock
        $vehicleService = $this->CreateMock(VehicleService::class);
        $vehicleService->expects($this->any())
            ->method('getVehicleSummary')
            ->willReturn($this->throwException(new \Exception('This is an Exception', 4235)));

        // Run the method of manager
        $manager = new VehicleManager($vehicleService);
        $manager->setLogger($logger);
        $userId = '1111';
        $vehicleId = '2222';
        $managerResponse = $manager->getVehicleSummary($userId, $vehicleId);

        // check if result is correct
        $this->assertInstanceOf(ErrorResponse::class, $managerResponse);
        $result = $managerResponse->toArray();
        $this->assertIsArray($result);
        $this->assertSame('This is an Exception', $result['content']['error']['message']);
        $this->assertEquals(4235, $result['code']);
    }
   
    public function testAddVehicleWithSuccedResponse():void
    {
        // create a response mock
            $response = $this->CreateMock(WSResponse::class);
            $response->expects($this->any())
                ->method("getCode")
                ->willReturn(Response::HTTP_OK);
            $response->expects($this->any())
                ->method("getData")
                ->willReturn(['success' => 'Success result']);
        // create a mock for VehicleService that return response mock
        $vehicleService = $this->CreateMock(VehicleService::class);
        $vehicleService->expects($this->any())
                    ->method('addVehicle')
                    ->willReturn($response);
        $mockLogger = $this->createMock(LoggerInterface::class);
        $manager = new VehicleManager($vehicleService); 
        $brand = 'citrion';
        $country = 'india';
        $language = 'english';
        $source = 'query';
        $vin = 'VR3UPHNKSKT101600';
        $mileage = '0';
        $managerResponse = $manager->AddVehicle($brand,$country,$language,$source,$vin,$mileage);
        // check if result is correct
        $this->assertInstanceOf(SuccessResponse::class, $managerResponse);
        $this->assertSame('Success result', $managerResponse->getData());
    }

    public function testAddVehicleWithExeception(): void
    {
        $logger = $this->CreateMock(LoggerInterface::class);
        $logger->expects($this->once())
            ->method('error');

        // create a mock for VehicleService that return response mock
        $vehicleService = $this->CreateMock(VehicleService::class);
        $vehicleService->expects($this->any())
            ->method('addVehicle')
            ->willReturn($this->throwException(new \Exception('This is an Exception', 4235)));
        // Run the method of manager
        $manager = new VehicleManager($vehicleService);
        $manager->setLogger($logger);       
        $brand = 'Citrion';
        $country = 'india';
        $language = 'english';
        $source = 'query';
        $vin = 'VR3UPHNKSKT101600';
        $mileage = '0';
        $managerResponse = $manager->AddVehicle($brand,$country,$language,$source,$vin,$mileage);
        // check if result is correct
        $this->assertInstanceOf(ErrorResponse::class, $managerResponse);
        $result = $managerResponse->toArray();
        $this->assertIsArray($result);
        $this->assertSame('This is an Exception', $result['content']['error']['message']);
        $this->assertEquals(4235, $result['code']);
    }  

    public function testAddVehicleWithErrorResponse(): void
    {
        $brand = 'citrion';
        $country = 'india';
        $language = 'english';
        $source = 'query';
        $vin = 'VR3UPHNKSKT101600';
        $mileage = '0';
        $this->vehicleService->expects($this->once())
            ->method('addVehicle')
            ->with($brand)
            ->willReturn(new WSResponse(404, ['Not found']));
        $response = $this->vehicleManager->AddVehicle($brand,$country,$language,$source,$vin,$mileage);
        $this->assertInstanceOf(ErrorResponse::class, $response);
    }
    

    

}