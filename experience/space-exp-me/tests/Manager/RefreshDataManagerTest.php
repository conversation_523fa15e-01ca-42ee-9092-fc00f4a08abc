<?php

namespace App\Tests\Manager;

use App\Helper\ErrorResponse;
use App\Helper\SuccessResponse;
use App\Manager\RefreshDataManager;
use App\Model\ObjectsListToRefreshModel;
use App\Service\RefreshQueueService;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;

class RefreshDataManagerTest extends TestCase
{
    private RefreshDataManager $refreshDataManager;
    private RefreshQueueService $refreshQueueServiceMock;

    public function setUp(): void
    {
        $this->refreshQueueServiceMock = $this->createMock(RefreshQueueService::class);

        $loggerMock = $this->createMock(LoggerInterface::class);
        $loggerMock->expects($this->any())
            ->method('info')
        ;
        $loggerMock->expects($this->any())
            ->method('error')
        ;
        $this->refreshDataManager = new RefreshDataManager($this->refreshQueueServiceMock);

        $this->refreshDataManager->setLogger($loggerMock);
    }

    public function testSuccessEnqueueObjectsToRefresh()
    {
        $objectsList = new ObjectsListToRefreshModel(userId: '123', source: 'web', objects: [['object1' => ['vin' => 'XXX123']], ['object2' => ['attr' => 'test']]])
        ;
        $this->refreshQueueServiceMock->expects($this->any())
            ->method('enqueueObject')
            ->willReturn(true);

        $response = $this->refreshDataManager->enqueueObjectsToRefresh($objectsList);

        $this->assertInstanceOf(SuccessResponse::class, $response);
    }

    public function testOneFailedEnqueueObjectsToRefresh()
    {
        $objectsList = new ObjectsListToRefreshModel(userId: '123', source: 'web', objects: [['object1' => ['vin' => 'XXX123']], ['object2' => ['attr' => 'test']]])
        ;

        $this->refreshQueueServiceMock->expects($this->exactly(2))
            ->method('enqueueObject')
            ->willThrowException(new \Exception());

        $response = $this->refreshDataManager->enqueueObjectsToRefresh($objectsList);

        $this->assertInstanceOf(ErrorResponse::class, $response);
    }
}
