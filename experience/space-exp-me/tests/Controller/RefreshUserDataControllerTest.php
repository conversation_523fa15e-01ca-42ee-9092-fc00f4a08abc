<?php

namespace App\Tests\Controller;

use App\Controller\RefreshUserDataController;
use App\Helper\SuccessResponse;
use App\Manager\RefreshDataManager;
use PHPUnit\Framework\TestCase;
use Psr\Container\ContainerInterface;
use Symfony\Component\HttpFoundation\HeaderBag;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Validator\ConstraintViolationInterface;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class RefreshUserDataControllerTest extends TestCase
{
    public function testRefreshData()
    {
        $controller = new RefreshUserDataController();
        $requestMock = $this->createMock(Request::class);
        $refreshDataManagerMock = $this->createMock(RefreshDataManager::class);
        $validatorMock = $this->createMock(ValidatorInterface::class);
        $headerBagMock = $this->createMock(HeaderBag::class);
        $headerBagMock->expects($this->once())
            ->method('get')
            ->with('userId')
            ->willReturn('123');
        $requestMock->expects($this->once())
            ->method('getContent')
            ->willReturn('{
                "source": "web",
                "objects": 
                    {
                        "subscriptions": {
                            "vin" :"xxxxxx"
            
                        }
                    }
                }');

        $requestMock->headers = $headerBagMock;

        $validatorMock->expects($this->once())
            ->method('validate')
            ->willReturn($this->createMock(ConstraintViolationListInterface::class));

        $refreshDataManagerMock
            ->expects($this->once())
            ->method('enqueueObjectsToRefresh')
            ->willReturn(new SuccessResponse('success message'));
        $controller->setContainer($this->createMock(ContainerInterface::class));
        $response = $controller->refreshData(
            $requestMock,
            $refreshDataManagerMock,
            $validatorMock
        );

        $this->assertEquals(JsonResponse::HTTP_OK, $response->getStatusCode());
        $this->assertStringContainsString('success message', $response->getContent());
        $this->assertInstanceOf(JsonResponse::class, $response);
    }

    public function testInvalidObjectList()
    {
        $controller = new RefreshUserDataController();
        $controller->setContainer($this->createMock(ContainerInterface::class));
        $requestMock = $this->createMock(Request::class);
        $refreshDataManagerMock = $this->createMock(RefreshDataManager::class);
        $validatorMock = $this->createMock(ValidatorInterface::class);
        $headerBagMock = $this->createMock(HeaderBag::class);
        $headerBagMock->expects($this->once())
            ->method('get')
            ->with('userId')
            ->willReturn('123');

        $requestMock->headers = $headerBagMock;
        $requestMock->expects($this->once())
            ->method('getContent')
            ->willReturn('{}');
        $constraintViolationMock = $this->createMock(ConstraintViolationInterface::class);
        $constraintViolationMock->expects($this->any())
            ->method('getMessage')
            ->willReturn('validation failed !');

        $validatorMock->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList([$constraintViolationMock]));

        $response = $controller->refreshData(
            $requestMock,
            $refreshDataManagerMock,
            $validatorMock
        );

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());
        $this->assertSame($response->getContent(), '{"error":{"message":"validation_failed","errors":{"":"validation failed !"}}}');

        
    }
}
