<?php

declare(strict_types=1);

namespace App\Tests\Controller;

use App\Controller\UserVehicleController;
use App\Helper\SuccessResponse;
use App\Kernel;
use App\Manager\VehicleManager;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBag;
use Symfony\Component\HttpFoundation\HeaderBag;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Bundle\MakerBundle\Validator;
use Symfony\Component\Validator\ConstraintViolation;
use PHPUnit\Framework\TestCase;
use Symfony\Component\DependencyInjection\ContainerInterface;


class UserVehicleControllerTest extends WebTestCase
{
    private $validator;

    protected static function getKernelClass(): string
    {
        return Kernel::class;
    }

    protected function setUp(): void
    {
        self::bootKernel();
        $container = static::getContainer();
        $this->validator = $container->get(ValidatorInterface::class);
    }

    public function testGetVehiclesReturnsError()
    {
        $vehicleManager = $this->createMock(VehicleManager::class);
        $controller = new UserVehicleController();
        $controller->setContainer(static::getContainer());

        $request = Request::create('/v1/vehicles', 'GET', ['brand' => 'AP']);
        $expected["error"] = [
            "message" => "validation_failed",
            "errors" => ['userId' => 'This value should not be blank.'
            ]
        ];

        $response = $controller->getVehicles($request, $vehicleManager, $this->validator);
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals($expected, json_decode($response->getContent(), true));
    }
    public function testGetVehiclesReturnsSuccess()
    {
        $validator = $this->createMock(ValidatorInterface::class);
        $request = $this->createMock(Request::class);
        $vehicleManager = $this->createMock(VehicleManager::class);
        $controller = new UserVehicleController();
        $controller->setContainer(static::getContainer());
        $headerBag = $this->createMock(HeaderBag::class);
        $queryBag = $this->createMock(ParameterBag::class);

        $headerBag->expects($this->any())
            ->method('get')
            ->willReturn('userId');
        $queryBag->expects($this->any())
            ->method('get')
            ->willReturn('brand');
        $request->headers = $headerBag;
        $request->query = $queryBag;

        $validator->expects($this->once())
            ->method('validate')
            ->willReturn($this->createMock(ConstraintViolationListInterface::class));

        $vehicleManager->expects($this->once())
            ->method('getVehicles')
            ->willReturn(new SuccessResponse(['vehicles' => []], Response::HTTP_OK));

        $response = $controller->getVehicles($request, $vehicleManager, $validator);
        $expectedResponse = new JsonResponse(['success' => ['vehicles' => []]], Response::HTTP_OK);
        $this->assertSame($response->getContent(), $expectedResponse->getContent());
        $this->assertInstanceOf(JsonResponse::class, $response);
    }

    public function testVehicleOrderSummaryWithSuccess()
    {
        $vehicleManagerMock = $this->createMock(VehicleManager::class);

        $success = new SuccessResponse('Success result', Response::HTTP_OK);
        $vehicleManagerMock->method('getVehicleSummary')
            ->willReturn($success);

        $container = static::getContainer();
        $validator = $container->get(ValidatorInterface::class);

        $userId = 1;
        $vehicleId = '123';
        $url = "https://api-sys-idp.space.com/v1/vehicles/$vehicleId/summary";
        $request = Request::create($url, 'GET', [], [], [], ['HTTP_USERID' => $userId]);

        // Use the container to retrieve the controller
        $controller = $container->get(UserVehicleController::class);
        $response = $controller->VehicleOrderSummary($validator, $vehicleManagerMock, $request, $vehicleId);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
    }

    public function testVehicleOrderSummaryWithError()
    {
        $vehicleManagerMock = $this->createMock(VehicleManager::class);

        $vehicleManagerMock
            ->expects($this->never())
            ->method('getVehicleSummary'); 

        $container = static::getContainer();

        $validator = $container->get(ValidatorInterface::class);

        $vehicleId = '123';
        $url = "https://api-sys-idp.space.com/v1/vehicles/$vehicleId/summary";
        $request = Request::create($url, 'GET', [], [], [], []);

        // Use the container to retrieve the controller
        $controller = $container->get(UserVehicleController::class);
        $response = $controller->VehicleOrderSummary($validator, $vehicleManagerMock, $request, $vehicleId);
        
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);
        
        $error = $responseData['error'] ?? null;        
        $this->assertIsArray($error);
    }

    public function testAddVehicleWithSuccess()
    {
        $vehicleManager = $this->createMock(VehicleManager::class);
        $vehicleManager->expects($this->once())
            ->method('addVehicle')
            ->willReturn(new SuccessResponse(['success'=>true],Response::HTTP_OK));

        $validator = $this->createMock(ValidatorInterface::class);
        $validator->expects($this->once())
        ->method('validate')
         ->willReturn(new ConstraintViolationList([]));

        $container = $this->createMock(ContainerInterface::class);
        $container->expects($this->once())
            ->method('has')
            ->willReturn(false);

        $controller = new UserVehicleController($vehicleManager);
        $controller->setContainer($container);
        $request = new Request();
        $request->query->set('brand', 'citrion');
        $request->query->set('country', 'india');
        $request->query->set('language', 'FR');
        $request->query->set('source', 'query');

        $request->request->set('vin', '1234567890');
        $request->request->set('mileage', '10000');

        $response = $controller->addVehicle($validator,$vehicleManager,$request);
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
    }


    public function testAddVehicleWithError()
    {
        $vehicleManager = $this->createMock(VehicleManager::class);
        $vehicleManager->expects($this->never())
                    ->method('addVehicle'); 

        $validator = $this->createMock(ValidatorInterface::class);
        $validator->expects($this->once())
                  ->method('validate')
                  ->willReturn(new ConstraintViolationList([
        // Simulate validation errors
        $this->createMock(ConstraintViolation::class),
        $this->createMock(ConstraintViolation::class),
        ]));
        $container = $this->createMock(ContainerInterface::class);
        $container->expects($this->once())
            ->method('has')
            ->willReturn(false);

        $controller = new UserVehicleController($vehicleManager);
        $controller->setContainer($container);
        
        $request = new Request();
        $request->query->set('brand', 'citrion');
        $request->query->set('country', 'india');
        $request->query->set('language', 'FR');
        $request->query->set('source', 'query');

        $request->request->set('vin', '1234567890');
        $request->request->set('mileage', '-10000'); // Invalid mileage       
        $response = $controller->addVehicle($validator, $vehicleManager, $request);
        
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);
    }  
 
}
   
