<?php

namespace App\Tests\Service;

use App\Model\ObjectToRefreshModel;
use App\Service\AwsSqsService;
use App\Service\RefreshQueueService;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\Serializer\SerializerInterface;

class RefreshQueueServiceTest extends TestCase
{
    private AwsSqsService $awsSqsServiceMock;
    private RefreshQueueService $service;

    public function setUp(): void
    {
        $this->awsSqsServiceMock = $this->createMock(AwsSqsService::class);
        $serializerMock = $this->createMock(SerializerInterface::class);
        $queuesConfig = ['subscriptions' => ['queue_name' => 'subscriptions_test_queue']];

        $loggerMock = $this->createMock(LoggerInterface::class);
        $loggerMock->expects($this->any())
            ->method('info')
        ;
        $loggerMock->expects($this->any())
            ->method('error')
        ;
        $this->service = new RefreshQueueService($this->awsSqsServiceMock, $queuesConfig, $serializerMock);
        $this->service->setLogger($loggerMock);
    }

    public function testEnqueueObjectSuccess(): void
    {
        $objectToRefreshName = 'subscriptions';
        $objectToRefresh = new ObjectToRefreshModel(name: $objectToRefreshName, userId: '123', source: 'web', object: [$objectToRefreshName => ['vin' => 'xxxxxxx']]);

        $this->awsSqsServiceMock
             ->expects($this->once())
             ->method('setQueue')
             ->willReturn('http://aws_sqs_queue_url.test/test_queue');

        $response = $this->service->enqueueObject($objectToRefresh);

        $this->assertTrue($response);
    }

    public function testFailedEnqueueObject()
    {
        $objectToRefreshName = 'subscriptions';
        $objectToRefresh = new ObjectToRefreshModel(name: $objectToRefreshName, userId: '123', source: 'web', object: [$objectToRefreshName => ['vin' => 'xxxxxxx']]);

        $this->awsSqsServiceMock
             ->expects($this->once())
             ->method('sendMessage')
             ->willThrowException(new \Exception());
        $this->expectException(\Exception::class);

        $response = $this->service->enqueueObject($objectToRefresh);
    }

    public function testNonConfiguredQueue()
    {
        $objectToRefreshName = 'not_configred_object';
        $objectToRefresh = new ObjectToRefreshModel(name: $objectToRefreshName, userId: '123', source: 'web', object: [$objectToRefreshName => ['vin' => 'xxxxxxx']]);

        $this->expectException(\Exception::class);

        $this->service->enqueueObject($objectToRefresh);
    }
}
