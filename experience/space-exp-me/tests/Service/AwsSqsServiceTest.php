<?php

namespace App\Tests\Service;

use App\Service\AwsSqsService;
use Aws\Sqs\SqsClient;
use PHPUnit\Framework\TestCase;

class AwsSqsServiceTest extends TestCase
{
    private SqsClient $sqsClientMock;
    private string $queueBaseUrl;
    private AwsSqsService $service;

    protected function setUp(): void
    {
        $this->sqsClientMock = $this->createMock(SqsClient::class);
        $this->queueBaseUrl = 'https://queue_url_test.test';

        $this->service = new AwsSqsService($this->sqsClientMock, $this->queueBaseUrl);
    }

    public function testSetQueue()
    {
        $queueName = 'test_queue';
        $response = $this->service->setQueue($queueName);
        $this->assertSame($this->queueBaseUrl.'/'.$queueName, $response);
    }
}
