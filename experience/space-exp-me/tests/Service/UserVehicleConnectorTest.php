<?php

namespace App\Tests\Service;

use App\Helper\WSResponse;
use App\Manager\VehicleManager;
use App\Service\ProcUserVehicleConnector;
use App\Service\connector;
use App\Service\CustomHttpClient;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpClient\Response\MockResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class UserVehicleConnectorTest extends TestCase
{
    /**
     * @var ProcUserVehicleConnector
     */
    private $connector;

    /**
     * @var CustomHttpClient
     */
    private $client;

    public function setUp(): void
    {
        $this->client = $this->createMock(CustomHttpClient::class);
        $url = "";
        $this->connector = new ProcUserVehicleConnector($this->client,$url);

        $logger = $this->createMock(LoggerInterface::class);
        $logger->expects($this->any())
               ->method('info')
               ;
        $logger->expects($this->any())
               ->method('error')
               ;
         $this->connector->setLogger($logger);
    }

    public function testCallMethodReturnsWSResponse()
    {
         $response = new MockResponse('{"status": "success"}', [
            'http_code' => 200
        ]);
        $this->client->expects($this->once())
                ->method('request')
                ->willReturn(new WSResponse(Response::HTTP_OK,[]));
        $userId = '';
        $wsResponse = $this->connector->call('GET','/v1/vehicles',['headers' => ['userId' => $userId]]);
        $this->assertInstanceOf(WSResponse::class, $wsResponse);
    }

    public function testCallMethodThrowException()
    {
        $this->client->expects($this->once())
            ->method('request')
            ->willThrowException(new \Exception('API error', 500));
        $userId = '';
        $wsResponse = $this->connector->call('GET','/v1/vehicles',['headers' => ['userId' => $userId]]);

        $this->assertInstanceOf(WSResponse::class, $wsResponse);
        $this->assertEquals(500, $wsResponse->getCode());
        $this->assertEquals('API error', $wsResponse->getData());
    }
}