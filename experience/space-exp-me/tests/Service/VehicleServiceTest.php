<?php

namespace App\Tests\Service;

use App\Helper\WSResponse;
use App\Service\ProcUserVehicleConnector;
use App\Service\VehicleService;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class VehicleServiceTest extends TestCase
{
    /**
     * @var VehicleService
     */
    private $vehicleService;
    /**
     * @var ProcUserVehicleConnector
     */
    private $connector;

    public function setUp(): void
    {
        $this->connector = $this->createMock(ProcUserVehicleConnector::class);
        $this->vehicleService = new VehicleService($this->connector);

        $logger = $this->createMock(LoggerInterface::class);
        $logger->expects($this->any())
            ->method('info')
            ;
        $logger->expects($this->any())
            ->method('error')
            ;
        $this->vehicleService->setLogger($logger);

        $this->vehicleService->setLogger($logger);
    }

    public function testIfResponseIsWSResponse()
    {
        $this->connector->expects($this->once())
            ->method('call')
            ->willReturn(new WSResponse(Response::HTTP_OK, []));
        $userId = '';
        $response = $this->vehicleService->getVehicles($userId);
        $this->assertInstanceOf(WSResponse::class, $response);
    }

    public function testIfGetVehiclesThrowsException()
    {
        $this->connector->expects($this->once())
            ->method('call')
            ->willThrowException(new \Exception('API error', 400));
        $userId = '';
        $response = $this->vehicleService->getVehicles($userId);
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(400, $response->getCode());
        $this->assertEquals('API error', $response->getData());
    }

    public function testGetVehiclesReturnWithSuccess(): void
    {
        $userId = '123';
        $wsResponse = new WSResponse(1122, 'This is a success response');
        $options = ['headers' => ['userId' => '123']];

        $this->connector
            ->expects($this->once())
            ->method('call')
            ->with(
                Request::METHOD_GET,
                $this->anything(),
                $this->equalTo($options)
            )
            ->willReturn($wsResponse);

        $response = $this->vehicleService->getVehicles($userId);

        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertSame(1122, $response->getCode());
        $this->assertSame('This is a success response', $response->getData());
    }

    public function testGetVehiclesReturnWithException(): void
    {
        $userId = '123';

        $this->connector
            ->expects($this->once())
            ->method('call')
            ->willReturn($this->throwException(new \Exception('This is an exception', 123)));

        $response = $this->vehicleService->getVehicles($userId);

        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertSame(123, $response->getCode());
        $this->assertSame('This is an exception', $response->getData());
    }

    public function testGetVehiclesSummaryReturnWithSuccess(): void
    {
        $userId = '123';
        $vehicleId = '456';

        $wsResponse = new WSResponse(1122, 'This is a success response');
        $options = ['headers' => ['userId' => '123']];

        $this->connector
            ->expects($this->once())
            ->method('call')
            ->with(
                Request::METHOD_GET,
                $this->anything(),
                $this->equalTo($options)
            )
            ->willReturn($wsResponse);

        $response = $this->vehicleService->getVehicleSummary($userId, $vehicleId);

        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertSame(1122, $response->getCode());
        $this->assertSame('This is a success response', $response->getData());
    }

    public function testGetVehiclesSummaryReturnWithException(): void
    {
        $userId = '123';
        $vehicleId = '456';

        $this->connector
            ->expects($this->once())
            ->method('call')
            ->willReturn($this->throwException(new \Exception('This is an exception', 123)));

        $response = $this->vehicleService->getVehicleSummary($userId, $vehicleId);

        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertSame(123, $response->getCode());
        $this->assertSame('This is an exception', $response->getData());
    }

    public function testAddVehicleReturnWithSuccess(): void
    {
        $brand = 'citrion';
        $country = 'india';
        $language = 'english';
        $source = 'query';
        $vin = 'VR3UPHNKSKT101600';
        $mileage = '0';

        $wsResponse = new WSResponse(Response::HTTP_OK, 'This is a success response');
        $this->connector
            ->expects($this->once())
            ->method('call')
            ->with(
                Request::METHOD_POST,
                '/v1/vehicles/add',
                $this->equalTo( [
                    'query' => ['brand' => $brand,
                                'country'=> $country, 
                                'language'=> $language,
                                'source'=> $source ],
                    'json' => ['vin' => $vin, 'mileage'=> $mileage]                    
                ])
            )
            ->willReturn($wsResponse);
        $response = $this->vehicleService->addVehicle($brand,$country,$language,$source,$vin,$mileage);
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertSame(Response::HTTP_OK, $response->getCode());
        $this->assertSame('This is a success response', $response->getData());
    }

    public function testAddVehiclesReturnWithException(): void
    {
        $brand = 'citrion';
        $country = 'india';
        $language = 'english';
        $source = 'query';
        $vin = 'VR3UPHNKSKT101600';
        $mileage = '0';

        $this->connector
            ->expects($this->once())
            ->method('call')
            ->willReturn($this->throwException(new \Exception('This is an exception', 123)));

        $response = $this->vehicleService->AddVehicle($brand,$country,$language,$source,$vin,$mileage);

        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertSame(123, $response->getCode());
        $this->assertSame('This is an exception', $response->getData());
    }

}