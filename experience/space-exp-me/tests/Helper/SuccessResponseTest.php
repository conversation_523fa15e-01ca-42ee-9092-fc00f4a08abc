<?php

namespace App\Tests\Helper;

use App\Helper\SuccessResponse;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;

class SuccessResponseTest extends TestCase
{
    private $data;
    private $code;

    public function setup(): void
    {
        $this->data = ['data' => 'test data'];
        $this->code = Response::HTTP_OK;
    }

    public function testConstruct(): void
    {
        $response = new SuccessResponse($this->data, $this->code);
        $this->assertEquals($response->getCode(), $this->code);
        $this->assertEquals($response->getData(), $this->data);
    }

    public function testSetters(): void
    {
        $response = new SuccessResponse('content', Response::HTTP_CREATED);
        $response->setCode($this->code);
        $response->setData($this->data);
        $this->assertEquals($response->getCode(), $this->code);
        $this->assertEquals($response->getData(), $this->data);
    }

    public function testToArray(): void
    {
        $expectedResponse = [
            'content' => ["success" => $this->data],
            'code'    => $this->code
        ];
        $response = new SuccessResponse($this->data, $this->code);
        $this->assertEquals($response->toArray(), $expectedResponse);
    }
}