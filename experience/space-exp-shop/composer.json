{"type": "project", "license": "proprietary", "minimum-stability": "stable", "prefer-stable": true, "require": {"php": ">=8.1", "ext-ctype": "*", "ext-iconv": "*", "friendsofsymfony/rest-bundle": "^3.7", "nelmio/api-doc-bundle": "^4.12", "phpdocumentor/reflection-docblock": "^5.3", "phpstan/phpdoc-parser": "^1.25", "symfony/asset": "6.4.*", "symfony/console": "6.4.*", "symfony/dotenv": "6.4.*", "symfony/flex": "^2", "symfony/framework-bundle": "6.4.*", "symfony/http-client": "6.4.*", "symfony/intl": "6.4.*", "symfony/monolog-bundle": "^3.10", "symfony/property-access": "6.4.*", "symfony/property-info": "6.4.*", "symfony/runtime": "6.4.*", "symfony/serializer": "6.4.*", "symfony/twig-bundle": "6.4.*", "symfony/validator": "6.4.*", "symfony/yaml": "6.4.*"}, "config": {"allow-plugins": {"composer/package-versions-deprecated": true, "symfony/flex": true, "symfony/runtime": true}, "optimize-autoloader": true, "preferred-install": {"*": "dist"}, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php73": "*", "symfony/polyfill-php74": "*", "symfony/polyfill-php80": "*", "symfony/polyfill-php81": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "6.4.*"}}, "require-dev": {"phpstan/phpstan": "^1.11", "phpunit/phpunit": "^9.5", "rector/rector": "^1.1", "symfony/browser-kit": "6.4.*", "symfony/css-selector": "6.4.*", "symfony/phpunit-bridge": "^7.0"}}