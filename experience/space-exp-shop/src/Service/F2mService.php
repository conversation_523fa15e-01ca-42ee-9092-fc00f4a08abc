<?php

namespace App\Service;

use App\Connector\ProcShopConnector;
use App\Helper\WSResponse;
use App\Trait\LoggerTrait;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Contracts\Cache\ItemInterface;

/**
 * Catalog service.
 */
class F2mService
{
    use LoggerTrait;

    const DEFAULT_CACHE_TTL = 60; // 1 min

    public function __construct(
        private ProcShopConnector $procShopConnector,
        private TagAwareCacheAdapter $tagAwareCacheAdapter
    ) {

    }

    /**
     * search charging stations
     */
    public function search(array $params): WSResponse
    {
        return $this->tagAwareCacheAdapter->get("space_process_shop_{$params['brand']}_{$params['longitude']}_{$params['latitude']}", function (ItemInterface $item) use ($params) {
            $item->tag(["f2m_{$params['brand']}_{$params['longitude']}_{$params['latitude']}"]);
            $options = [
                'query' => [
                    'brand' => $params['brand'],
                    'longitude' => $params['longitude'],
                    'latitude' => $params['latitude']
                ]
            ];
            $url = "/v1/f2m_locator/search";
            $this->logger->info("=> " . __METHOD__ . " => Call API [$url] with options ", $options);
            $response = $this->procShopConnector->call(Request::METHOD_GET, $url, $options);
            $catalogCacheTtl = $_ENV['CATALOG_CACHE_TTL'] ?? self::DEFAULT_CACHE_TTL;
            $catalogCacheTtl = (int)$catalogCacheTtl;
            $item->expiresAfter($catalogCacheTtl);

            return $response;
        });
    }
}
