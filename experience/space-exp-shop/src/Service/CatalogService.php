<?php

namespace App\Service;

use App\Connector\ProcShopConnector;
use App\Helper\WSResponse;
use App\Trait\LoggerTrait;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Contracts\Cache\ItemInterface;

/**
 * Catalog service.
 */
class CatalogService
{
    use LoggerTrait;

    const DEFAULT_CACHE_TTL = 60; // 1 min

    public function __construct(
        private ProcShopConnector $procShopConnector,
        private TagAwareCacheAdapter $tagAwareCacheAdapter
    ) {

    }

    /**
     * get Catalog data
     */
    public function getCatalog(array $params): WSResponse
    {
        if ($params['refresh-cache'] === 1) {
            $this->tagAwareCacheAdapter->invalidateTags(["catalog_{$params['userId']}_{$params['vin']}_{$params['brand']}_{$params['country']}_{$params['language']}"]);
        }

        return $this->tagAwareCacheAdapter->get("space_process_shop_{$params['userId']}_{$params['vin']}_{$params['brand']}_{$params['country']}_{$params['language']}", function (ItemInterface $item) use ($params) {
            $item->tag(["catalog_{$params['userId']}_{$params['vin']}_{$params['brand']}_{$params['country']}_{$params['language']}"]);
            $options = [
                'query' => [
                    'brand' => $params['brand'],
                    'country' => $params['country'],
                    'language' => $params['language']
                ],
                'headers' => [
                    'userId' => $params['userId'],
                    'vin' => $params['vin']
                ]
            ];
            $url = "/v1/catalog";
            $this->logger->info("=> " . __METHOD__ . " => Call API [$url] with options ", $options);
            $response = $this->procShopConnector->call(Request::METHOD_GET, $url, $options);
            $catalogCacheTtl = $_ENV['CATALOG_CACHE_TTL'] ?? self::DEFAULT_CACHE_TTL;
            $catalogCacheTtl = (int)$catalogCacheTtl;
            $item->expiresAfter($catalogCacheTtl);

            return $response;
        });



    }
}
