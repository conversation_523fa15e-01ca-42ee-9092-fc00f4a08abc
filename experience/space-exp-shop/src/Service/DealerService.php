<?php

namespace App\Service;

use App\Connector\ProcShopConnector;
use App\DtoRequest\XfEmeaParameterRequest;
use App\Helper\WSResponse;
use App\Trait\LoggerTrait;
use Symfony\Component\HttpFoundation\Request;

/**
 * Dealer service.
 */
class DealerService
{
    use LoggerTrait;

    public const FILTER_RESULTMAX = 10;

    public const FILTER_RMAX = 100;

    public const FILTER_UNIT = 'km';

    public function __construct(
        private ProcShopConnector $connector
    ) {
    }

    /**
     * get Dealer list
     * @param array $params
     * @return WSResponse
     */
    public function getDealerList(array $params): WSResponse
    {
        $options = [
            'query' => [
                'brand' => $params['brand'],
                'rmax' => self::FILTER_RMAX,
                'country' => $params['country'],
                'resultmax' => self::FILTER_RESULTMAX,
                'latitude' => $params['latitude'],
                'unit' => self::FILTER_UNIT,
                'longitude' => $params['longitude'],
                'language' => $params['language'],
                'criterias' => $params['criterias']
            ]
        ];
        $url = "/v1/dealers-list";
        $this->logger->info("=> " . __METHOD__ . " => Call API [$url] with options ", $options);

        $response = $this->connector->call(Request::METHOD_GET, $url, $options);

        return $response;
    }

    /**
     * get Xf Emea Dealer list.
     */
    public function getXfEmeaDealerList(XfEmeaParameterRequest $params): WSResponse
    {
        $options = [
            'query' => $params->__toArray(),
        ];
        $url = '/v1/dealers-list';
        $this->logger->info('=> '.__METHOD__." => Call API [$url] with options for xf Emea ", $options);
        return $this->connector->call(
            Request::METHOD_GET,
            $url,
            $options
        );
    }
}
