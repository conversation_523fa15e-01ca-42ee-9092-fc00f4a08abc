<?php

namespace App\Service;

use Symfony\Component\Cache\Adapter\TagAwareAdapter;
use Symfony\Contracts\Cache\TagAwareCacheInterface;

/**
 * Adapter Used to avoid symfony lock Component acquire and release problems
 * And adapt PSR-16 to PSR-6
 * when using tags
 */
class TagAwareCacheAdapter implements TagAwareCacheInterface
{
    /**
     * @var TagAwareAdapter
     */
    private $_cache;

    /**
     * @param TagAwareAdapter $cache
     */
    public function __construct(TagAwareAdapter $cache)
    {
        $this->_cache = $cache;
    }
    
    /**
     * Remove a group of data from cache
     *
     * @param array $tags
     * @return void
     */
    public function invalidateTags(array $tags): bool 
    {

        return $this->_cache->invalidateTags($tags);
    }

    /**
     * Method to adapt PSR-16 to PSR-6 when getting data
     *
     * @param string $key
     * @param callable $callback
     * @param float $beta
     * @param array $metadata
     * @return mixed
     */
    public function get(string $key, callable $callback, float $beta = null, array &$metadata = null): mixed {
        $item = $this->_cache->getItem($key);
        if (!$item->isHit()) {
            $result = $callback($item);
            $item->set($result);
            $this->_cache->save($item);
        }
        return $item->get();
    }

    /**
     * Delete a cache
     * @param string $key
     * @return boolean
     */
    public function delete(string $key): bool {
        $item = $this->_cache->getItem($key);
        if (!$item->isHit()) { 
            return true;
        }
        return $this->_cache->deleteItem($key);
    }
}
