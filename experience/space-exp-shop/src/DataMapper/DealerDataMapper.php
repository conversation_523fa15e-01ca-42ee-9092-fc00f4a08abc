<?php

namespace App\DataMapper;

use App\Model\DealerModel;
use Symfony\Component\Serializer\SerializerInterface;

class DealerDataMapper
{

    public function __construct(private SerializerInterface $serializer)
    {
    }

    /**
     * load dealers list from data of WS GEOLOC API
     */
    public function createList(array $dealerData): array
    {
        $dealerData = $dealerData['Dealers'] ?? null;

        if (is_null($dealerData) || !is_array($dealerData)) {

            return [];
        }

        $dealersFull = [];
        foreach ($dealerData as $dealerItem) {
            $dealersFull[] = $this->mapData($dealerItem);
        }

        return ['Dealers' => $dealersFull];
    }

    private function mapData(array $dealerData): DealerModel
    {
        $dealerData['distanceFromPoint'] = floatval($dealerData['distanceFromPoint']);
        $openingHoursList = $dealerData['openingHoursList'];
        // get opening hour by specific format 
        $dealerData['openingHoursList'] = $this->getNewOpeningHoursList($openingHoursList);

        return $this->serializer->denormalize($dealerData, DealerModel::class);
    }

    /**
     * group hours/time/days for each type 
     */
    private function getNewOpeningHoursList(array $openingHours): array
    {
        $newList = [];
        foreach ($openingHours as $value) {
            $newList[$value['type']] = $this->transformOpeningHours($value['label']);
        }

        return $newList;
    }

    /**
     * get hours/time/days for a type 
     */
    private function transformOpeningHours(string $label): array
    {
        $label = str_replace("<br />", "\n", $label);
        $lines = explode("\n", trim($label));
        $openingHours = [];
        
        foreach ($lines as $line) {
            list($day, $hours) = explode(":", $line, 2);
            $day = trim($day);
            $hours = trim($hours);
            $openingHours[$day] = $this->getHours($hours);
        }

        return $openingHours;
    }

    /**
     * get the beginning and end of time, if not closed flag added
     */
    private function getHours(string $hours): array
    {
        $timePattern = '/^(\d{2}:\d{2})-(\d{2}:\d{2})$/';
        $time = explode(" ", $hours, 2);
        
        if (preg_match($timePattern, $time[0])) {
            $hoursArray = [
                'closed' => false,
                'time' => $time
            ];
        } else {
            $hoursArray = [
                'closed' => true
            ];
        }

        return $hoursArray;
    }
}
