<?php

namespace App\Transformer;

use App\DtoResponse\XfEmeaDealerDto;
use App\DtoResponse\XfEmeaFtcCodeListDto;
use App\DtoResponse\XfEmeaResponseDto;

class XfEmeaResponseTransformer
{
    public static function mapper(
        ?array $data
    ): XfEmeaResponseDto
    {
        $xfEmeaResponse = new XfEmeaResponseDto();
        foreach ($data['dealers'] as $value) {
            $xfEmeaDealer = new XfEmeaDealerDto();
            $xfEmeaDealer->setDealerId($value['dealerId'] ?? '');
            $xfEmeaDealer->setName($value['name'] ?? '');
            $xfEmeaDealer->setAddress($value['address'] ?? '');
            $xfEmeaDealer->setPhoneNumber($value['phoneNumber'] ?? '');
            $xfEmeaDealer->setLatitude($value['latitude'] ?? '');
            $xfEmeaDealer->setLongitude($value['longitude'] ?? '');
            $xfEmeaResponse->addDealer($xfEmeaDealer);
        }

        foreach ($data['ftcCodeList'] as $ftcCodeData) {
            $ftcCode = new XfEmeaFtcCodeListDto();
            $ftcCode->setDealerCode($ftcCodeData['dealerCode'] ?? '');
            $ftcCode->setMainDealerCode($ftcCodeData['mainDealerCode'] ?? '');
            $ftcCode->setMarket($ftcCodeData['market'] ?? '');
            $ftcCode->setOic($ftcCodeData['oic'] ?? '');
            $xfEmeaResponse->addFtcCodeList($ftcCode);
        }
        
        return $xfEmeaResponse;
    }
}