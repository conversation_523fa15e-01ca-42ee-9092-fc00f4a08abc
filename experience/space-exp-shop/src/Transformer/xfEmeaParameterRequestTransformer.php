<?php

namespace App\Transformer;

use App\DtoRequest\XfEmeaParameterRequest;

class xfEmeaParameterRequestTransformer
{
    public static function mapper(
        array $data
    ): XfEmeaParameterRequest
    {
        $criterias = $data['criterias'] ?? null;
        $xfEmeaParameterRequest = new XfEmeaParameterRequest();
        $xfEmeaParameterRequest->setBrand($data['brand'] ?? '');
        $xfEmeaParameterRequest->setCountry($data['country'] ?? '');
        $xfEmeaParameterRequest->setLatitude($data['latitude'] ?? '');
        $xfEmeaParameterRequest->setLongitude($data['longitude'] ?? '');
        $xfEmeaParameterRequest->setLanguage($data['language'] ?? '');
        $xfEmeaParameterRequest->setRmax($data['limit'] ?? 5);
        if($criterias) {
            $xfEmeaParameterRequest->setCriterias($data['criterias']);
        }

        return $xfEmeaParameterRequest;
    }
}