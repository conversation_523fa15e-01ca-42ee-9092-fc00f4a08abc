<?php

namespace App\Model;

use App\Model\FunctionalityModel;
use App\Model\OfferModel;
use App\Model\FeatureDetailModel;

/**
 * CatalogModel.
 */
class CatalogModel
{
    private string $id;

    private string $brand;

    private string $title;

    private string $productUrlSso;

    private string $description;

    private string $shortDescription;

    private string $fullDescription;

    private string $type;

    private string $productGroupName;

    private string $familyName;

    private string $durationType;

    private string $topMainImage;

    private array $functionalities;

    private array $offers;

    private array $featuresDetails;

    public function getId(): string
    {
        return $this->id;
    }

    public function setId(string $id): void
    {
        $this->id = $id;
    }

    public function getBrand(): string
    {
        return $this->brand;
    }

    public function setBrand(string $brand): void
    {
        $this->brand = $brand;
    }

    public function getTitle(): string
    {
        return $this->title;
    }

    public function setTitle(string $title): void
    {
        $this->title = $title;
    }

    public function getProductUrlSso(): string
    {
        return $this->productUrlSso;
    }

    public function setProductUrlSso(string $productUrlSso): void
    {
        $this->productUrlSso = $productUrlSso;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function setDescription(string $description): void
    {
        $this->description = $description;
    }

    public function getShortDescription(): string
    {
        return $this->shortDescription;
    }

    public function setShortDescription(string $shortDescription): void
    {
        $this->shortDescription = $shortDescription;
    }

    public function getFullDescription(): string
    {
        return $this->fullDescription;
    }

    public function setFullDescription(string $fullDescription): void
    {
        $this->fullDescription = $fullDescription;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function setType(string $type): void
    {
        $this->type = $type;
    }

    public function getProductGroupName(): string
    {
        return $this->productGroupName;
    }

    public function setProductGroupName(string $productGroupName): void
    {
        $this->productGroupName = $productGroupName;
    }

    public function getFamilyName(): string
    {
        return $this->familyName;
    }

    public function setFamilyName(string $familyName): void
    {
        $this->familyName = $familyName;
    }

    public function getDurationType(): string
    {
        return $this->durationType;
    }

    public function setDurationType(string $durationType): void
    {
        $this->durationType = $durationType;
    }

    public function getTopMainImage(): string
    {
        return $this->topMainImage;
    }

    public function setTopMainImage(string $topMainImage): void
    {
        $this->topMainImage = $topMainImage;
    }

    /**
     * @return array<FunctionalityModel>
     */
    public function getFunctionalities(): array
    {
        return $this->functionalities;
    }

    public function setFunctionalities(array $functionalities): void
    {
        $this->functionalities = $functionalities;
    }

    /**
     * @return array<OfferModel>
     */
    public function getOffers(): array
    {
        return $this->offers;
    }

    public function setOffers(array $offers): void
    {
        $this->offers = $offers;
    }

    /**
     * @return array<FeatureDetailModel>
     */
    public function getFeaturesDetails(): array
    {
        return $this->featuresDetails;
    }

    public function setFeaturesDetails(array $featuresDetails): void
    {
        $this->featuresDetails = $featuresDetails;
    }    

}
