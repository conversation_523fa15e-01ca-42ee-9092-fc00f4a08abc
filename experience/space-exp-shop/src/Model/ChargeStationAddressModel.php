<?php

namespace App\Model;

class ChargeStationAddressModel
{
    private ?string $streetNumber;
    private ?string $streetName;
    private ?string $municipalitySubdivision;
    private ?string $municipality;
    private ?string $countrySubdivision;
    private ?string $countrySecondarySubdivision;
    private ?string $countryTertiarySubdivision;
    private ?string $countrySubdivisionName;
    private ?string $postalCode;
    private ?string $extendedPostalCode;
    private ?string $countryCode;
    private ?string $country;
    private ?string $countryCodeISO3;
    private ?string $freeformAddress;
    private ?string $localName;

    public function getStreetNumber(): ?string
    {
        return $this->streetNumber;
    }

    public function setStreetNumber(?string $streetNumber): void
    {
        $this->streetNumber = $streetNumber;
    }

    public function getStreetName(): ?string
    {
        return $this->streetName;
    }

    public function setStreetName(?string $streetName): void
    {
        $this->streetName = $streetName;
    }

    public function getMunicipalitySubdivision(): ?string
    {
        return $this->municipalitySubdivision;
    }

    public function setMunicipalitySubdivision(?string $municipalitySubdivision): void
    {
        $this->municipalitySubdivision = $municipalitySubdivision;
    }

    public function getMunicipality(): ?string
    {
        return $this->municipality;
    }

    public function setMunicipality(?string $municipality): void
    {
        $this->municipality = $municipality;
    }

    public function getCountrySubdivision(): ?string
    {
        return $this->countrySubdivision;
    }

    public function setCountrySubdivision(?string $countrySubdivision): void
    {
        $this->countrySubdivision = $countrySubdivision;
    }

    public function getCountrySecondarySubdivision(): ?string
    {
        return $this->countrySecondarySubdivision;
    }

    public function setCountrySecondarySubdivision(?string $countrySecondarySubdivision): void
    {
        $this->countrySecondarySubdivision = $countrySecondarySubdivision;
    }

    public function getCountryTertiarySubdivision(): ?string
    {
        return $this->countryTertiarySubdivision;
    }

    public function setCountryTertiarySubdivision(?string $countryTertiarySubdivision): void
    {
        $this->countryTertiarySubdivision = $countryTertiarySubdivision;
    }

    public function getCountrySubdivisionName(): ?string
    {
        return $this->countrySubdivisionName;
    }

    public function setCountrySubdivisionName(?string $countrySubdivisionName): void
    {
        $this->countrySubdivisionName = $countrySubdivisionName;
    }

    public function getPostalCode(): ?string
    {
        return $this->postalCode;
    }

    public function setPostalCode(?string $postalCode): void
    {
        $this->postalCode = $postalCode;
    }

    public function getExtendedPostalCode(): ?string
    {
        return $this->extendedPostalCode;
    }

    public function setExtendedPostalCode(?string $extendedPostalCode): void
    {
        $this->extendedPostalCode = $extendedPostalCode;
    }

    public function getCountryCode(): ?string
    {
        return $this->countryCode;
    }

    public function setCountryCode(?string $countryCode): void
    {
        $this->countryCode = $countryCode;
    }

    public function getCountry(): ?string
    {
        return $this->country;
    }

    public function setCountry(?string $country): void
    {
        $this->country = $country;
    }

    public function getCountryCodeISO3(): ?string
    {
        return $this->countryCodeISO3;
    }

    public function setCountryCodeISO3(?string $countryCodeISO3): void
    {
        $this->countryCodeISO3 = $countryCodeISO3;
    }

    public function getFreeformAddress(): ?string
    {
        return $this->freeformAddress;
    }

    public function setFreeformAddress(?string $freeformAddress): void
    {
        $this->freeformAddress = $freeformAddress;
    }

    public function getLocalName(): ?string
    {
        return $this->localName;
    }

    public function setLocalName(?string $localName): void
    {
        $this->localName = $localName;
    }
}