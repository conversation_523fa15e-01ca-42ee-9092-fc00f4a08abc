<?php

namespace App\Model;

class EmailModel
{
	private string $email;

	private string $emailApv;

	private string $emailAgent;

	private string $emailGer;

	private string $emailGrc;

	private string $emailPr;

	private string $emailSales;

	private string $emailVo;

	public function getEmail(): string
	{
		return $this->email;
	}

	public function getEmailApv(): string
	{
		return $this->emailApv;
	}

	public function getEmailAgent(): string
	{
		return $this->emailAgent;
	}

	public function getEmailGer(): string
	{
		return $this->emailGer;
	}

	public function getEmailGrc(): string
	{
		return $this->emailGrc;
	}

	public function getEmailPr(): string
	{
		return $this->emailPr;
	}

	public function getEmailSales(): string
	{
		return $this->emailSales;
	}

	public function getEmailVo(): string
	{
		return $this->emailVo;
	}

	public function setEmail(string $email): self
	{
		$this->email = $email;
		return $this;
	}

	public function setEmailApv(string $emailApv): self
	{
		$this->emailApv = $emailApv;
		return $this;
	}

	public function setEmailAgent(string $emailAgent): self
	{
		$this->emailAgent = $emailAgent;
		return $this;
	}

	public function setEmailGer(string $emailGer): self
	{
		$this->emailGer = $emailGer;
		return $this;
	}

	public function setEmailGrc(string $emailGrc): self
	{
		$this->emailGrc = $emailGrc;
		return $this;
	}

	public function setEmailPr(string $emailPr): self
	{
		$this->emailPr = $emailPr;
		return $this;
	}

	public function setEmailSales(string $emailSales): self
	{
		$this->emailSales = $emailSales;
		return $this;
	}

	public function setEmailVo(string $emailVo): self
	{
		$this->emailVo = $emailVo;
		return $this;
	}
}