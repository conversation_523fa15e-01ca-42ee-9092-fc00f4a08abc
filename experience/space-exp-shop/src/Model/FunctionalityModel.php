<?php

namespace App\Model;

/**
 * FunctionalityModel.
 */
class FunctionalityModel
{
    private string $name;

    private string $description;

    private string $PSAFeatureCode;

    private string $type;

    private int $isMandatory;

    private array $serviceIds;

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): void
    {
        $this->name = $name;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function setDescription(string $description): void
    {
        $this->description = $description;
    }

    public function getPSAFeatureCode(): string
    {
        return $this->PSAFeatureCode;
    }

    public function setPSAFeatureCode(string $PSAFeatureCode): void
    {
        $this->PSAFeatureCode = $PSAFeatureCode;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function setType(string $type): void
    {
        $this->type = $type;
    }

    public function getIsMandatory(): int
    {
        return $this->isMandatory;
    }

    public function setIsMandatory(int $isMandatory): void
    {
        $this->isMandatory = $isMandatory;
    }

    public function getServiceIds(): array
    {
        return $this->serviceIds;
    }

    public function setServiceIds(array $serviceIds): void
    {
        $this->serviceIds = $serviceIds;
    }

}
