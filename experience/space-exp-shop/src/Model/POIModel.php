<?php

namespace App\Model;

class POIModel
{
    private string $name;
    private ?string $phone;
    private ?string $floorLevel;
    private ?array $openingHours;
    private ?string $accessType;
    private ?array $acceptablePayments;
    private ?array $specialRestrictions;
    private ?array $partnerIDs;

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): void
    {
        $this->name = $name;
    }

    public function getPhone(): ?string
    {
        return $this->phone;
    }

    public function setPhone(?string $phone): void
    {
        $this->phone = $phone;
    }

    public function getFloorLevel(): ?string
    {
        return $this->floorLevel;
    }

    public function setFloorLevel(?string $floorLevel): void
    {
        $this->floorLevel = $floorLevel;
    }

    public function getOpeningHours(): ?array
    {
        return $this->openingHours;
    }

    public function setOpeningHours(?array $openingHours): void
    {
        $this->openingHours = $openingHours;
    }

    public function getAccessType(): ?string
    {
        return $this->accessType;
    }

    public function setAccessType(?string $accessType): void
    {
        $this->accessType = $accessType;
    }

    public function getAcceptablePayments(): ?array
    {
        return $this->acceptablePayments;
    }

    public function setAcceptablePayments(?array $acceptablePayments): void
    {
        $this->acceptablePayments = $acceptablePayments;
    }

    public function getSpecialRestrictions(): ?array
    {
        return $this->specialRestrictions;
    }

    public function setSpecialRestrictions(?array $specialRestrictions): void
    {
        $this->specialRestrictions = $specialRestrictions;
    }

    public function getPartnerIDs(): ?array
    {
        return $this->partnerIDs;
    }

    public function setPartnerIDs(?array $partnerIDs): void
    {
        $this->partnerIDs = $partnerIDs;
    }
}