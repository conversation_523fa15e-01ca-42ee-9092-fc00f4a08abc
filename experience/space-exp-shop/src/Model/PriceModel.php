<?php

namespace App\Model;

/**
 * PriceModel.
 */
class PriceModel
{
    private string $currency;
    
    private int $price;
    
    private string $periodType;

    public function getCurrency(): string
    {
        return $this->currency;
    }

    public function setCurrency(string $currency)
    {
        $this->currency = $currency;

        return $this;
    }
    
    public function getPrice(): int
    {
        return $this->price;
    }

    public function setPrice(int $price)
    {
        $this->price = $price;

        return $this;
    }
    
    public function getPeriodType(): string
    {
        return $this->periodType;
    }

    public function setPeriodType(string $periodType)
    {
        $this->periodType = $periodType;

        return $this;
    }

}
