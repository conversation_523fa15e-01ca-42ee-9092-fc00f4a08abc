<?php

namespace App\Model;

class PhoneModel
{
	private string $phoneApv;

	private string $phoneNumber;

	private string $phonePr;

	private string $phoneVn;

	private string $phoneVo;

	public function getPhoneApv(): string
	{
		return $this->phoneApv;
	}

	public function getPhoneNumber(): string
	{
		return $this->phoneNumber;
	}

	public function getPhonePr(): string
	{
		return $this->phonePr;
	}

	public function getPhoneVn(): string
	{
		return $this->phoneVn;
	}

	public function getPhoneVo(): string
	{
		return $this->phoneVo;
	}

	public function setPhoneApv(string $phoneApv): self
	{
		$this->phoneApv = $phoneApv;
		return $this;
	}

	public function setPhoneNumber(string $phoneNumber): self
	{
		$this->phoneNumber = $phoneNumber;
		return $this;
	}

	public function setPhonePr(string $phonePr): self
	{
		$this->phonePr = $phonePr;
		return $this;
	}

	public function setPhoneVn(string $phoneVn): self
	{
		$this->phoneVn = $phoneVn;
		return $this;
	}

	public function setPhoneVo(string $phoneVo): self
	{
		$this->phoneVo = $phoneVo;
		return $this;
	}
}