<?php

namespace App\Model;

class WebSiteModel
{
	private string $private;

	private string $public;

	public function getPrivate(): string
	{
		return $this->private;
	}

	public function getPublic(): string
	{
		return $this->public;
	}

	public function setPrivate(string $private): self
	{
		$this->private = $private;
		return $this;
	}

	public function setPublic(string $public): self
	{
		$this->public = $public;
		return $this;
	}
}