<?php

namespace App\Model;

class DealerModel
{

	private AddressModel $address;

	private array $businessList;

	private CoordinateModel $coordinates;

	private string $countryId;

	private string $culture;

	private float $distanceFromPoint;

	private EmailModel $emails;

	private IndicatorModel $indicator;

	private string $name;

	private array $openingHoursList;

	private PhoneModel $phones;

	private string $rrdi;

	private string $siteGeo;

	private UrlPageModel $urlPages;

	private WebSiteModel $webSites;

	private string $brand;

	private string $caracRdvi;

	private array $ftcCodeList;

	public function getAddress(): AddressModel
	{
		return $this->address;
	}

	/**
	 * @return array<BusinessModel>
	 */
	public function getBusinessList(): array
	{
		return $this->businessList;
	}

	public function getCoordinates(): CoordinateModel
	{
		return $this->coordinates;
	}

	public function getCountryId(): string
	{
		return $this->countryId;
	}

	public function getCulture(): string
	{
		return $this->culture;
	}

	public function getDistanceFromPoint(): float
	{
		return $this->distanceFromPoint;
	}

	public function getEmails(): EmailModel
	{
		return $this->emails;
	}

	public function getIndicator(): IndicatorModel
	{
		return $this->indicator;
	}

	public function getName(): string
	{
		return $this->name;
	}

	/**
	 * @return array
	 */
	public function getOpeningHoursList(): array
	{
		return $this->openingHoursList;
	}

	public function getPhones(): PhoneModel
	{
		return $this->phones;
	}

	public function getRrdi(): string
	{
		return $this->rrdi;
	}

	public function getSiteGeo(): string
	{
		return $this->siteGeo;
	}

	public function getUrlPages(): UrlPageModel
	{
		return $this->urlPages;
	}

	public function getWebSites(): WebSiteModel
	{
		return $this->webSites;
	}

	public function getBrand(): string
	{
		return $this->brand;
	}

	public function getCaracRdvi(): string
	{
		return $this->caracRdvi;
	}

	/**
	 * @return array<FtcCodeModel>
	 */
	public function getFtcCodeList(): array
	{
		return $this->ftcCodeList;
	}

	public function setAddress(AddressModel $address): self
	{
		$this->address = $address;
		return $this;
	}

	public function setBusinessList(array $businessList): self
	{
		$this->businessList = $businessList;
		return $this;
	}

	public function setCoordinates(CoordinateModel $coordinates): self
	{
		$this->coordinates = $coordinates;
		return $this;
	}

	public function setCountryId(string $countryId): self
	{
		$this->countryId = $countryId;
		return $this;
	}

	public function setCulture(string $culture): self
	{
		$this->culture = $culture;
		return $this;
	}

	public function setDistanceFromPoint(float $distanceFromPoint): self
	{
		$this->distanceFromPoint = $distanceFromPoint;
		return $this;
	}

	public function setEmails(EmailModel $emails): self
	{
		$this->emails = $emails;
		return $this;
	}

	public function setIndicator(IndicatorModel $indicator): self
	{
		$this->indicator = $indicator;
		return $this;
	}

	public function setName(string $name): self
	{
		$this->name = $name;
		return $this;
	}

	public function setOpeningHoursList(array $openingHoursList): self
	{
		$this->openingHoursList = $openingHoursList;
		return $this;
	}

	public function setPhones(PhoneModel $phones): self
	{
		$this->phones = $phones;
		return $this;
	}

	public function setRrdi(string $rrdi): self
	{
		$this->rrdi = $rrdi;
		return $this;
	}

	public function setSiteGeo(string $siteGeo): self
	{
		$this->siteGeo = $siteGeo;
		return $this;
	}

	public function setUrlPages(UrlPageModel $urlPages): self
	{
		$this->urlPages = $urlPages;
		return $this;
	}

	public function setWebSites(WebSiteModel $webSites): self
	{
		$this->webSites = $webSites;
		return $this;
	}

	public function setBrand(string $brand): self
	{
		$this->brand = $brand;
		return $this;
	}

	public function setCaracRdvi(string $caracRdvi): self
	{
		$this->caracRdvi = $caracRdvi;
		return $this;
	}

	public function setFtcCodeList(array $ftcCodeList): self
	{
		$this->ftcCodeList = $ftcCodeList;
		return $this;
	}
}
