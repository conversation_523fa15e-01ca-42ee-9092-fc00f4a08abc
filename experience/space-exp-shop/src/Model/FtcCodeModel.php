<?php

namespace App\Model;

class FtcCodeModel
{
	private string $codeActeur;

	private string $codeActeurPrincipal;

	private string $codeNature;

	private string $dealerCode;

	private string $idSite;

	private string $mainDealerCode;

	private string $market;

	private string $oic;

	private string $outlet;

	public function getCodeActeur(): string
	{
		return $this->codeActeur;
	}

	public function getCodeActeurPrincipal(): string
	{
		return $this->codeActeurPrincipal;
	}

	public function getCodeNature(): string
	{
		return $this->codeNature;
	}

	public function getDealerCode(): string
	{
		return $this->dealerCode;
	}

	public function getIdSite(): string
	{
		return $this->idSite;
	}

	public function getMainDealerCode(): string
	{
		return $this->mainDealerCode;
	}

	public function getMarket(): string
	{
		return $this->market;
	}

	public function getOic(): string
	{
		return $this->oic;
	}

	public function getOutlet(): string
	{
		return $this->outlet;
	}

	public function setCodeActeur(string $codeActeur): self
	{
		$this->codeActeur = $codeActeur;
		return $this;
	}

	public function setCodeActeurPrincipal(string $codeActeurPrincipal): self
	{
		$this->codeActeurPrincipal = $codeActeurPrincipal;
		return $this;
	}

	public function setCodeNature(string $codeNature): self
	{
		$this->codeNature = $codeNature;
		return $this;
	}

	public function setDealerCode(string $dealerCode): self
	{
		$this->dealerCode = $dealerCode;
		return $this;
	}

	public function setIdSite(string $idSite): self
	{
		$this->idSite = $idSite;
		return $this;
	}

	public function setMainDealerCode(string $mainDealerCode): self
	{
		$this->mainDealerCode = $mainDealerCode;
		return $this;
	}

	public function setMarket(string $market): self
	{
		$this->market = $market;
		return $this;
	}

	public function setOic(string $oic): self
	{
		$this->oic = $oic;
		return $this;
	}

	public function setOutlet(string $outlet): self
	{
		$this->outlet = $outlet;
		return $this;
	}
}