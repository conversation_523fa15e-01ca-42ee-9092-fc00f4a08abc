<?php

namespace App\Model;

use App\Model\PriceModel;

/**
 * OfferModel.
 */
class OfferModel
{
    private string $name;
    
    private string $target;
    
    private string $pricingModel;
    
    private string $durationType;
    
    private string $duration;
    
    private string $description;
    
    private int $isFreeTrial;
    
    private string $freeTrialDuration;
    
    private string $freeTrialDurationType;
    
    private string $category;
    
    private int $isRenewable;
    
    private string $fromPrice;
    
    private array $prices;

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): void
    {
        $this->name = $name;
    }
    
    public function getTarget(): string
    {
        return $this->target;
    }

    public function setTarget(string $target): void
    {
        $this->target = $target;
    }
    
    public function getPricingModel(): string
    {
        return $this->pricingModel;
    }

    public function setPricingModel(string $pricingModel): void
    {
        $this->pricingModel = $pricingModel;
    }
    
    public function getDurationType(): string
    {
        return $this->durationType;
    }

    public function setDurationType(string $durationType): void
    {
        $this->durationType = $durationType;
    }
    
    public function getDuration(): string
    {
        return $this->duration;
    }

    public function setDuration(string $duration): void
    {
        $this->duration = $duration;
    }
    
    public function getDescription(): string
    {
        return $this->description;
    }

    public function setDescription(string $description): void
    {
        $this->description = $description;
    }
    
    public function getIsFreeTrial(): int
    {
        return $this->isFreeTrial;
    }

    public function setIsFreeTrial(int $isFreeTrial): void
    {
        $this->isFreeTrial = $isFreeTrial;
    }
    
    public function getFreeTrialDuration(): string
    {
        return $this->freeTrialDuration;
    }

    public function setFreeTrialDuration(string $freeTrialDuration): void
    {
        $this->freeTrialDuration = $freeTrialDuration;
    }
    
    public function getFreeTrialDurationType(): string
    {
        return $this->freeTrialDurationType;
    }

    public function setFreeTrialDurationType(string $freeTrialDurationType): void
    {
        $this->freeTrialDurationType = $freeTrialDurationType;
    }
    
    public function getCategory(): string
    {
        return $this->category;
    }

    public function setCategory(string $category): void
    {
        $this->category = $category;
    }
    
    public function getIsRenewable(): int
    {
        return $this->isRenewable;
    }

    public function setIsRenewable(int $isRenewable): void
    {
        $this->isRenewable = $isRenewable;
    }
    
    public function getFromPrice(): string
    {
        return $this->fromPrice;
    }

    public function setFromPrice(string $fromPrice): void
    {
        $this->fromPrice = $fromPrice;
    }
    
    /**
     * @return array<PriceModel>
     */
    public function getPrices(): array
    {
        return $this->prices;
    }

    public function setPrices(array $prices): void
    {
        $this->prices = $prices;
    }

}
