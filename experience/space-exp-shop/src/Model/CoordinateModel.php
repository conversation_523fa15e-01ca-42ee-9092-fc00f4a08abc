<?php

namespace App\Model;

class CoordinateModel
{
	private float $latitude;

	private float $longitude;

	public function getLatitude(): float
	{
		return $this->latitude;
	}

	public function getLongitude(): float
	{
		return $this->longitude;
	}

	public function setLatitude(float $latitude): self
	{
		$this->latitude = $latitude;
		return $this;
	}

	public function setLongitude(float $longitude): self
	{
		$this->longitude = $longitude;
		return $this;
	}
}