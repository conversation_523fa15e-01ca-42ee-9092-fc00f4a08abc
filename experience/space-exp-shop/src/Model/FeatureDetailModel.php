<?php

namespace App\Model;

/**
 * FeatureDetailModel.
 */
class FeatureDetailModel
{

    private string $desc;
    
    private string $icon;

    public function getDesc(): string
    {
        return $this->desc;
    }

    public function setDesc(string $desc): void
    {
        $this->desc = $desc;
    }

    public function getIcon(): string
    {
        return $this->icon;
    }

    public function setIcon(string $icon): void
    {
        $this->icon = $icon;
    }

}
