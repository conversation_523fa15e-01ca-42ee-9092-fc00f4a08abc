<?php

namespace App\Model;

use Symfony\Component\Serializer\Attribute\SerializedName;

class AddressModel
{
    private string $city;

    private string $country;

    #[SerializedName('street1')]
    private string $line1;

    #[SerializedName('street2')]
    private string $line2;

    #[SerializedName('street3')]
    private string $line3;

    private string $region;

    private string $zipCode;

    public function getCity(): string
    {
        return $this->city;
    }

    public function getCountry(): string
    {
        return $this->country;
    }

    public function getLine1(): string
    {
        return $this->line1;
    }

    public function getLine2(): string
    {
        return $this->line2;
    }

    public function getLine3(): string
    {
        return $this->line3;
    }

    public function getRegion(): string
    {
        return $this->region;
    }

    public function getZipCode(): string
    {
        return $this->zipCode;
    }

    public function setCity(string $city): self
    {
        $this->city = $city;
        return $this;
    }

    public function setCountry(string $country): self
    {
        $this->country = $country;
        return $this;
    }

    public function setLine1(string $line1): self
    {
        $this->line1 = $line1;
        return $this;
    }

    public function setLine2(string $line2): self
    {
        $this->line2 = $line2;
        return $this;
    }

    public function setLine3(string $line3): self
    {
        $this->line3 = $line3;
        return $this;
    }

    public function setRegion(string $region): self
    {
        $this->region = $region;
        return $this;
    }

    public function setZipCode(string $zipCode): self
    {
        $this->zipCode = $zipCode;
        return $this;
    }
}
