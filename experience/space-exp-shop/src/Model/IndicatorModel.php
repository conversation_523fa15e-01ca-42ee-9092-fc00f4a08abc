<?php

namespace App\Model;

class IndicatorModel
{
	private string $code;

	private string $label;

	public function getCode(): string
	{
		return $this->code;
	}

	public function getLabel(): string
	{
		return $this->label;
	}

	public function setCode(string $code): self
	{
		$this->code = $code;
		return $this;
	}

	public function setLabel(string $label): self
	{
		$this->label = $label;
		return $this;
	}
}