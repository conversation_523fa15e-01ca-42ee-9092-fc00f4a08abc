<?php

namespace App\DtoRequest;

use App\Validator\BrandValidator;
use Symfony\Component\Validator\Constraints as Assert;

class XfEmeaParameterRequest
{
    private ?int $market;

    #[Assert\NotBlank]
    #[Assert\Choice(BrandValidator::XF)]
    private ?string $brand;

    #[Assert\NotBlank]
    #[Assert\Type('string')]
    private ?string $criterias;

    #[Assert\NotBlank]
    #[Assert\Country()]
    private ?string $country;

    #[Assert\NotBlank]
    #[Assert\Regex(
        pattern: '/^\d+(\.\d+)?/'
    )]
    private string $latitude;

    #[Assert\NotBlank]
    #[Assert\Regex(
        pattern: '/^\d+(\.\d+)?/'
    )]
    private string $longitude;

    #[Assert\NotBlank]
    #[Assert\Regex(array(
        'pattern' => '/^[1-9]\d*$/'
        )
    )]
    private string $rmax;
    private string $language;

    public function __toArray(){
        return call_user_func('get_object_vars', $this);
    }

    public function getMarket(): int
    {
        return $this->market;
    }

    public function setMarket(?int $market): self
    {
        $this->market = $market;
        return $this;
    }

    public function getBrand(): string
    {
        return $this->brand;
    }

    public function setBrand(?string $brand): self
    {
        $this->brand = $brand;
        return $this;
    }

    public function getCriterias(): ?string
    {
        return $this->criterias;
    }

    public function setCriterias(?string $criterias): self
    {
        $this->criterias = $criterias;
        return $this;
    }

    public function getLatitude(): string
    {
        return $this->latitude;
    }

    public function setLatitude(string $latitude): self
    {
        $this->latitude = $latitude;
        return $this;
    }

    public function getLongitude(): string
    {
        return $this->longitude;
    }

    public function setLongitude(string $longitude): self
    {
        $this->longitude = $longitude;
        return $this;
    }

    public function getRmax(): string
    {
        return $this->rmax;
    }

    public function setRmax(string $rmax): self
    {
        $this->rmax = $rmax;
        return $this;
    }

    /**
     * Get the value of country
     */ 
    public function getCountry(): string
    {
        return $this->country;
    }

    /**
     * Set the value of country
     *
     * @return  self
     */ 
    public function setCountry(string $country): self
    {
        $this->country = $country;

        return $this;
    }

    public function getLanguage(): string
    {
        return $this->language;
    }

    public function setLanguage(string $language): self
    {
        $this->language = $language;

        return $this;
    }
}
