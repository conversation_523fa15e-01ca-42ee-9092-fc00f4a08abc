<?php

namespace App\Validator;

use Symfony\Component\Validator\Constraints as Assert;

class BrandValidator
{
    public const XF = [
        'SP',
        'FT',
        'FO',
        'AH',
        'AR',
        'CY',
        'DG',
        'JE',
        'LA',
        'RM',
        'MA'
    ];

    public static function isXf(?string $brand): ?bool
    {
        return in_array($brand, self::XF);
    }

    /**
     * Get all the brands.
     *
     * @return array
     */
    public static function getBrands(): array
    {
        return ['AP', 'AC', 'DS', 'OP', 'VX', 'FT', 'FO', 'AH', 'AR', 'CY', 'DG', 'JE', 'LA', 'RM', 'MA'];
    }

    /**
     * Get brand constraints.
     *
     * @return array
     */
    public static function getConstraints(): array
    {
        return [
            new Assert\NotBlank,
            new Assert\Choice(BrandValidator::getBrands()),
        ];
    }
}
