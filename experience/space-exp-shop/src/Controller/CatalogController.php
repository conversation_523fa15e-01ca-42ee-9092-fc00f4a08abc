<?php
namespace App\Controller;

use App\Manager\CatalogManager;
use App\Model\CatalogModel;
use App\Trait\ValidationResponseTrait;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;
use OpenApi\Attributes as OA;
use Nelmio\ApiDocBundle\Annotation\Model;
use OpenApi\Attributes\JsonContent;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Validator\Constraints as Assert;


#[Route('/v1/catalog', name: 'catalog_')]
class CatalogController extends AbstractController
{
    use ValidationResponseTrait;

    #[Route('', name: 'index', methods: ['GET'])]
    #[OA\Parameter(
        name: 'userId',
        in: 'header',
        required:true,
        description: 'User ID',
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'vin',
        in: 'header',
        required:true,
        description: 'VIN',
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'brand',
        in: 'query',
        required:true,
        description: 'Brand',
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'country',
        in: 'query',
        required:true,
        description: 'Country',
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'language',
        in: 'query',
        required:true,
        description: 'Language',
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'refresh-cache',
        in: 'header',
        description: 'Refresh-cache ? ( 0 OR 1)',
        schema: new OA\Schema(type: 'integer')
    )]
    #[OA\Response(
        response: 400,
        description: 'Bad Request',
    )]
    #[OA\Response(
        response: 200,
        description: 'Successful Response',
        content: new Model(type: CatalogModel::class)
    )]
    #[OA\Tag(name: 'API CATALOG')]
    public function index(ValidatorInterface $validator, CatalogManager $manager, Request $request): JsonResponse
    {
        $userId = $request->headers->get('userId');
        $vin = $request->headers->get('vin');
        $brand = $request->query->get('brand');
        $country = $request->query->get('country');
        $language = $request->query->get('language');
        $refreshCache = (int)$request->headers->get('refresh-cache') ?? 0;

        $errors = $validator->validate(
            compact('userId', 'vin', 'brand', 'country', 'language'),
            new Assert\Collection([
                    'userId' => new Assert\NotBlank(),
                    'vin' => new Assert\NotBlank(),
                    'brand' => new Assert\NotBlank(),
                    'country' => new Assert\NotBlank(),
                    'language' => new Assert\NotBlank(),
                ])
        );
        $messages = $this->getValidationMessages($errors);
        if (!empty($messages)) {
            $response = $this->getValidationErrorResponse($messages)->toArray();

            return $this->json($response['content'], $response['code']);
        }
        $params = [
            'userId' => $userId,
            'vin' => $vin,
            'brand' => $brand,
            'country' => $country,
            'language' => $language,
            'refresh-cache' => $refreshCache
        ];
        
        $response = $manager->getCatalog($params)->toArray();

        return $this->json($response['content'], $response['code']);
    }
}
