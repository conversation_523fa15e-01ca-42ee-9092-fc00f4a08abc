<?php

namespace App\Controller;

use App\Helper\BrandHelper;
use App\Helper\MarketHelper;
use App\Helper\RegionHelper;
use App\Manager\DealerManager;
use App\Model\DealerModel;
use App\Trait\ValidationResponseTrait;
use App\Transformer\xfEmeaParameterRequestTransformer;
use App\Validator\BrandValidator;
use App\Validator\CountryValidator;
use App\Validator\LanguageValidator;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;
use OpenApi\Attributes as OA;
use Nelmio\ApiDocBundle\Annotation\Model;
use OpenApi\Attributes\JsonContent;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Validator\Constraints as Assert;


#[Route('/v1', name: 'dealer_')]
class DealerController extends AbstractController
{
    use ValidationResponseTrait;

    #[Route('/dealers-list', name: 'list', methods: ['GET'])]
    #[OA\Parameter(
        name: 'brand',
        in: 'query',
        description: 'Brand',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'country',
        in: 'query',
        description: 'Country',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'language',
        in: 'query',
        description: 'Language',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'latitude',
        in: 'query',
        description: 'latitude',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'longitude',
        in: 'query',
        description: 'longitude',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'criterias',
        in: 'query',
        description: 'Criterias, example : VN, APV, VO',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'limit',
        in: 'query',
        description: 'limit',
        required: false,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Response(
        response: 400,
        description: 'Bad Request',
    )]
    #[OA\Response(
        response: 200,
        description: 'Successful Response',
        content: new Model(type: DealerModel::class)
    )]
    #[OA\Tag(name: 'Dealer API')]
    public function list(ValidatorInterface $validator, DealerManager $manager, Request $request): JsonResponse
    {
        $brand = $request->query->get('brand');
        $country = $request->query->get('country');
        $language = $request->query->get('language');
        $latitude = $request->query->get('latitude');
        $longitude = $request->query->get('longitude');
        $criterias = $request->query->get('criterias');

        $marketCode = MarketHelper::getMarket($country);
        $marketRegion = MarketHelper::getRegion($country);
        
        if (BrandHelper::isXf($brand) && $marketRegion == RegionHelper::EMEA) {
            $brandCode = BrandHelper::getCode($brand);
            $xfEmeaParameterRequest = xfEmeaParameterRequestTransformer::mapper(
                $request->query->all(),
                $marketCode,
                $brandCode
            );
            $errors = $validator->validate($xfEmeaParameterRequest);

            $messages = $this->getValidationMessages($errors);
            if (!empty($messages)) {
                $response = $this->getValidationErrorResponse($messages)->toArray();

                return $this->json($response['content'], $response['code']);
            }

            $response = $manager->getDealerXfEmeaList($xfEmeaParameterRequest)->toArray(false);
        } else {


            $errors = $validator->validate(
                compact('brand', 'country', 'language', 'latitude', 'longitude', 'criterias'),
                new Assert\Collection([
                    'brand' => BrandValidator::getConstraints(),
                    'country' => CountryValidator::getConstraints(),
                    'language' => LanguageValidator::getConstraints(),
                    'latitude' => new Assert\NotBlank(),
                    'longitude' => new Assert\NotBlank(),
                    'criterias' => new Assert\NotBlank(),
                ])
            );
            $messages = $this->getValidationMessages($errors);
            if (!empty($messages)) {
                $response = $this->getValidationErrorResponse($messages)->toArray();

                return $this->json($response['content'], $response['code']);
            }
            $params = [
                'brand' => $brand,
                'country' => $country,
                'language' => $language,
                'latitude' => $latitude,
                'longitude' => $longitude,
                'criterias' => $criterias,
            ];

            $response = $manager->getDealerList($params)->toArray();
        }

        return $this->json($response['content'], $response['code']);
    }
}
