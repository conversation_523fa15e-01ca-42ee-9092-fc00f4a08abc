<?php

namespace App\Manager;

use App\DataMapper\DealerDataMapper;
use App\DtoRequest\XfEmeaParameterRequest;
use App\Helper\ErrorResponse;
use App\Helper\ResponseArrayFormat;
use App\Helper\SuccessResponse;
use App\Service\DealerService;
use App\Trait\LoggerTrait;
use App\Transformer\XfEmeaResponseTransformer;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\SerializerInterface;

/**
 * Dealer Manager.
 */
class DealerManager
{
    use LoggerTrait;

    public function __construct(
        private DealerService $service,
        private SerializerInterface $serializer,
        private DealerDataMapper $dealerDataMapper
    ) {}

    /**
     * get Dealer data
     */
    public function getDealerList(array $params): ResponseArrayFormat
    {
        $this->logger->info("=> Call Dealer API : " . __METHOD__ . " with parameters : ", $params);
        try {
            $response = $this->service->getDealerList($params);
            if ($response->getCode() == Response::HTTP_OK) {
                $dealers = $response->getData()['success'] ?? [];
                $dealerData = $this->dealerDataMapper->createList($dealers);

                $this->logger->info("=> " . __METHOD__ . " => Success data : ", $dealers);

                return new SuccessResponse($dealerData);
            }
            $responseData = $response->getData();
            $result = (isset($responseData['error']['message'])) ? $responseData['error']['message'] : $responseData;

            $this->logger->error("=> " . __METHOD__ . " => error : " . $result);

            return new ErrorResponse($result, $response->getCode());
        } catch (\Exception $e) {
            $this->logger->error("=> " . __METHOD__ . " Catched Exception DealerManager::getDealerList " . $e->getMessage());

            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }

    /**
     * get Xf Emea Dealer data.
     */
    public function getDealerXfEmeaList(XfEmeaParameterRequest $params): ResponseArrayFormat
    {
        $this->logger->info('=> Call Dealer for xf Emea API : ' . __METHOD__ . ' with parameters : ', (array)$params);
        try {
            $response = $this->service->getXfEmeaDealerList($params);
            if (isset($response->getData()['error'])) {
                return (new ErrorResponse(
                    $response->getData()['error']['message'] ?? '',
                    $response->getCode()
                ))->setErrors([]);
            }

            return new SuccessResponse($response->getData()['success'] ?? []);
        } catch (\Exception $e) {
            $this->logger->error('=> ' . __METHOD__ . ' Catched Exception for xf Emea DealerManager::getDealerList ' . $e->getMessage());
            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }
}
