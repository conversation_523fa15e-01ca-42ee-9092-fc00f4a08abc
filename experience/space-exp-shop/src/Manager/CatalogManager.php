<?php

namespace App\Manager;

use App\Helper\ErrorResponse;
use App\Helper\ResponseArrayFormat;
use App\Helper\SuccessResponse;
use App\Model\CatalogModel;
use App\Service\CatalogService;
use App\Trait\LoggerTrait;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\SerializerInterface;

/**
 * Catalog Manager.
 */
class CatalogManager
{
    use LoggerTrait;

    public function __construct(private CatalogService $service, private SerializerInterface $serializer) 
    {

    }

    /**
     * get Catalog data
     */
    public function getCatalog(array $params): ResponseArrayFormat
    {
        $this->logger->info("=> Call Catalog API : " . __METHOD__ . " with parameters : ", $params);

        try {
            $response = $this->service->getCatalog($params);
            if ($response->getCode() == Response::HTTP_OK) {
                $catalog = $response->getData()['success'];
                $catalogData = [];
                if(isset($catalog['id'])) {
                    $catalogData[] = $this->serializer->denormalize($catalog, CatalogModel::class);
                } else if(is_array($catalog)) {
                    foreach ($catalog as $item) {
                        $catalogData[] = $this->serializer->denormalize($item, CatalogModel::class);
                    }
                }

                $this->logger->info("=> " . __METHOD__ . " => Success data for VIN : [{$params['vin']}] and user Id :  [{$params['userId']}], is : ", $catalog);

                return new SuccessResponse($catalogData);
            }
            $responseData = $response->getData();
            $result = (isset($responseData['error']['message'])) ? $responseData['error']['message'] : $responseData;

            $this->logger->error("=> " . __METHOD__ . " => error : " . $result);

            return new ErrorResponse($result, $response->getCode());
        } catch (\Exception $e) {
            $this->logger->error("=> " . __METHOD__ . 'Catched Exception CatalogManager::getCatalog '.$e->getMessage());

            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }
}
