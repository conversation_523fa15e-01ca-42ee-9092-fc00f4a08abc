<?php

namespace App\Manager;

use App\Helper\ErrorResponse;
use App\Helper\ResponseArrayFormat;
use App\Helper\SuccessResponse;
use App\Service\F2mService;
use App\Trait\LoggerTrait;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\SerializerInterface;

/**
 * F2m Manager.
 */
class F2mManager
{
    use LoggerTrait;

    public function __construct(private F2mService $service, private SerializerInterface $serializer) 
    {

    }

    /**
     * get Catalog data
     */
    public function search(array $params): ResponseArrayFormat
    {
        $this->logger->info("=> Call F2M API : " . __METHOD__ . " with parameters : ", $params);

        try {
            $response = $this->service->search($params);
            if ($response->getCode() == Response::HTTP_OK) {
                $responseData = $response->getData()['success'];

                $this->logger->info("=> " . __METHOD__ . " => Success data for brand : " . $params['brand']);

                return new SuccessResponse($responseData);
            }
            $responseData = $response->getData();
            $result = (isset($responseData['error']['message'])) ? $responseData['error']['message'] : $responseData;

            $this->logger->error("=> " . __METHOD__ . " => error : " . $result);

            return (new ErrorResponse($result, $response->getCode()))->setErrors("");
        } catch (\Exception $e) {
            $this->logger->error("=> " . __METHOD__ . 'Catched Exception F2mManager::search '.$e->getMessage());

            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }
}
