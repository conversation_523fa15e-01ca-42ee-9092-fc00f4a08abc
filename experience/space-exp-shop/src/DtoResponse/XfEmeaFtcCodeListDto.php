<?php

namespace App\DtoResponse;

use Symfony\Component\Serializer\Annotation\SerializedName;

class XfEmeaFtcCodeListDto
{
    #[SerializedName('dealer_code')]
    private string $dealerCode;
    #[SerializedName('main_dealer_code')]
    private string $mainDealerCode;
    private string $market;
    private string $oic;
    private string $outlet;


    /**
     * Get the value of dealerCode
     */ 
    public function getDealerCode(): string
    {
        return $this->dealerCode;
    }

    /**
     * Set the value of dealerCode
     *
     * @return  self
     */ 
    public function setDealerCode($dealerCode): self
    {
        $this->dealerCode = $dealerCode;

        return $this;
    }

    /**
     * Get the value of mainDealerCode
     */ 
    public function getMainDealerCode(): string
    {
        return $this->mainDealerCode;
    }

    /**
     * Set the value of mainDealerCode
     *
     * @return  self
     */ 
    public function setMainDealerCode($mainDealerCode): self
    {
        $this->mainDealerCode = $mainDealerCode;

        return $this;
    }

    /**
     * Get the value of market
     */ 
    public function getMarket(): string
    {
        return $this->market;
    }

    /**
     * Set the value of market
     *
     * @return  self
     */ 
    public function setMarket($market): self
    {
        $this->market = $market;

        return $this;
    }

    /**
     * Get the value of oic
     */ 
    public function getOic(): string
    {
        return $this->oic;
    }

    /**
     * Set the value of oic
     *
     * @return  self
     */ 
    public function setOic($oic): self
    {
        $this->oic = $oic;

        return $this;
    }

    /**
     * Get the value of outlet
     */ 
    public function getOutlet(): string
    {
        return $this->outlet;
    }

    /**
     * Set the value of outlet
     *
     * @return  self
     */ 
    public function setOutlet($outlet): self
    {
        $this->outlet = $outlet;

        return $this;
    }
}
