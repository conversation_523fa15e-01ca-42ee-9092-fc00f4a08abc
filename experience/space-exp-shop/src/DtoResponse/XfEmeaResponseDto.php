<?php

namespace App\DtoResponse;

use Symfony\Component\Serializer\Annotation\SerializedName;

class XfEmeaResponseDto
{
    #[SerializedName('Dealers')]
    private array $dealers = [];
    #[SerializedName('FtcCodeList')]
    private array $ftcCodeList = [];

    /**
     * Get the value of dealers
     */
    public function getDealers(): array
    {
        return $this->dealers;
    }

    /**
     * Set the value of dealers
     *
     * @return  self
     */
    public function setDealers(array $dealers): self
    {
        $this->dealers = $dealers;

        return $this;
    }

    /**
     * Set the value of dealers
     *
     * @return  self
     */
    public function addDealer(XfEmeaDealerDto $dealer): self
    {
        $this->dealers[] = $dealer;

        return $this;
    }

    /**
     * Get the value of ftcCodeList
     */ 
    public function getFtcCodeList(): array
    {
        return $this->ftcCodeList;
    }

    /**
     * Set the value of ftcCodeList
     *
     * @return  self
     */ 
    public function setFtcCodeList($ftcCodeList): self
    {
        $this->ftcCodeList = $ftcCodeList;

        return $this;
    }

    
    /**
     * Set the value of dealers
     *
     * @return  self
     */
    public function addFtcCodeList(XfEmeaFtcCodeListDto $ftcCode): self
    {
        $this->ftcCodeList[] = $ftcCode;

        return $this;
    }
}
