<?php

namespace App\Helper;

use Symfony\Component\Yaml\Yaml;

/**
 * BrandHelper class.
 */
class BrandHelper
{
    private const FILE_PATH =  __DIR__ . '/../../config/parametrage/brandCode.yml';
    private const XF = [
        'SP',
        'FT',
        'FO',
        'AH',
        'AR',
        'CY',
        'DG',
        'JE',
        'LA',
        'RM',
        'MA'
    ];

    public static function isXf(?string $brand): ?bool
    {
        return in_array($brand, self::XF);
    }

    public static function getCode(?string $brand): ?int
    {
        try {
            $brandCodes = Yaml::parseFile(self::FILE_PATH);
            $code = null;
            foreach ($brandCodes as $key => $value) {
                if($value == $brand) {
                    $code = $key;
                    break;
                }
            }
            return $code;
        } catch (\Exception $e) {
            return null;
        }
    }
}
