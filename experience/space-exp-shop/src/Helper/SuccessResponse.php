<?php

namespace App\Helper;

use Symfony\Component\HttpFoundation\Response;

/**
 * Success response class.
 */
class SuccessResponse implements ResponseArrayFormat
{
    public function __construct(private mixed $data, private ?int $code = Response::HTTP_OK)
    {

    }

    public function setCode($code)
    {
        $this->code = $code;

        return $this;
    }

    public function setData($data)
    {
        $this->data = $data;

        return $this;
    }

    public function getData()
    {
        return $this->data;
    }

    public function getCode()
    {
        return $this->code;
    }

    public function toArray(bool $isJosn = true): array
    {
        if($isJosn) {
            $this->data = !is_array($this->data) ? json_decode($this->data, true) : $this->data;
        }

        return [
            'content' => ['success' => $this->data],
            'code' => $this->code,
        ];
    }
}
