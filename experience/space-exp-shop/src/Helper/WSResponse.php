<?php

namespace App\Helper;

use Symfony\Component\HttpFoundation\Response;

/**
 * Manage rest responses.
 */
class WSResponse
{

    public function __construct(private ?int $code = Response::HTTP_OK, private mixed $data = "")
    {
    }

    public function setCode($code)
    {
        $this->code = $code;

        return $this;
    }

    public function getCode()
    {
        return $this->code;
    }

    public function setData($data)
    {
        $this->data = $data;

        return $this;
    }

    public function getData()
    {
        return $this->data;
    }
}
