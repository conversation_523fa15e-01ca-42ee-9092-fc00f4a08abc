<?php

namespace App\Helper;

use Symfony\Component\Yaml\Yaml;

/**
 * MarketHelper class.
 */
class MarketHelper
{
    private const FILE_PATH =  __DIR__ . '/../../config/parametrage/market.yml';

    public static function getMarket(?string $country): ?int
    {
        try {
            $markets = self::getMarketFile();
            $marketCode = array_values(array_filter($markets, function ($m) use ($country) {
                return $m['I_COUNTRY'] == $country;
            }));
            return reset($marketCode)['I_MARKET_CODE'] ?? null;
        } catch (\Exception $e) {
            return null;
        }
    }

    public static function getRegion(?string $country): ?string
    {
        try {
            $markets = self::getMarketFile();
            $marketRegion = array_values(array_filter($markets, function ($r) use ($country) {
                return $r['I_COUNTRY'] == $country;
            }));
            return reset($marketRegion)['C_REGION'] ?? null;
        } catch (\Exception $e) {
            return null;
        }
    }

    public static function getMarketFile() {
        return Yaml::parseFile(self::FILE_PATH);
    }
}
