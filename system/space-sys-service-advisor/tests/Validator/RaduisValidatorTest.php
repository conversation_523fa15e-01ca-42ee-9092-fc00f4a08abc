<?php

namespace App\Tests\Validator;

use App\Validator\RaduisValidator;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Regex;
use Symfony\Component\Validator\Validation;

class RaduisValidatorTest extends TestCase
{
    private $validator;

    protected function setUp(): void
    {
        $this->validator = Validation::createValidator();
    }

    public function testGetConstraints(): void
    {
        $constraints = RaduisValidator::getConstraints();

        $this->assertIsArray($constraints);
        $this->assertCount(2, $constraints);
        $this->assertInstanceOf(NotBlank::class, $constraints[0]);
        $this->assertInstanceOf(Regex::class, $constraints[1]);
        $this->assertEquals('/^[1-9]\d*$/', $constraints[1]->pattern);
    }

    public function testValidRadius(): void
    {
        $constraints = RaduisValidator::getConstraints();
        
        $validRadii = [
            '1',
            '10',
            '100',
            '9999'
        ];

        foreach ($validRadii as $radius) {
            $violations = $this->validator->validate($radius, $constraints);
            $this->assertCount(0, $violations, "Radius '$radius' should be valid");
        }
    }

    public function testInvalidRadius(): void
    {
        $constraints = RaduisValidator::getConstraints();
        
        $invalidRadii = [
            '',
            ' ',
            '0',
            '-1',
            'abc',
            '1.5',
            '01'
        ];

        foreach ($invalidRadii as $radius) {
            $violations = $this->validator->validate($radius, $constraints);
            $this->assertGreaterThan(0, $violations->count(), "Radius '$radius' should be invalid");
        }
    }
}
