<?php

namespace App\Tests\Validator;

use App\Validator\MarketValidator;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Regex;
use Symfony\Component\Validator\Validation;

class MarketValidatorTest extends TestCase
{
    private $validator;

    protected function setUp(): void
    {
        $this->validator = Validation::createValidator();
    }

    public function testGetConstraints(): void
    {
        $constraints = MarketValidator::getConstraints();

        $this->assertIsArray($constraints);
        $this->assertCount(2, $constraints);
        $this->assertInstanceOf(NotBlank::class, $constraints[0]);
        $this->assertInstanceOf(Regex::class, $constraints[1]);
        $this->assertEquals('/^[0-9]{4}$/', $constraints[1]->pattern);
    }

    public function testValidMarket(): void
    {
        $constraints = MarketValidator::getConstraints();

        $validMarkets = [
            '1000',
            '2000',
            '9999'
        ];

        foreach ($validMarkets as $market) {
            $violations = $this->validator->validate($market, $constraints);
            $this->assertCount(0, $violations, "Market '$market' should be valid");
        }
    }

    public function testInvalidMarket(): void
    {
        $constraints = MarketValidator::getConstraints();

        $invalidMarkets = [
            '',
            ' ',
            'abc',
            'A123',
            '12.34',
            '-123',
            'a1000'
        ];

        foreach ($invalidMarkets as $market) {
            $violations = $this->validator->validate($market, $constraints);
            $this->assertGreaterThan(0, $violations->count(), "Market '$market' should be invalid");
        }
    }
}
