<?php

namespace App\Tests\Validator;

use App\Validator\BrandValidator;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Regex;
use Symfony\Component\Validator\Validation;

class BrandValidatorTest extends TestCase
{
    private $validator;

    protected function setUp(): void
    {
        $this->validator = Validation::createValidator();
    }

    public function testGetConstraints(): void
    {
        $constraints = BrandValidator::getConstraints();

        $this->assertIsArray($constraints);
        $this->assertCount(2, $constraints);
        $this->assertInstanceOf(NotBlank::class, $constraints[0]);
        $this->assertInstanceOf(Regex::class, $constraints[1]);
        $this->assertEquals('/^[0-9]\d*$/', $constraints[1]->pattern);
    }

    public function testValidBrand(): void
    {
        $constraints = BrandValidator::getConstraints();
        
        $validBrands = [
            '00',
            '01',
            '123',
            '9999'
        ];

        foreach ($validBrands as $brand) {
            $violations = $this->validator->validate($brand, $constraints);
            $this->assertCount(0, $violations, "Brand '$brand' should be valid");
        }
    }

    public function testInvalidBrand(): void
    {
        $constraints = BrandValidator::getConstraints();
        
        $invalidBrands = [
            '',
            ' ',
            'abc',
            'A123',
            '12.34',
            '-123',
            'a00'
        ];

        foreach ($invalidBrands as $brand) {
            $violations = $this->validator->validate($brand, $constraints);
            $this->assertGreaterThan(0, $violations->count(), "Brand '$brand' should be invalid");
        }
    }
}
