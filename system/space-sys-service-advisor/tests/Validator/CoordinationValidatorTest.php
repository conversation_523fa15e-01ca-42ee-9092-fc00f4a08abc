<?php

namespace App\Tests\Validator;

use App\Validator\CoordinationValidator;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Validator\Constraints\Regex;
use Symfony\Component\Validator\Validation;

class CoordinationValidatorTest extends TestCase
{
    private $validator;

    protected function setUp(): void
    {
        $this->validator = Validation::createValidator();
    }

    public function testGetConstraints(): void
    {
        $constraints = CoordinationValidator::getConstraints();

        $this->assertIsArray($constraints);
        $this->assertCount(1, $constraints);
        $this->assertInstanceOf(Regex::class, $constraints[0]);
        $this->assertEquals('/^\d+(\.\d+)?/', $constraints[0]->pattern);
    }

    public function testValidCoordinates(): void
    {
        $constraints = CoordinationValidator::getConstraints();

        $validCoordinates = [
            '40.7128',
            '74.0060',
            '0.0',
            '90',
            '180',
            '123.456789'
        ];

        foreach ($validCoordinates as $coordinate) {
            $violations = $this->validator->validate($coordinate, $constraints);
            $this->assertCount(0, $violations, "Coordinate '$coordinate' should be valid");
        }
    }

    public function testInvalidCoordinates(): void
    {
        $constraints = CoordinationValidator::getConstraints();

        $invalidCoordinates = [
            // Skip empty string test as the current implementation doesn't validate it
            // '',
            // ' ',
            'abc',
            'N40.7128',
            'W74.0060',
            '-40.7128', // Note: The current regex doesn't validate negative numbers, which might be a bug
            'a123.456'
        ];

        foreach ($invalidCoordinates as $coordinate) {
            $violations = $this->validator->validate($coordinate, $constraints);
            $this->assertGreaterThan(0, $violations->count(), "Coordinate '$coordinate' should be invalid");
        }
    }
}
