<?php

namespace App\Tests\Controller;

use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\HttpFoundation\Response;
use App\Controller\DealerController;
use App\Service\DealerService;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use App\Helper\SuccessResponse;
use App\Helper\ErrorResponse;


class DealerControllerTest extends KernelTestCase
{
    private ValidatorInterface $validator;
    private DealerController $controller;

    protected function setUp(): void
    {
        self::bootKernel();
        $container = static::getContainer();
        $this->validator = $container->get(ValidatorInterface::class);
        $this->controller = $container->get(DealerController::class);
    }


    public function testSearchDealersWithSuccessResponse(): void
    {
       
        $dealerServiceMock = $this->createMock(DealerService::class);
        $success = new SuccessResponse(['dealers' => [
            ['name' => 'Dealer 1', 'address' => '123 Main St'],
            ['name' => 'Dealer 2', 'address' => '456 Oak Ave']
        ]], Response::HTTP_OK);
        $dealerServiceMock->method('getDealer')
            ->willReturn($success);

        $market = '1000';
        $brand = '00';
        $service = 'aftersales';
        $latitude = '40.7128';
        $longitude = '74.0060';
        $radius = 10;

        $url = '/v1/dealer/search';
        $request = Request::create($url, Request::METHOD_GET, [
            'market' => $market,
            'brand' => $brand,
            'service' => $service,
            'latitude' => $latitude,
            'longitude' => $longitude,
            'raduis' => $radius,
        ]);

        $response = $this->controller->searchDealers($request, $this->validator, $dealerServiceMock);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);
        $this->assertIsArray($responseData);
        $this->assertArrayHasKey('success', $responseData);
    }

    public function testSearchDealersWithMissingParameters(): void
    {
       
        $dealerServiceMock = $this->createMock(DealerService::class);

        $url = '/v1/dealer/search';
        $request = Request::create($url, Request::METHOD_GET, []);

        $response = $this->controller->searchDealers($request, $this->validator, $dealerServiceMock);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);
        $this->assertIsArray($responseData);
        $this->assertArrayHasKey('error', $responseData);
    }

    public function testSearchDealersWithInvalidMarket(): void
    {
       
        $dealerServiceMock = $this->createMock(DealerService::class);

        $market = 'invalid'; // Invalid market
        $brand = '00';
        $service = 'aftersales';
        $latitude = '40.7128';
        $longitude = '74.0060';
        $radius = 10;

        $url = '/v1/dealer/search';
        $request = Request::create($url, Request::METHOD_GET, [
            'market' => $market,
            'brand' => $brand,
            'service' => $service,
            'latitude' => $latitude,
            'longitude' => $longitude,
            'raduis' => $radius,
        ]);

        $response = $this->controller->searchDealers($request, $this->validator, $dealerServiceMock);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
    }

    public function testSearchDealersWithInvalidBrand(): void
    {
      
        $dealerServiceMock = $this->createMock(DealerService::class);

        $market = '1000';
        $brand = 'invalid'; // Invalid brand
        $service = 'aftersales';
        $latitude = '40.7128';
        $longitude = '74.0060';
        $radius = 10;

        $url = '/v1/dealer/search';
        $request = Request::create($url, Request::METHOD_GET, [
            'market' => $market,
            'brand' => $brand,
            'service' => $service,
            'latitude' => $latitude,
            'longitude' => $longitude,
            'raduis' => $radius,
        ]);

        $response = $this->controller->searchDealers($request, $this->validator, $dealerServiceMock);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
    }

    public function testSearchDealersWithInvalidCoordinates(): void
    {
      
        $dealerServiceMock = $this->createMock(DealerService::class);

        $market = '1000';
        $brand = '00';
        $service = 'aftersales';
        $latitude = 'invalid'; // Invalid latitude
        $longitude = 'invalid'; // Invalid longitude
        $radius = 10;

        $url = '/v1/dealer/search';
        $request = Request::create($url, Request::METHOD_GET, [
            'market' => $market,
            'brand' => $brand,
            'service' => $service,
            'latitude' => $latitude,
            'longitude' => $longitude,
            'raduis' => $radius,
        ]);

        $response = $this->controller->searchDealers($request, $this->validator, $dealerServiceMock);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
    }

    public function testSearchDealersWithDealerNotFoundException(): void
    {
 
        $dealerServiceMock = $this->createMock(DealerService::class);
        $error = new ErrorResponse(['message' => 'Dealer Not Found'], Response::HTTP_NOT_FOUND);
        $dealerServiceMock->method('getDealer')
            ->willReturn($error);

        $market = '1000';
        $brand = '00';
        $service = 'aftersales';
        $latitude = '40.7128';
        $longitude = '74.0060';
        $radius = 10;

        $url = '/v1/dealer/search';
        $request = Request::create($url, Request::METHOD_GET, [
            'market' => $market,
            'brand' => $brand,
            'service' => $service,
            'latitude' => $latitude,
            'longitude' => $longitude,
            'raduis' => $radius,
        ]);

        $response = $this->controller->searchDealers($request, $this->validator, $dealerServiceMock);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_NOT_FOUND, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
    }

    public function testSearchDealersWithDealerException(): void
    {
       
        $dealerServiceMock = $this->createMock(DealerService::class);
        $error = new ErrorResponse(['message' => 'Ko Response from SI Service Advisor'], Response::HTTP_BAD_REQUEST);
        $dealerServiceMock->method('getDealer')
            ->willReturn($error);

        $market = '1000';
        $brand = '00';
        $service = 'aftersales';
        $latitude = '40.7128';
        $longitude = '74.0060';
        $radius = 10;

        $url = '/v1/dealer/search';
        $request = Request::create($url, Request::METHOD_GET, [
            'market' => $market,
            'brand' => $brand,
            'service' => $service,
            'latitude' => $latitude,
            'longitude' => $longitude,
            'raduis' => $radius,
        ]);

        $response = $this->controller->searchDealers($request, $this->validator, $dealerServiceMock);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
    }

    // DEALER DETAILS TESTS

    public function testGetDealerDetailsWithSuccessResponse(): void
    {
        
        $dealerServiceMock = $this->createMock(DealerService::class);
        $success = new SuccessResponse(['dealer' => [
            'name' => 'Dealer 1',
            'address' => '123 Main St',
            'phone' => '************',
            'email' => '<EMAIL>'
        ]], Response::HTTP_OK);
        $dealerServiceMock->method('getDealerDetails')
            ->willReturn($success);

        $brand = '00';
        $market = '1000';
        $siteCode = 'SITE123';
        $sincom = 'SINCOM123';

        $url = '/v1/dealer/detail';
        $request = Request::create($url, Request::METHOD_GET, [
            'brand' => $brand,
            'market' => $market,
            'siteCode' => $siteCode,
            'sincom' => $sincom,
        ]);

        $response = $this->controller->getDealerDetails($request, $this->validator, $dealerServiceMock);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);
        $this->assertIsArray($responseData);
        $this->assertArrayHasKey('success', $responseData);
    }

    public function testGetDealerDetailsWithMissingParameters(): void
    {
       
        $dealerServiceMock = $this->createMock(DealerService::class);

        $url = '/v1/dealer/detail';
        $request = Request::create($url, Request::METHOD_GET, []);

        $response = $this->controller->getDealerDetails($request, $this->validator, $dealerServiceMock);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);
        $this->assertIsArray($responseData);
        $this->assertArrayHasKey('error', $responseData);
    }

    public function testGetDealerDetailsWithInvalidBrand(): void
    {
     
        $dealerServiceMock = $this->createMock(DealerService::class);

        $brand = 'invalid'; // Invalid brand
        $market = '1000';
        $siteCode = 'SITE123';
        $sincom = 'SINCOM123';

        $url = '/v1/dealer/detail';
        $request = Request::create($url, Request::METHOD_GET, [
            'brand' => $brand,
            'market' => $market,
            'siteCode' => $siteCode,
            'sincom' => $sincom,
        ]);

        $response = $this->controller->getDealerDetails($request, $this->validator, $dealerServiceMock);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
    }

    public function testGetDealerDetailsWithInvalidMarket(): void
    {
 
        $dealerServiceMock = $this->createMock(DealerService::class);

        $brand = '00';
        $market = 'invalid'; // Invalid market
        $siteCode = 'SITE123';
        $sincom = 'SINCOM123';

        $url = '/v1/dealer/detail';
        $request = Request::create($url, Request::METHOD_GET, [
            'brand' => $brand,
            'market' => $market,
            'siteCode' => $siteCode,
            'sincom' => $sincom,
        ]);

        $response = $this->controller->getDealerDetails($request, $this->validator, $dealerServiceMock);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
    }

    public function testGetDealerDetailsWithDealerNotFoundException(): void
    {
       
        $dealerServiceMock = $this->createMock(DealerService::class);
        $error = new ErrorResponse(['message' => 'Dealer Not Found'], Response::HTTP_NOT_FOUND);
        $dealerServiceMock->method('getDealerDetails')
            ->willReturn($error);

        $brand = '00';
        $market = '1000';
        $siteCode = 'NONEXISTENT';
        $sincom = 'NONEXISTENT';

        $url = '/v1/dealer/detail';
        $request = Request::create($url, Request::METHOD_GET, [
            'brand' => $brand,
            'market' => $market,
            'siteCode' => $siteCode,
            'sincom' => $sincom,
        ]);

        $response = $this->controller->getDealerDetails($request, $this->validator, $dealerServiceMock);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_NOT_FOUND, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
    }

    public function testGetDealerDetailsWithDealerException(): void
    {
     
        $dealerServiceMock = $this->createMock(DealerService::class);
        $error = new ErrorResponse(['message' => 'Ko Response from SI Service Advisor'], Response::HTTP_BAD_REQUEST);
        $dealerServiceMock->method('getDealerDetails')
            ->willReturn($error);

        $brand = '00';
        $market = '1000';
        $siteCode = 'SITE123';
        $sincom = 'SINCOM123';

        $url = '/v1/dealer/detail';
        $request = Request::create($url, Request::METHOD_GET, [
            'brand' => $brand,
            'market' => $market,
            'siteCode' => $siteCode,
            'sincom' => $sincom,
        ]);

        $response = $this->controller->getDealerDetails($request, $this->validator, $dealerServiceMock);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
    }
}