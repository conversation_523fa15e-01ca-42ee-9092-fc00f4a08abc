<?php

namespace App\Tests\Exception;

use App\Exception\DealerNotFoundException;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;

class DealerNotFoundExceptionTest extends TestCase
{
    public function testMake(): void
    {
        $exception = DealerNotFoundException::make();
        
        $this->assertInstanceOf(DealerNotFoundException::class, $exception);
        $this->assertEquals('Dealer Not Found', $exception->getMessage());
        $this->assertEquals(Response::HTTP_NOT_FOUND, $exception->getCode());
    }
}
