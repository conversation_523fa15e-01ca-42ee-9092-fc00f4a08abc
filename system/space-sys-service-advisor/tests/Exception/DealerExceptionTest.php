<?php

namespace App\Tests\Exception;

use App\Exception\DealerException;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;

class DealerExceptionTest extends TestCase
{
    public function testMake(): void
    {
        $exception = DealerException::make();
        
        $this->assertInstanceOf(DealerException::class, $exception);
        $this->assertEquals('Ko Response from SI Service Advisor', $exception->getMessage());
        $this->assertEquals(Response::HTTP_INTERNAL_SERVER_ERROR, $exception->getCode());
    }
}
