<?php

namespace App\Tests\Trait;

use App\Trait\LoggerTrait;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;

class LoggerTraitTest extends TestCase
{
    public function testSetLogger(): void
    {
        $mockLogger = $this->createMock(LoggerInterface::class);
        
        $object = new class {
            use LoggerTrait;
        };
        
        $object->setLogger($mockLogger);
        
        $reflection = new \ReflectionClass($object);
        $property = $reflection->getProperty('_logger');
        $property->setAccessible(true);
        
        $this->assertSame($mockLogger, $property->getValue($object));
    }
}
