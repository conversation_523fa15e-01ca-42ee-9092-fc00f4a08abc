<?php

namespace App\Tests\Trait;

use App\Trait\ValidationResponseTrait;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Component\Validator\ConstraintViolationList;

class ValidationResponseTraitTest extends TestCase
{
    private $object;

    protected function setUp(): void
    {
        $this->object = new class {
            use ValidationResponseTrait;
        };
    }

    public function testGetValidationMessagesWithNoViolations(): void
    {
        $errors = new ConstraintViolationList();
        
        $method = new \ReflectionMethod($this->object, 'getValidationMessages');
        $method->setAccessible(true);
        
        $result = $method->invoke($this->object, $errors);
        
        $this->assertIsArray($result);
        $this->assertEmpty($result);
    }

    public function testGetValidationMessagesWithViolations(): void
    {
        $errors = new ConstraintViolationList([
            new ConstraintViolation(
                'Error message 1',
                null,
                [],
                null,
                'field1',
                null
            ),
            new ConstraintViolation(
                'Error message 2',
                null,
                [],
                null,
                'field2',
                null
            )
        ]);
        
        $method = new \ReflectionMethod($this->object, 'getValidationMessages');
        $method->setAccessible(true);
        
        $result = $method->invoke($this->object, $errors);
        
        $this->assertIsArray($result);
        $this->assertCount(2, $result);
        $this->assertEquals('Error message 1', $result['field1']);
        $this->assertEquals('Error message 2', $result['field2']);
    }

    public function testGetValidationMessagesWithNestedPropertyPath(): void
    {
        $errors = new ConstraintViolationList([
            new ConstraintViolation(
                'Error message',
                null,
                [],
                null,
                'parent[child]',
                null
            )
        ]);
        
        $method = new \ReflectionMethod($this->object, 'getValidationMessages');
        $method->setAccessible(true);
        
        $result = $method->invoke($this->object, $errors);
        
        $this->assertIsArray($result);
        $this->assertCount(1, $result);
        $this->assertEquals('Error message', $result['parentchild']);
    }

    public function testGetValidationErrorResponse(): void
    {
        $messages = [
            'field1' => 'Error message 1',
            'field2' => 'Error message 2'
        ];
        
        $method = new \ReflectionMethod($this->object, 'getValidationErrorResponse');
        $method->setAccessible(true);
        
        $result = $method->invoke($this->object, $messages);
        
        $this->assertInstanceOf(JsonResponse::class, $result);
        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $result->getStatusCode());
        
        $content = json_decode($result->getContent(), true);
        $this->assertIsArray($content);
        $this->assertFalse($content['success']);
        $this->assertEquals('validation_failed', $content['error']);
        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $content['status']);
        $this->assertEquals($messages, $content['messages']);
    }

    public function testGetValidationErrorResponseWithCustomStatusCode(): void
    {
        $messages = [
            'field1' => 'Error message 1'
        ];
        $statusCode = Response::HTTP_BAD_REQUEST;
        
        $method = new \ReflectionMethod($this->object, 'getValidationErrorResponse');
        $method->setAccessible(true);
        
        $result = $method->invoke($this->object, $messages, $statusCode);
        
        $this->assertInstanceOf(JsonResponse::class, $result);
        $this->assertEquals($statusCode, $result->getStatusCode());
        
        $content = json_decode($result->getContent(), true);
        $this->assertEquals($statusCode, $content['status']);
    }
}
