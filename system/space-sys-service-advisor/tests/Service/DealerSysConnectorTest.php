<?php

namespace App\Tests\Service;

use App\Service\DealerSysConnector;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Contracts\HttpClient\ResponseInterface;

class DealerSysConnectorTest extends TestCase
{
    private HttpClientInterface $httpClient;
    private DealerSysConnector $dealerSysConnector;
    private string $url = 'https://api.example.com';

    protected function setUp(): void
    {
        $this->httpClient = $this->createMock(HttpClientInterface::class);
        $this->dealerSysConnector = new DealerSysConnector($this->url, $this->httpClient);
    }

    public function testCallWithGetMethod(): void
    {
        $method = Request::METHOD_GET;
        $uri = '/dealers';
        $parameters = ['brand' => '00', 'market' => '1000'];
        $headers = ['X-API-Key' => 'test-key'];
        $expectedOptions = [
            'query' => $parameters,
            'headers' => $headers,
            'timeout' => 20
        ];

        $response = $this->createMock(ResponseInterface::class);

        $this->httpClient->expects($this->once())->method('request')
            ->with($method, $this->url . $uri, $expectedOptions)
            ->willReturn($response);

        $result = $this->dealerSysConnector->call($method, $uri, $parameters, $headers);

        $this->assertSame($response, $result);
    }

    public function testCallWithPostMethod(): void
    {
        $method = Request::METHOD_POST;
        $uri = '/dealers';
        $parameters = ['brand' => '00', 'market' => '1000'];
        $headers = ['Content-Type' => 'application/json'];
        $expectedOptions = [
            'query' => $parameters,
            'headers' => $headers,
            'timeout' => 20
        ];

        $response = $this->createMock(ResponseInterface::class);

        $this->httpClient->expects($this->once())
            ->method('request')
            ->with($method, $this->url . $uri, $expectedOptions)
            ->willReturn($response);

        $result = $this->dealerSysConnector->call($method, $uri, $parameters, $headers);

        $this->assertSame($response, $result);
    }

    public function testCallWithEmptyParameters(): void
    {
        $method = Request::METHOD_GET;
        $uri = '/dealers';
        $parameters = [];
        $headers = [];
        $expectedOptions = [
            'query' => $parameters,
            'headers' => $headers,
            'timeout' => 20
        ];

        $response = $this->createMock(ResponseInterface::class);

        $this->httpClient->expects($this->once())
            ->method('request')
            ->with($method, $this->url . $uri, $expectedOptions)
            ->willReturn($response);

        $result = $this->dealerSysConnector->call($method, $uri, $parameters, $headers);

        $this->assertSame($response, $result);
    }

    public function testCallWithDefaultHeaders(): void
    {
        $method = Request::METHOD_GET;
        $uri = '/dealers';
        $parameters = ['brand' => '00', 'market' => '1000'];
        $expectedOptions = [
            'query' => $parameters,
            'headers' => [],
            'timeout' => 20
        ];

        $response = $this->createMock(ResponseInterface::class);

        $this->httpClient->expects($this->once())
            ->method('request')
            ->with($method, $this->url . $uri, $expectedOptions)
            ->willReturn($response);

        $result = $this->dealerSysConnector->call($method, $uri, $parameters);

        $this->assertSame($response, $result);
    }
}
