<?php

namespace App\Tests\Service;

use App\Service\CacheAdapter;
use PHPUnit\Framework\TestCase;
use Psr\Cache\CacheItemInterface;
use Psr\Cache\CacheItemPoolInterface;

class CacheAdapterTest extends TestCase
{
    private CacheItemPoolInterface $cacheItemPool;
    private CacheAdapter $cacheAdapter;
    private CacheItemInterface $cacheItem;

    protected function setUp(): void
    {
        $this->cacheItemPool = $this->createMock(CacheItemPoolInterface::class);
        $this->cacheItem = $this->createMock(CacheItemInterface::class);
        $this->cacheAdapter = new CacheAdapter($this->cacheItemPool);
    }

    public function testGetWithCacheHit(): void
    {
        $key = 'test_key';
        $expectedValue = ['data' => 'test_value'];
        $callback = function () {
            $this->fail('Callback should not be called on cache hit');
        };

        // Mock cache item behavior for a hit
        $this->cacheItem->expects($this->once())
            ->method('isHit')
            ->willReturn(true);
        $this->cacheItem->expects($this->once())
            ->method('get')
            ->willReturn($expectedValue);

        // Mock cache pool behavior
        $this->cacheItemPool->expects($this->once())
            ->method('getItem')
            ->with($key)
            ->willReturn($this->cacheItem);

        // Cache item should not be saved on a hit
        $this->cacheItemPool->expects($this->never())
            ->method('save');

        $result = $this->cacheAdapter->get($key, $callback);

        $this->assertEquals($expectedValue, $result);
    }

    public function testGetWithCacheMiss(): void
    {
        $key = 'test_key';
        $callbackValue = ['data' => 'generated_value'];
        $callback = function ($item) use ($callbackValue) {
            return $callbackValue;
        };

        // Mock cache item behavior for a miss
        $this->cacheItem->expects($this->once())
            ->method('isHit')
            ->willReturn(false);
        $this->cacheItem->expects($this->once())
            ->method('set')
            ->with($callbackValue)
            ->willReturnSelf();
        $this->cacheItem->expects($this->once())
            ->method('get')
            ->willReturn($callbackValue);

        // Mock cache pool behavior
        $this->cacheItemPool->expects($this->once())
            ->method('getItem')
            ->with($key)
            ->willReturn($this->cacheItem);

        // Cache item should be saved on a miss
        $this->cacheItemPool->expects($this->once())
            ->method('save')
            ->with($this->cacheItem);

        $result = $this->cacheAdapter->get($key, $callback);

        $this->assertEquals($callbackValue, $result);
    }

    public function testDeleteWithCacheHit(): void
    {
        $key = 'test_key';

        // Mock cache item behavior for a hit
        $this->cacheItem->expects($this->once())
            ->method('isHit')
            ->willReturn(true);

        // Mock cache pool behavior
        $this->cacheItemPool->expects($this->once())
            ->method('getItem')
            ->with($key)
            ->willReturn($this->cacheItem);

        // Cache item should be deleted
        $this->cacheItemPool->expects($this->once())
            ->method('deleteItem')
            ->with($key)
            ->willReturn(true);

        $result = $this->cacheAdapter->delete($key);

        $this->assertTrue($result);
    }

    public function testDeleteWithCacheMiss(): void
    {
        $key = 'test_key';

        // Mock cache item behavior for a miss
        $this->cacheItem->expects($this->once())
            ->method('isHit')
            ->willReturn(false);

        // Mock cache pool behavior
        $this->cacheItemPool->expects($this->once())
            ->method('getItem')
            ->with($key)
            ->willReturn($this->cacheItem);

        // Cache item should not be deleted
        $this->cacheItemPool->expects($this->never())
            ->method('deleteItem');

        $result = $this->cacheAdapter->delete($key);

        $this->assertTrue($result);
    }
}
