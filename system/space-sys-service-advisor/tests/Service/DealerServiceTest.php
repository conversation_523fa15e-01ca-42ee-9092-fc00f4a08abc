<?php
// phpcs:ignoreFile
// @phpstan-ignore-file
// @intelephense-ignore-file
// This file contains intentionally unused variables in callbacks

namespace App\Tests\Service;

use App\Exception\DealerException;
use App\Exception\DealerNotFoundException;
use App\Helper\SuccessResponse;
use App\Service\CacheAdapter;
use App\Service\CacheParametersService;
use App\Service\DealerService;
use App\Service\DealerSysConnector;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\Cache\ItemInterface;
use Symfony\Contracts\HttpClient\ResponseInterface;

/**
 * @phpstan-ignore-next-line
 */
class DealerServiceTest extends TestCase
{
    /**
     * @var DealerSysConnector|\PHPUnit\Framework\MockObject\MockObject
     */
    private $dealerSysConnector;

    /**
     * @var CacheParametersService|\PHPUnit\Framework\MockObject\MockObject
     */
    private $cacheParametersService;

    /**
     * @var CacheAdapter|\PHPUnit\Framework\MockObject\MockObject
     */
    private $cache;

    private DealerService $dealerService;

    /**
     * @var ResponseInterface|\PHPUnit\Framework\MockObject\MockObject
     */
    private $httpResponse;

    /**
     * @var ItemInterface|\PHPUnit\Framework\MockObject\MockObject
     */
    private $cacheItem;

    protected function setUp(): void
    {
        $this->dealerSysConnector = $this->createMock(DealerSysConnector::class);
        $this->cacheParametersService = $this->createMock(CacheParametersService::class);
        $this->cache = $this->createMock(CacheAdapter::class);
        $this->httpResponse = $this->createMock(ResponseInterface::class);
        $this->cacheItem = $this->createMock(ItemInterface::class);

        /** @phpstan-ignore-next-line */
        $this->dealerService = new DealerService(
            $this->dealerSysConnector,
            $this->cacheParametersService,
            $this->cache
        );

        // Set up logger
        /** @var \Psr\Log\LoggerInterface|\PHPUnit\Framework\MockObject\MockObject $logger */
        $logger = $this->getMockBuilder('Psr\Log\LoggerInterface')
            ->getMock();
        $this->dealerService->setLogger($logger);
    }

    public function testGetDealerSuccess(): void
    {
        $market = '1000';
        $brand = '00';
        $service = 'aftersales';
        $latitude = '40.7128';
        $longitude = '74.0060';
        $radius = 10;
        $cacheKey = "api_sys_service_advisor_{$market}_{$brand}_{$service}_{$latitude}_{$longitude}_{$radius}";
        $dealerData = [
            ['name' => 'Dealer 1', 'address' => '123 Main St'],
            ['name' => 'Dealer 2', 'address' => '456 Oak Ave']
        ];

        // Mock cache behavior
        $this->cache->expects($this->once())
            ->method('get')
            ->with($cacheKey, $this->isType('callable'))
            ->willReturn($dealerData);

        $result = $this->dealerService->getDealer($market, $brand, $service, $latitude, $longitude, $radius);

        $this->assertInstanceOf(SuccessResponse::class, $result);
        $this->assertEquals($dealerData, $result->getData());
    }

    public function testGetDealerDetailsSuccess(): void
    {
        $brand = '00';
        $siteCode = 'SITE123';
        $sincom = 'SINCOM123';
        $market = '1000';
        $cacheKey = "api_sys_service_advisor_{$brand}_{$siteCode}_{$sincom}_{$market}";
        $dealerData = [
            'name' => 'Dealer 1',
            'address' => '123 Main St',
            'phone' => '************',
            'email' => '<EMAIL>'
        ];

        // Mock cache behavior
        $this->cache->expects($this->once())
            ->method('get')
            ->with($cacheKey, $this->isType('callable'))
            ->willReturn($dealerData);

        $result = $this->dealerService->getDealerDetails($brand, $siteCode, $sincom, $market);

        $this->assertInstanceOf(SuccessResponse::class, $result);
        $this->assertEquals($dealerData, $result->getData());
    }

    public function testGetDealerWithCacheMiss(): void
    {
        $market = '1000';
        $brand = '00';
        $service = 'aftersales';
        $latitude = '40.7128';
        $longitude = '74.0060';
        $radius = 10;
        $cacheKey = "api_sys_service_advisor_{$market}_{$brand}_{$service}_{$latitude}_{$longitude}_{$radius}";
        $dealerData = [
            ['name' => 'Dealer 1', 'address' => '123 Main St'],
            ['name' => 'Dealer 2', 'address' => '456 Oak Ave']
        ];
        $jsonResponse = 'callback({"results":' . json_encode($dealerData) . '})';

        // Mock HTTP response
        $this->httpResponse->expects($this->once())
            ->method('getStatusCode')
            ->willReturn(Response::HTTP_OK);
        $this->httpResponse->expects($this->once())
            ->method('getContent')
            ->with(false)
            ->willReturn($jsonResponse);

        // Mock connector
        $this->dealerSysConnector->expects($this->once())
            ->method('call')
            ->with(
                Request::METHOD_GET,
                DealerService::BASE_URL,
                $this->isType('array')
            )
            ->willReturn($this->httpResponse);

        // Mock cache item
        $this->cacheItem->expects($this->exactly(2))
            ->method('expiresAfter')
            ->willReturnSelf();

        // Mock cache parameters
        $this->cacheParametersService->expects($this->once())
            ->method('getCacheExpireDuration')
            ->willReturn(3600);

        // Mock cache with callback execution
        $this->cache->expects($this->once())
            ->method('get')
            ->with($cacheKey, $this->isType('callable'))
            ->willReturnCallback(function ($_, callable $callback) {
                // Using $_ as a convention for unused parameters
                return $callback($this->cacheItem);
            });

        $result = $this->dealerService->getDealer($market, $brand, $service, $latitude, $longitude, $radius);

        $this->assertInstanceOf(SuccessResponse::class, $result);
    }

    public function testGetDealerWithHttpError(): void
    {
        $market = '1000';
        $brand = '00';
        $service = 'aftersales';
        $latitude = '40.7128';
        $longitude = '74.0060';
        $radius = 10;
        $cacheKey = "api_sys_service_advisor_{$market}_{$brand}_{$service}_{$latitude}_{$longitude}_{$radius}";

        // Mock HTTP response with error
        $this->httpResponse->expects($this->once())
            ->method('getStatusCode')
            ->willReturn(Response::HTTP_BAD_REQUEST);

        // Mock connector
        $this->dealerSysConnector->expects($this->once())
            ->method('call')
            ->willReturn($this->httpResponse);

        // Mock cache item
        $this->cacheItem->expects($this->once())
            ->method('expiresAfter')
            ->willReturnSelf();

        // Expect exception
        $this->expectException(DealerException::class);

        // Mock cache with callback execution that should throw exception
        $this->cache->expects($this->once())
            ->method('get')
            ->with($cacheKey, $this->isType('callable'))
            ->willReturnCallback(function ($_, $callback) {
                return $callback($this->cacheItem);
            });

        $this->dealerService->getDealer($market, $brand, $service, $latitude, $longitude, $radius);
    }

    public function testGetDealerWithEmptyResults(): void
    {
        $market = '1000';
        $brand = '00';
        $service = 'aftersales';
        $latitude = '40.7128';
        $longitude = '74.0060';
        $radius = 10;
        $cacheKey = "api_sys_service_advisor_{$market}_{$brand}_{$service}_{$latitude}_{$longitude}_{$radius}";
        $jsonResponse = 'callback({"results":[]})';

        // Mock HTTP response
        $this->httpResponse->expects($this->once())
            ->method('getStatusCode')
            ->willReturn(Response::HTTP_OK);
        $this->httpResponse->expects($this->once())
            ->method('getContent')
            ->with(false)
            ->willReturn($jsonResponse);

        // Mock connector
        $this->dealerSysConnector->expects($this->once())
            ->method('call')
            ->willReturn($this->httpResponse);

        // Mock cache item
        $this->cacheItem->expects($this->exactly(2))
            ->method('expiresAfter')
            ->willReturnSelf();

        // Expect exception
        $this->expectException(DealerNotFoundException::class);

        // Mock cache with callback execution that should throw exception
        $this->cache->expects($this->once())
            ->method('get')
            ->with($cacheKey, $this->isType('callable'))
            ->willReturnCallback(function ($key, $callback) {
                return $callback($this->cacheItem);
            });

        $this->dealerService->getDealer($market, $brand, $service, $latitude, $longitude, $radius);
    }
    public function testGetDealerDetailsWithCacheMiss(): void
    {
        $brand = '00';
        $siteCode = 'SITE123';
        $sincom = 'SINCOM123';
        $market = '1000';
        $cacheKey = "api_sys_service_advisor_{$brand}_{$siteCode}_{$sincom}_{$market}";
        $dealerData = [
            'name' => 'Dealer 1',
            'address' => '123 Main St',
            'phone' => '************',
            'email' => '<EMAIL>'
        ];
        $jsonResponse = 'callback({"results":' . json_encode($dealerData) . '})';

        // Mock HTTP response
        $this->httpResponse->expects($this->once())
            ->method('getStatusCode')
            ->willReturn(Response::HTTP_OK);
        $this->httpResponse->expects($this->once())
            ->method('getContent')
            ->with(false)
            ->willReturn($jsonResponse);

        // Mock connector
        $this->dealerSysConnector->expects($this->once())
            ->method('call')
            ->with(
                Request::METHOD_GET,
                DealerService::BASE_URL,
                $this->isType('array')
            )
            ->willReturn($this->httpResponse);

        // Mock cache item
        $this->cacheItem->expects($this->exactly(2))
            ->method('expiresAfter')
            ->willReturnSelf();

        // Mock cache parameters
        $this->cacheParametersService->expects($this->once())
            ->method('getCacheExpireDuration')
            ->willReturn(3600);

        // Mock cache with callback execution
        $this->cache->expects($this->once())
            ->method('get')
            ->with($cacheKey, $this->isType('callable'))
            ->willReturnCallback(function ($key, $callback) use ($dealerData) {
                return $callback($this->cacheItem);
            });

        $result = $this->dealerService->getDealerDetails($brand, $siteCode, $sincom, $market);

        $this->assertInstanceOf(SuccessResponse::class, $result);
    }

    public function testGetDealerDetailsWithHttpError(): void
    {
        $brand = '00';
        $siteCode = 'SITE123';
        $sincom = 'SINCOM123';
        $market = '1000';
        $cacheKey = "api_sys_service_advisor_{$brand}_{$siteCode}_{$sincom}_{$market}";

        // Mock HTTP response with error
        $this->httpResponse->expects($this->once())
            ->method('getStatusCode')
            ->willReturn(Response::HTTP_BAD_REQUEST);

        // Mock connector
        $this->dealerSysConnector->expects($this->once())
            ->method('call')
            ->willReturn($this->httpResponse);

        // Mock cache item
        $this->cacheItem->expects($this->once())
            ->method('expiresAfter')
            ->willReturnSelf();

        // Expect exception
        $this->expectException(DealerException::class);

        // Mock cache with callback execution that should throw exception
        $this->cache->expects($this->once())
            ->method('get')
            ->with($cacheKey, $this->isType('callable'))
            ->willReturnCallback(function ($key, $callback) {
                return $callback($this->cacheItem);
            });

        $this->dealerService->getDealerDetails($brand, $siteCode, $sincom, $market);
    }

    public function testGetDealerDetailsWithEmptyResults(): void
    {
        $brand = '00';
        $siteCode = 'SITE123';
        $sincom = 'SINCOM123';
        $market = '1000';
        $cacheKey = "api_sys_service_advisor_{$brand}_{$siteCode}_{$sincom}_{$market}";
        $jsonResponse = 'callback({"results":[]})';

        // Mock HTTP response
        $this->httpResponse->expects($this->once())
            ->method('getStatusCode')
            ->willReturn(Response::HTTP_OK);
        $this->httpResponse->expects($this->once())
            ->method('getContent')
            ->with(false)
            ->willReturn($jsonResponse);

        // Mock connector
        $this->dealerSysConnector->expects($this->once())
            ->method('call')
            ->willReturn($this->httpResponse);

        // Mock cache item
        $this->cacheItem->expects($this->exactly(2))
            ->method('expiresAfter')
            ->willReturnSelf();

        // Expect exception
        $this->expectException(DealerNotFoundException::class);

        // Mock cache with callback execution that should throw exception
        $this->cache->expects($this->once())
            ->method('get')
            ->with($cacheKey, $this->isType('callable'))
            ->willReturnCallback(function ($key, $callback) {
                return $callback($this->cacheItem);
            });

        $this->dealerService->getDealerDetails($brand, $siteCode, $sincom, $market);
    }
}