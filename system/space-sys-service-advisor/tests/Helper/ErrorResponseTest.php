<?php

namespace App\Tests\Helper;

use App\Helper\ErrorResponse;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class ErrorResponseTest extends TestCase
{
    public function testConstructorWithString(): void
    {
        $message = 'Error message';
        $code = Response::HTTP_BAD_REQUEST;

        $response = new ErrorResponse($message, $code);

        $this->assertEquals($message, $this->getPrivateProperty($response, 'message'));
        $this->assertEquals($code, $this->getPrivateProperty($response, 'code'));
    }

    public function testConstructorWithArray(): void
    {
        $data = ['field' => 'Error message'];
        $code = Response::HTTP_BAD_REQUEST;
        $expectedMessage = json_encode($data);

        $response = new ErrorResponse($data, $code);

        $this->assertEquals($expectedMessage, $this->getPrivateProperty($response, 'message'));
        $this->assertEquals($code, $this->getPrivateProperty($response, 'code'));
    }

    public function testSetCode(): void
    {
        $message = 'Error message';
        $initialCode = Response::HTTP_BAD_REQUEST;
        $newCode = Response::HTTP_NOT_FOUND;

        $response = new ErrorResponse($message, $initialCode);
        $result = $response->setCode($newCode);

        $this->assertSame($response, $result);
        $this->assertEquals($newCode, $this->getPrivateProperty($response, 'code'));
    }

    public function testSetMessage(): void
    {
        $initialMessage = 'Initial error';
        $newMessage = 'New error message';

        $response = new ErrorResponse($initialMessage);
        $result = $response->setMessage($newMessage);

        $this->assertSame($response, $result);
        $this->assertEquals($newMessage, $this->getPrivateProperty($response, 'message'));
    }

    public function testGetArrayFormat(): void
    {
        $message = 'Error message';
        $code = Response::HTTP_BAD_REQUEST;

        $response = new ErrorResponse($message, $code);
        $result = $response->getArrayFormat();

        $expected = [
            'error' => [
                'message' => $message
            ]
        ];
        $this->assertEquals($expected, $result);
    }

    public function testGetJsonFormat(): void
    {
        $message = 'Error message';
        $code = Response::HTTP_BAD_REQUEST;

        $response = new ErrorResponse($message, $code);
        $result = $response->getJsonFormat();

        $this->assertInstanceOf(JsonResponse::class, $result);
        $this->assertEquals($code, $result->getStatusCode());
        
        $expected = [
            'error' => [
                'message' => $message
            ]
        ];
        $this->assertEquals(json_encode($expected), $result->getContent());
    }

    public function testGetJsonFormatWithDefaultCode(): void
    {
        $message = 'Error message';

        $response = new ErrorResponse($message);
        $result = $response->getJsonFormat();

        $this->assertInstanceOf(JsonResponse::class, $result);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->getStatusCode());
    }

    /**
     * Helper method to access private properties
     */
    private function getPrivateProperty($object, $propertyName)
    {
        $reflection = new \ReflectionClass($object);
        $property = $reflection->getProperty($propertyName);
        $property->setAccessible(true);
        return $property->getValue($object);
    }
}
