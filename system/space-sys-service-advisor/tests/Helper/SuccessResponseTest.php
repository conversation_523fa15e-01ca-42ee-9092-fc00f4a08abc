<?php

namespace App\Tests\Helper;

use App\Helper\SuccessResponse;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class SuccessResponseTest extends TestCase
{
    public function testConstructor(): void
    {
        $data = ['key' => 'value'];
        $code = Response::HTTP_CREATED;

        $response = new SuccessResponse($data, $code);

        $this->assertEquals($data, $response->getData());
    }

    public function testSetCode(): void
    {
        $data = ['key' => 'value'];
        $initialCode = Response::HTTP_OK;
        $newCode = Response::HTTP_CREATED;

        $response = new SuccessResponse($data, $initialCode);
        $result = $response->setCode($newCode);

        $this->assertSame($response, $result);
        $this->assertEquals($newCode, $this->getPrivateProperty($response, 'code'));
    }

    public function testSetData(): void
    {
        $initialData = ['key' => 'value'];
        $newData = ['new_key' => 'new_value'];

        $response = new SuccessResponse($initialData);
        $result = $response->setData($newData);

        $this->assertSame($response, $result);
        $this->assertEquals($newData, $response->getData());
    }

    public function testGetData(): void
    {
        $data = ['key' => 'value'];

        $response = new SuccessResponse($data);

        $this->assertEquals($data, $response->getData());
    }

    public function testGetArrayFormat(): void
    {
        $data = ['key' => 'value'];

        $response = new SuccessResponse($data);
        $result = $response->getArrayFormat();

        $this->assertEquals(['success' => $data], $result);
    }

    public function testGetJsonFormat(): void
    {
        $data = ['key' => 'value'];
        $code = Response::HTTP_CREATED;

        $response = new SuccessResponse($data, $code);
        $result = $response->getJsonFormat();

        $this->assertInstanceOf(JsonResponse::class, $result);
        $this->assertEquals($code, $result->getStatusCode());
        $this->assertEquals(json_encode(['success' => $data]), $result->getContent());
    }

    public function testGetJsonFormatWithDefaultCode(): void
    {
        $data = ['key' => 'value'];

        $response = new SuccessResponse($data);
        $result = $response->getJsonFormat();

        $this->assertInstanceOf(JsonResponse::class, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getStatusCode());
    }

    /**
     * Helper method to access private properties
     */
    private function getPrivateProperty($object, $propertyName)
    {
        $reflection = new \ReflectionClass($object);
        $property = $reflection->getProperty($propertyName);
        $property->setAccessible(true);
        return $property->getValue($object);
    }
}
