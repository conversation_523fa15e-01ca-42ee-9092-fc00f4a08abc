<?php

namespace App\Tests\Connector;

use App\Connector\WsGeolocConnector;
use App\Helper\WSResponse;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Contracts\HttpClient\ResponseInterface;

class WsGeolocConnectorTest extends TestCase
{
    private $httpClient;
    private $logger;
    private $connector;

    protected function setUp(): void
    {
        $this->httpClient = $this->createMock(HttpClientInterface::class);
        $this->logger = $this->createMock(LoggerInterface::class);
        
        $this->connector = new WsGeolocConnector($this->httpClient);
        
        // Set logger using reflection since it's injected via a trait
        $reflection = new \ReflectionClass($this->connector);
        $loggerProperty = $reflection->getProperty('logger');
        $loggerProperty->setAccessible(true);
        $loggerProperty->setValue($this->connector, $this->logger);
    }

    public function testCallSuccess(): void
    {
        // Create a mock response
        $mockResponse = $this->createMock(ResponseInterface::class);
        $mockResponse->method('getStatusCode')->willReturn(Response::HTTP_OK);
        $mockResponse->method('getContent')->willReturn('{"success": true}');

        // Set up expectations
        $this->httpClient->expects($this->once())
            ->method('request')
            ->with(
                'GET',
                '/test',
                ['json' => ['param' => 'value']]
            )
            ->willReturn($mockResponse);

        // Execute the method
        $result = $this->connector->call('GET', '/test', ['json' => ['param' => 'value']]);

        // Assert the result
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
        $this->assertEquals('{"success": true}', $result->getData());
    }

    public function testCallException(): void
    {
        // Set up expectations
        $this->httpClient->expects($this->once())
            ->method('request')
            ->willThrowException(new \Exception('Connection error', 500));

        // Execute the method
        $result = $this->connector->call('GET', '/test', []);

        // Assert the result
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(500, $result->getCode());
        $this->assertEquals('Connection error', $result->getData());
    }
}
