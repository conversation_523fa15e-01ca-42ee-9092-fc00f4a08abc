<?php

namespace App\Tests\Helper;

use App\Helper\WSResponse;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;

class WSResponseTest extends TestCase
{
    public function testConstructor(): void
    {
        $response = new WSResponse();
        
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals("", $response->getData());
    }
    
    public function testConstructorWithParams(): void
    {
        $code = Response::HTTP_CREATED;
        $data = '{"success": true}';
        $response = new WSResponse($code, $data);
        
        $this->assertEquals($code, $response->getCode());
        $this->assertEquals($data, $response->getData());
    }
    
    public function testSetCode(): void
    {
        $response = new WSResponse();
        $response->setCode(Response::HTTP_CREATED);
        
        $this->assertEquals(Response::HTTP_CREATED, $response->getCode());
    }
    
    public function testSetData(): void
    {
        $response = new WSResponse();
        $data = '{"success": true}';
        $response->setData($data);
        
        $this->assertEquals($data, $response->getData());
    }
    
    public function testFluentInterface(): void
    {
        $response = new WSResponse();
        
        $result = $response->setCode(Response::HTTP_CREATED);
        $this->assertSame($response, $result);
        
        $result = $response->setData('{"success": true}');
        $this->assertSame($response, $result);
    }
}
