<?php

namespace App\Tests\Helper;

use App\Helper\SuccessResponse;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;

class SuccessResponseTest extends TestCase
{
    public function testConstructor(): void
    {
        $data = ['test' => 'data'];
        $response = new SuccessResponse($data);

        $this->assertEquals($data, $response->getData());
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
    }

    public function testConstructorWithCustomCode(): void
    {
        $data = ['test' => 'data'];
        $code = Response::HTTP_CREATED;
        $response = new SuccessResponse($data, $code);

        $this->assertEquals($data, $response->getData());
        $this->assertEquals($code, $response->getCode());
    }

    public function testSetCode(): void
    {
        $response = new SuccessResponse([]);
        $response->setCode(Response::HTTP_CREATED);

        $this->assertEquals(Response::HTTP_CREATED, $response->getCode());
    }

    public function testSetData(): void
    {
        $response = new SuccessResponse([]);
        $newData = ['new' => 'data'];
        $response->setData($newData);

        $this->assertEquals($newData, $response->getData());
    }

    public function testToArrayWithArrayData(): void
    {
        $data = ['test' => 'data'];
        $response = new SuccessResponse($data);

        $result = $response->toArray();

        $this->assertEquals(Response::HTTP_OK, $result['code']);
        $this->assertArrayHasKey('success', $result['content']);
        $this->assertEquals($data, $result['content']['success']);
    }

    public function testToArrayWithJsonData(): void
    {
        $jsonData = '{"test": "data"}';
        $response = new SuccessResponse($jsonData);

        $result = $response->toArray();

        $this->assertEquals(Response::HTTP_OK, $result['code']);
        $this->assertArrayHasKey('success', $result['content']);
        $this->assertEquals(['test' => 'data'], $result['content']['success']);
    }
}
