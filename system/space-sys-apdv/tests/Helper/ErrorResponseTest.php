<?php

namespace App\Tests\Helper;

use App\Helper\ErrorResponse;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;

class ErrorResponseTest extends TestCase
{
    public function testConstructorWithStringError(): void
    {
        $errorMessage = 'Error message';
        $response = new ErrorResponse($errorMessage);

        $this->assertEquals($errorMessage, $response->getMessage());
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getCode());
    }

    public function testConstructorWithArrayError(): void
    {
        $errors = ['field' => 'Error message'];
        $response = new ErrorResponse($errors);

        $this->assertEquals(json_encode($errors), $response->getMessage());
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getCode());
    }

    public function testConstructorWithCustomCode(): void
    {
        $errorMessage = 'Error message';
        $code = Response::HTTP_NOT_FOUND;
        $response = new ErrorResponse($errorMessage, $code);

        $this->assertEquals($errorMessage, $response->getMessage());
        $this->assertEquals($code, $response->getCode());
    }

    public function testSetCode(): void
    {
        $response = new ErrorResponse('Error');
        $response->setCode(Response::HTTP_NOT_FOUND);

        $this->assertEquals(Response::HTTP_NOT_FOUND, $response->getCode());
    }

    public function testSetMessage(): void
    {
        $response = new ErrorResponse('Error');
        $newMessage = 'New error message';
        $response->setMessage($newMessage);

        $this->assertEquals($newMessage, $response->getMessage());
    }

    public function testSetErrors(): void
    {
        $response = new ErrorResponse('Error');
        $newErrors = ['field' => 'Error message'];
        $response->setErrors($newErrors);

        $this->assertEquals($newErrors, $response->getErrors());
    }

    public function testToArrayWithMessageOnly(): void
    {
        $errorMessage = 'Error message';
        $response = new ErrorResponse($errorMessage);

        $result = $response->toArray();

        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result['code']);
        $this->assertArrayHasKey('error', $result['content']);
        $this->assertArrayHasKey('message', $result['content']['error']);
        $this->assertEquals($errorMessage, $result['content']['error']['message']);
    }

    public function testToArrayWithMessageAndErrors(): void
    {
        $errorMessage = 'Error message';
        $errors = ['field' => 'Error message'];
        $response = new ErrorResponse($errorMessage);
        $response->setErrors($errors);

        $result = $response->toArray();

        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result['code']);
        $this->assertArrayHasKey('error', $result['content']);
        $this->assertArrayHasKey('message', $result['content']['error']);
        $this->assertArrayHasKey('errors', $result['content']['error']);
        $this->assertEquals($errorMessage, $result['content']['error']['message']);
        $this->assertEquals($errors, $result['content']['error']['errors']);
    }

    public function testToArrayWithLowCode(): void
    {
        $response = new ErrorResponse('Error', 200);

        $result = $response->toArray();

        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result['code']);
    }
}
