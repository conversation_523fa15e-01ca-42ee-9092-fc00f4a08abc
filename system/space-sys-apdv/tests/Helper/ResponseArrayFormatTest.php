<?php

namespace App\Tests\Helper;

use App\Helper\ResponseArrayFormat;
use App\Helper\ErrorResponse;
use App\Helper\SuccessResponse;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;

class ResponseArrayFormatTest extends TestCase
{
    public function testErrorResponseImplementsInterface(): void
    {
        $errorResponse = new ErrorResponse('Test error');
        $this->assertInstanceOf(ResponseArrayFormat::class, $errorResponse);
        
        $result = $errorResponse->toArray();
        $this->assertIsArray($result);
        $this->assertArrayHasKey('code', $result);
        $this->assertArrayHasKey('content', $result);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result['code']);
        $this->assertArrayHasKey('error', $result['content']);
    }
    
    public function testSuccessResponseImplementsInterface(): void
    {
        $data = ['test' => 'data'];
        $successResponse = new SuccessResponse($data);
        $this->assertInstanceOf(ResponseArrayFormat::class, $successResponse);
        
        $result = $successResponse->toArray();
        $this->assertIsArray($result);
        $this->assertArrayHasKey('code', $result);
        $this->assertArrayHasKey('content', $result);
        $this->assertEquals(Response::HTTP_OK, $result['code']);
        $this->assertArrayHasKey('success', $result['content']);
        $this->assertEquals($data, $result['content']['success']);
    }
    
    public function testErrorResponseWithCustomCode(): void
    {
        $errorResponse = new ErrorResponse('Test error', Response::HTTP_NOT_FOUND);
        
        $result = $errorResponse->toArray();
        $this->assertEquals(Response::HTTP_NOT_FOUND, $result['code']);
    }
    
    public function testSuccessResponseWithCustomCode(): void
    {
        $data = ['test' => 'data'];
        $successResponse = new SuccessResponse($data, Response::HTTP_CREATED);
        
        $result = $successResponse->toArray();
        $this->assertEquals(Response::HTTP_CREATED, $result['code']);
    }
    
    public function testErrorResponseWithArrayErrors(): void
    {
        $errors = ['field1' => 'Error 1', 'field2' => 'Error 2'];
        $errorResponse = new ErrorResponse($errors);
        $errorResponse->setErrors($errors);
        
        $result = $errorResponse->toArray();
        $this->assertArrayHasKey('errors', $result['content']['error']);
        $this->assertEquals($errors, $result['content']['error']['errors']);
    }
}
