<?php

namespace App\Tests\Trait;

use App\Trait\LoggerTrait;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;

class LoggerTraitTest extends TestCase
{
    public function testSetLogger(): void
    {
        // Create a class that uses the LoggerTrait
        $loggerTraitUser = new class {
            use LoggerTrait;

            public function getLogger()
            {
                return $this->logger;
            }
        };

        // Create a mock logger
        $mockLogger = $this->createMock(LoggerInterface::class);

        // Call the setLogger method directly
        $loggerTraitUser->setLogger($mockLogger);

        // Verify the logger was set
        $this->assertSame($mockLogger, $loggerTraitUser->getLogger());
    }

    public function testLoggerUsage(): void
    {
        // Create a class that uses the LoggerTrait
        $loggerTraitUser = new class {
            use LoggerTrait;

            public function logInfo($message, array $context = [])
            {
                $this->logger->info($message, $context);
            }

            public function logError($message, array $context = [])
            {
                $this->logger->error($message, $context);
            }

            public function logWarning($message, array $context = [])
            {
                $this->logger->warning($message, $context);
            }

            public function logDebug($message, array $context = [])
            {
                $this->logger->debug($message, $context);
            }
        };

        // Create a mock logger
        $mockLogger = $this->createMock(LoggerInterface::class);

        // Set the logger using the setter method
        $loggerTraitUser->setLogger($mockLogger);

        // Test logging methods
        $mockLogger->expects($this->once())
            ->method('info')
            ->with('Test info message', ['context' => 'value']);

        $mockLogger->expects($this->once())
            ->method('error')
            ->with('Test error message', ['context' => 'value']);

        $mockLogger->expects($this->once())
            ->method('warning')
            ->with('Test warning message', ['context' => 'value']);

        $mockLogger->expects($this->once())
            ->method('debug')
            ->with('Test debug message', ['context' => 'value']);

        $loggerTraitUser->logInfo('Test info message', ['context' => 'value']);
        $loggerTraitUser->logError('Test error message', ['context' => 'value']);
        $loggerTraitUser->logWarning('Test warning message', ['context' => 'value']);
        $loggerTraitUser->logDebug('Test debug message', ['context' => 'value']);
    }
}
