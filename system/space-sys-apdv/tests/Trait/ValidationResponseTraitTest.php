<?php

namespace App\Tests\Trait;

use App\Helper\ErrorResponse;
use App\Trait\ValidationResponseTrait;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Component\Validator\ConstraintViolationList;

class ValidationResponseTraitTest extends TestCase
{
    public function testGetValidationMessages(): void
    {
        // Create a class that uses the ValidationResponseTrait
        $validationResponseTraitUser = new class {
            use ValidationResponseTrait;
            
            public function getMessages(ConstraintViolationList $errors): array
            {
                return $this->getValidationMessages($errors);
            }
        };
        
        // Create a constraint violation list
        $violations = new ConstraintViolationList([
            new ConstraintViolation(
                'This value should not be blank.',
                'This value should not be blank.',
                [],
                null,
                'field1',
                null
            ),
            new ConstraintViolation(
                'This value is not valid.',
                'This value is not valid.',
                [],
                null,
                'field2',
                null
            )
        ]);
        
        // Get validation messages
        $messages = $validationResponseTraitUser->getMessages($violations);
        
        // Verify the messages
        $this->assertCount(2, $messages);
        $this->assertEquals('This value should not be blank.', $messages['field1']);
        $this->assertEquals('This value is not valid.', $messages['field2']);
    }
    
    public function testGetValidationMessagesWithEmptyList(): void
    {
        // Create a class that uses the ValidationResponseTrait
        $validationResponseTraitUser = new class {
            use ValidationResponseTrait;
            
            public function getMessages(ConstraintViolationList $errors): array
            {
                return $this->getValidationMessages($errors);
            }
        };
        
        // Create an empty constraint violation list
        $violations = new ConstraintViolationList([]);
        
        // Get validation messages
        $messages = $validationResponseTraitUser->getMessages($violations);
        
        // Verify the messages
        $this->assertEmpty($messages);
    }
    
    public function testGetValidationErrorResponse(): void
    {
        // Create a class that uses the ValidationResponseTrait
        $validationResponseTraitUser = new class {
            use ValidationResponseTrait;
            
            public function getErrorResponse(array $messages): ErrorResponse
            {
                return $this->getValidationErrorResponse($messages);
            }
        };
        
        // Create validation messages
        $messages = [
            'field1' => 'This value should not be blank.',
            'field2' => 'This value is not valid.'
        ];
        
        // Get error response
        $errorResponse = $validationResponseTraitUser->getErrorResponse($messages);
        
        // Verify the error response
        $this->assertInstanceOf(ErrorResponse::class, $errorResponse);
        $this->assertEquals('validation_failed', $errorResponse->getMessage());
        $this->assertEquals($messages, $errorResponse->getErrors());
        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $errorResponse->getCode());
    }
}
