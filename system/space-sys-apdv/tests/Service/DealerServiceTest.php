<?php

namespace App\Tests\Service;

use App\Connector\WsGeolocConnector;
use App\Helper\WSResponse;
use App\Service\DealerService;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class DealerServiceTest extends TestCase
{
    private $connector;
    private $logger;
    private $dealerService;

    protected function setUp(): void
    {
        $this->connector = $this->createMock(WsGeolocConnector::class);
        $this->logger = $this->createMock(LoggerInterface::class);

        $this->dealerService = new DealerService($this->connector);

        // Set logger using reflection since it's injected via a trait
        $reflection = new \ReflectionClass($this->dealerService);
        $loggerProperty = $reflection->getProperty('logger');
        $loggerProperty->setAccessible(true);
        $loggerProperty->setValue($this->dealerService, $this->logger);
    }

    public function testGetDealerList(): void
    {
        // Test data
        $params = [
            'brand' => 'TestBrand',
            'country' => 'FR',
            'language' => 'fr',
            'latitude' => '48.8566',
            'longitude' => '2.3522',
            'rmax' => '50',
            'resultmax' => '10',
            'unit' => 'km',
            'criterias' => 'VN,APV'
        ];

        $expectedOptions = [
            "parameters" => [
                "Brand" => $params['brand'],
                "Details" => "max",
                "Consumer" => "SPACE",
                "RMax" => (int) $params['rmax'],
                "Country" => $params['country'],
                "Sort" => "Distance",
                "ResultMax" => (int) $params['resultmax'],
                "Latitude" => $params['latitude'],
                "Unit" => $params['unit'],
                "Longitude" => $params['longitude'],
                "Culture" => $params['language'],
                "lcdv2" => "",
                'Criterias' => $params['criterias']
            ]
        ];

        $mockResponse = new WSResponse(Response::HTTP_OK, '{"DealersFull": []}');

        // Set up expectations
        $this->connector->expects($this->once())
            ->method('call')
            ->with(
                Request::METHOD_POST,
                '/GetDealerList',
                ['json' => $expectedOptions]
            )
            ->willReturn($mockResponse);

        // Execute the method
        $result = $this->dealerService->getDealerList($params);

        // Assert the result
        $this->assertSame($mockResponse, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
        $this->assertEquals('{"DealersFull": []}', $result->getData());
    }

    public function testGetDealer(): void
    {
        // Test data
        $params = [
            'brand' => 'TestBrand',
            'country' => 'FR',
            'language' => 'fr',
            'siteGeo' => '12345'
        ];

        $expectedOptions = [
            "parameters" => [
                "Brand" => $params['brand'],
                "Country" => $params['country'],
                "Consumer" => "SPACE",
                "Culture" => $params['language'],
                "SiteGeo" => $params['siteGeo']
            ]
        ];

        $mockResponse = new WSResponse(Response::HTTP_OK, '{"Dealer": {"id": "12345"}}');

        // Set up expectations
        $this->connector->expects($this->exactly(2))
            ->method('call')
            ->with(
                Request::METHOD_POST,
                '/GetDealer',
                ['json' => $expectedOptions]
            )
            ->willReturn($mockResponse);

        // Execute the method
        $result = $this->dealerService->getDealer($params);

        // Assert the result
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
        $this->assertEquals('{"Dealer": {"id": "12345"}}', $result->getData());
    }

    public function testGetDealerNotFound(): void
    {
        // Test data
        $params = [
            'brand' => 'TestBrand',
            'country' => 'FR',
            'language' => 'fr',
            'siteGeo' => '99999' // Non-existent dealer
        ];

        $mockResponse = new WSResponse(Response::HTTP_OK, '{"Dealer": null}');

        // Set up expectations
        $this->connector->expects($this->exactly(2))
            ->method('call')
            ->willReturn($mockResponse);

        // Execute the method
        $result = $this->dealerService->getDealer($params);

        // Assert the result
        $this->assertEquals(Response::HTTP_NOT_FOUND, $result->getCode());
    }

    public function testGetDealerError(): void
    {
        // Test data
        $params = [
            'brand' => 'TestBrand',
            'country' => 'FR',
            'language' => 'fr',
            'siteGeo' => '12345'
        ];

        $mockResponse = new WSResponse(Response::HTTP_SERVICE_UNAVAILABLE, '{"error": {"message": "Service unavailable"}}');

        // Set up expectations
        $this->connector->expects($this->exactly(2))
            ->method('call')
            ->willReturn($mockResponse);

        // Execute the method
        $result = $this->dealerService->getDealer($params);

        // Assert the result
        $this->assertEquals(Response::HTTP_SERVICE_UNAVAILABLE, $result->getCode());
        $this->assertEquals('{"error": {"message": "Service unavailable"}}', $result->getData());
    }
}
