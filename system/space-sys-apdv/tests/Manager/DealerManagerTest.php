<?php

namespace App\Tests\Manager;

use App\DataMapper\DealerDataMapper;
use App\Helper\ErrorResponse;
use App\Helper\SuccessResponse;
use App\Helper\WSResponse;
use App\Manager\DealerManager;
use App\Service\DealerService;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\SerializerInterface;

class DealerManagerTest extends TestCase
{
    private $dealerService;
    private $serializer;
    private $dealerDataMapper;
    private $logger;
    private $dealerManager;

    protected function setUp(): void
    {
        $this->dealerService = $this->createMock(DealerService::class);
        $this->serializer = $this->createMock(SerializerInterface::class);
        $this->dealerDataMapper = $this->createMock(DealerDataMapper::class);
        $this->logger = $this->createMock(LoggerInterface::class);

        $this->dealerManager = new DealerManager(
            $this->dealerService,
            $this->serializer,
            $this->dealerDataMapper
        );

        // Set logger using reflection since it's injected via a trait
        $reflection = new \ReflectionClass($this->dealerManager);
        $loggerProperty = $reflection->getProperty('logger');
        $loggerProperty->setAccessible(true);
        $loggerProperty->setValue($this->dealerManager, $this->logger);
    }

    public function testGetDealerListSuccess(): void
    {
        // Test data
        $params = [
            'brand' => 'TestBrand',
            'country' => 'FR',
            'language' => 'fr',
            'latitude' => '48.8566',
            'longitude' => '2.3522',
            'rmax' => '50',
            'resultmax' => '10',
            'unit' => 'km',
            'criterias' => 'VN,APV'
        ];

        $jsonResponse = '{"DealersFull": [{"id": "123", "name": "Test Dealer"}]}';
        $mockWSResponse = new WSResponse(Response::HTTP_OK, $jsonResponse);

        // Set up expectations
        $this->dealerService->expects($this->once())
            ->method('getDealerList')
            ->with($params)
            ->willReturn($mockWSResponse);

        // Execute the method
        $result = $this->dealerManager->getDealerList($params);

        // Assert the result
        $this->assertInstanceOf(SuccessResponse::class, $result);
        $resultArray = $result->toArray();
        $this->assertEquals(Response::HTTP_OK, $resultArray['code']);
        $this->assertEquals(['success' => [['id' => '123', 'name' => 'Test Dealer']]], $resultArray['content']);
    }

    public function testGetDealerListError(): void
    {
        // Test data
        $params = [
            'brand' => 'TestBrand',
            'country' => 'FR',
            'language' => 'fr',
            'latitude' => '48.8566',
            'longitude' => '2.3522',
            'rmax' => '50',
            'resultmax' => '10',
            'unit' => 'km',
            'criterias' => 'VN,APV'
        ];

        $errorResponse = ['error' => ['message' => 'Service unavailable']];
        $mockWSResponse = new WSResponse(Response::HTTP_SERVICE_UNAVAILABLE, json_encode($errorResponse));

        // Set up expectations
        $this->dealerService->expects($this->once())
            ->method('getDealerList')
            ->with($params)
            ->willReturn($mockWSResponse);

        // Execute the method
        $result = $this->dealerManager->getDealerList($params);

        // Assert the result
        $this->assertInstanceOf(ErrorResponse::class, $result);
        $resultArray = $result->toArray();
        $this->assertEquals(Response::HTTP_SERVICE_UNAVAILABLE, $resultArray['code']);
        $this->assertArrayHasKey('error', $resultArray['content']);
        $this->assertArrayHasKey('message', $resultArray['content']['error']);
        $this->assertStringContainsString('Service unavailable', $resultArray['content']['error']['message']);
    }

    public function testGetDealerListException(): void
    {
        // Test data
        $params = [
            'brand' => 'TestBrand',
            'country' => 'FR',
            'language' => 'fr',
            'latitude' => '48.8566',
            'longitude' => '2.3522',
            'rmax' => '50',
            'resultmax' => '10',
            'unit' => 'km',
            'criterias' => 'VN,APV'
        ];

        // Set up expectations
        $this->dealerService->expects($this->once())
            ->method('getDealerList')
            ->with($params)
            ->willThrowException(new \Exception('Connection error', 500));

        // Execute the method
        $result = $this->dealerManager->getDealerList($params);

        // Assert the result
        $this->assertInstanceOf(ErrorResponse::class, $result);
        $resultArray = $result->toArray();
        $this->assertEquals(500, $resultArray['code']);
        $this->assertEquals(['error' => ['message' => 'Connection error', 'errors' => 'Connection error']], $resultArray['content']);
    }

    public function testGetDealerSuccess(): void
    {
        // Test data
        $params = [
            'brand' => 'TestBrand',
            'country' => 'FR',
            'language' => 'fr',
            'siteGeo' => '12345'
        ];

        $jsonResponse = '{"Dealer": {"id": "12345", "name": "Test Dealer"}}';
        $mockWSResponse = new WSResponse(Response::HTTP_OK, $jsonResponse);

        // Set up expectations
        $this->dealerService->expects($this->once())
            ->method('getDealer')
            ->with($params)
            ->willReturn($mockWSResponse);

        // Execute the method
        $result = $this->dealerManager->getDealer($params);

        // Assert the result
        $this->assertInstanceOf(SuccessResponse::class, $result);
        $resultArray = $result->toArray();
        $this->assertEquals(Response::HTTP_OK, $resultArray['code']);
        $this->assertEquals(['success' => ['id' => '12345', 'name' => 'Test Dealer']], $resultArray['content']);
    }

    public function testGetDealerNotFound(): void
    {
        // Test data
        $params = [
            'brand' => 'TestBrand',
            'country' => 'FR',
            'language' => 'fr',
            'siteGeo' => '99999'
        ];

        $mockWSResponse = new WSResponse(Response::HTTP_NOT_FOUND, '{}');

        // Set up expectations
        $this->dealerService->expects($this->once())
            ->method('getDealer')
            ->with($params)
            ->willReturn($mockWSResponse);

        // Execute the method
        $result = $this->dealerManager->getDealer($params);

        // Assert the result
        $this->assertInstanceOf(ErrorResponse::class, $result);
        $resultArray = $result->toArray();
        $this->assertEquals(Response::HTTP_NOT_FOUND, $resultArray['code']);
        $this->assertEquals(['error' => ['message' => 'No dealer matches the criteria you indicated', 'errors' => 'No dealer matches the criteria you indicated']], $resultArray['content']);
    }

    public function testGetDealerError(): void
    {
        // Test data
        $params = [
            'brand' => 'TestBrand',
            'country' => 'FR',
            'language' => 'fr',
            'siteGeo' => '12345'
        ];

        $errorResponse = ['error' => ['message' => 'Service unavailable']];
        $mockWSResponse = new WSResponse(Response::HTTP_SERVICE_UNAVAILABLE, json_encode($errorResponse));

        // Set up expectations
        $this->dealerService->expects($this->once())
            ->method('getDealer')
            ->with($params)
            ->willReturn($mockWSResponse);

        // Execute the method
        $result = $this->dealerManager->getDealer($params);

        // Assert the result
        $this->assertInstanceOf(ErrorResponse::class, $result);
        $resultArray = $result->toArray();
        $this->assertEquals(Response::HTTP_SERVICE_UNAVAILABLE, $resultArray['code']);
        $this->assertArrayHasKey('error', $resultArray['content']);
        $this->assertArrayHasKey('message', $resultArray['content']['error']);
        $this->assertStringContainsString('Service unavailable', $resultArray['content']['error']['message']);
    }

    public function testGetDealerException(): void
    {
        // Test data
        $params = [
            'brand' => 'TestBrand',
            'country' => 'FR',
            'language' => 'fr',
            'siteGeo' => '12345'
        ];

        // Set up expectations
        $this->dealerService->expects($this->once())
            ->method('getDealer')
            ->with($params)
            ->willThrowException(new \Exception('Connection error', 500));

        // Execute the method
        $result = $this->dealerManager->getDealer($params);

        // Assert the result
        $this->assertInstanceOf(ErrorResponse::class, $result);
        $resultArray = $result->toArray();
        $this->assertEquals(500, $resultArray['code']);
        $this->assertEquals(['error' => ['message' => 'Connection error', 'errors' => 'Connection error']], $resultArray['content']);
    }
}
