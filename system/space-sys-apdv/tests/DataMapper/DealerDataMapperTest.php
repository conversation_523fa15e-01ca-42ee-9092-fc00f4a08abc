<?php

namespace App\Tests\DataMapper;

use App\DataMapper\DealerDataMapper;
use App\Model\DealerModel;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Serializer\SerializerInterface;

class DealerDataMapperTest extends TestCase
{
    private $serializer;
    private $dealerDataMapper;

    protected function setUp(): void
    {
        // Create a mock that implements both SerializerInterface and DenormalizerInterface
        $this->serializer = $this->getMockBuilder(SerializerInterface::class)
            ->addMethods(['denormalize'])
            ->getMockForAbstractClass();

        $this->dealerDataMapper = new DealerDataMapper($this->serializer);
    }

    public function testCreateListWithValidData(): void
    {
        // Test data
        $jsonData = json_encode([
            'DealersFull' => [
                [
                    'Address' => ['Street' => '123 Main St'],
                    'BusinessList' => [],
                    'Coordinates' => ['Latitude' => 48.8566, 'Longitude' => 2.3522],
                    'CountryId' => 'FR',
                    'Culture' => 'fr',
                    'DistanceFromPoint' => '10.5',
                    'Emails' => ['Email' => '<EMAIL>'],
                    'Indicator' => ['Value' => 'Test'],
                    'Name' => 'Test Dealer',
                    'OpeningHoursList' => [],
                    'Phones' => ['Phone' => '123456789'],
                    'RRDI' => 'RRDI123',
                    'SiteGeo' => '12345',
                    'UrlPages' => ['Url' => 'http://example.com'],
                    'WebSites' => ['WebSite' => 'http://example.com'],
                    'Brand' => 'TestBrand',
                    'carac_rdvi' => 'Test',
                    'FtcCodeList' => []
                ]
            ]
        ]);

        // Mock dealer model
        $mockDealerModel = $this->createMock(DealerModel::class);

        // Set up serializer expectations for denormalize method
        $this->serializer->expects($this->once())
            ->method('denormalize')
            ->willReturn($mockDealerModel);

        // Execute the method
        $result = $this->dealerDataMapper->createList($jsonData);

        // Assert the result
        $this->assertIsArray($result);
        $this->assertArrayHasKey('Dealers', $result);
        $this->assertCount(1, $result['Dealers']);
        $this->assertSame($mockDealerModel, $result['Dealers'][0]);
    }

    public function testCreateListWithInvalidData(): void
    {
        // Test with invalid JSON
        $result = $this->dealerDataMapper->createList('{"InvalidKey": []}');
        $this->assertIsArray($result);
        $this->assertEmpty($result);

        // Test with null data
        $result = $this->dealerDataMapper->createList('{"DealersFull": null}');
        $this->assertIsArray($result);
        $this->assertEmpty($result);
    }

    public function testCreateDetailWithValidData(): void
    {
        // Test data
        $jsonData = json_encode([
            'Dealer' => [
                'Address' => ['Street' => '123 Main St'],
                'BusinessList' => [],
                'Coordinates' => ['Latitude' => 48.8566, 'Longitude' => 2.3522],
                'CountryId' => 'FR',
                'Culture' => 'fr',
                'DistanceFromPoint' => '10.5',
                'Emails' => ['Email' => '<EMAIL>'],
                'Indicator' => ['Value' => 'Test'],
                'Name' => 'Test Dealer',
                'OpeningHoursList' => [],
                'Phones' => ['Phone' => '123456789'],
                'RRDI' => 'RRDI123',
                'SiteGeo' => '12345',
                'UrlPages' => ['Url' => 'http://example.com'],
                'WebSites' => ['WebSite' => 'http://example.com'],
                'Brand' => 'TestBrand',
                'carac_rdvi' => 'Test',
                'FtcCodeList' => []
            ]
        ]);

        // Mock dealer model
        $mockDealerModel = $this->createMock(DealerModel::class);

        // Set up serializer expectations
        $this->serializer->expects($this->once())
            ->method('denormalize')
            ->willReturn($mockDealerModel);

        // Execute the method
        $result = $this->dealerDataMapper->createDetail($jsonData);

        // Assert the result
        $this->assertIsArray($result);
        $this->assertArrayHasKey('Dealer', $result);
        $this->assertSame($mockDealerModel, $result['Dealer']);
    }

    public function testCreateDetailWithInvalidData(): void
    {
        // Test with invalid JSON
        $result = $this->dealerDataMapper->createDetail('{"InvalidKey": {}}');
        $this->assertIsArray($result);
        $this->assertEmpty($result);

        // Test with null data
        $result = $this->dealerDataMapper->createDetail('{"Dealer": null}');
        $this->assertIsArray($result);
        $this->assertEmpty($result);
    }
}
