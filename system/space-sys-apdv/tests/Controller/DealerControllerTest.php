<?php

namespace App\Tests\Controller;

use App\Controller\DealerController;
use App\Helper\ErrorResponse;
use App\Helper\SuccessResponse;
use App\Manager\DealerManager;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Component\HttpFoundation\HeaderBag;
use Symfony\Component\DependencyInjection\ContainerInterface;

class DealerControllerTest extends KernelTestCase
{
    private $validator;
    private $dealerManager;
    private $controller;
    private $container;

    protected function setUp(): void
    {
        self::bootKernel();
        $this->container = static::getContainer();
        
        // Get the validator service from the container
        $this->validator = $this->container->get(ValidatorInterface::class);
        
        // Create a mock for the DealerManager
        $this->dealerManager = $this->createMock(DealerManager::class);
        
        // Create the controller and set the container
        $this->controller = new DealerController();
        $this->controller->setContainer($this->container);
    }

    public function testIndexWithValidParameters(): void
    {
        // Create a request with query parameters
        $request = new Request([
            'brand' => 'TestBrand',
            'country' => 'FR',
            'language' => 'fr',
            'latitude' => '48.8566',
            'longitude' => '2.3522',
            'rmax' => '50',
            'resultmax' => '10',
            'unit' => 'km',
            'criterias' => 'VN,APV'
        ]);

        // Set up manager to return a success response
        $successResponse = new SuccessResponse([['id' => '123', 'name' => 'Test Dealer']]);
        
        $this->dealerManager->expects($this->once())
            ->method('getDealerList')
            ->willReturn($successResponse);

        // Execute the controller method
        $response = $this->controller->index($this->validator, $this->dealerManager, $request);

        // Assert the response
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        
        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('success', $responseData);
        $this->assertEquals([['id' => '123', 'name' => 'Test Dealer']], $responseData['success']);
    }

    public function testIndexWithInvalidParameters(): void
    {
        // Create a request with missing parameters
        $request = new Request([
            'brand' => 'TestBrand',
            // Missing required parameters
        ]);

        // Execute the controller method
        $response = $this->controller->index($this->validator, $this->dealerManager, $request);

        // Assert the response
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        
        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
    }

    public function testDetailWithValidParameters(): void
    {
        // Create a request with query parameters
        $request = new Request([
            'brand' => 'TestBrand',
            'country' => 'FR',
            'language' => 'fr'
        ]);

        // Set up manager to return a success response
        $successResponse = new SuccessResponse(['id' => '12345', 'name' => 'Test Dealer']);
        
        $this->dealerManager->expects($this->once())
            ->method('getDealer')
            ->willReturn($successResponse);

        // Execute the controller method
        $response = $this->controller->detail('12345', $this->validator, $this->dealerManager, $request);

        // Assert the response
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        
        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('success', $responseData);
        $this->assertEquals(['id' => '12345', 'name' => 'Test Dealer'], $responseData['success']);
    }

    public function testDetailWithInvalidParameters(): void
    {
        // Create a request with missing parameters
        $request = new Request([
            'brand' => 'TestBrand',
            // Missing required parameters
        ]);

        // Execute the controller method
        $response = $this->controller->detail('12345', $this->validator, $this->dealerManager, $request);

        // Assert the response
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        
        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
    }

    public function testDetailWithErrorResponse(): void
    {
        // Create a request with query parameters
        $request = new Request([
            'brand' => 'TestBrand',
            'country' => 'FR',
            'language' => 'fr'
        ]);

        // Set up manager to return an error response
        $errorResponse = new ErrorResponse('Dealer not found', Response::HTTP_NOT_FOUND);
        
        $this->dealerManager->expects($this->once())
            ->method('getDealer')
            ->willReturn($errorResponse);

        // Execute the controller method
        $response = $this->controller->detail('99999', $this->validator, $this->dealerManager, $request);

        // Assert the response
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_NOT_FOUND, $response->getStatusCode());
        
        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
    }
}
