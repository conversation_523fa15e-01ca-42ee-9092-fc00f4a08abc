<?php

namespace App\Tests\Model;

use App\Model\OpeningHourModel;
use PHPUnit\Framework\TestCase;

class OpeningHourModelTest extends TestCase
{
    private OpeningHourModel $openingHourModel;

    protected function setUp(): void
    {
        $this->openingHourModel = new OpeningHourModel();
    }

    public function testGetSetLabel(): void
    {
        $label = 'Monday: 9:00 AM - 5:00 PM';
        $this->openingHourModel->setLabel($label);
        $this->assertEquals($label, $this->openingHourModel->getLabel());
    }

    public function testGetSetType(): void
    {
        $type = 'Regular';
        $this->openingHourModel->setType($type);
        $this->assertEquals($type, $this->openingHourModel->getType());
    }

    public function testFluentInterface(): void
    {
        $result = $this->openingHourModel->setLabel('Monday: 9:00 AM - 5:00 PM');
        $this->assertSame($this->openingHourModel, $result);

        $result = $this->openingHourModel->setType('Regular');
        $this->assertSame($this->openingHourModel, $result);
    }
}
