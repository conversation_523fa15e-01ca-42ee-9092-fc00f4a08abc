<?php

namespace App\Tests\Model;

use App\Model\WebSiteModel;
use PHPUnit\Framework\TestCase;

class WebSiteModelTest extends TestCase
{
    private WebSiteModel $webSiteModel;

    protected function setUp(): void
    {
        $this->webSiteModel = new WebSiteModel();
    }

    public function testGetSetPrivate(): void
    {
        $url = 'https://private.example.com';
        $this->webSiteModel->setPrivate($url);
        $this->assertEquals($url, $this->webSiteModel->getPrivate());
    }

    public function testGetSetPublic(): void
    {
        $url = 'https://public.example.com';
        $this->webSiteModel->setPublic($url);
        $this->assertEquals($url, $this->webSiteModel->getPublic());
    }

    public function testFluentInterface(): void
    {
        $result = $this->webSiteModel->setPrivate('https://private.example.com');
        $this->assertSame($this->webSiteModel, $result);

        $result = $this->webSiteModel->setPublic('https://public.example.com');
        $this->assertSame($this->webSiteModel, $result);
    }
}
