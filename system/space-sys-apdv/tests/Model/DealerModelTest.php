<?php

namespace App\Tests\Model;

use App\Model\AddressModel;
use App\Model\BusinessModel;
use App\Model\CoordinateModel;
use App\Model\DealerModel;
use App\Model\EmailModel;
use App\Model\FtcCodeModel;
use App\Model\IndicatorModel;
use App\Model\OpeningHourModel;
use App\Model\PhoneModel;
use App\Model\UrlPageModel;
use App\Model\WebSiteModel;
use PHPUnit\Framework\TestCase;

class DealerModelTest extends TestCase
{
    private DealerModel $dealerModel;
    private AddressModel $addressModel;
    private CoordinateModel $coordinateModel;
    private EmailModel $emailModel;
    private IndicatorModel $indicatorModel;
    private PhoneModel $phoneModel;
    private UrlPageModel $urlPageModel;
    private WebSiteModel $webSiteModel;
    private BusinessModel $businessModel;
    private OpeningHourModel $openingHourModel;
    private FtcCodeModel $ftcCodeModel;

    protected function setUp(): void
    {
        $this->dealerModel = new DealerModel();
        $this->addressModel = new AddressModel();
        $this->coordinateModel = new CoordinateModel();
        $this->emailModel = new EmailModel();
        $this->indicatorModel = new IndicatorModel();
        $this->phoneModel = new PhoneModel();
        $this->urlPageModel = new UrlPageModel();
        $this->webSiteModel = new WebSiteModel();
        $this->businessModel = new BusinessModel();
        $this->openingHourModel = new OpeningHourModel();
        $this->ftcCodeModel = new FtcCodeModel();
    }

    public function testGetSetAddress(): void
    {
        $this->dealerModel->setAddress($this->addressModel);
        $this->assertSame($this->addressModel, $this->dealerModel->getAddress());
    }

    public function testGetSetBusinessList(): void
    {
        $businessList = [$this->businessModel];
        $this->dealerModel->setBusinessList($businessList);
        $this->assertSame($businessList, $this->dealerModel->getBusinessList());
    }

    public function testGetSetCoordinates(): void
    {
        $this->dealerModel->setCoordinates($this->coordinateModel);
        $this->assertSame($this->coordinateModel, $this->dealerModel->getCoordinates());
    }

    public function testGetSetCountryId(): void
    {
        $countryId = 'FR';
        $this->dealerModel->setCountryId($countryId);
        $this->assertEquals($countryId, $this->dealerModel->getCountryId());
    }

    public function testGetSetCulture(): void
    {
        $culture = 'fr';
        $this->dealerModel->setCulture($culture);
        $this->assertEquals($culture, $this->dealerModel->getCulture());
    }

    public function testGetSetDistanceFromPoint(): void
    {
        $distance = 10.5;
        $this->dealerModel->setDistanceFromPoint($distance);
        $this->assertEquals($distance, $this->dealerModel->getDistanceFromPoint());
    }

    public function testGetSetEmails(): void
    {
        $this->dealerModel->setEmails($this->emailModel);
        $this->assertSame($this->emailModel, $this->dealerModel->getEmails());
    }

    public function testGetSetIndicator(): void
    {
        $this->dealerModel->setIndicator($this->indicatorModel);
        $this->assertSame($this->indicatorModel, $this->dealerModel->getIndicator());
    }

    public function testGetSetName(): void
    {
        $name = 'Test Dealer';
        $this->dealerModel->setName($name);
        $this->assertEquals($name, $this->dealerModel->getName());
    }

    public function testGetSetOpeningHoursList(): void
    {
        $openingHoursList = [$this->openingHourModel];
        $this->dealerModel->setOpeningHoursList($openingHoursList);
        $this->assertSame($openingHoursList, $this->dealerModel->getOpeningHoursList());
    }

    public function testGetSetPhones(): void
    {
        $this->dealerModel->setPhones($this->phoneModel);
        $this->assertSame($this->phoneModel, $this->dealerModel->getPhones());
    }

    public function testGetSetRrdi(): void
    {
        $rrdi = 'RRDI123';
        $this->dealerModel->setRrdi($rrdi);
        $this->assertEquals($rrdi, $this->dealerModel->getRrdi());
    }

    public function testGetSetSiteGeo(): void
    {
        $siteGeo = '12345';
        $this->dealerModel->setSiteGeo($siteGeo);
        $this->assertEquals($siteGeo, $this->dealerModel->getSiteGeo());
    }

    public function testGetSetUrlPages(): void
    {
        $this->dealerModel->setUrlPages($this->urlPageModel);
        $this->assertSame($this->urlPageModel, $this->dealerModel->getUrlPages());
    }

    public function testGetSetWebSites(): void
    {
        $this->dealerModel->setWebSites($this->webSiteModel);
        $this->assertSame($this->webSiteModel, $this->dealerModel->getWebSites());
    }

    public function testGetSetBrand(): void
    {
        $brand = 'TestBrand';
        $this->dealerModel->setBrand($brand);
        $this->assertEquals($brand, $this->dealerModel->getBrand());
    }

    public function testGetSetCaracRdvi(): void
    {
        $caracRdvi = 'Test';
        $this->dealerModel->setCaracRdvi($caracRdvi);
        $this->assertEquals($caracRdvi, $this->dealerModel->getCaracRdvi());
    }

    public function testGetSetFtcCodeList(): void
    {
        $ftcCodeList = [$this->ftcCodeModel];
        $this->dealerModel->setFtcCodeList($ftcCodeList);
        $this->assertSame($ftcCodeList, $this->dealerModel->getFtcCodeList());
    }

    public function testFluentInterface(): void
    {
        $result = $this->dealerModel->setAddress($this->addressModel);
        $this->assertSame($this->dealerModel, $result);

        $result = $this->dealerModel->setBusinessList([$this->businessModel]);
        $this->assertSame($this->dealerModel, $result);

        $result = $this->dealerModel->setCoordinates($this->coordinateModel);
        $this->assertSame($this->dealerModel, $result);

        $result = $this->dealerModel->setCountryId('FR');
        $this->assertSame($this->dealerModel, $result);

        $result = $this->dealerModel->setCulture('fr');
        $this->assertSame($this->dealerModel, $result);

        $result = $this->dealerModel->setDistanceFromPoint(10.5);
        $this->assertSame($this->dealerModel, $result);

        $result = $this->dealerModel->setEmails($this->emailModel);
        $this->assertSame($this->dealerModel, $result);

        $result = $this->dealerModel->setIndicator($this->indicatorModel);
        $this->assertSame($this->dealerModel, $result);

        $result = $this->dealerModel->setName('Test Dealer');
        $this->assertSame($this->dealerModel, $result);

        $result = $this->dealerModel->setOpeningHoursList([$this->openingHourModel]);
        $this->assertSame($this->dealerModel, $result);

        $result = $this->dealerModel->setPhones($this->phoneModel);
        $this->assertSame($this->dealerModel, $result);

        $result = $this->dealerModel->setRrdi('RRDI123');
        $this->assertSame($this->dealerModel, $result);

        $result = $this->dealerModel->setSiteGeo('12345');
        $this->assertSame($this->dealerModel, $result);

        $result = $this->dealerModel->setUrlPages($this->urlPageModel);
        $this->assertSame($this->dealerModel, $result);

        $result = $this->dealerModel->setWebSites($this->webSiteModel);
        $this->assertSame($this->dealerModel, $result);

        $result = $this->dealerModel->setBrand('TestBrand');
        $this->assertSame($this->dealerModel, $result);

        $result = $this->dealerModel->setCaracRdvi('Test');
        $this->assertSame($this->dealerModel, $result);

        $result = $this->dealerModel->setFtcCodeList([$this->ftcCodeModel]);
        $this->assertSame($this->dealerModel, $result);
    }
}
