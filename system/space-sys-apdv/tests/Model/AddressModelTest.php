<?php

namespace App\Tests\Model;

use App\Model\AddressModel;
use PHPUnit\Framework\TestCase;

class AddressModelTest extends TestCase
{
    private AddressModel $addressModel;

    protected function setUp(): void
    {
        $this->addressModel = new AddressModel();
    }

    public function testGetSetCity(): void
    {
        $city = 'Paris';
        $this->addressModel->setCity($city);
        $this->assertEquals($city, $this->addressModel->getCity());
    }

    public function testGetSetCountry(): void
    {
        $country = 'France';
        $this->addressModel->setCountry($country);
        $this->assertEquals($country, $this->addressModel->getCountry());
    }

    public function testGetSetLine1(): void
    {
        $line1 = '123 Main Street';
        $this->addressModel->setLine1($line1);
        $this->assertEquals($line1, $this->addressModel->getLine1());
    }

    public function testGetSetLine2(): void
    {
        $line2 = 'Apartment 4B';
        $this->addressModel->setLine2($line2);
        $this->assertEquals($line2, $this->addressModel->getLine2());
    }

    public function testGetSetLine3(): void
    {
        $line3 = 'Building C';
        $this->addressModel->setLine3($line3);
        $this->assertEquals($line3, $this->addressModel->getLine3());
    }

    public function testGetSetRegion(): void
    {
        $region = 'Île-de-France';
        $this->addressModel->setRegion($region);
        $this->assertEquals($region, $this->addressModel->getRegion());
    }

    public function testGetSetZipCode(): void
    {
        $zipCode = '75001';
        $this->addressModel->setZipCode($zipCode);
        $this->assertEquals($zipCode, $this->addressModel->getZipCode());
    }

    public function testFluentInterface(): void
    {
        $result = $this->addressModel->setCity('Paris');
        $this->assertSame($this->addressModel, $result);

        $result = $this->addressModel->setCountry('France');
        $this->assertSame($this->addressModel, $result);

        $result = $this->addressModel->setLine1('123 Main Street');
        $this->assertSame($this->addressModel, $result);

        $result = $this->addressModel->setLine2('Apartment 4B');
        $this->assertSame($this->addressModel, $result);

        $result = $this->addressModel->setLine3('Building C');
        $this->assertSame($this->addressModel, $result);

        $result = $this->addressModel->setRegion('Île-de-France');
        $this->assertSame($this->addressModel, $result);

        $result = $this->addressModel->setZipCode('75001');
        $this->assertSame($this->addressModel, $result);
    }
}
