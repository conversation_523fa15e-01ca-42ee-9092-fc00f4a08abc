<?php

namespace App\Tests\Model;

use App\Model\CoordinateModel;
use PHPUnit\Framework\TestCase;

class CoordinateModelTest extends TestCase
{
    private CoordinateModel $coordinateModel;

    protected function setUp(): void
    {
        $this->coordinateModel = new CoordinateModel();
    }

    public function testGetSetLatitude(): void
    {
        $latitude = 48.8566;
        $this->coordinateModel->setLatitude($latitude);
        $this->assertEquals($latitude, $this->coordinateModel->getLatitude());
    }

    public function testGetSetLongitude(): void
    {
        $longitude = 2.3522;
        $this->coordinateModel->setLongitude($longitude);
        $this->assertEquals($longitude, $this->coordinateModel->getLongitude());
    }

    public function testFluentInterface(): void
    {
        $result = $this->coordinateModel->setLatitude(48.8566);
        $this->assertSame($this->coordinateModel, $result);

        $result = $this->coordinateModel->setLongitude(2.3522);
        $this->assertSame($this->coordinateModel, $result);
    }
}
