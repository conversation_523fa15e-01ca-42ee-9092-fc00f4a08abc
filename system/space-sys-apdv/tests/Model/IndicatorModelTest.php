<?php

namespace App\Tests\Model;

use App\Model\IndicatorModel;
use PHPUnit\Framework\TestCase;

class IndicatorModelTest extends TestCase
{
    private IndicatorModel $indicatorModel;

    protected function setUp(): void
    {
        $this->indicatorModel = new IndicatorModel();
    }

    public function testGetSetCode(): void
    {
        $code = 'IND001';
        $this->indicatorModel->setCode($code);
        $this->assertEquals($code, $this->indicatorModel->getCode());
    }

    public function testGetSetLabel(): void
    {
        $label = 'Indicator Label';
        $this->indicatorModel->setLabel($label);
        $this->assertEquals($label, $this->indicatorModel->getLabel());
    }

    public function testFluentInterface(): void
    {
        $result = $this->indicatorModel->setCode('IND001');
        $this->assertSame($this->indicatorModel, $result);

        $result = $this->indicatorModel->setLabel('Indicator Label');
        $this->assertSame($this->indicatorModel, $result);
    }
}
