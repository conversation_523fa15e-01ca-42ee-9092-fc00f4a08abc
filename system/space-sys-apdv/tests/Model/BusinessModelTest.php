<?php

namespace App\Tests\Model;

use App\Model\BusinessModel;
use PHPUnit\Framework\TestCase;

class BusinessModelTest extends TestCase
{
    private BusinessModel $businessModel;

    protected function setUp(): void
    {
        $this->businessModel = new BusinessModel();
    }

    public function testGetSetCode(): void
    {
        $code = 'BUS001';
        $this->businessModel->setCode($code);
        $this->assertEquals($code, $this->businessModel->getCode());
    }

    public function testGetSetLabel(): void
    {
        $label = 'Business Label';
        $this->businessModel->setLabel($label);
        $this->assertEquals($label, $this->businessModel->getLabel());
    }

    public function testGetSetType(): void
    {
        $type = 'Business Type';
        $this->businessModel->setType($type);
        $this->assertEquals($type, $this->businessModel->getType());
    }

    public function testFluentInterface(): void
    {
        $result = $this->businessModel->setCode('BUS001');
        $this->assertSame($this->businessModel, $result);

        $result = $this->businessModel->setLabel('Business Label');
        $this->assertSame($this->businessModel, $result);

        $result = $this->businessModel->setType('Business Type');
        $this->assertSame($this->businessModel, $result);
    }
}
