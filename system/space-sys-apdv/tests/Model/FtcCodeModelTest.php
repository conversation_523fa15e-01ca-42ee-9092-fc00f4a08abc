<?php

namespace App\Tests\Model;

use App\Model\FtcCodeModel;
use PHPUnit\Framework\TestCase;

class FtcCodeModelTest extends TestCase
{
    private FtcCodeModel $ftcCodeModel;

    protected function setUp(): void
    {
        $this->ftcCodeModel = new FtcCodeModel();
    }

    public function testGetSetCodeActeur(): void
    {
        $code = 'CA001';
        $this->ftcCodeModel->setCodeActeur($code);
        $this->assertEquals($code, $this->ftcCodeModel->getCodeActeur());
    }

    public function testGetSetCodeActeurPrincipal(): void
    {
        $code = 'CAP001';
        $this->ftcCodeModel->setCodeActeurPrincipal($code);
        $this->assertEquals($code, $this->ftcCodeModel->getCodeActeurPrincipal());
    }

    public function testGetSetCodeNature(): void
    {
        $code = 'CN001';
        $this->ftcCodeModel->setCodeNature($code);
        $this->assertEquals($code, $this->ftcCodeModel->getCodeNature());
    }

    public function testGetSetDealerCode(): void
    {
        $code = 'DC001';
        $this->ftcCodeModel->setDealerCode($code);
        $this->assertEquals($code, $this->ftcCodeModel->getDealerCode());
    }

    public function testGetSetIdSite(): void
    {
        $id = 'SITE001';
        $this->ftcCodeModel->setIdSite($id);
        $this->assertEquals($id, $this->ftcCodeModel->getIdSite());
    }

    public function testGetSetMainDealerCode(): void
    {
        $code = 'MDC001';
        $this->ftcCodeModel->setMainDealerCode($code);
        $this->assertEquals($code, $this->ftcCodeModel->getMainDealerCode());
    }

    public function testGetSetMarket(): void
    {
        $market = 'FR';
        $this->ftcCodeModel->setMarket($market);
        $this->assertEquals($market, $this->ftcCodeModel->getMarket());
    }

    public function testGetSetOic(): void
    {
        $oic = 'OIC001';
        $this->ftcCodeModel->setOic($oic);
        $this->assertEquals($oic, $this->ftcCodeModel->getOic());
    }

    public function testGetSetOutlet(): void
    {
        $outlet = 'OUT001';
        $this->ftcCodeModel->setOutlet($outlet);
        $this->assertEquals($outlet, $this->ftcCodeModel->getOutlet());
    }

    public function testFluentInterface(): void
    {
        $result = $this->ftcCodeModel->setCodeActeur('CA001');
        $this->assertSame($this->ftcCodeModel, $result);

        $result = $this->ftcCodeModel->setCodeActeurPrincipal('CAP001');
        $this->assertSame($this->ftcCodeModel, $result);

        $result = $this->ftcCodeModel->setCodeNature('CN001');
        $this->assertSame($this->ftcCodeModel, $result);

        $result = $this->ftcCodeModel->setDealerCode('DC001');
        $this->assertSame($this->ftcCodeModel, $result);

        $result = $this->ftcCodeModel->setIdSite('SITE001');
        $this->assertSame($this->ftcCodeModel, $result);

        $result = $this->ftcCodeModel->setMainDealerCode('MDC001');
        $this->assertSame($this->ftcCodeModel, $result);

        $result = $this->ftcCodeModel->setMarket('FR');
        $this->assertSame($this->ftcCodeModel, $result);

        $result = $this->ftcCodeModel->setOic('OIC001');
        $this->assertSame($this->ftcCodeModel, $result);

        $result = $this->ftcCodeModel->setOutlet('OUT001');
        $this->assertSame($this->ftcCodeModel, $result);
    }
}
