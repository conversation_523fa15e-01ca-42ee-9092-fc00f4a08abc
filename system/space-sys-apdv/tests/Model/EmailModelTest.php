<?php

namespace App\Tests\Model;

use App\Model\EmailModel;
use PHPUnit\Framework\TestCase;

class EmailModelTest extends TestCase
{
    private EmailModel $emailModel;

    protected function setUp(): void
    {
        $this->emailModel = new EmailModel();
    }

    public function testGetSetEmail(): void
    {
        $email = '<EMAIL>';
        $this->emailModel->setEmail($email);
        $this->assertEquals($email, $this->emailModel->getEmail());
    }

    public function testGetSetEmailApv(): void
    {
        $email = '<EMAIL>';
        $this->emailModel->setEmailApv($email);
        $this->assertEquals($email, $this->emailModel->getEmailApv());
    }

    public function testGetSetEmailAgent(): void
    {
        $email = '<EMAIL>';
        $this->emailModel->setEmailAgent($email);
        $this->assertEquals($email, $this->emailModel->getEmailAgent());
    }

    public function testGetSetEmailGer(): void
    {
        $email = '<EMAIL>';
        $this->emailModel->setEmailGer($email);
        $this->assertEquals($email, $this->emailModel->getEmailGer());
    }

    public function testGetSetEmailGrc(): void
    {
        $email = '<EMAIL>';
        $this->emailModel->setEmailGrc($email);
        $this->assertEquals($email, $this->emailModel->getEmailGrc());
    }

    public function testGetSetEmailPr(): void
    {
        $email = '<EMAIL>';
        $this->emailModel->setEmailPr($email);
        $this->assertEquals($email, $this->emailModel->getEmailPr());
    }

    public function testGetSetEmailSales(): void
    {
        $email = '<EMAIL>';
        $this->emailModel->setEmailSales($email);
        $this->assertEquals($email, $this->emailModel->getEmailSales());
    }

    public function testGetSetEmailVo(): void
    {
        $email = '<EMAIL>';
        $this->emailModel->setEmailVo($email);
        $this->assertEquals($email, $this->emailModel->getEmailVo());
    }

    public function testFluentInterface(): void
    {
        $result = $this->emailModel->setEmail('<EMAIL>');
        $this->assertSame($this->emailModel, $result);

        $result = $this->emailModel->setEmailApv('<EMAIL>');
        $this->assertSame($this->emailModel, $result);

        $result = $this->emailModel->setEmailAgent('<EMAIL>');
        $this->assertSame($this->emailModel, $result);

        $result = $this->emailModel->setEmailGer('<EMAIL>');
        $this->assertSame($this->emailModel, $result);

        $result = $this->emailModel->setEmailGrc('<EMAIL>');
        $this->assertSame($this->emailModel, $result);

        $result = $this->emailModel->setEmailPr('<EMAIL>');
        $this->assertSame($this->emailModel, $result);

        $result = $this->emailModel->setEmailSales('<EMAIL>');
        $this->assertSame($this->emailModel, $result);

        $result = $this->emailModel->setEmailVo('<EMAIL>');
        $this->assertSame($this->emailModel, $result);
    }
}
