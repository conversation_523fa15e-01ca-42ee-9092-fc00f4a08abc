<?php

namespace App\Tests\Model;

use App\Model\PhoneModel;
use PHPUnit\Framework\TestCase;

class PhoneModelTest extends TestCase
{
    private PhoneModel $phoneModel;

    protected function setUp(): void
    {
        $this->phoneModel = new PhoneModel();
    }

    public function testGetSetPhoneApv(): void
    {
        $phone = '+33123456789';
        $this->phoneModel->setPhoneApv($phone);
        $this->assertEquals($phone, $this->phoneModel->getPhoneApv());
    }

    public function testGetSetPhoneNumber(): void
    {
        $phone = '+33123456790';
        $this->phoneModel->setPhoneNumber($phone);
        $this->assertEquals($phone, $this->phoneModel->getPhoneNumber());
    }

    public function testGetSetPhonePr(): void
    {
        $phone = '+33123456791';
        $this->phoneModel->setPhonePr($phone);
        $this->assertEquals($phone, $this->phoneModel->getPhonePr());
    }

    public function testGetSetPhoneVn(): void
    {
        $phone = '+33123456792';
        $this->phoneModel->setPhoneVn($phone);
        $this->assertEquals($phone, $this->phoneModel->getPhoneVn());
    }

    public function testGetSetPhoneVo(): void
    {
        $phone = '+33123456793';
        $this->phoneModel->setPhoneVo($phone);
        $this->assertEquals($phone, $this->phoneModel->getPhoneVo());
    }

    public function testFluentInterface(): void
    {
        $result = $this->phoneModel->setPhoneApv('+33123456789');
        $this->assertSame($this->phoneModel, $result);

        $result = $this->phoneModel->setPhoneNumber('+33123456790');
        $this->assertSame($this->phoneModel, $result);

        $result = $this->phoneModel->setPhonePr('+33123456791');
        $this->assertSame($this->phoneModel, $result);

        $result = $this->phoneModel->setPhoneVn('+33123456792');
        $this->assertSame($this->phoneModel, $result);

        $result = $this->phoneModel->setPhoneVo('+33123456793');
        $this->assertSame($this->phoneModel, $result);
    }
}
