<?php

namespace App\Tests\Model;

use App\Model\UrlPageModel;
use PHPUnit\Framework\TestCase;

class UrlPageModelTest extends TestCase
{
    private UrlPageModel $urlPageModel;

    protected function setUp(): void
    {
        $this->urlPageModel = new UrlPageModel();
    }

    public function testGetSetUrlApvForm(): void
    {
        $url = 'https://example.com/apvform';
        $this->urlPageModel->setUrlApvForm($url);
        $this->assertEquals($url, $this->urlPageModel->getUrlApvForm());
    }

    public function testGetSetUrlContact(): void
    {
        $url = 'https://example.com/contact';
        $this->urlPageModel->setUrlContact($url);
        $this->assertEquals($url, $this->urlPageModel->getUrlContact());
    }

    public function testGetSetUrlNewCarStock(): void
    {
        $url = 'https://example.com/newcarstock';
        $this->urlPageModel->setUrlNewCarStock($url);
        $this->assertEquals($url, $this->urlPageModel->getUrlNewCarStock());
    }

    public function testGetSetUrlUsedCarStock(): void
    {
        $url = 'https://example.com/usedcarstock';
        $this->urlPageModel->setUrlUsedCarStock($url);
        $this->assertEquals($url, $this->urlPageModel->getUrlUsedCarStock());
    }

    public function testGetSetUrlUsefullInformation(): void
    {
        $url = 'https://example.com/usefullinformation';
        $this->urlPageModel->setUrlUsefullInformation($url);
        $this->assertEquals($url, $this->urlPageModel->getUrlUsefullInformation());
    }

    public function testFluentInterface(): void
    {
        $result = $this->urlPageModel->setUrlApvForm('https://example.com/apvform');
        $this->assertSame($this->urlPageModel, $result);

        $result = $this->urlPageModel->setUrlContact('https://example.com/contact');
        $this->assertSame($this->urlPageModel, $result);

        $result = $this->urlPageModel->setUrlNewCarStock('https://example.com/newcarstock');
        $this->assertSame($this->urlPageModel, $result);

        $result = $this->urlPageModel->setUrlUsedCarStock('https://example.com/usedcarstock');
        $this->assertSame($this->urlPageModel, $result);

        $result = $this->urlPageModel->setUrlUsefullInformation('https://example.com/usefullinformation');
        $this->assertSame($this->urlPageModel, $result);
    }
}
