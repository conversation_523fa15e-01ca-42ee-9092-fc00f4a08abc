{"type": "project", "license": "proprietary", "minimum-stability": "stable", "prefer-stable": true, "require": {"php": ">=8.1", "ext-ctype": "*", "ext-iconv": "*", "aws/aws-sdk-php": "^3.342", "doctrine/dbal": "^3", "doctrine/doctrine-bundle": "^2.14", "doctrine/doctrine-migrations-bundle": "^3.4", "doctrine/mongodb-odm-bundle": "^5.3", "doctrine/orm": "^3.3", "friendsofsymfony/rest-bundle": "^3.8", "jms/serializer-bundle": "^5.5", "mongodb/mongodb": "^2.0", "nelmio/api-doc-bundle": "^4.11", "phpdocumentor/reflection-docblock": "^5.6", "phpstan/phpdoc-parser": "^2.1", "symfony/asset": "6.4.*", "symfony/asset-mapper": "6.4.*", "symfony/cache": "6.4.*", "symfony/cache-contracts": "^3.5", "symfony/console": "6.4.*", "symfony/doctrine-messenger": "6.4.*", "symfony/dotenv": "6.4.*", "symfony/expression-language": "6.4.*", "symfony/flex": "^2", "symfony/form": "6.4.*", "symfony/framework-bundle": "6.4.*", "symfony/http-client": "6.4.*", "symfony/intl": "6.4.*", "symfony/mailer": "6.4.*", "symfony/mime": "6.4.*", "symfony/monolog-bundle": "^3.0", "symfony/notifier": "6.4.*", "symfony/process": "6.4.*", "symfony/property-access": "6.4.*", "symfony/property-info": "6.4.*", "symfony/runtime": "6.4.*", "symfony/security-bundle": "6.4.*", "symfony/serializer": "6.4.*", "symfony/stimulus-bundle": "^2.24", "symfony/string": "6.4.*", "symfony/translation": "6.4.*", "symfony/twig-bundle": "6.4.*", "symfony/ux-turbo": "^2.24", "symfony/validator": "6.4.*", "symfony/web-link": "6.4.*", "symfony/yaml": "6.4.*", "twig/extra-bundle": "^2.12|^3.0", "twig/twig": "^2.12|^3.0"}, "config": {"allow-plugins": {"php-http/discovery": true, "symfony/flex": true, "symfony/runtime": true}, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php73": "*", "symfony/polyfill-php74": "*", "symfony/polyfill-php80": "*", "symfony/polyfill-php81": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd", "importmap:install": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "6.4.*"}}, "require-dev": {"phpunit/phpunit": "^9.5", "symfony/browser-kit": "6.4.*", "symfony/css-selector": "6.4.*", "symfony/debug-bundle": "6.4.*", "symfony/maker-bundle": "^1.0", "symfony/phpunit-bridge": "^7.2", "symfony/stopwatch": "6.4.*", "symfony/web-profiler-bundle": "6.4.*"}}