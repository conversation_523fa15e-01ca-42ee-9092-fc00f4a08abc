{% extends 'base.html.twig' %}

{% block title %}List Documents{% endblock %}

{% block body %}
    <h1 class="mb-4">MongoDB Documents</h1>
    <table class="table table-striped">
        <thead>
            <tr>
                <th>ID</th>
                <th>Name</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for document in documents %}
                <tr>
                    <td>{{ document.id }}</td>
                    <td>{{ document.name }}</td>
                    <td>
                        <a href="{{ path('app_document_edit', {id: document.id}) }}" class="btn btn-sm btn-warning">Edit</a>
                        <a href="{{ path('app_document_delete', {id: document.id}) }}" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure?')">Delete</a>
                    </td>
                </tr>
            {% endfor %}
        </tbody>
    </table>
{% endblock %}