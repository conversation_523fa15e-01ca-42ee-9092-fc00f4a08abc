<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}MongoDB CRUD (Symfony 6.4){% endblock %}</title>
    {# Bootstrap 5 CSS #}
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    {# Custom CSS (optional) #}
    <style>
        body { padding-top: 20px; }
        .flash-messages { margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="container">
        {# Navigation Bar #}
        <nav class="navbar navbar-expand-lg navbar-dark bg-dark mb-4 rounded">
            <div class="container-fluid">
                <a class="navbar-brand" href="{{ path('app_home') }}">MongoDB CRUD</a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ path('app_document_list') }}">List Documents</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ path('app_document_new') }}">Create New</a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>

        {# Flash Messages (success/error alerts) #}
        <div class="flash-messages">
            {% for message in app.flashes('success') %}
                <div class="alert alert-success">{{ message }}</div>
            {% endfor %}
            {% for message in app.flashes('error') %}
                <div class="alert alert-danger">{{ message }}</div>
            {% endfor %}
        </div>

        {# Main Content Block (for CRUD pages) #}
        <div class="content">
            {% block body %}{% endblock %}
        </div>
    </div>

    {# Bootstrap 5 JS + Popper #}
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    {# Custom JS (optional) #}
    {% block javascripts %}{% endblock %}
</body>
</html>