app.swagger_ui:
    path: /
    methods: GET
    defaults: { _controller: nelmio_api_doc.controller.swagger_ui }

app.swagger_ui_doc:
    path: /doc
    methods: GET
    defaults: { _controller: nelmio_api_doc.controller.swagger_ui }

app.swagger:
    path: /doc.json
    methods: GET
    defaults: { _controller: nelmio_api_doc.controller.swagger }

# Expose your documentation as JSON swagger compliant
# app.swagger:
#     path: /api/doc.json
#     methods: GET
#     defaults: { _controller: nelmio_api_doc.controller.swagger }
 
## Requires the Asset component and the Twig bundle
## $ composer require twig asset
#app.swagger_ui:
#    path: /api/doc
#    methods: GET
#    defaults: { _controller: nelmio_api_doc.controller.swagger_ui }