# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices.html#use-parameters-for-application-configuration
imports:
    - { resource: logincident_parameters.yaml }
parameters:

services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.

    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    App\:
        resource: '../src/'
        exclude:
            - '../src/DependencyInjection/'
            - '../src/Entity/'
            - '../src/Kernel.php'
    App\Manager\LogIncidentManager:
        arguments:
            $cacheDuration: "%logincident.cache_duration%"
            $requestTtl: "%logincident.request_ttl%"
    Aws\Credentials\Credentials:
        arguments:
            - "@=parameter('account')['access_key']"
            - "@=parameter('account')['secret_key']"

    App\Service\AwsSdkService:
        arguments:
            - "%account%"
            - '@Aws\Credentials\Credentials'

    App\Validator\ContentBase64Validator:
        arguments:
            $maxFileSize: "%logincident.file_max_size_ko%"
            
    App\Service\FileService:
        arguments:
            $mediaBucket: "%env(LOG_INCIDENT_S3_ID)%"
            $mediaUrl: "%env(LOG_INCIDENT_BASE_URL)%"
            $folder: "%logincident.folder%"
    # add more service definitions when explicit configuration is needed
    # please note that last definitions always *replace* previous ones
