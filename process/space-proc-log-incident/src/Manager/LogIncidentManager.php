<?php

namespace App\Manager;

use App\Document\File;
use App\Document\LogIncident;
use App\Helpers\FormErrorResponseTrait;
use App\Helpers\LoggerTrait;
use App\Helpers\ResponseTrait;
use App\Model\LogIncidentPaginator;
use App\Service\FileService;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\Cache\TagAwareCacheInterface;
use App\Service\LogIncidentService;
use Symfony\Contracts\Cache\ItemInterface;


class LogIncidentManager
{
    use ResponseTrait;
    use FormErrorResponseTrait;
    use LoggerTrait;

    public const ACTIF = 0;
    public const TO_DELETE = 1;

    public const CACHE_GLOBAL_PREFIX = 'log_incidents_all';
    public const CACHE_DETAILS_PREFIX = 'log_incident_details';

    public function __construct(
        private LogIncidentService $service,
        private FileService $fileService,
        private TagAwareCacheInterface $cache,
        private int $cacheDuration,
        private int $requestTtl
    ) {}

    public function manageFormData(FormInterface $form, LogIncident $logIncident): array
    {
        if ($form->isValid()) {
            $logIncident = $this->service->createLogIncident($logIncident);
            $this->cache->invalidateTags([self::CACHE_GLOBAL_PREFIX]);

            return $this->buildSuccessResponse($logIncident, Response::HTTP_CREATED);
        }

        return $this->buildErrorResponse(
            'validation_errors',
            Response::HTTP_BAD_REQUEST,
            $this->generateResponseErrors($form)
        );
    }

    public function checkLogIncidentExist(
        string $email,
        ?string $vin,
        ?string $id_client,
        string $title,
        string $comment
    ): array|false {
        $now = new \DateTimeImmutable();
        $startDate = $now->sub(new \DateInterval('PT' . $this->requestTtl . 'S'));
        $endDate = $now;

        $logIncidents = $this->service->checkLogIncidentExist(
            $email,
            $vin,
            $id_client,
            $title,
            $comment,
            $startDate,
            $endDate
        );

        if (!empty($logIncidents)) {
            return $this->buildSuccessResponse($logIncidents[0], Response::HTTP_CREATED);
        }

        return false;
    }


    public function getLogIncidentsWithPaginatorParams(
        string $brand,
        string $country,
        LogIncidentPaginator $paginatorParameters,
        int $status = self::ACTIF
    ): array {
        $logIncidents = $this->service->getLogIncidentsWithPaginatorParams($brand, $country, $status, $paginatorParameters);
        $totalCount = $this->service->getTotalLogIncidentsWithPaginatorParams($brand, $country, $status, $paginatorParameters);

        $data = [
            'data' => $logIncidents,
            'total' => $totalCount
        ];

        return $this->buildSuccessResponse($data, Response::HTTP_OK);
    }

    /**
     * Get log incident and linked media files from S3 by ID, brand, and country.
     */
    public function findLogIncidentWithMediaDetailsByBrandAndCountry(
        string $id,
        ?string $brand = null,
        ?string $country = null
    ): array {
        $cacheKey = self::CACHE_DETAILS_PREFIX . "_{$id}_{$brand}_{$country}";

        $logIncident = $this->cache->get($cacheKey, function (ItemInterface $item) use ($id, $brand, $country) {
            $logIncident = $this->service->findById($id);

            $item->tag([self::CACHE_DETAILS_PREFIX . "_{$id}"]);
            $item->expiresAfter(0);

            if (!$logIncident) {
                return null;
            }

            $isUnrestrictedBrand = in_array($brand, ['CT', 'XX']);
            $matchesBrandCountry = strpos($logIncident->getSiteCode(), "{$brand}_{$country}") === 0;

            if ($isUnrestrictedBrand || $matchesBrandCountry) {
                $item->expiresAfter($this->cacheDuration);
                $logIncident->setFiles($this->fileService->getFiles($id));
                return $logIncident;
            }

            return null;
        });

        if (!$logIncident) {
            return $this->buildErrorResponse("Log Incident not found", Response::HTTP_NOT_FOUND);
        }

        return $this->buildSuccessResponse($logIncident, Response::HTTP_OK);
    }

    public function upload(File $fileToUpload, FormInterface $form, LogIncident $logIncident): array
    {
        // dd($fileToUpload, $form->isValid(), $logIncident);
        if (!$form->isValid()) {
            return $this->buildErrorResponse(
                'validation_errors',
                Response::HTTP_BAD_REQUEST,
                $this->generateResponseErrors($form)
            );
        }

        $id = $logIncident->getId();
        $uploadResult = $this->fileService->uploadFile(
            $fileToUpload->getContentBase64(),
            $fileToUpload->getName(),
            $id
        );

        if ($uploadResult === true) {
            $this->cache->invalidateTags([self::CACHE_DETAILS_PREFIX . "_{$id}"]);
            $logIncident->setHasLogFile(true);
            $this->service->updateLogIncident($logIncident);

            return $this->buildSuccessResponse(['message' => 'Successfully Uploaded'], Response::HTTP_OK);
        }

        return $this->buildErrorResponse($uploadResult, Response::HTTP_BAD_REQUEST);
    }

    public function findLogIncidentById(string $id): array
    {
        $logIncident = $this->service->findById($id);

        if (!$logIncident instanceof LogIncident) {
            return $this->buildErrorResponse('Log Incident not found', Response::HTTP_NOT_FOUND);
        }
        return $this->buildSuccessResponse($logIncident, Response::HTTP_OK);
    }

    public function findLogIncidentByIdAndBrandAndCountry(string $id, string $brand, string $country): array
    {
        $logIncident = $this->service->findById($id);

        if (!$logIncident instanceof LogIncident) {
            return $this->buildErrorResponse('Log Incident not found', Response::HTTP_NOT_FOUND);
        }

        $siteCode = $logIncident->getSiteCode();
        $matchesSiteCode = strpos($siteCode, "{$brand}_{$country}") === 0;

        if (in_array($brand, ['CT', 'XX']) || $matchesSiteCode) {
            return $this->buildSuccessResponse($logIncident, Response::HTTP_OK);
        }

        return $this->buildErrorResponse('Log Incident not found', Response::HTTP_NOT_FOUND);
    }

    public function managePut(FormInterface $form, LogIncident $logIncident): array
    {
        if (!$form->isValid()) {
            return $this->buildErrorResponse(
                'validation_errors',
                Response::HTTP_BAD_REQUEST,
                $this->generateResponseErrors($form)
            );
        }

        $this->service->updateLogIncident($logIncident);

        $this->cache->invalidateTags([
            self::CACHE_GLOBAL_PREFIX,
            self::CACHE_DETAILS_PREFIX . '_' . $logIncident->getId()
        ]);

        return $this->buildSuccessResponse(['message' => 'Successfully Updated'], Response::HTTP_OK);
    }
}
