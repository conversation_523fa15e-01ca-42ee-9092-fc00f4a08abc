<?php

namespace App\Service;

use App\Document\LogIncident;
use App\Helpers\SiteCodeHelper;
use App\Message\LogIncidentMessage;
use App\Model\LogIncidentPaginator;
use App\Model\UserLogIncident;
use Doctrine\ODM\MongoDB\DocumentManager;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Exception;

class LogIncidentService
{
    private const SUPPORTED_BRANDS_PUSH = ['LC'];

    public function __construct(
        private DocumentManager $documentManager,
        private MessageBusInterface $bus,
        private LoggerInterface $logger,
    ) {
    }

    public function createLogIncident(LogIncident $logIncident): LogIncident
    {
        $this->documentManager->persist($logIncident);
        $this->documentManager->flush();
        
        [$brand, $country] = SiteCodeHelper::extractData($logIncident->getSiteCode());
        $message = new LogIncidentMessage($logIncident->getId(), $brand, $country);

        if (in_array($brand, self::SUPPORTED_BRANDS_PUSH)) {
            $this->pushMessageToSQS($message);
        }
        
        return $logIncident;
    }
    
    public function checkLogIncidentExist(
        string $email,
        ?string $vin,
        ?string $id_client,
        string $title,
        string $comment,
        ?\DateTimeInterface $start_date,
        ?\DateTimeInterface $end_date
    ): array {
        return $this->documentManager->getRepository(LogIncident::class)->checkLogIncidentExist($email, $vin, $id_client, $title, $comment, $start_date, $end_date);
    }
    
    public function getUserLogIncidents(UserLogIncident $userLogIncident): array
    {
        return $this->documentManager->getRepository(LogIncident::class)->getUserLogIncidents($userLogIncident->getVin(), $userLogIncident->getAccountId());
    }

    private function pushMessageToSQS(LogIncidentMessage $message): void
    {
        try {
            $this->bus->dispatch($message);
        } catch (Exception $e) {
            $this->logger->error('Failed to push message to SQS', [
                'error' => $e->getMessage(),
                'incidentId' => $message->getId()
            ]);
        }
    }

    public function getLogIncidentsWithPaginatorParams($brand, $country, $status, LogIncidentPaginator $logIncidentPaginator)
    {
        $logIncidents = $this->documentManager->getRepository(LogIncident::class)->getLogIncidentsForPagination($brand, $country, $status, $logIncidentPaginator);
        return $logIncidents;
    }

    public function getTotalLogIncidentsWithPaginatorParams($brand, $country, $status, LogIncidentPaginator $logIncidentPaginator)
    {
        try {
            $count = $this->documentManager->getRepository(LogIncident::class)->getTotalCountLogIncidentsForPagination($brand, $country, $status, $logIncidentPaginator);
            return $count;
        } catch (Exception $e) {
            return 0;
        }
    }

    public function findById(string $id)
    {
        $logIncident = $this->documentManager->getRepository(LogIncident::class)->find($id);
        return $logIncident;
    }

    public function updateLogIncident(LogIncident $logIncident): LogIncident
    {
        $this->documentManager->flush();
        return $logIncident;
    }

}