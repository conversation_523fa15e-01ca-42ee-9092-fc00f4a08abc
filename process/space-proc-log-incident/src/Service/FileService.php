<?php

namespace App\Service;

use App\Helpers\LoggerTrait;
use Aws\S3\S3Client;
use Exception;

class FileService
{
    use LoggerTrait;
    /**
     * @var S3Client $_sdkClient
     */
    private $_sdkClient;
    /**
     * @var string
     */
    private $_mediaBucket;
    /**
     * @var string
     */
    private $_mediaUrl;
    /**
     * @var string
     */
    private $_folder;

    /**
     * @param AwsSdkService $awsSdkService
     * @param string $mediaBucket
     * @param string $mediaUrl
     */
    public function __construct(AwsSdkService $awsSdkService, string $mediaBucket, string $mediaUrl, string $folder)
    {
        $this->_sdkClient   = $awsSdkService->getS3Client();
        $this->_mediaBucket = $mediaBucket;
        $this->_mediaUrl    = $mediaUrl;
        $this->_folder      = $folder;
    }

    /**
     * Retrieve log incident files
     *
     * @param integer $id
     * @return array
     */
    public function getFiles(string $id): array
    {

        $this->_logger->info("Scanning log incident files id :" . $id);
        $files = [];
        // Iterator on bucket objects
        $objects = $this->_sdkClient->getIterator('ListObjects', array(
            "Bucket" => $this->_mediaBucket,
            "Prefix" => $this->_folder . '/' . $id
        ));
        foreach ($objects as $object) {
            $files[] = $this->_mediaUrl . '/' . $object['Key'];
        }
        return $files;
    }

    /**
     * Upload a file to s3 bucket
     *
     * @param string $base64_code
     * @param string $fileName
     * @param int $id
     * @return void
     */
    public function uploadFile(string $base64_code, string $fileName, string $id)
    {
        try {
            // Get the extension of the file
            $ext = pathinfo($fileName, PATHINFO_EXTENSION);
            // replace les espaces par des "_"
            $fileName = str_replace(' ', '_', $fileName);
            $fileName = $this->_folder . '/' . $id . '/' . $fileName;
            if ($ext) {
                $ext = '.' . $ext;
            }

            $currentHour = date('H');
            $currentMinute = date('i');
          
            $newFileName = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $currentHour . $currentMinute .  $ext;


            $newFileName = pathinfo($fileName, PATHINFO_DIRNAME) . '/' . $newFileName;

      
            /**
             * If file name already exists, generate a new file name suffix from random numbers
             */
            while ($this->_sdkClient->doesObjectExist($this->_mediaBucket, $newFileName)) {
                $newFileName = str_replace($ext, '', $newFileName) . '_' . rand(0, 10000) . $ext;
            }
            $this->_logger->info("Uploading file {$newFileName}");
            $this->_sdkClient->putObject([
                'Bucket'      => $this->_mediaBucket,
                'Key'         => $newFileName,
                'Body'        => base64_decode($base64_code)
            ]);
            $this->_logger->info("{$newFileName} successfully uploaded");
            return true;
        } catch (Exception $e) {
            $this->_logger->error("Error while uploading {$newFileName}");
            $this->_logger->error("Exception: {$e->getMessage()}");
            return $e->getMessage();
        }
    }

    /**
     * Remove log incident files
     *
     * @param string $id
     * @return array
     */
    public function removeFiles($id): bool
    {
        $folder = $this->_folder . '/' . $id;
        try {
            $this->_logger->info("Removing folder {$folder}");
            $this->_sdkClient->deleteMatchingObjects($this->_mediaBucket, $folder);
            $this->_logger->info("{$folder} successfully removed");
            return true;
        } catch (Exception $e) {
            $this->_logger->error("Error while removing {$folder}");
            $this->_logger->error("Exception: {$e->getMessage()}");
            return false;
        }
    }
}
