<?php

namespace App\Service;

use Aws\S3\S3Client;
use Aws\Credentials\Credentials;
use Aws\Sdk;
use Aws\Sts\StsClient;


class AwsSdkService
{

    /**
     * @var S3Client
     */
    private $s3Client;

    /**
     * @var Credentials
     */
    private $credentials;

    /**
     * @var array
     */
    private $account;


    /**
     * AwsSdkService constructor.
     * @param array $account
     */
    public function __construct(array $account, Credentials $credentials)
    {
        $this->account = $account;
        $this->credentials = $credentials;
    }

    /**
     * @return S3Client
     */
    public function getS3Client()
    {
        if ($this->account['used_iam_instance_profile']) {
            $this->s3Client = new S3Client([
                'version' => $this->account['version'],
                'region' => $this->account['region']
            ]);

            return $this->s3Client;
        }

        $stsClient = new StsClient([
            'credentials' => $this->credentials,
            'region' => $this->account['region'],
            'version' => $this->account['version']
        ]);
        $assumeRole = $stsClient->AssumeRole([
            'RoleArn' => $this->account['role_arn'],
            'RoleSessionName' => 's3-access',
        ]);

        $sdk = new Sdk([
            'region' => $this->account['region'],
            'version' => $this->account['version']
        ]);

        $this->s3Client = $sdk->createS3([
            'region' => $this->account['region'],
            'version' => $this->account['version'],
            'credentials' => [
                'key' => $assumeRole['Credentials']['AccessKeyId'],
                'secret' => $assumeRole['Credentials']['SecretAccessKey'],
                'token' => $assumeRole['Credentials']['SessionToken']
            ]
        ]);

        return $this->s3Client;
    }

}
