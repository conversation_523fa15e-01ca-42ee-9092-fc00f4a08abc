<?php

namespace App\Document;

use App\Validator\ContentBase64Validator;
use App\Validator\FileName;
use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use Symfony\Component\Validator\Constraints as Assert;

#[MongoDB\Document]
class File
{
    #[MongoDB\Id]
    private ?string $id = null;

    #[MongoDB\Field(type: 'string')]
    #[Assert\NotBlank]
    #[ContentBase64Validator]
    private ?string $contentBase64 = null;

    #[MongoDB\Field(type: 'string')]
    #[Assert\NotBlank]
    // #[FileName]
    private ?string $name = null;

    public function getId(): ?string
    {
        return $this->id;
    }

    public function getContentBase64(): ?string
    {
        return $this->contentBase64;
    }

    public function setContentBase64(string $contentBase64): self
    {
        $this->contentBase64 = $contentBase64;
        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;
        return $this;
    }
}
