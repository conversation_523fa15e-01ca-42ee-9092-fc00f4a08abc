<?php

namespace App\Document;

use App\Repository\LogIncidentRepository;
use DateTime;
use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;
use Symfony\Component\Validator\Constraints as Assert;
use App\Validator\BrandAndCountry;

#[MongoDB\Document(collection: 'log_incident', repositoryClass: LogIncidentRepository::class)]
#[MongoDB\Index(keys: ['case_id' => 'asc'], name: 'caseid_index')]
#[BrandAndCountry]
#[MongoDB\HasLifecycleCallbacks]
class LogIncident
{
    public const TYPE_NON_CARE = 'non-care';
    public const TYPE_C1ST = 'c1st';

    #[MongoDB\Id]
    #[Serializer\Groups(["details", "list", "create"])]
    #[Serializer\SerializedName("incidentid")]
    private ?string $id = null;

    #[MongoDB\Field(type: 'string')]
    #[Assert\NotBlank]
    #[Assert\Email(message: "Email not valid")]
    #[Assert\Length(max: 100)]
    #[Serializer\Groups(["details", "list"])]
    private ?string $email = null;

    #[MongoDB\Field(type: 'string', nullable: true)]
    #[Assert\Length(max: 100)]
    #[Serializer\Groups(["details", "list"])]
    private ?string $vin = null;

    #[MongoDB\Field(type: 'string', nullable: true)]
    #[Assert\Length(max: 20)]
    #[Serializer\Groups(["details", "list"])]
    private ?string $idClient = null;

    #[MongoDB\Field(type: 'string')]
    #[Assert\NotBlank]
    #[Assert\Length(max: 100)]
    #[Serializer\Groups(["details", "list"])]
    private ?string $title = null;

    #[MongoDB\Field(type: 'string', nullable: true)]
    #[Assert\Length(max: 100)]
    #[Serializer\Groups(["details", "list"])]
    private ?string $brand = null;

    #[MongoDB\Field(type: 'string', nullable: true)]
    #[Assert\Length(max: 100)]
    #[Serializer\Groups(["details", "list"])]
    private ?string $country = null;

    #[MongoDB\Field(type: 'string')]
    #[Serializer\Groups(["details", "list"])]
    private ?string $comment = null;

    #[MongoDB\Field(type: 'bool')]
    #[Serializer\Groups(["details"])]
    private bool $mailSendStatus = false;

    #[MongoDB\Field(type: 'int')]
    #[Serializer\Groups(["details"])]
    private int $mailNbReminders = 0;

    #[MongoDB\Field(type: 'date')]
    #[Serializer\Groups(["details", "list"])]
    private ?DateTime $creationDate = null;

    #[MongoDB\Field(type: 'int')]
    #[Assert\Choice([0, 1], message: "Status should be 0 or 1")]
    #[Serializer\Groups(["details"])]
    private int $status = 0;

    #[MongoDB\Field(type: 'string')]
    #[Assert\Length(max: 12)]
    #[Serializer\Groups(["details", "list"])]
    private ?string $siteCode = null;

    #[MongoDB\Field(type: 'string', nullable: true)]
    #[Assert\Length(max: 5)]
    #[Serializer\Groups(["details", "list"])]
    private ?string $culture = null;

    #[MongoDB\Field(type: 'bool')]
    #[Serializer\Groups(["details"])]
    private bool $optin1 = false;

    #[MongoDB\Field(type: 'bool')]
    #[Serializer\Groups(["details"])]
    private bool $optin2 = false;

    #[Serializer\Groups(["details"])]
    private ?array $files = null;

    #[MongoDB\Field(type: 'string', nullable: true)]
    #[Assert\Length(max: 50)]
    #[Serializer\Groups(["details", "list"])]
    private ?string $type = self::TYPE_NON_CARE;

    #[MongoDB\Field(type: 'bool', nullable: true)]
    #[Serializer\Groups(["details", "List"])]
    private ?bool $hasLogFile = false;

    #[MongoDB\Field(type: 'string', nullable: true)]
    #[Assert\Length(max: 30)]
    #[Serializer\Groups(["details", "list"])]
    private ?string $caseId = null;

    public function __construct()
    {
        $this->creationDate = new DateTime();
        $this->type = self::TYPE_NON_CARE;
    }

    public function getId(): ?string
    {
        return $this->id;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(string $email): self
    {
        $this->email = $email;

        return $this;
    }

    public function getVin(): ?string
    {
        return $this->vin;
    }

    public function setVin(string $vin): self
    {
        $this->vin = $vin;

        return $this;
    }

    public function getBrand(): ?string
    {
        return $this->brand;
    }

    public function getCountry(): ?string
    {
        return $this->country;
    }

    public function setBrand(string $brand): ?string
    {
        return $this->brand = $brand;
    }

    public function setCountry(string $country): ?string
    {
        return $this->country = $country;
    }

    public function getIdClient(): ?string
    {
        return $this->idClient;
    }

    public function setIdClient(string $idClient): self
    {
        $this->idClient = $idClient;

        return $this;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(string $title): self
    {
        $this->title = $title;

        return $this;
    }

    public function getComment(): ?string
    {
        return $this->comment;
    }

    public function setComment(string $comment): self
    {
        $this->comment = $comment;

        return $this;
    }

    public function getMailSendStatus(): ?bool
    {
        return $this->mailSendStatus;
    }

    public function setMailSendStatus(bool $mailSendStatus): self
    {
        $this->mailSendStatus = $mailSendStatus;

        return $this;
    }

    public function getMailNbReminders(): ?int
    {
        return $this->mailNbReminders;
    }

    public function setMailNbReminders(int $mailNbReminders): self
    {
        $this->mailNbReminders = $mailNbReminders;

        return $this;
    }

    public function getCreationDate(): ?\DateTimeInterface
    {
        return $this->creationDate;
    }

    public function setCreationDate(\DateTimeInterface $creationDate): self
    {
        $this->creationDate = $creationDate;

        return $this;
    }

    public function getStatus(): ?int
    {
        return $this->status;
    }

    public function setStatus(int $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getSiteCode(): ?string
    {
        return $this->siteCode;
    }

    public function setSiteCode(string $siteCode): self
    {
        $this->siteCode = $siteCode;

        return $this;
    }

    public function getOptin1(): ?bool
    {
        return $this->optin1;
    }

    public function setOptin1(bool $optin1): self
    {
        $this->optin1 = $optin1;

        return $this;
    }

    public function getOptin2(): ?bool
    {
        return $this->optin2;
    }

    public function setOptin2(bool $optin2): self
    {
        $this->optin2 = $optin2;

        return $this;
    }

    public function getFiles()
    {
        return $this->files;
    }

    public function setFiles(array $files)
    {
        $this->files = $files;

        return $this;
    }

    public function getCulture(): ?string
    {
        return $this->culture;
    }

    public function setCulture(?string $culture): self
    {
        $this->culture = $culture;

        return $this;
    }

    public function getType()
    {
        return $this->type;
    }

    public function setType($type): void
    {
        $this->type = $type;
    }

    public function getHasLogFile()
    {
        return $this->hasLogFile;
    }

    public function setHasLogFile($hasLogFile): void
    {
        $this->hasLogFile = $hasLogFile;
    }

    public function getCaseId()
    {
        return $this->caseId;
    }

    public function setCaseId($caseId): void
    {
        $this->caseId = $caseId;
    }


    #[MongoDB\PrePersist]
    public function formatData(): void
    {
        if ($this->vin) {
            $this->vin = strtoupper($this->vin);
        }
        if ($this->siteCode) {
            $this->siteCode = strtoupper($this->siteCode);
        }
        if ($this->culture) {
            $this->culture = strtolower(substr($this->culture, 0, 2)) . '-' . strtoupper(substr($this->culture, 3));
        }
    }
    public function toArray(): array
    {
        return [
            'incidentid' => $this->id,
            'email' => $this->email,
            'vin' => $this->vin,
            'idClient' => $this->idClient,
            'title' => $this->title,
            'comment' => $this->comment,
            'brand' => $this->brand,
            'country' => $this->country,
            'siteCode' => $this->siteCode,
            'files'=>$this->files,
            'culture' => $this->culture,
            'optin1' => $this->optin1,
            'optin2' => $this->optin2,
            'status' => $this->status,
            'mailSendStatus' => $this->mailSendStatus,
            'mailNbReminders' => $this->mailNbReminders,
            'creationDate' => $this->creationDate?->format('Y-m-d H:i:s'),
        ];
    }
}
