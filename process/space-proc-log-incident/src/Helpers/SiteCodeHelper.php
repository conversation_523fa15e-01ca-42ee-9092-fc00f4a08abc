<?php
namespace App\Helpers;



/**
 * Helper used to extract data from site code
 */
class SiteCodeHelper
{
  /**
   * extract data from site code
   * @param string $siteCode
   * return Array
   */
  public static function extractData(string $siteCode = '')  {
  	try {
        $country = '';
        $brand = '' ;
        if ($siteCode) {
            $data = explode('_', $siteCode);
            if (count($data) === 3) {
                $country = strtoupper($data[1]);
                $brand = strtoupper($data[0]);
            }
        }
        return [$brand, $country];

	  	} catch (\Exception $e) {
            return ['', ''];
	  	}

  }
}