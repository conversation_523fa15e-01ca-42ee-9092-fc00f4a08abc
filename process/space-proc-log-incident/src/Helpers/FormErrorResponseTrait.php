<?php

namespace App\Helpers;

use Symfony\Component\Form\FormInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\ConstraintViolationListInterface;

/**
 * Form errors retriever
 */
trait FormErrorResponseTrait
{

    /**
     * Retrieve errors from FormInterface Object
     *
     * @param FormInterface $form
     * @return array
     */
    public function generateResponseErrors(FormInterface $form): array
    {
        $errors = array();

        // Global
        foreach ($form->getErrors() as $error) {
            $errors[$form->getName()][] = $error->getMessage();
        }

        // Fields
        foreach ($form as $child
            /** @var Form $child */
        ) {
            if (!$child->isValid()) {
                foreach ($child->getErrors() as $error) {
                    $errors[$child->getName()][] = $error->getMessage();
                }
            }
        }
        return $errors;
    }

    /**
     * @param  ConstraintViolationListInterface  $errors
     *
     * @return array
     */
    protected function getValidationMessages(ConstraintViolationListInterface $errors): array
    {
        $messages = [];

        if ($errors->count() === 0) {
            return $messages;
        }

        foreach ($errors as $error) {
            /** @var  ConstraintViolation  $error */
            $name            = str_replace(['[', ']'], '', $error->getPropertyPath());
            $messages[$name] = $error->getMessage();
        }

        return $messages;
    }
}
