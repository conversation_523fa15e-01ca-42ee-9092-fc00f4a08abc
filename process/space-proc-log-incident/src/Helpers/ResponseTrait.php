<?php

namespace App\Helpers;

use Symfony\Component\HttpFoundation\Response;

/**
 * Centralizer response manager
 */
trait ResponseTrait
{

    /**
     * Error response builder
     *
     * @param  $message
     * @param  $code
     * @param  $errors
     * @return array
     */
    public function buildErrorResponse($message, $code, $errors = null): array
    {
        $response = [
            'message' => $message ?? ''
        ];
        if ($errors) {
            $response['errors'] = $errors;
        }
        return [
            'code'    => $code == 0 ? Response::HTTP_BAD_REQUEST : $code,
            'content' => ["errors" => $response]
        ];
    }

    /**
     * Success Reponse builder
     *
     * @param $content
     * @param $code
     * @return array
     */
    public function buildSuccessResponse($content, $code): array
    {
        return [
            'content' => ["success" => $content],
            'code'    => $code
        ];
    }
}
