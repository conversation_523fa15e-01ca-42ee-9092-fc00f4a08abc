<?php

declare(strict_types=1);

namespace App\Repository;

use App\Document\LogIncident;
use App\Model\LogIncidentPaginator;
use Doctrine\ODM\MongoDB\Repository\DocumentRepository;

class LogIncidentRepository extends DocumentRepository
{
    public function getUserLogIncidents(string $vin, string $accountId): array
    {
        return $this->createQueryBuilder()
            ->field('vin')->equals($vin)
            ->field('idClient')->equals($accountId)
            ->getQuery()
            ->execute()
            ->toArray();
    }

    public function checkLogIncidentExist(
        string $email,
        ?string $vin,
        ?string $idClient,
        string $title,
        string $comment,
        ?\DateTimeInterface $startDate,
        ?\DateTimeInterface $endDate
    ): array {
        $qb = $this->createQueryBuilder()
            ->field('email')->equals($email)
            ->field('title')->equals($title)
            ->field('comment')->equals($comment);

        if ($vin !== null) {
            $qb->field('vin')->equals($vin);
        }

        if ($idClient !== null) {
            $qb->field('idClient')->equals($idClient);
        }

        if ($startDate !== null && $endDate !== null) {
            $qb->field('creationDate')->range($startDate, $endDate);
        }

        return $qb->getQuery()->execute()->toArray();
    }

    public function getLogIncidentsForPagination(
        string $brand,
        string $country,
        int $status,
        LogIncidentPaginator $paginator
    )  {
        $qb = $this->buildQueryWithFilters($brand, $country, $status, $paginator);

        if ($paginator->getOrderBy() && $paginator->getOrderDir()) {
            $qb->sort($paginator->getOrderBy(), $paginator->getOrderDir());
        }
        
        return $qb
            ->getQuery()
            ->execute()->toArray();
        
    }

    public function getTotalCountLogIncidentsForPagination(
        string $brand,
        string $country,
        int $status,
        LogIncidentPaginator $paginator
    ): int {
        $qb = $this->buildQueryWithFilters($brand, $country, $status, $paginator);

        return $qb->getQuery()->execute()->count();
    }

    private function buildQueryWithFilters(
        string $brand,
        string $country,
        int $status,
        LogIncidentPaginator $paginator
    ) {
        $qb = $this->createQueryBuilder();

        // Match full siteCode prefix (like DS_FR), assuming siteCode is fully known or pre-filtered
        // $expectedPrefix = $brand . '_' . $country;

        // You’ll need to pre-filter known siteCodes that start with this prefix
        $qb->field('brand')->equals($brand);
        $qb->field('country')->equals($country); 
        $qb->field('status')->equals(0);

        if ($paginator->getVin()) {
            $qb->field('vin')->equals($paginator->getVin());
        }

        if ($paginator->getSearch()) {
            // Optional: use contains/match logic across fields without regex (only exact matches possible)
            $qb->addOr([
                ['title' => $paginator->getSearch()],
                ['comment' => $paginator->getSearch()],
            ]);
        }

        if ($paginator->getStartDate() && $paginator->getEndDate()) {
            $qb->field('creationDate')->range(
                $paginator->getStartDate(),
                $paginator->getEndDate()
            );
        }

        if ($paginator->getEmail()) {
            $qb->field('email')->equals($paginator->getEmail());
        }

        if ($paginator->getIdClient()) {
            $qb->field('idClient')->equals($paginator->getIdClient());
        }

        return $qb;
    }
}
