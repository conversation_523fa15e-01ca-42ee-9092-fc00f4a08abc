<?php
namespace App\Message;

class LogIncidentMessage
{    
    /**
    * @var string $id
    */
    private $id;
    /**
    * @var string $brand
    */
    private $brand;
    /**
    * @var string $country
    */
    private $country;

    public function __construct(string $id, string $brand, string $country)
    {
        $this->id = $id;
        $this->brand = $brand;
        $this->country = $country;
    }

    public function getId(): string
    {
        return $this->id;
    }

    public function getCountry(): string
    {
        return $this->country;
    }

    public function getBrand(): string
    {
        return $this->brand;
    }

    public function toString()
    {
        return [
            "id" => $this->getId(),
            "country" => $this->getCountry(),
            "brand" => $this->getBrand()
        ];
    }
}