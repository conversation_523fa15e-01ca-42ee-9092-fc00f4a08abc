<?php

namespace App\Controller;

use App\Document\LogIncident;
use App\Form\LogIncidentType;
use App\Model\LogIncidentPaginator;
use App\Manager\LogIncidentManager;
use App\Helpers\FormErrorResponseTrait;
use App\Helpers\ResponseTrait;
use App\Validator\BrandValidator;
use App\Validator\CountryValidator;
use App\Document\File;
use App\Form\FileToUploadType;
use App\Form\PutLogIncidentType;
use OpenApi\Attributes as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Validator\ValidatorInterface;

#[Route('/api/log-incident')]
class LogIncidentController extends AbstractController
{
    use FormErrorResponseTrait, ResponseTrait;
    public function __construct(
        private LogIncidentManager $manager
    ) {}

    #[Route('/post', name: 'api_log_incident_post', methods: ['POST'])]
    #[OA\RequestBody(
        required: true,
        content: new OA\JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'vin', type: 'string'),
                new OA\Property(property: 'email', type: 'string'),
                new OA\Property(property: 'idClient', type: 'string'),
                new OA\Property(property: 'title', type: 'string'),
                new OA\Property(property: 'comment', type: 'string'),
                new OA\Property(property: 'siteCode', type: 'string'),
                new OA\Property(property: 'culture', type: 'string'),
                new OA\Property(property: 'optin1', type: 'boolean'),
                new OA\Property(property: 'optin2', type: 'boolean'),
            ]
        )
    )]
    #[OA\Response(response: 201, description: 'Created')]
    #[OA\Response(response: 400, description: 'Form validation failed')]
    #[OA\Tag(name: 'Log Incident')]
    public function postLogIncident(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);

        $logIncident = new LogIncident();
        $form = $this->createForm(LogIncidentType::class, $logIncident);
        $form->submit($data);
        $siteCode = $logIncident->getSiteCode();

        if ($siteCode && str_contains($siteCode, '_')) {
            $parts = explode('_', strtoupper($siteCode)); // e.g. DS_FR_ESP → ['DS', 'FR', 'ESP']

            if (count($parts) >= 2) {
                $logIncident->setBrand($parts[0]);
                $logIncident->setCountry($parts[1]);
            }
        }

        $existing = $this->manager->checkLogIncidentExist(
            $logIncident->getEmail(),
            $logIncident->getVin(),
            $logIncident->getIdClient(),
            $logIncident->getTitle(),
            $logIncident->getComment()
        );

        if ($existing) {
            return new JsonResponse($existing['content'], $existing['code']);
        }

        $response = $this->manager->manageFormData($form, $logIncident);

        return new JsonResponse($response['content'], $response['code']);
    }

    #[Route('/get', name: 'api_log_incident_get', methods: ['GET'])]
    #[OA\Parameter(name: 'brand', in: 'query', required: true, schema: new OA\Schema(type: 'string', enum: ['AC', 'AP', 'DS', 'OP', 'VX', 'LC', 'LA']))]
    #[OA\Parameter(name: 'country', in: 'query', required: true, schema: new OA\Schema(type: 'string'))]
    #[OA\Parameter(name: 'status', in: 'query', required: false, schema: new OA\Schema(type: 'integer', enum: [0, 1]), description: 'Status Filter: 0 for actif, 1 for to_delete')]
    #[OA\Parameter(name: 'vin', in: 'query', required: false, schema: new OA\Schema(type: 'string'))]
    #[OA\Parameter(name: 'search', in: 'query', required: false, schema: new OA\Schema(type: 'string'))]
    #[OA\Parameter(name: 'limit', in: 'query', required: false, schema: new OA\Schema(type: 'integer'), example: 10)]
    #[OA\Parameter(name: 'offset', in: 'query', required: false, schema: new OA\Schema(type: 'integer'), example: 0)]
    #[OA\Parameter(name: 'order_by', in: 'query', required: false, schema: new OA\Schema(type: 'string', enum: ['incidentid', 'site_code', 'vin', 'title', 'creation_date']))]
    #[OA\Parameter(name: 'order_dir', in: 'query', required: false, schema: new OA\Schema(type: 'string', enum: ['asc', 'desc']))]
    #[OA\Parameter(name: 'start_date', in: 'query', required: false, schema: new OA\Schema(type: 'string'))]
    #[OA\Parameter(name: 'end_date', in: 'query', required: false, schema: new OA\Schema(type: 'string'))]
    #[OA\Parameter(name: 'type', in: 'query', required: false, schema: new OA\Schema(type: 'string', enum: ['c1st', 'non-care']), example: 'non-care')]
    #[OA\Parameter(name: 'email', in: 'query', required: false, schema: new OA\Schema(type: 'string'))]
    #[OA\Parameter(name: 'id_client', in: 'query', required: false, schema: new OA\Schema(type: 'string'))]
    #[OA\Response(response: 200, description: 'List of log incidents')]
    #[OA\Tag(name: 'Log Incident')]
    public function getLogIncidents(Request $request, ValidatorInterface $validator): JsonResponse
    {
        $brand = mb_strtoupper(trim($request->get('brand', '')));
        $brand = str_replace('LC', 'LA', $brand);
        $country = mb_strtoupper(trim($request->get('country', '')));
        $status = $request->get('status', 0);

        $paginatorParameters = LogIncidentPaginator::putRequestParameters($request);

        if (in_array($brand, ['CT', 'XX'])) {
            $messages = $this->getValidationMessages(
                $validator->validate($paginatorParameters)
            );
        } else {
            $errors = $validator->validate(
                compact('brand', 'country'),
                new Assert\Collection([
                    'brand'   => BrandValidator::getConstraintsForAll(),
                    'country' => CountryValidator::getConstraintsForCountry()
                ])
            );

            $messages = array_merge(
                $this->getValidationMessages($errors),
                $this->getValidationMessages($validator->validate($paginatorParameters))
            );
        }

        if (!empty($messages)) {
            $response = $this->buildErrorResponse("validation_error", JsonResponse::HTTP_UNPROCESSABLE_ENTITY, $messages);
        } else {
            $response = $this->manager->getLogIncidentsWithPaginatorParams(
                $brand,
                $country,
                $paginatorParameters,
                $status

            );
        }
        $data = array_map(fn(LogIncident $doc) => $doc->toArray(), $response['content']['success']['data']);
        return new JsonResponse([
            'success' => [
                'data' => $data,
                'total' => count($data)
            ]
        ]);
    }

    #[Route('/getById/{id}', name: 'api_log_incident_get_byid', methods: ['GET'], requirements: ['id' => '[a-fA-F0-9]{24}'])]
    #[OA\Parameter(name: 'id', in: 'path', required: true, schema: new OA\Schema(type: 'string'), description: 'Log Incident Id')]
    #[OA\Parameter(name: 'brand', in: 'query', required: true, schema: new OA\Schema(type: 'string', enum: ['AC', 'AP', 'DS', 'OP', 'VX', 'LC', 'LA']))]
    #[OA\Parameter(name: 'country', in: 'query', required: true, schema: new OA\Schema(type: 'string'))]
    #[OA\Response(response: 200, description: 'Log Incident details')]
    #[OA\Response(response: 404, description: 'Log Incident not found')]
    #[OA\Tag(name: 'Log Incident')]
    public function getLogIncidentById(string $id, Request $request, ValidatorInterface $validator): JsonResponse
    {
        $brand = mb_strtoupper(trim($request->get('brand', '')));
        $brand = str_replace('LC', 'LA', $brand);
        $country = mb_strtoupper(trim($request->get('country', '')));
        if (in_array($brand, ['CT', 'XX'])) {
            $messages = [];
        } else {
            $errors = $validator->validate(
                compact('brand', 'country'),
                new Assert\Collection([
                    'brand'   => BrandValidator::getConstraintsForAll(),
                    'country' => CountryValidator::getConstraintsForCountry()
                ])
            );
            $messages = $this->getValidationMessages($errors);
        }
        if (!empty($messages)) {
            $response = $this->buildErrorResponse("validation_error", JsonResponse::HTTP_UNPROCESSABLE_ENTITY, $messages);
            return new JsonResponse($response['content'], $response['code']);
        }
        $response = $this->manager->findLogIncidentWithMediaDetailsByBrandAndCountry($id, $brand, $country);
        // dd($response);
        return new JsonResponse($response['content']['success']->toArray(), $response['code']);
    }

    #[Route('/pj/{id}', name: 'api_log_incident_pj_post', methods: ['POST'], requirements: ['id' => '[a-fA-F0-9]{24}'])]
    #[OA\Parameter(
        name: 'id',
        in: 'path',
        required: true,
        description: 'Log Incident Id',
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\RequestBody(
        required: true,
        content: new OA\JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'filename', type: 'string'),
                new OA\Property(property: 'data', type: 'string')
            ]
        )
    )]
    #[OA\Response(response: 200, description: 'Return message')]
    #[OA\Response(response: 400, description: 'Form validation failed')]
    #[OA\Response(response: 404, description: 'Log Incident not found')]
    #[OA\Tag(name: 'Log Incident')]
    public function postPjLogIncident(string $id, Request $request): JsonResponse
    {
        $logIncident = $this->manager->findLogIncidentById($id);

        if ($logIncident['code'] !== JsonResponse::HTTP_OK) {
            return new JsonResponse($logIncident['content'], $logIncident['code']);
        }

        $data = json_decode($request->getContent(), true);

        $file = new File();
        $form = $this->createForm(FileToUploadType::class, $file);
        $form->submit($data);

        $response = $this->manager->upload($file, $form, $logIncident['content']['success']);

        return new JsonResponse($response['content'], $response['code']);
    }

    #[Route('/update/{id}', name: 'api_log_incident_put', methods: ['PUT'], requirements: ['id' => '[a-fA-F0-9]{24}'])]
    #[OA\Parameter(name: 'id', in: 'path', required: true, description: 'Log Incident Id', schema: new OA\Schema(type: 'string'))]
    #[OA\Parameter(name: 'brand', in: 'query', required: true, schema: new OA\Schema(type: 'string', enum: ['AC', 'AP', 'DS', 'OP', 'VX', 'LC', 'LA']))]
    #[OA\Parameter(name: 'country', in: 'query', required: true, schema: new OA\Schema(type: 'string'))]
    #[OA\RequestBody(
        required: true,
        content: new OA\JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'status', type: 'integer')
            ]
        )
    )]
    #[OA\Response(response: 200, description: 'Return data')]
    #[OA\Response(response: 400, description: 'Form validation failed')]
    #[OA\Response(response: 404, description: 'Log Incident not found')]
    #[OA\Tag(name: 'Log Incident')]
    public function putLogIncident(
        string $id,
        Request $request,
        ValidatorInterface $validator
    ): JsonResponse {
        $brand = mb_strtoupper(trim($request->get('brand', '')));
        $brand = str_replace('LC', 'LA', $brand);
        $country = mb_strtoupper(trim($request->get('country', '')));

        // Validate brand & country
        if (in_array($brand, ['CT', 'XX'])) {
            $messages = [];
        } else {
            $validation = $validator->validate(
                compact('brand', 'country'),
                new Assert\Collection([
                    'brand' => BrandValidator::getConstraintsForAll(),
                    'country' => CountryValidator::getConstraintsForCountry()
                ])
            );
            $messages = $this->getValidationMessages($validation);
        }

        if (!empty($messages)) {
            $response = $this->buildErrorResponse("validation_error", Response::HTTP_UNPROCESSABLE_ENTITY, $messages);
            return new JsonResponse($response['content'], $response['code']);
        }

        // Retrieve log incident
        $response = $this->manager->findLogIncidentByIdAndBrandAndCountry($id, $brand, $country);
        if ($response['code'] !== Response::HTTP_OK) {
            return new JsonResponse($response['content'], $response['code']);
        }

        $logIncident = $response['content']['success'];

        // Process update
        $data = json_decode($request->getContent(), true);
        $form = $this->createForm(PutLogIncidentType::class, $logIncident);
        $form->submit($data);

        $response = $this->manager->managePut($form, $logIncident);

        return new JsonResponse($response['content'], $response['code']);
    }
}
