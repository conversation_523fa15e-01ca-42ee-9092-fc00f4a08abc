<?php

namespace App\Form;

use App\Document\LogIncident;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class LogIncidentType extends AbstractType
{
    /**
     * @param FormBuilderInterface $builder
     * @param array $options
     * @return void
     */
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add('email')
            ->add('vin')
            ->add('idClient')
            ->add('title')
            ->add('comment')
            ->add('siteCode')
            ->add('culture')
            ->add('optin1')
            ->add('optin2');
    }

    /**
     * @param OptionsResolver $resolver
     * @return void
     */
    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'data_class' => LogIncident::class,
            'csrf_protection' => false,
            'allow_extra_fields' => true
        ]);
    }
}
