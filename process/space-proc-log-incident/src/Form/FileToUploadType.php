<?php

namespace App\Form;

use App\Document\File;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\OptionsResolver\OptionsResolver;

class FileToUploadType extends AbstractType
{
    /**
     * @param FormBuilderInterface $builder
     * @param array $options
     * @return void
     */
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add('filename', TextType::class, [
                'property_path' => 'name'
            ])
            ->add('data', TextType::class, [
                'property_path' => 'contentBase64'
            ])
        ;
        $builder->addEventListener(FormEvents::PRE_SUBMIT, function(FormEvent $event) {
            $data = $event->getData();
            if (isset($data['data'])) {        
                $data['data'] = preg_replace('#^data:image/\w+;base64,#i', '', $data['data']);;             
                $event->setData($data); 
            }
        });
    }

    /**
     * @param OptionsResolver $resolver
     * @return void
     */
    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'data_class' => File::class,
            'allow_extra_fields' => true,
            'csrf_protection' => false
        ]);
    }
}
