<?php

namespace App\Model;

use App\Validator\Vin;
use Symfony\Component\Validator\Constraints as Assert;

class UserLogIncident
{

    /**
     * @Vin
     * @var string
     */
    private $vin;

    /**
     * @Assert\NotBlank
     * @var string
     */
    private $accountId;

    /**
     * Get the value of vin
     * @return  string
     */
    public function getVin()
    {
        return $this->vin;
    }

    /**
     * Set the value of vin
     *
     * @param  string  $vin
     *
     * @return  self
     */
    public function setVin(string $vin)
    {
        $this->vin = $vin;

        return $this;
    }

    /**
     * Get the value of accountId
     *
     * @return  string
     */
    public function getAccountId()
    {
        return $this->accountId;
    }

    /**
     * Set the value of accountId
     *
     * @param  string  $accountId
     *
     * @return  self
     */
    public function setAccountId(string $accountId)
    {
        $this->accountId = $accountId;

        return $this;
    }
}
