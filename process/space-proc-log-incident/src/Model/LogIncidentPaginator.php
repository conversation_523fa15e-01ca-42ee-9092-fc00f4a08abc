<?php

namespace App\Model;

use Symfony\Component\HttpFoundation\Request;
use App\Validator\PaginatorParameters;

/**
 * @PaginatorParameters
 */
class LogIncidentPaginator
{
    const DEFAULT_ORDER_BY = 'creationDate';
    const DEFAULT_ORDER_DIR = 'desc';
    
    const OBJECT_MATCHER_FIELD = [
        'incidentid' => 'id',
        'site_code' => 'siteCode',
        'vin' => 'vin',
        'title' => 'title',
        'creation_date' => 'creationDate'
    ];

    /**
     * @var string
     */
    private $vin;

    /**
     * @var string
     */
    private $search;

    /**
     * @var string
     */
    private $limit;

    /**
     * @var string
     */
    private $offset;

    /**
     * @var string
     */
    private $orderBy;

    /**
     * @var string
     */
    private $orderDir;

    /**
     * @var string
     */
    private $startDate;

    /**
     * @var string
     */
    private $endDate;
    
    /**
     * @var string
     */
    private $type;

    /**
     * @var string
     */
    private $email;

    /**
     * @var string
     */
    private $idClient;

    /**
     * Get the value of vin
     *
     * @return  string
     */
    public function getVin()
    {
        return $this->vin;
    }

    /**
     * Set the value of vin
     *
     * @param  string  $vin
     *
     * @return  self
     */
    public function setVin(?string $vin)
    {
        $this->vin = $vin;

        return $this;
    }

    /**
     * Get the value of search
     *
     * @return  string
     */
    public function getSearch()
    {
        return $this->search;
    }

    /**
     * Set the value of search
     *
     * @param  string  $search
     *
     * @return  self
     */
    public function setSearch(?string $search)
    {
        $this->search = $search;

        return $this;
    }

    /**
     * Get the value of limit
     *
     * @return  integer
     */
    public function getLimit()
    {
        return $this->limit;
    }

    /**
     * Set the value of limit
     *
     * @param   $limit
     *
     * @return  self
     */
    public function setLimit($limit)
    {
        $this->limit = $limit;

        return $this;
    }

    /**
     * Get the value of offset
     *
     * @return  integer
     */
    public function getOffset()
    {
        return $this->offset;
    }

    /**
     * Set the value of offset
     *
     * @param $offset
     *
     * @return  self
     */
    public function setOffset($offset)
    {
        $this->offset = $offset;

        return $this;
    }

    /**
     * Get the value of orderBy
     *
     * @return  string
     */
    public function getOrderBy()
    {
        $orderBy = $this->orderBy;
        if ($this->orderBy) {
            return isset(self::OBJECT_MATCHER_FIELD[$orderBy]) ? self::OBJECT_MATCHER_FIELD[$orderBy] : self::DEFAULT_ORDER_BY;
        }
        return self::DEFAULT_ORDER_BY;
    }

    /**
     * Set the value of orderBy
     *
     * @param  string  $orderBy
     *
     * @return  self
     */
    public function setOrderBy(?string $orderBy)
    {
        $this->orderBy = $orderBy;

        return $this;
    }

    /**
     * Get the value of orderDir
     *
     * @return  string
     */
    public function getOrderDir()
    {
        $orderDir = $this->orderDir;
        if (!in_array($orderDir, ['asc', 'desc'])) {
            return self::DEFAULT_ORDER_DIR;
        }
        return $this->orderDir;
    }

    /**
     * Set the value of orderDir
     *
     * @param  string  $orderDir
     *
     * @return  self
     */
    public function setOrderDir(?string $orderDir)
    {
        $this->orderDir = $orderDir;

        return $this;
    }

    /**
     * Get the value of startDate
     *
     * @return  string
     */
    public function getStartDate()
    {
        return $this->startDate;
    }

    /**
     * Set the value of startDate
     *
     * @param  string  $startDate
     *
     * @return  self
     */
    public function setStartDate(?string $startDate)
    {
        $this->startDate = $startDate;

        return $this;
    }

    /**
     * Get the value of endDate
     *
     * @return  string
     */
    public function getEndDate()
    {
        return $this->endDate;
    }

    /**
     * Set the value of endDate
     *
     * @param  string  $endDate
     *
     * @return  self
     */
    public function setEndDate(?string $endDate)
    {
        $this->endDate = $endDate;

        return $this;
    }

    public static function getFormattedDate(?string $date) {
        if (!$date) {
            return null;
        }
        $stamps = strtotime($date);
        $date = date('Y-m-d', $stamps);
        if ($date) {
            return $date;
        }
        return null;
    }

     /**
     * Get the value of type
     */
    public function getType(): string
    {
        return $this->type;
    }

    /**
     * Set the value of type
     */
    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }

    /**
     * Get the value of idClient
     */
    public function getIdClient(): string
    {
        return $this->idClient;
    }

    /**
     * Set the value of idClient
     */
    public function setIdClient(string $idClient): self
    {
        $this->idClient = $idClient;

        return $this;
    }

    /**
     * Get the value of email
     */
    public function getEmail(): string
    {
        return $this->email;
    }

    /**
     * Set the value of email
     */
    public function setEmail(string $email): self
    {
        $this->email = $email;

        return $this;
    }

    public static function putRequestParameters(Request $request)
    {
        return (new self)
            ->setVin(trim($request->get('vin', '')))
            ->setSearch(trim($request->get('search', '')))
            ->setLimit($request->get('limit', 25))
            ->setOffset($request->get('offset', 0))
            ->setOrderBy(trim($request->get('order_by', '')))
            ->setOrderDir(trim($request->get('order_dir', '')))
            ->setStartDate(self::getFormattedDate(trim($request->get('start_date', ''))))
            ->setEndDate(self::getFormattedDate(trim($request->get('end_date', ''))))
            ->setType(trim($request->get('type', '')) ?: 'non-care')
            ->setEmail(trim($request->get('email', '')))
            ->setIdClient(trim($request->get('id_client', '')));
    }

    public function getSearchQuery(string $paramName, string $objName) {
        $fields = self::OBJECT_MATCHER_FIELD;
        $searchFields = array_map(function($field) use ($paramName, $objName) {
            if ($field == "creationDate") {
                return "DATE_FORMAT({$objName}.{$field}, '%Y-%m-%d') LIKE {$paramName}";
            }
            return "UPPER({$objName}.{$field}) LIKE {$paramName}";
        }, $fields);
        return implode(' or ', $searchFields);
    }

}