<?php

namespace App\Validator;

use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;

class ContentBase64Validator extends ConstraintValidator
{
    /**
     * @var integer
     */
    private $_maxFileSize;

    /**
     * Injected from parameters file
     *
     * @param int $maxFileSize
     */
    public function __construct($maxFileSize)
    {
        $this->_maxFileSize = $maxFileSize;
    }
    public function validate($value, Constraint $constraint)
    {
        /**
         * .Formula to calculate file size from base64 string
         */
        $size = ((strlen($value) * (3 / 4)) - substr_count(substr($value, -2), '=')) / 1024.0;
        if ($size > $this->_maxFileSize) {
            $this->context->buildViolation("Max allowed size is " . $this->_maxFileSize . " KO")
                ->addViolation();
        }
        /**
         * Check if base64 data is valid
         */
        if (!(bool) preg_match('/^[a-zA-Z0-9\/\r\n+]*={0,2}$/', $value)) {
            $this->context->buildViolation("Invalid base64 code")
                ->addViolation();
        }
        return;
    }
}
