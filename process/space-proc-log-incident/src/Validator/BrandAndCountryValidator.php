<?php

namespace App\Validator;

use App\Document\LogIncident;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Context\ExecutionContextInterface;
use Symfony\Component\Validator\Constraints\Regex;
use Symfony\Component\Validator\Constraints\NotBlank;

class BrandAndCountryValidator extends ConstraintValidator
{
    public function validate(mixed $logIncident, Constraint $constraint): void
    {
        if (!$logIncident instanceof LogIncident) {
            return;
        }

        $vin = $logIncident->getVin();
        $siteCode = $logIncident->getSiteCode();
        $culture = $logIncident->getCulture();

        $this->validateSiteCode($siteCode);
        $this->validateCulture($culture);
        $this->validateVin($vin);

        if ($this->context->getViolations()->count() > 0) {
            return;
        }

        $this->matchCountries($siteCode, $culture);
        // $this->matchVinAndBrand($siteCode, $vin); // uncomment if needed
    }

    private function matchCountries(?string $siteCode, ?string $culture): void
    {
        if (!$siteCode || !$culture) {
            return;
        }

        if (strtoupper(substr($culture, 3)) !== strtoupper(substr($siteCode, 3, 2))) {
            $this->context->buildViolation("Country in culture field does not match the country in the site_code field")
                ->atPath('culture')
                ->addViolation();
        }
    }

    private function validateVin(?string $vin): void
    {
        if ($vin && !preg_match('/^[a-zA-Z0-9_]{17}$/', $vin)) {
            $this->context->buildViolation("Invalid VIN format")
                ->atPath('vin')
                ->addViolation();
        }
    }

    private function validateCulture(?string $culture): void
    {
        if (empty($culture)) {
            $this->context->buildViolation("Culture is required")
                ->atPath('culture')
                ->addViolation();
            return;
        }

        if (!preg_match('/^[a-z]{2}_[A-Z]{2}$/', $culture)) {
            $this->context->buildViolation("Culture must follow the format 'xx_XX' (e.g., fr_FR)")
                ->atPath('culture')
                ->addViolation();
        }
    }

    private function validateSiteCode(?string $siteCode): void
    {
        if (empty($siteCode)) {
            $this->context->buildViolation("Site code is required")
                ->atPath('siteCode')
                ->addViolation();
            return;
        }

        // Check format: BRAND_COUNTRY_CODE
        if (!preg_match('/^[A-Z]{2,5}_[A-Z]{2}_[A-Z0-9_]+$/', $siteCode)) {
            $this->context->buildViolation("Site code must follow the format BRAND_COUNTRY_CODE (e.g., DS_FR_ESP)")
                ->atPath('siteCode')
                ->addViolation();
        }

        // Optional: check if there are exactly 3 segments
        $parts = explode('_', $siteCode);
        if (count($parts) !== 3) {
            $this->context->buildViolation("Site code must contain 3 parts separated by underscores")
                ->atPath('siteCode')
                ->addViolation();
        }
    }


    // Optional: VIN and brand consistency rule
    private function matchVinAndBrand(string $siteCode, string $vin): void
    {
        $brand = strtoupper(substr(trim($siteCode), 0, 2));
        $vin = trim($vin);

        if (!preg_match($this->getVinRegexByBrand($brand), $vin)) {
            $this->context->buildViolation("This VIN does not match brand $brand")
                ->atPath('vin')
                ->addViolation();
        }
    }

    private function getVinRegexByBrand(string $brand): string
    {
        return match ($brand) {
            'AC', 'DS' => '/^(VL4|VF7|VR1|VR7|Z8T)[a-zA-Z0-9_]{14}$/i',
            'AP' => '/^(VF3|VR3|Z8T|8AD|935|936|8A[a-zA-Z0-9])[a-zA-Z0-9_]{14}$/i',
            default => '/^[a-zA-Z0-9_]{17}$/i',
        };
    }
}
