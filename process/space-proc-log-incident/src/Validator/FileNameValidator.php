<?php

namespace App\Validator;

use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;

class FileNameValidator extends ConstraintValidator
{
    public function validate($value, Constraint $constraint)
    {
        /* @var $constraint \App\Validator\FileName */

        if (null === $value || '' === $value) {
            return;
        }

        if (!(bool) preg_match('/^(\w+ ?)+ ?([\.]|[a-zA-Z0-9_-])+\.[a-zA-Z0-9]+$/', $value)) {
            $this->context->buildViolation("File name invalid")
                ->addViolation();
        }
    }
}
