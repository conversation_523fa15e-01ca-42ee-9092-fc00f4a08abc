<?php

namespace App\Validator;

use Symfony\Component\Validator\Constraints as Assert;

class BrandValidator
{
    /**
     * Get all the brands.
     *
     * @return array
     */
    public static function all(): array
    {
        return ['AP', 'AC', 'DS', 'OP', 'VX', 'LC', 'LA'];
    }

    /**
     * Get brand constraints.
     *
     * @return array
     */
    public static function getConstraintsForAll(): array
    {
        return [
            new Assert\NotBlank,
            new Assert\Choice(BrandValidator::all()),
        ];
    }
}
