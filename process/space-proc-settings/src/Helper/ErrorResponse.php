<?php

namespace App\Helper;

use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

/**
 * Error response class
 */
class ErrorResponse implements IResponseArrayFormat
{
    private $code = Response::HTTP_INTERNAL_SERVER_ERROR;
    private $message = null;

    public function setCode(int $code): self
    {
        $this->code = $code;
        return $this;
    }

    public function setMessage(string $message): self
    {
        $this->message = $message;
        return $this;
    }

    public function getArrayFormat(): array
    {
        return [
            //'code'    => $this->code ?: Response::HTTP_BAD_REQUEST,
            'error' => [
                'message' => $this->message ?? ''
            ]
        ];
    }

    public function getJsonFormat(): JsonResponse
    {
        return new JsonResponse($this->getArrayFormat(), $this->code);
    }
}
