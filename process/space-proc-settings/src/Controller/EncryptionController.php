<?php

namespace App\Controller;

use App\Manager\EncryptionManager;
use OpenApi\Attributes\JsonContent;
use App\Trait\ValidationResponseTrait;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\{Request, Response};
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;
use OpenApi\Attributes as OA;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Component\Validator\Constraints as Assert;


#[Route('/v1/data')]
class EncryptionController extends AbstractController
{
    use ValidationResponseTrait;

    #[OA\RequestBody(
        content: new OA\JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'data', default: 'VR3UPHNKSKT101600', type: 'string'),
                new OA\Property(property: 'target', default: 'SAMS', type: 'string', description: 'Encrypt data by the given target; ex: OLB, SAMS'),
            ]
        )
    )]

    #[OA\Response(
        response: 400,
        description: 'Bad Request',
    )]
    #[OA\Response(
        response: 404,
        description: 'Target is not found',
    )]
    #[OA\Response(
        response: 500,
        description: 'Internal Error',
    )]
    #[OA\Response(
        response: 200,
        description: 'Successful Response',
        content: new OA\JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'success', type: 'object', properties: [
                    new OA\Property(property: 'data', type: 'string'),
                ])
            ]
        )
    )]
    #[OA\Tag(name: 'Encryption')]
    #[Route('/encrypt', name: 'data_encrypt', methods: [Request::METHOD_POST])]
    public function encrypt(
        Request $request,
        EncryptionManager $encryptionManager,
        ValidatorInterface $validator
    ): Response {
        $content = json_decode($request->getContent(), true);
        $target = mb_strtoupper($content['target'] ?? '');
        $data = $content['data'] ?? '';
        $errors = $validator->validate(
            compact('target', 'data'),
            new Assert\Collection([
                'target' => new Assert\NotBlank(),
                'data' => new Assert\NotBlank(),
            ])
        );
        $messages = $this->getValidationMessages($errors);
        if (!empty($messages)) {
            return $this->getValidationErrorResponse($messages);
        }
        return $encryptionManager->encryptData($data, $target)->getJsonFormat();
    }

    #[Route('/decrypt', name: 'data_decrypt', methods: ['POST'])]
    #[OA\RequestBody(
        content: new OA\JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'data', type: 'string')
            ]
        )
    )]
    #[OA\Response(
        response: 200,
        description: 'Success Response',
        content: new JsonContent(type: 'object', properties: [
            new OA\Property(property: 'success', properties: [
                new OA\Property(property: 'data')
            ])
        ])
    )]
    #[OA\Response(
        response: 400,
        description: 'Bad Request',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: [])
                ])
            ]
        )
    )]
    #[OA\Response(
        response: 500,
        description: 'Internal Error'
    )]
    #[OA\Tag(name: 'Decryption')]
    public function decryptData(Request $request, ValidatorInterface $validator, EncryptionManager $encryptionManager): JsonResponse
    {
        $requestData = json_decode($request->getContent(), true);

        $dataToDecrypt = $requestData['data'] ?? '';

        $errors = $validator->validate(
            compact('dataToDecrypt'),
            new Assert\Collection([
                'dataToDecrypt' => new Assert\NotBlank(),
            ])
        );

        $messages = static::getValidationMessages($errors);

        if (!empty($messages)) {
            return static::getValidationErrorResponse($messages);
        }

        $response = $encryptionManager->decryptData($dataToDecrypt);

        return $response->getJsonFormat();
    }
}