<?php

declare(strict_types=1);

namespace App\Exception;

use Symfony\Component\HttpFoundation\Response;

/**
 * Class BadRequestException
 *
 * @package App\Exception
 */
class BadRequestException extends \InvalidArgumentException
{
    /**
     * Make a new exception.
     *
     * @return static
     */
    public static function make(): self
    {
        return new static('Bad Request !', Response::HTTP_BAD_REQUEST);
    }
}