<?php

declare(strict_types=1);

namespace App\Exception;

use Symfony\Component\HttpFoundation\Response;

/**
 * Class TargetNotFoundException
 *
 * @package App\Exception
 */
class TargetNotFoundException extends \InvalidArgumentException
{
    /**
     * Make a new exception.
     *
     * @return static
     */
    public static function make(): self
    {
        return new static('Target not found !', Response::HTTP_NOT_FOUND);
    }
}
