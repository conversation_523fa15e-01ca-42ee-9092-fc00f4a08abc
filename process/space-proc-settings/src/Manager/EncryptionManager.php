<?php

namespace App\Manager;

use App\Exception\BadRequestException;
use App\Helper\IResponseArrayFormat;
use App\Helper\SuccessResponse;
use App\Service\EncryptionService;
use App\Trait\LoggerTrait;

class EncryptionManager
{
    use LoggerTrait;

    function __construct(private EncryptionService $encryptionService)
    {
    }

    public function encryptData(string $dataToEncrypt, string $target): IResponseArrayFormat
    {
        $data = $this->encryptionService->encrypt($dataToEncrypt, $target);
        if (!$data) {
            $this->logger->error('Manager :: Error While Encrypting data');
            throw BadRequestException::make();
        }

        return (new SuccessResponse())->setData(['data' => $data]);
    }

    public function decryptData(string $dataToDecrypt): IResponseArrayFormat
    {
        $decryptedData = $this->encryptionService->decryptData($dataToDecrypt);

        if (!$decryptedData) {
            $this->logger->error('Error while Decrypting Data');
            throw BadRequestException::make();
        }
        return (new SuccessResponse)->setData(['data' => $decryptedData]);
    }
}