<?php

namespace App\Service;

use App\Exception\BadRequestException;
use App\Exception\TargetNotFoundException;
use App\Trait\LoggerTrait;

class EncryptionService
{
    use LoggerTrait;

    const KEYS = [
        'olb',
        'sams'
    ];
    const FOLDER_NAME = 'publickeys';

    public function __construct(private string $keysPath, private string $keyName)
    {
    }

    /**
     * @param string $data
     * @param string $key
     * @return string|null
     */
    public function encrypt(string $data, string $key): ?string
    {
        $key = strtolower($key);
        $file = $this->keysPath . '/' . self::FOLDER_NAME . '/' . $key . '.key';
        //check target and file existence
        $this->checkTargetAndFile($file, $key);
        $publicKey = file_get_contents($file);
        return openssl_public_encrypt($data, $cryptedData, $publicKey)
            ? base64_encode($cryptedData)
            : null;
    }

    /**
     * @param string $file
     * @param string $key
     * @return void
     */
    private function checkTargetAndFile(string $file, string $key): void
    {
        if (!in_array($key, self::KEYS)) {
            $this->logger->error('Service :: Bad Target requested : ' . $key);
            throw BadRequestException::make();
        }

        if (!file_exists($file)) {
            $this->logger->error('Service :: Public key file not exists : ' . $file);
            throw TargetNotFoundException::make();
        }
    }

    public function decryptData(string $dataToDecrypt): ?string
    {
        $privateKeyContent = file_get_contents($this->keysPath . '/privatekeys/' . $this->keyName . '.key');
        $privateKey = openssl_pkey_get_private($privateKeyContent);

        $decryptedData = "";

        return openssl_private_decrypt(base64_decode($dataToDecrypt), $decryptedData, $privateKey) ?
            $decryptedData : null;
    }
}