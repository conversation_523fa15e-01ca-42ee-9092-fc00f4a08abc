<?php

namespace App\Tests\Controller;

use App\Controller\EncryptionController;
use App\Helper\SuccessResponse;
use App\Manager\EncryptionManager;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\HttpFoundation\ParameterBag;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\ConstraintViolationListInterface;

class EncryptionControllerTest extends KernelTestCase
{
    private EncryptionController $controller;
    private ValidatorInterface $validator;
    private EncryptionManager $encryptionManager;

    public function setUp(): void
    {
        self::bootKernel();
        $this->controller = new EncryptionController();
        $this->validator = $this->createMock(ValidatorInterface::class);
        $this->encryptionManager = $this->createMock(EncryptionManager::class);
        $this->controller->setContainer($this->getContainer());
        parent::setUp();
    }

    public function testEncryptionFaild(): void
    {
        $request = $this->createMock(Request::class);
        $request->request = new ParameterBag([
            'target' => '',
            'data' => 'data'
        ]);

        $manager = $this->createMock(EncryptionManager::class);
        $validator = $this->createMock(ValidatorInterface::class);
        $constraint = new ConstraintViolation(
            'This value should not be blank.',
            null,
            ['target'],
            null,
            'target',
            null
        );
        $validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList([$constraint]));

        $this->getContainer()->get('request_stack')->push($request);
        $response = $this->controller->encrypt($request, $manager, $validator);
        $this->assertEquals(422, $response->getStatusCode());
    }

    public function testEncryptionSuccess(): void
    {
        $request = $this->createMock(Request::class);
        $request->query = new ParameterBag([
            'target' => 'target',
            'data' => 'data'
        ]);

        $manager = $this->createMock(EncryptionManager::class);
        $manager->expects($this->once())
            ->method('encryptData')
            ->willReturn(new SuccessResponse());
        $validator = $this->createMock(ValidatorInterface::class);

        $this->getContainer()->get('request_stack')->push($request);
        $response = $this->controller->encrypt($request, $manager, $validator);
        $this->assertEquals(200, $response->getStatusCode());
    }

    public function testGetVehiclesReturnsSuccess()
    {
        $request = $this->createMock(Request::class);
        $request->request = new ParameterBag([
            'data' => 'data'
        ]);

        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn($this->createMock(ConstraintViolationListInterface::class));

        $this->encryptionManager->expects($this->once())
            ->method('decryptData')
            ->willReturn((new SuccessResponse())->setCode(Response::HTTP_OK)->setData(['data' => []]));

        $response = $this->controller->decryptData($request, $this->validator, $this->encryptionManager);

        $expectedResponse = new JsonResponse(['success' => ['data' => []]], Response::HTTP_OK);

        $responseData = json_decode($response->getContent(), true);
        $expectedData = json_decode($expectedResponse->getContent(), true);

        $this->assertSame($responseData['success'], $expectedData['success']);
        $this->assertSame(Response::HTTP_OK, $expectedResponse->getStatusCode());
        $this->assertInstanceOf(JsonResponse::class, $response);
    }
    public function testGetVehiclesReturnsValidationError()
    {
        $request = $this->createMock(Request::class);
        $request->request = new ParameterBag([
            'data' => ''
        ]);

        $constraint = new ConstraintViolation(
            'This value should not be blank.',
            null,
            ['data'],
            null,
            'data',
            null
        );
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList([$constraint]));

        $response = $this->controller->decryptData($request, $this->validator, $this->encryptionManager);

        $this->assertEquals(422, $response->getStatusCode());
    }
}