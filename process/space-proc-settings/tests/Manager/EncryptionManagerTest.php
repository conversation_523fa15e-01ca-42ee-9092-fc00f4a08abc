<?php

namespace App\Tests\Manager;

use App\Exception\BadRequestException;
use App\Helper\SuccessResponse;
use App\Manager\EncryptionManager;
use App\Service\EncryptionService;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use PHPUnit\Framework\TestCase;

class EncryptionManagerTest extends KernelTestCase
{
    private EncryptionManager $encryptionManager;
    private EncryptionService $encryptionService;
    private LoggerInterface $logger;

    protected function setUp(): void
    {
        $this->encryptionService = $this->createMock(EncryptionService::class);
        $this->logger = $this->createMock(LoggerInterface::class);
        $this->logger->expects($this->any())
            ->method('error');
        $this->encryptionManager = new EncryptionManager($this->encryptionService);
        $this->encryptionManager->setLogger($this->logger);
    }

    public function testEncryptDataFailed(): void
    {
        $this->encryptionService->expects($this->once())
            ->method('encrypt')
            ->willReturn(null);

        $this->expectException(BadRequestException::class);
        $this->encryptionManager->encryptData('data', 'target');
    }

    public function testEncryptDataSuccess(): void
    {
        $this->encryptionService->expects($this->once())
            ->method('encrypt')
            ->willReturn('encryptedData');

        $response = $this->encryptionManager->encryptData('data', 'target');
        $this->assertInstanceOf(SuccessResponse::class, $response);
    }

    public function testDecryptDataWithSuccessfulDecryption()
    {
        $dataToDecrypt = 'encrypted_data';
        $decryptedData = 'decrypted_data';

        $this->encryptionService->expects($this->once())
            ->method('decryptData')
            ->with($dataToDecrypt)
            ->willReturn($decryptedData);

        $response = $this->encryptionManager->decryptData($dataToDecrypt);

        $this->assertInstanceOf(SuccessResponse::class, $response);
        $this->assertEquals(['data' => $decryptedData], $response->getData());
    }

    public function testDecryptDataWithFailedDecryption()
    {
        $dataToDecrypt = 'encrypted_data';

        $this->encryptionService->expects($this->once())
            ->method('decryptData')
            ->with($dataToDecrypt)
            ->willReturn('');

        $this->expectException(BadRequestException::class);
        $this->encryptionManager->decryptData($dataToDecrypt);
    }
}
