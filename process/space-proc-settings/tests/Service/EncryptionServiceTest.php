<?php

namespace App\Tests\Service;

use App\Exception\BadRequestException;
use App\Exception\TargetNotFoundException;
use App\Service\EncryptionService;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class EncryptionServiceTest extends KernelTestCase
{
    private EncryptionService $service;
    private LoggerInterface $logger;
    public function setUp(): void
    {
        self::bootKernel();
        $this->service = new EncryptionService('path', '');
        $this->logger = $this->createMock(LoggerInterface::class);
        $this->service->setLogger($this->logger);
        parent::setUp();
    }

    public function testEncryptWrongKey(): void
    {
        $this->expectException(BadRequestException::class);
        $this->service->encrypt('file', 'key');
    }

    public function testEncryptWrongFile(): void
    {
        $this->expectException(TargetNotFoundException::class);
        $this->service->encrypt('data', 'olb');
    }

    public function testEncryptSuccess(): void
    {
        $service = new EncryptionService($this->getContainer()->getParameter('keysPath'), '');
        $response = $service->encrypt('data', 'olb');
        $this->assertStringEndsWith('==', $response);
    }

    public function testDecryptDataReturnsEmpty()
    {
        $encryptionService = new EncryptionService($this->getContainer()->getParameter('keysPath'), 'sp4_private');
        $dataToDecrypt = 'base64_encoded_data';
        $expectedDecryptedData = '';

        $decryptedData = $encryptionService->decryptData($dataToDecrypt);

        $this->assertEquals($expectedDecryptedData, $decryptedData);
    }
}