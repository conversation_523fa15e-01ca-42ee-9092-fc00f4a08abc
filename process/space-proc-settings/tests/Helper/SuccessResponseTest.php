<?php

namespace App\Tests\Helper;

use App\Helper\SuccessResponse;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;

class SuccessResponseTest extends TestCase
{
    public function testGetData(): void
    {
        $helper = new SuccessResponse();
        $data = 'test';
        $helper->setData($data);
        $helper->setCode(Response::HTTP_OK);
        $this->assertEquals('test', $helper->getData());
    }
}
