# see https://symfony.com/doc/current/reference/configuration/framework.html
framework:
    secret: '%env(APP_SECRET)%'
    #csrf_protection: true
    annotations: false
    http_method_override: false
    handle_all_throwables: true

    # Enables session support. Note that the session will ONLY be started if you read or write from it.
    # Remove or comment this section to explicitly disable session support.
    session:
        handler_id: null
        cookie_secure: auto
        cookie_samesite: lax

    #esi: true
    #fragments: true
    php_errors:
        log: true
    
    http_client:
        scoped_clients:
            mongo_atlas_client:
                base_uri: '%env(MONGO_ATLAS_BASE_URL)%'
                headers:
                    api-key: '%env(MONGO_ATLAS_API_KEY)%'
                    Content-Type: application/json

when@test:
    framework:
        test: true
        session:
            storage_factory_id: session.storage.factory.mock_file
