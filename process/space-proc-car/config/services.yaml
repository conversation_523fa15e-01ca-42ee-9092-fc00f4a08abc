# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices.html#use-parameters-for-application-configuration
parameters:
    mongo_db.app: "%env(MONGO_APP)%"
    mongo_db.database: "%env(MONGO_DATABASE)%"
    mongo_db.datasource: "%env(MONGO_DATASOURCE)%"
    brands.xp: ['AP', 'AC', 'DS', 'OP', 'VX', 'JE']

imports:
    - { resource: 'visual_settings.yaml' }

services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.

    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    App\:
        resource: '../src/'
        exclude:
            - '../src/DependencyInjection/'
            - '../src/Entity/'
            - '../src/Kernel.php'

    # add more service definitions when explicit configuration is needed
    # please note that last definitions always *replace* previous ones

    App\Service\CorvetSysConnector:
        arguments:
            $url: "%env(CORVET_URL)%"
    
    App\Service\VehicleVisualService:
        arguments:
            $visualSettings: '%visual3d%'

    App\Service\CacheParametersService:
        arguments:
            $filePath: "%kernel.project_dir%/config/cache_parameters.yaml"
    
    App\Service\MongoAtlasApiConnector:
        $mongoApp: '%mongo_db.app%'
    
    App\Service\MongoAtlasQueryService:
        arguments:
            $database: '%mongo_db.database%'
            $dataSource: '%mongo_db.datasource%'

    App\EventListener\ExceptionListener:
        tags:
        - { name: kernel.event_listener, event: kernel.exception }
        
    App\Helper\BrandHelper:
        arguments:
            $xpBrands: '%brands.xp%'
