<?php

namespace App\Tests\Service;

use App\Helper\SuccessResponse;
use App\Manager\VehicleManager;
use App\Manager\Visual3DManager;
use App\Service\CorvetService;
use App\Service\LabelVehiculeService;
use Exception;
use PHPUnit\Framework\TestCase;

class VehicleManagerTest extends TestCase
{
    private VehicleManager $vehicleManager;
    private CorvetService $corvetService;
    private LabelVehiculeService $labelVehiculeService;
    private Visual3DManager $visual3DManager;

    public function setUp(): void
    {
        $this->corvetService = $this->createMock(CorvetService::class);
        $this->labelVehiculeService = $this->createMock(LabelVehiculeService::class);
        $this->visual3DManager = $this->createMock(Visual3DManager::class);

        $this->vehicleManager = new VehicleManager(
            $this->corvetService,
            $this->labelVehiculeService,
            $this->visual3DManager
        );
    }

    public function testGetVehicleSuccess(): void
    {
        $corvetService = $this->createMock(CorvetService::class);
        $corvetService->expects($this->once())
            ->method('getLcdv')
            ->willReturn(
                [
                    'data' => '1PK0NNRKAQC0A0B0P0XYCYFX',
                    'warranty_start_date' => '2020/20/10',
                    'options' => [],
                    'type' => 0,
                    'attributes' => [],
                    'types' => [],
                ]
            );
        $labelVehiculeService = $this->createMock(LabelVehiculeService::class);
        $labelVehiculeService->expects($this->once())
            ->method('getLvh')
            ->willReturn(
                (new SuccessResponse())
                    ->setData(['label' => '2008'])
            );
        $visual3DManager = $this->createMock(Visual3DManager::class);
        $visual3DManager->expects($this->once())
            ->method('loadImages')
            ->willReturn([
                'data' => "https://www.test.com"
            ]);
        $vehicleService = new VehicleManager(
            $corvetService,
            $labelVehiculeService,
            $visual3DManager
        );
        $parameters = [
            'brand' => 'AP',
            'country' => 'FR',
            'language' => 'fr',
            'source' => 'APP',
            'isfullDetail' => true,
            'vin' => 'vf3veahhwfz059259'
        ];
        // Call the saveFeature method
        $vehicle = $vehicleService->getVehicle($parameters);

        $this->assertArrayHasKey('success', $vehicle->getArrayFormat());
    }

    public function testGetVehicleFailed(): void
    {
        $parameters = [
            'brand' => 'AP',
            'country' => 'FR',
            'language' => 'fr',
            'source' => 'APP',
            'isfullDetail' => true,
            'vin' => 'vf3veahhwfz059259'
        ];

        $corvetService = $this->createMock(CorvetService::class);

        $corvetService->expects($this->once())
            ->method('getLcdv')
            ->willReturn(
                [
                    'data' => '',
                    'warranty_start_date' => '2020/20/10',
                    'options' => [],
                    'type' => 0,
                    'attributes' => [],
                    'types' => [],
                ]
            );

        $labelVehiculeService = $this->createMock(LabelVehiculeService::class);

        $visual3DManager = $this->createMock(Visual3DManager::class);

        $service = new VehicleManager(
            $corvetService,
            $labelVehiculeService,
            $visual3DManager
        );
        $this->expectException(Exception::class);
        $service->getVehicle($parameters);
    }

    public function testGetVehicleSuccessWithoutDet(): void
    {
        $corvetService = $this->createMock(CorvetService::class);
        $corvetService->expects($this->once())
            ->method('getLcdv')
            ->willReturn(
                [
                    'data' => '1PK0NNRKAQC0A0B0P0XYCYFX',
                    'warranty_start_date' => '2020/20/10',
                    'options' => [],
                    'type' => 0,
                    'attributes' => [],
                    'types' => [],
                ]
            );
        $labelVehiculeService = $this->createMock(LabelVehiculeService::class);
        $labelVehiculeService->expects($this->once())
            ->method('getLvh')
            ->willReturn(
                (new SuccessResponse())
                    ->setData(['label' => '2008'])
            );
        $visual3DManager = $this->createMock(Visual3DManager::class);
        $visual3DManager->expects($this->once())
            ->method('loadImages')
            ->willReturn([
                'data' => "https://www.test.com"
            ]);
        $vehicleService = new VehicleManager(
            $corvetService,
            $labelVehiculeService,
            $visual3DManager
        );
        $parameters = [
            'brand' => 'AP',
            'country' => 'FR',
            'language' => 'fr',
            'source' => 'APP',
            'isfullDetail' => false,
            'vin' => 'vf3veahhwfz059259'
        ];
        // Call the saveFeature method
        $vehicle = $vehicleService->getVehicle($parameters);

        $this->assertArrayHasKey('success', $vehicle->getArrayFormat());
    }

    public function testMapSearchApiResultsReturnsBasicData(): void
    {
        $corvetService = $this->createMock(CorvetService::class);
        $lvhService = $this->createMock(LabelVehiculeService::class);
        $visual3DManager = $this->createMock(Visual3DManager::class);

        $vehicleManager = new VehicleManager(
            $corvetService,
            $lvhService,
            $visual3DManager
        );

        $data = [
            'vin' => 'test-vin',
            'label' => 'test-short-label',
            'picture' => 'test-picture-url',
            'isO2x' => true,
        ];

        $result = $vehicleManager->mapSearchApiResults($data, false);

        $this->assertArrayHasKey('vin', $result);
        $this->assertEquals('test-vin', $result['vin']);
        $this->assertArrayHasKey('label', $result);
        $this->assertEquals('test-short-label', $result['label']);
        $this->assertArrayHasKey('visual', $result);
        $this->assertEquals('test-picture-url', $result['visual']);
        $this->assertArrayHasKey('isO2x', $result);
        $this->assertTrue($result['isO2x']);
        $this->assertArrayNotHasKey('lcdv', $result);
        $this->assertArrayNotHasKey('warrantyStartDate', $result);
    }

    public function testMapSearchApiResultsReturnsFullDetailData(): void
    {
        $corvetService = $this->createMock(CorvetService::class);
        $lvhService = $this->createMock(LabelVehiculeService::class);
        $visual3DManager = $this->createMock(Visual3DManager::class);

        $vehicleManager = new VehicleManager(
            $corvetService,
            $lvhService,
            $visual3DManager
        );

        $data = [
            'vin' => 'test-vin',
            'label' => 'test-short-label',
            'picture' => 'test-picture-url',
            'isO2x' => false,
            'lcdv' => 'test-lcdv',
            'warrantyStartDate' => 'test-warranty-start-date',
        ];

        $result = $vehicleManager->mapSearchApiResults($data, true);

        $this->assertArrayHasKey('vin', $result);
        $this->assertEquals('test-vin', $result['vin']);
        $this->assertArrayHasKey('label', $result);
        $this->assertEquals('test-short-label', $result['label']);
        $this->assertArrayHasKey('visual', $result);
        $this->assertEquals('test-picture-url', $result['visual']);
        $this->assertArrayHasKey('isO2x', $result);
        $this->assertFalse($result['isO2x']);
        $this->assertArrayHasKey('lcdv', $result);
        $this->assertEquals('test-lcdv', $result['lcdv']);
        $this->assertArrayHasKey('warrantyStartDate', $result);
        $this->assertEquals('test-warranty-start-date', $result['warrantyStartDate']);
    }

    public function testMapSearchApiResultsAsXPFormat()
    {
        $corvetService = $this->createMock(CorvetService::class);
        $lvhService = $this->createMock(LabelVehiculeService::class);
        $visual3DManager = $this->createMock(Visual3DManager::class);

        $vehicleManager = new VehicleManager(
            $corvetService,
            $lvhService,
            $visual3DManager
        );

        $data = [
            'vin' => 'VF30U9HD8ES290122',
            'lcdv' => '1PTBSYHBM604A0F1M09P05FX',
            'warrantyStartDate' => 1423612800,
            'picture' => 'https://visuel3d-secure.peugeot.com/V3DImage.ashx?client=MyMarque&format=png&color=0MM00N9P&trim=0P050RFX&back=0&width=1080&version=1PTBSYHBM604A0F1&view=001&OPT1=VD09&OPT2=WLWF&OPT3=ZD09&OPT4=ZH47'
        ];

        $expectedResult = [
            'vin' => 'VF30U9HD8ES290122',
            'lcdv' => '1PTBSYHBM604A0F1M09P05FX',
            'short_label' => null,
            'warranty_start_date' => 1423612800,
            'type_of_vehicle' => null,
            'elegibility' => null,
            'attributes' => null,
            'types' => null,
            'visual' => 'https://visuel3d-secure.peugeot.com/V3DImage.ashx?client=MyMarque&format=png&color=0MM00N9P&trim=0P050RFX&back=0&width=1080&version=1PTBSYHBM604A0F1&view=001&OPT1=VD09&OPT2=WLWF&OPT3=ZD09&OPT4=ZH47',
            'label' => null,
            'isO2x' => null,
        ];

        $result = $vehicleManager->mapSearchApiResultsAsXPFormat($data, true);

        $this->assertEquals($expectedResult, $result);
    }

    public function testRemoveNullValues(): void
    {
        $input = [
            'key1' => 'value1',
            'key2' => null,
            'key3' => 'value3',
            'key4' => [
                'nestedKey1' => 'nestedValue1',
                'nestedKey2' => null,
                'nestedKey3' => 'nestedValue3',
            ],
            'key5' => null,
        ];

        $expectedOutput = [
            'key1' => 'value1',
            'key3' => 'value3',
            'key4' => [
                'nestedKey1' => 'nestedValue1',
                'nestedKey3' => 'nestedValue3',
            ],
        ];

        $result = $this->vehicleManager->removeNullValues($input);

        $this->assertEquals($expectedOutput, $result);
    }

    public function testRemoveNullValuesARealCase(): void
    {
        $jsonContent = '{"success":[{"vin":"VIN123456789012345","lcdv":null,"visual":"http:\/\/example.com\/picture1.jpg","short_label":"My Car","warranty_start_date":null,"command":null,"sdp":"SDP1"},{"vin":"VIN987654321098765","lcdv":"LCDV2","visual":"http:\/\/example.com\/picture2.jpg","short_label":"Family Car","warranty_start_date":1234567890,"command":"command2","sdp":"SDP2"}]}';
        $input = json_decode($jsonContent, true);

        $expectedOutput = [
            'success' => [
                [
                    'vin' => 'VIN123456789012345',
                    'visual' => 'http://example.com/picture1.jpg',
                    'short_label' => 'My Car',
                    'sdp' => 'SDP1',
                ],
                [
                    'vin' => 'VIN987654321098765',
                    'lcdv' => 'LCDV2',
                    'visual' => 'http://example.com/picture2.jpg',
                    'short_label' => 'Family Car',
                    'warranty_start_date' => 1234567890,
                    'command' => 'command2',
                    'sdp' => 'SDP2',
                ],
            ],
        ];

        $result = $this->vehicleManager->removeNullValues($input);

        $this->assertEquals($expectedOutput, $result);
    }
}
