<?php

namespace App\Tests\Manager;

use App\Helper\SuccessResponse;
use App\Manager\Visual3DManager;
use App\Service\VehicleVisualService;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class Visual3DManagerTest extends KernelTestCase
{

    public function testLoadImages(): void
    {
        $data = (new SuccessResponse())->setData([
            'visualSettings' => [
                'with' => '',
                'height' => '',
                'visual' => '',
                'test' => ''
            ]
        ]);
        $vehicleVisualService = $this->createMock(VehicleVisualService::class);
        $vehicleVisualService->expects($this->once())
            ->method('getVisualVehicle')
            ->willReturn($data);
        $manager = new Visual3DManager($vehicleVisualService);

        $response = $manager->loadImages(
            '1PK0NNRKAQC0A0B0P0XYCYFX',
            'AP',
            'APP'
        );

        $this->assertArrayHasKey('success', $response);
    }

    public function testLoadImagesDefault(): void
    {
        $data = (new SuccessResponse())->setData([
            'visualSettings' => [
                'with' => '',
                'height' => '',
                'visual' => ''
            ]
        ]);
        $vehicleVisualService = $this->createMock(VehicleVisualService::class);
        $vehicleVisualService->expects($this->once())
            ->method('getVisualVehicle')
            ->willReturn($data);
        $manager = new Visual3DManager($vehicleVisualService);

        $response = $manager->loadImages(
            '',
            'AP',
            'APP'
        );

        $this->assertArrayHasKey('success', $response);
    }

    public function testLoadImagesWeb(): void
    {
        $data = (new SuccessResponse())->setData([
            'visualSettings' => [
                'with' => '',
                'height' => '',
                'visual' => 'www.test.ma'
            ]
        ]);
        $vehicleVisualService = $this->createMock(VehicleVisualService::class);
        $vehicleVisualService->expects($this->once())
            ->method('getVisualVehicle')
            ->willReturn($data);
        $manager = new Visual3DManager($vehicleVisualService);

        $response = $manager->loadImages(
            '',
            'AP',
            'WEB',
            ['xxx']
        );

        $this->assertArrayHasKey('success', $response);
    }
}
