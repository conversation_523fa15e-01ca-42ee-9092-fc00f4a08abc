<?php

namespace App\Tests\Helper;

use App\Helper\SecuredAccessor;
use PHPUnit\Framework\TestCase;

class SecuredAccessorTest extends TestCase
{
    public function testGetSecuredFromArray(): void
    {
        $helper = SecuredAccessor::getSecuredFromArray(
            'test',
            'test',
            'test'
        );
        $this->assertEquals('test', $helper);
    }

    public function testGetArrayByKeys(): void
    {
        $helper = SecuredAccessor::getArrayByKeys(
            [
                'test' => 'test',
                'key' => 'key'
            ],
            'test',
            'test'
        );

        $this->assertArrayHasKey('test', $helper);
    }
}
