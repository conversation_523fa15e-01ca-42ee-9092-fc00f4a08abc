<?php

namespace App\Tests\Controller;

use App\Controller\VehicleController;
use App\Helper\SuccessResponse;
use App\Manager\VehicleManager;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\ParameterBag;
use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class VehicleControllerTest extends KernelTestCase
{
    private VehicleController $controller;

    public function setUp(): void
    {
        self::bootKernel();
        $this->controller = new VehicleController();
        // adding the container to the tested controller
        $this->controller->setContainer($this->getContainer());
        parent::setUp();
    }

    public function testGetVehicleSuccess(): void
    {
        $request = $this->createMock(Request::class);
        $validator = $this->createMock(ValidatorInterface::class);
        $vehicleManager = $this->createMock(VehicleManager::class);
        $constraint = $this->createMock(ConstraintViolationListInterface::class);
        $vin = "vf3veahhwfz059259";
        $request->headers = new ParameterBag([
            'vin' => $vin
        ]);
        $request->query = new ParameterBag(
            [
                'source' => 'APP'
            ]
        );

        $validator->expects($this->once())
            ->method('validate')
            ->willReturn($constraint);

        $vehicleManager->expects($this->once())
            ->method('getVehicle')
            ->willReturn((new SuccessResponse())
                ->setCode(Response::HTTP_OK));
        $this->getContainer()
            ->get('request_stack')
            ->push($request);
        $response = $this->controller->getVehicle(
            $request,
            $validator,
            $vehicleManager
        );
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
    }

    public function getVehicleErrorProvider(): array
    {
        return [
            ['vin', Response::HTTP_NOT_FOUND],
            ['any_other', Response::HTTP_UNPROCESSABLE_ENTITY],
        ];
    }
    
    /**
     * @dataProvider getVehicleErrorProvider
     */
    public function testGetVehicleError(string $propertyName, int $httpResponseCode): void
    {
        $request = $this->createMock(Request::class);
        $validator = $this->createMock(ValidatorInterface::class);
        $vehicleManager = $this->createMock(VehicleManager::class);
        $constraint = (new ConstraintViolationList());
        $constraint->add(new ConstraintViolation(
            'This value is not valid.',
            'This value',
            [],
            null,
            $propertyName,
            'invalidValue'
        ));
        $vin = "vf3veahhwfz059259";
        $request->headers = new ParameterBag([
            'vin' => $vin
        ]);
        $request->query = new ParameterBag(
            [
                'source' => 'APP'
            ]
        );

        $validator->expects($this->once())
            ->method('validate')
            ->willReturn($constraint);

        $this->getContainer()
            ->get('request_stack')
            ->push($request);
        $response = $this->controller->getVehicle(
            $request,
            $validator,
            $vehicleManager
        );

        $this->assertEquals($httpResponseCode, $response->getStatusCode());
    }
}
