<?php

namespace App\Tests\Service;

use App\Helper\SuccessResponse;
use App\Service\MongoAtlasApiConnector;
use App\Service\MongoAtlasQueryService;
use Exception;
use Monolog\Logger;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;

class MongoAtlasQueryServiceTest extends KernelTestCase
{
    private MongoAtlasApiConnector $mongoAtlasApiConnector;
    private NormalizerInterface $normalizerInterface;
    private LoggerInterface $logger;

    public function setUp(): void
    {
        $this->normalizerInterface = $this->createMock(NormalizerInterface::class);
        $this->mongoAtlasApiConnector = $this->createMock(MongoAtlasApiConnector::class);
        $this->logger = $this->createMock(Logger::class);

        self::bootKernel();
    }

    public function testFindSuccess(): void
    {
        $dataSource = '';
        $database = '';

        $service = new MongoAtlasQueryService(
            $this->normalizerInterface,
            $this->mongoAtlasApiConnector,
            $dataSource,
            $database
        );
        $service->setLogger($this->logger);
        $service::buildQuery('', '');
        $find = $service->find('', []);
        $this->assertInstanceOf(SuccessResponse::class, $find);
    }

    public function testFindFailed(): void
    {
        $dataSource = '';
        $database = '';

        $this->mongoAtlasApiConnector
            ->method('call')
            ->willThrowException(new Exception());

        $service = new MongoAtlasQueryService(
            $this->normalizerInterface,
            $this->mongoAtlasApiConnector,
            $dataSource,
            $database
        );

        $service->setLogger($this->logger);
        $service::buildQuery('', '');
        $this->expectException(Exception::class);
        $service->find('', []);
    }

    public function testAggregateSuccess(): void
    {
        $dataSource = '';
        $database = '';

        $service = new MongoAtlasQueryService(
            $this->normalizerInterface,
            $this->mongoAtlasApiConnector,
            $dataSource,
            $database
        );

        $service->setLogger($this->logger);
        $service::buildQuery('', '');
        $find = $service->aggregate('', []);
        $this->assertInstanceOf(SuccessResponse::class, $find);
    }

    public function testAggregateFailed(): void
    {
        $dataSource = '';
        $database = '';

        $this->mongoAtlasApiConnector
            ->method('call')
            ->willThrowException(new Exception());

        $service = new MongoAtlasQueryService(
            $this->normalizerInterface,
            $this->mongoAtlasApiConnector,
            $dataSource,
            $database
        );

        $service->setLogger($this->logger);
        $service::buildQuery('', '');
        $this->expectException(Exception::class);
        $service->aggregate('', []);
    }
}
