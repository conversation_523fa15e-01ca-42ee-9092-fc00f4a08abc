<?php

namespace App\Tests\Service;

use App\Service\CacheParametersService;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class CacheParametersServiceTest extends KernelTestCase
{

    public function testFileExist(): void
    {
        $service = new CacheParametersService(
            __DIR__ . '/../../config/cache_parameters.yaml'
        );
        $response = $service->getCacheExpireDuration();
        $this->assertIsInt($response);
    }

    public function testFileNotExist(): void
    {
        $service = new CacheParametersService(
            __DIR__ . '/../../config/cache_parameterss.yaml'
        );
        $response = $service->getCacheExpireDuration();
        $this->assertIsInt($response);
    }
}
