<?php

namespace App\Tests\Service;

use App\Service\CorvetSysConnector;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Contracts\HttpClient\ResponseInterface;

class CorvetSysConnectorTest extends KernelTestCase
{

    public function testCall(): void
    {
        $client = $this->createMock(HttpClientInterface::class);
        $responseInterface = $this->createMock(ResponseInterface::class);
        $client->expects($this->once())
            ->method('request')
            ->willReturn($responseInterface);
        $url = '';
        $service = new CorvetSysConnector($url, $client);
        $corvet = $service->call(Request::METHOD_GET,'', []);
        $this->assertInstanceOf(ResponseInterface::class, $corvet);
    }
}
