<?php

namespace App\Tests\Service;

use App\Service\MongoAtlasApiConnector;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Contracts\HttpClient\ResponseInterface;

class MongoAtlasApiConnectorTest extends KernelTestCase
{

    public function testCall(): void
    {
        $client = $this->createMock(HttpClientInterface::class);
        $responseInterface = $this->createMock(ResponseInterface::class);
        $client->expects($this->once())
            ->method('request')
            ->willReturn($responseInterface);

        $service = new MongoAtlasApiConnector($client, '');
        $mongoAtlas = $service->call('', '');
        $service->getEndpoint('');
        $this->assertInstanceOf(ResponseInterface::class, $mongoAtlas);
    }
}
