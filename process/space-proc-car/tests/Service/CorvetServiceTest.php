<?php

namespace App\Tests\Service;

use App\Service\CacheAdapter;
use App\Service\CacheParametersService;
use App\Service\CorvetService;
use App\Service\CorvetSysConnector;
use Exception;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Cache\Adapter\FilesystemAdapter;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\HttpClient\ResponseInterface;

class CorvetServiceTest extends KernelTestCase
{
    const LCDV = '1PK0NNRKAQC0A0B0P0XYCYFX';
    const DATE = '2020/12/12';
    const ATTR = '@attributes';

    public function testGetLcdvSuccess(): void
    {
        $corvetSysConnector = $this->createMock(CorvetSysConnector::class);
        $cacheParametersService = $this->createMock(CacheParametersService::class);
        $cache = new FilesystemAdapter('app.cache');
        $cacheAdapter = new CacheAdapter($cache);
        $cacheAdapter->delete('api_car_corvet_AP_FR__APP');
        $responseInterface = $this->createMock(ResponseInterface::class);
        $responseInterface->expects($this->once())
            ->method('getStatusCode')
            ->willReturn(Response::HTTP_OK);
        $responseInterface->expects($this->once())
            ->method('getContent')
            ->willReturn(json_encode([
                'success' => [
                    'VEHICULE' => [
                        'DONNEES_VEHICULE' => [
                            'LCDV_BASE' => self::LCDV,
                            'DATE_DEBUT_GARANTIE' => self::DATE,
                            'DATE_ENTREE_COMMERCIALISATION' => ''
                        ],
                        'LISTE_ATTRIBUTES_7' => [
                            'ATTRIBUT' => [
                                "DCX04CP",
                                "DCD02CP",
                                "DRE40CP",
                                "DRC07CP",
                                "DMW00CD",
                                "DVQ00CD",
                                "DJY00CD",
                                "D7K00CD",
                                "DME00CD",
                                "DE203CD",
                                "DZZ05CD",
                                "DLX18CD",
                                "DXD18CD"
                            ]
                        ],
                        self::ATTR => [
                            'Existe' => 'O'
                        ]
                    ]
                ]
            ]));

        $responseInterface->expects($this->once())
            ->method('toArray')
            ->willReturn([
                'success' => [
                    'VEHICULE' => [
                        'DONNEES_VEHICULE' => [
                            'LCDV_BASE' => self::LCDV,
                            'DATE_DEBUT_GARANTIE' => self::DATE,
                            'DATE_ENTREE_COMMERCIALISATION' => ''
                        ],
                        'LISTE_ATTRIBUTES_7' => [
                            'ATTRIBUT' => [
                                "DCX04CP",
                                "DCD02CP",
                                "DRE40CP",
                                "DRC07CP",
                                "DMW00CD",
                                "DVQ00CD",
                                "DJY00CD",
                                "D7K00CD",
                                "DME00CD",
                                "DE203CD",
                                "DZZ05CD",
                                "DLX18CD",
                                "DXD18CD"
                            ]
                        ],
                        self::ATTR => [
                            'Existe' => 'O'
                        ]
                    ]
                ]
            ]);

        $corvetSysConnector->expects($this->once())
            ->method('call')
            ->willReturn(
                $responseInterface
            );

        $service = new CorvetService(
            $corvetSysConnector,
            $cacheParametersService,
            $cacheAdapter
        );
        $logger = $this->createMock(LoggerInterface::class);
        $service->setLogger($logger);
        $brand = 'AP';
        $country = 'FR';
        $vin = '';

        $corvet = $service->getLcdv(
            $brand,
            $country,
            $vin,
            'APP',
            true
        );
        $this->assertArrayHasKey('success', $corvet);
    }

    public function testGetLcdvFailed(): void
    {
        $corvetSysConnector = $this->createMock(CorvetSysConnector::class);
        $cacheParametersService = $this->createMock(CacheParametersService::class);
        $cache = new FilesystemAdapter('app.cache');
        $cacheAdapter = new CacheAdapter($cache);
        $cacheAdapter->delete('api_car_corvet_AP_FR__APP');
        $responseInterface = $this->createMock(ResponseInterface::class);
        $responseInterface->expects($this->once())
            ->method('getStatusCode')
            ->willReturn(Response::HTTP_NOT_FOUND);

        $corvetSysConnector->expects($this->once())
            ->method('call')
            ->willReturn(
                $responseInterface
            );

        $service = new CorvetService(
            $corvetSysConnector,
            $cacheParametersService,
            $cacheAdapter
        );
        $logger = $this->createMock(LoggerInterface::class);
        $service->setLogger($logger);
        $brand = 'AP';
        $country = 'FR';
        $vin = '';
        $this->expectException(Exception::class);
        $service->getLcdv(
            $brand,
            $country,
            $vin,
            'APP',
            true
        );
    }

    public function testGetLcdvException(): void
    {
        $corvetSysConnector = $this->createMock(CorvetSysConnector::class);
        $cacheParametersService = $this->createMock(CacheParametersService::class);
        $cache = new FilesystemAdapter('app.cache');
        $cacheAdapter = new CacheAdapter($cache);
        $cacheAdapter->delete('api_car_corvet_AP_FR__APP');
        $responseInterface = $this->createMock(ResponseInterface::class);
        $responseInterface->expects($this->once())
            ->method('getStatusCode')
            ->willReturn(Response::HTTP_OK);
        $responseInterface->expects($this->once())
            ->method('getContent')
            ->willReturn(json_encode([
                'success' => [
                    'VEHICULE' => [
                        'DONNEES_VEHICULE' => [
                            'LCDV_BASE' => self::LCDV,
                            'DATE_DEBUT_GARANTIE' => self::DATE,
                            'DATE_ENTREE_COMMERCIALISATION' => ''
                        ],
                        'LISTE_ATTRIBUTES_7' => [
                            'ATTRIBUT' => ''
                        ],
                        self::ATTR => [
                            'Existe' => 'N'
                        ]
                    ]
                ]
            ]));

        $responseInterface->expects($this->once())
            ->method('toArray')
            ->willReturn([
                'success' => [
                    'VEHICULE' => [
                        'DONNEES_VEHICULE' => [
                            'LCDV_BASE' => self::LCDV,
                            'DATE_DEBUT_GARANTIE' => self::DATE,
                            'DATE_ENTREE_COMMERCIALISATION' => ''
                        ],
                        'LISTE_ATTRIBUTES_7' => [
                            'ATTRIBUT' => ''
                        ],
                        self::ATTR => [
                            'Existe' => 'N'
                        ]
                    ]
                ]
            ]);

        $corvetSysConnector->expects($this->once())
            ->method('call')
            ->willReturn(
                $responseInterface
            );

        $service = new CorvetService(
            $corvetSysConnector,
            $cacheParametersService,
            $cacheAdapter
        );
        $logger = $this->createMock(LoggerInterface::class);
        $service->setLogger($logger);
        $brand = 'AP';
        $country = 'FR';
        $vin = '';
        $this->expectException(Exception::class);
        $service->getLcdv(
            $brand,
            $country,
            $vin,
            'APP',
            true
        );
    }

    public function testGetLcdvAttr(): void
    {
        $corvetSysConnector = $this->createMock(CorvetSysConnector::class);
        $cacheParametersService = $this->createMock(CacheParametersService::class);
        $cache = new FilesystemAdapter('app.cache');
        $cacheAdapter = new CacheAdapter($cache);
        $cacheAdapter->delete('api_car_corvet_AP_FR__APP');
        $responseInterface = $this->createMock(ResponseInterface::class);
        $responseInterface->expects($this->once())
            ->method('getStatusCode')
            ->willReturn(Response::HTTP_OK);
        $responseInterface->expects($this->once())
            ->method('getContent')
            ->willReturn(json_encode([
                'success' => [
                    'VEHICULE' => [
                        'DONNEES_VEHICULE' => [
                            'LCDV_BASE' => self::LCDV,
                            'DATE_DEBUT_GARANTIE' => self::DATE,
                            'DATE_ENTREE_COMMERCIALISATION' => ''
                        ],
                        'LISTE_ATTRIBUTES_7' => [
                            'ATTRIBUT' => ''
                        ],
                        self::ATTR => [
                            'Existe' => 'O'
                        ]
                    ]
                ]
            ]));

        $responseInterface->expects($this->once())
            ->method('toArray')
            ->willReturn([
                'success' => [
                    'VEHICULE' => [
                        'DONNEES_VEHICULE' => [
                            'LCDV_BASE' => self::LCDV,
                            'DATE_DEBUT_GARANTIE' => self::DATE,
                            'DATE_ENTREE_COMMERCIALISATION' => ''
                        ],
                        'LISTE_ATTRIBUTES_7' => [
                            'ATTRIBUT' => ''
                        ],
                        self::ATTR => [
                            'Existe' => 'O'
                        ]
                    ]
                ]
            ]);

        $corvetSysConnector->expects($this->once())
            ->method('call')
            ->willReturn(
                $responseInterface
            );

        $service = new CorvetService(
            $corvetSysConnector,
            $cacheParametersService,
            $cacheAdapter
        );
        $logger = $this->createMock(LoggerInterface::class);
        $service->setLogger($logger);
        $brand = 'AP';
        $country = 'FR';
        $vin = '';

        $corvet = $service->getLcdv(
            $brand,
            $country,
            $vin,
            'APP',
            true
        );
        $this->assertArrayHasKey('success', $corvet);
    }
}
