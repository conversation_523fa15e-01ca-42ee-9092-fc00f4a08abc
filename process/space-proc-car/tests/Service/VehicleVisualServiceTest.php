<?php

namespace App\Tests\Service;

use App\Helper\SuccessResponse;
use App\Service\CacheAdapter;
use App\Service\CacheParametersService;
use App\Service\MongoAtlasQueryService;
use App\Service\VehicleVisualService;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Cache\Adapter\FilesystemAdapter;

class VehicleVisualServiceTest extends KernelTestCase
{
    public function testGetVisualVehicleSuccess(): void
    {
        $cacheParametersService = $this->createMock(CacheParametersService::class);
        $mongoAtlasQueryService = $this->createMock(MongoAtlasQueryService::class);
        $cache = new FilesystemAdapter('app.cache');
        $cacheAdapter = new CacheAdapter($cache);

        $mongoAtlasQueryService->expects($this->once())
            ->method('aggregate')
            ->willReturn(
                (new SuccessResponse())
                    ->setData(
                        [
                            'documents' => [
                                [
                                    'visualSettings' => [
                                        'visual' => 'www.test.ma',
                                        'vin' => '',
                                        'brand' => 'BRAND',
                                        'versionId' => ''
                                    ]
                                ]
                                
                            ]

                        ]
                    )
            );

        $service = new VehicleVisualService(
            $mongoAtlasQueryService,
            $cacheParametersService,
            $cacheAdapter,
            [
                'BRAND' => [
                    'baseUrl' => 'www.test.ma',
                ],
                'width' => 100,
                'height' => 200,
            ]
        );
        $logger = $this->createMock(LoggerInterface::class);
        $service->setLogger($logger);
        $response = $service->getVisualVehicle(
            'BRAND',
            '',
            ''
        );
        $cacheAdapter->get('api_car_visual_', function () {
            return true;
        });
        $cacheAdapter->delete('api_car_visual_');
        $this->assertArrayHasKey('success', $response->getArrayFormat());
    }

    public function testGetVisualVehicleNotExist(): void
    {
        $cacheParametersService = $this->createMock(CacheParametersService::class);
        $mongoAtlasQueryService = $this->createMock(MongoAtlasQueryService::class);
        $cache = new FilesystemAdapter('app.cache');
        $cacheAdapter = new CacheAdapter($cache);

        $mongoAtlasQueryService->expects($this->once())
            ->method('aggregate')
            ->willReturn(
                (new SuccessResponse())
                    ->setData(
                        [
                            'documents' => [
                                [
                                    
                                ]
                                
                            ]

                        ]
                    )
            );

        $service = new VehicleVisualService(
            $mongoAtlasQueryService,
            $cacheParametersService,
            $cacheAdapter,
            []
        );
        $logger = $this->createMock(LoggerInterface::class);
        $service->setLogger($logger);
        $response = $service->getVisualVehicle(
            '',
            '',
            ''
        );
        $cacheAdapter->get('api_car_visual_', function () {
            return true;
        });
        $cacheAdapter->delete('api_car_visual_');
        $this->assertArrayHasKey('success', $response->getArrayFormat());
    }
}
