<?php

namespace App\Tests\Service;

use App\Helper\SuccessResponse;
use App\Service\CacheAdapter;
use App\Service\CacheParametersService;
use App\Service\LabelVehiculeService;
use App\Service\MongoAtlasQueryService;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Cache\Adapter\FilesystemAdapter;

class LabelVehiculeServiceTest extends KernelTestCase
{

    public function testGetLvh(): void
    {
        $cacheParametersService = $this->createMock(CacheParametersService::class);
        $mongoAtlasQueryService = $this->createMock(MongoAtlasQueryService::class);
        $cache = new FilesystemAdapter('app.cache');
        $cacheAdapter = new CacheAdapter($cache);

        $jsonMockResult = '{"documents":[{"_id":"6781445e50c0b7378b159d7c","lcdv":["1PK0NNPKB"],"label":"TRAVELLER - PKB","isO2x":false,"sdp":"SSDP","bmvs":["BBMMMVVVS"],"visualSettings":{"view":"01","width":"400","defaultVisual":"https://www.ggogle.fr","type":"V3D","height":"400"}}]}';
        $mongoAtlasQueryService->expects($this->once())
            ->method('aggregate')
            ->willReturn(
                (new SuccessResponse())
                    ->setData(
                        json_decode($jsonMockResult, true)
                    )
            );


        $service = new LabelVehiculeService(
            $cacheParametersService,
            $cacheAdapter,
            $mongoAtlasQueryService
        );
        $logger = $this->createMock(LoggerInterface::class);
        $service->setLogger($logger);
        $brand = 'AP';
        $country = 'FR';
        $language = 'fr';
        $lcdv = '';

        $corvet = $service->getLvh(
            $brand,
            $country,
            $language,
            $lcdv
        );

        $result = $corvet->getArrayFormat();

        $this->assertArrayHasKey('success', $result);
        $this->assertArrayHasKey('lcdv', $result['success']);
        $this->assertIsArray($result['success']['lcdv']);
        $this->assertArrayHasKey('isO2x', $result['success']);
        $this->assertEquals($result['success']['isO2x'], false);
        $this->assertArrayHasKey('label', $result['success']);
        $this->assertEquals($result['success']['label'], 'TRAVELLER - PKB');

    }
}
