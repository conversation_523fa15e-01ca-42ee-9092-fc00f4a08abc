<?php

namespace App\Tests\Model;

use App\Model\MongoQueryModel;
use PHPUnit\Framework\TestCase;

class MongoQueryModelTest extends TestCase
{
    public function testGetCollection()
    {
        $mongoQueryModel = new MongoQueryModel();
        $title = 'Collection';

        $mongoQueryModel->setCollection($title);
        $result = $mongoQueryModel->getCollection();

        $this->assertEquals($title, $result);
    }

    public function testGetDatabase()
    {
        $mongoQueryModel = new MongoQueryModel();
        $title = 'Database';

        $mongoQueryModel->setDatabase($title);
        $result = $mongoQueryModel->getDatabase();

        $this->assertEquals($title, $result);
    }

    public function testGetDataSource()
    {
        $mongoQueryModel = new MongoQueryModel();
        $title = 'DataSource';

        $mongoQueryModel->setDataSource($title);
        $result = $mongoQueryModel->getDataSource();

        $this->assertEquals($title, $result);
    }

    public function testGetFilter()
    {
        $mongoQueryModel = new MongoQueryModel();
        $filter = ['filter' => []];

        $mongoQueryModel->setFilter($filter);
        $result = $mongoQueryModel->getFilter();

        $this->assertEquals($filter, $result);
    }

    public function testGetDocument()
    {
        $mongoQueryModel = new MongoQueryModel();
        $document = ['document' => []];

        $mongoQueryModel->setDocument($document);
        $result = $mongoQueryModel->getDocument();

        $this->assertEquals($document, $result);
    }

    public function testGetUpdate()
    {
        $mongoQueryModel = new MongoQueryModel();
        $update = ['update' => []];

        $mongoQueryModel->setUpdate($update);
        $result = $mongoQueryModel->getUpdate();

        $this->assertEquals($update, $result);
    }

    public function testGetPipeline()
    {
        $mongoQueryModel = new MongoQueryModel();
        $pipeline = ['pipeline' => []];

        $mongoQueryModel->setPipeline($pipeline);
        $result = $mongoQueryModel->getPipeline();

        $this->assertEquals($pipeline, $result);
    }

    public function testGetDocuments()
    {
        $mongoQueryModel = new MongoQueryModel();
        $documents = ['documents' => []];

        $mongoQueryModel->setDocuments($documents);
        $result = $mongoQueryModel->getDocuments();

        $this->assertEquals($documents, $result);
    }

    public function testGetUpsert()
    {
        $mongoQueryModel = new MongoQueryModel();
        $upsert = true;

        $mongoQueryModel->setUpsert($upsert);
        $result = $mongoQueryModel->getUpsert();

        $this->assertEquals($upsert, $result);
    }

    public function testGetLimit()
    {
        $mongoQueryModel = new MongoQueryModel();
        $limit = true;

        $mongoQueryModel->setLimit($limit);
        $result = $mongoQueryModel->getLimit();

        $this->assertEquals($limit, $result);
    }
}
