<?php

namespace App\Tests\EventListener;

use PHPUnit\Framework\TestCase;
use App\EventListener\ExceptionListener;
use ReflectionClass;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\ExceptionEvent;

class ExceptionListenerTest extends TestCase
{
    public function testOnKernelException()
    {
        $exception = new \Exception('Test Exception', 500);
        $reflectionClass = new ReflectionClass(ExceptionEvent::class);
        $event = $reflectionClass->newInstanceWithoutConstructor();
        $throwableProperty = $reflectionClass->getProperty('throwable');
        $throwableProperty->setAccessible(true);
        $throwableProperty->setValue($event, $exception);
        $listener = new ExceptionListener();
        $listener->onKernelException($event);
        $getResponseMethod = $reflectionClass->getMethod('getResponse');
        $getResponseMethod->setAccessible(true);
        $response = $getResponseMethod->invoke($event);

        // Asserting the response content
        $this->assertInstanceOf(Response::class, $response);
        $this->assertEquals(500, $response->getStatusCode());
    }
}
