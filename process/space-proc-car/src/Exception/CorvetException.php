<?php

declare(strict_types=1);

namespace App\Exception;

use Exception;
use Symfony\Component\HttpFoundation\Response;

/**
 * Class CorvetException
 * @package App\Exception
 */
class CorvetException extends Exception
{
    /**
     * Make a new exception.
     *
     * @return static
     */
    public static function make(): self
    {
        return new static('Ko Response from SI Corvet', Response::HTTP_INTERNAL_SERVER_ERROR);
    }
}
