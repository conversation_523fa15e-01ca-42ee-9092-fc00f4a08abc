<?php

declare(strict_types=1);

namespace App\Exception;

use Symfony\Component\HttpFoundation\Response;

/**
 * Class MongoAtlasUrlNotProvidedException
 *
 * @package App\Exception
 */
class MongoAtlasUrlNotProvidedException extends \InvalidArgumentException
{
    /**
     * Make a new exception.
     *
     * @return static
     */
    public static function make(): self
    {
        return new static('MongoAtlas error!', Response::HTTP_NOT_FOUND);
    }
}
