<?php

declare(strict_types=1);

namespace App\Exception;

use InvalidArgumentException;
use Symfony\Component\HttpFoundation\Response;

/**
 * Class VehicleNotFoundException
 * @package App\Exception
 */
class VehicleNotFoundException extends InvalidArgumentException
{
    /**
     * Make a new exception.
     *
     * @return static
     */
    public static function make(): self
    {
        return new static('Vehicle Not Found', Response::HTTP_NOT_FOUND);
    }
}
