<?php

namespace App\Trait;

use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\ConstraintViolationListInterface;

trait ValidationResponseTrait
{
    /**
     * Get the validation error messages.
     */
    protected static function getValidationMessages(ConstraintViolationListInterface $errors): array
    {
        $messages = [];

        if (0 === $errors->count()) {
            return $messages;
        }

        foreach ($errors as $error) {
            /** @var ConstraintViolation $error */
            $name = str_replace(['[', ']'], '', $error->getPropertyPath());
            $messages[$name] = $error->getMessage();
        }

        return $messages;
    }

    protected static function getValidationErrorResponse(array $messages, int $statusCode = Response::HTTP_UNPROCESSABLE_ENTITY): JsonResponse
    {
        return new JsonResponse([
            'success'  => false,
            'error'    => 'validation_failed',
            'status'   => $statusCode,
            'messages' => $messages,
        ], $statusCode);
    }
}
