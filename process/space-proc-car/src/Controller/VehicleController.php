<?php

namespace App\Controller;

use App\Manager\VehicleManager;
use App\Trait\ValidationResponseTrait;
use App\Validator\BrandValidator;
use App\Validator\CountryValidator;
use App\Validator\LanguageValidator;
use App\Validator\VinValidator;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;
use OpenApi\Attributes as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Component\Validator\Constraints as Assert;

#[Route('/v1')]
class VehicleController extends AbstractController
{
    use ValidationResponseTrait;

    #[Route('/vehicle/search', name: 'get_vehicle_search', methods: [Request::METHOD_GET])]
    #[OA\Get(
        path: '/v1/vehicle/search',
        summary: 'Get vehicle details',
        tags: ['Vehicle'],
        parameters: [
            new OA\Parameter(
                name: 'vin',
                in: 'header',
                description: 'VIN',
                required: true,
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'brand',
                in: 'query',
                description: 'Brand',
                required: true,
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'country',
                in: 'query',
                description: 'Country',
                required: true,
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'language',
                in: 'query',
                description: 'Language',
                required: true,
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'source',
                in: 'query',
                description: 'Source',
                required: false,
                schema: new OA\Schema(
                    type: 'string',
                    enum: ['APP', 'WEB'],
                    default: 'APP'
                ),
            ),
            new OA\Parameter(
                name: 'isfullDetail',
                in: 'query',
                description: 'Full Detail',
                required: false,
                schema: new OA\Schema(type: 'boolean', default: false),
            )
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Successful Response',
                content: new OA\JsonContent(
                    type: 'object',
                    properties: [
                        new OA\Property(
                            property: 'success',
                            type: 'object',
                            properties: [
                                new OA\Property(property: 'vin', type: 'string'),
                                new OA\Property(property: 'lcdv', type: 'string', nullable: true),
                                new OA\Property(property: 'short_label', type: 'string', nullable: true),
                                new OA\Property(property: 'warranty_start_date', type: 'string'),
                                new OA\Property(property: 'type_vehicle', type: 'integer'),
                                new OA\Property(
                                    property: 'eligibility',
                                    type: 'array',
                                    items: new OA\Items(type: 'string'),
                                    nullable: true
                                ),
                                new OA\Property(
                                    property: 'attributes',
                                    type: 'array',
                                    items: new OA\Items(type: 'string'),
                                    nullable: true
                                ),
                                new OA\Property(
                                    property: 'types',
                                    type: 'array',
                                    items: new OA\Items(type: 'string'),
                                    nullable: true
                                ),
                                new OA\Property(property: 'visual', type: 'string'),
                                new OA\Property(property: 'label', type: 'string', nullable: true),
                                new OA\Property(property: 'isO2x', type: 'boolean', nullable: true)
                            ]
                        )
                    ]
                )
            ),
            new OA\Response(
                response: 404,
                description: 'Vehicle Not Found',
            ),
            new OA\Response(
                response: 400,
                description: 'Bad Request'
            )
        ]
    )]
    public function getVehicle(
        Request $request,
        ValidatorInterface $validator,
        VehicleManager $vehicleManager
    ) {   
        $vin = $request->headers->get('vin', '');
        $brand = $request->query->get('brand', '');
        $country = $request->query->get('country', '');
        $language = $request->query->get('language', '');
        $source = $request->query->get('source', 'APP');
        $isfullDetail = filter_var(
            $request->query->get('isfullDetail', true),
            FILTER_VALIDATE_BOOLEAN
        );

        $errors = $validator->validate(
            compact('vin', 'brand', 'country', 'language'),
            new Assert\Collection([
                'vin' => VinValidator::getConstraints(),
                'brand' => BrandValidator::getConstraintsForBrand(),
                'country' => CountryValidator::getConstraintsForCountry(),
                'language' => LanguageValidator::getConstraintsForLanguage(),
            ])
        );
        $messages = $this->getValidationMessages($errors);
        if (!empty($messages)) {

            // return a 404 response if the vin is not found
            foreach ($messages as $propertyName => $message) {                

                if ($propertyName === 'vin') {
                    return $this->getValidationErrorResponse($messages, Response::HTTP_NOT_FOUND);
                }
            }
            
            return $this->getValidationErrorResponse($messages);
        }

        return $vehicleManager->getVehicle(
            compact(
                'brand',
                'country',
                'language',
                'vin',
                'source',
                'isfullDetail'
            )
        )->getJsonFormat();
    }
}
