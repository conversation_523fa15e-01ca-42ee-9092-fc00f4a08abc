<?php

namespace App\Service;

use Symfony\Component\Yaml\Yaml;
use Symfony\Contracts\Service\Attribute\Required;

class CacheParametersService
{
    /**
     * CacheParametersService constructor.
     * @param string $filePath
     */
    #[Required]
    public function __construct(private string $filePath)
    {
    }

    /**
     * Get Cache expired duration .
     * @return int
     */
    public function getCacheExpireDuration(): int
    {
        if (file_exists($this->filePath)) {
            $dataStructure = Yaml::parseFile($this->filePath);
            if (!empty($dataStructure))
            {
                return $dataStructure['cache_parameters']['expire_duration'];
            }
        }
        return 86400;
    }
}
