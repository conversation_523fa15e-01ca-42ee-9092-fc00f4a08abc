<?php

namespace App\Service;

use Psr\Cache\CacheItemPoolInterface;
use Symfony\Contracts\Cache\CacheInterface;

/**
 * Adapter Used to avoid symfony lock Component acquire and release problems
 * And adapt PSR-16 to PSR-6
 */
class CacheAdapter implements CacheInterface
{
    /**
     * @param CacheItemPoolInterface $cache
     */
    public function __construct(private CacheItemPoolInterface $cache)
    {
    }

    /**
     * Method to adapt PSR-16 to PSR-6 when getting data
     *
     * @param string $key
     * @param callable $callback
     * @param float $beta
     * @param array $metadata
     * @return mixed
     */
    public function get(string $key, callable $callback, float $beta = null, array &$metadata = null): mixed
    {
        $item = $this->cache->getItem($key);
        if (!$item->isHit()) {
            $result = $callback($item);
            $this->cache->save($item->set($result));
        }
        return $item->get();
    }

    /**
     * Delete a cache
     * @param string $key
     * @return boolean
     */
    public function delete(string $key): bool
    {
        $item = $this->cache->getItem($key);
        if (!$item->isHit()) {
            return true;
        }
        return $this->cache->deleteItem($key);
    }
}
