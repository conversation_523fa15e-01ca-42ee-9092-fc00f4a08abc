<?php


namespace App\Manager;

use App\Helper\BrandHelper;
use App\Helper\SecuredAccessor;
use App\Service\VehicleVisualService;
use Psr\Http\Message\ResponseInterface;
use Symfony\Component\HttpFoundation\Response;

class Visual3DManager
{

    const VERSION_RIGHT_LIMIT = 16;

    const COLOR_RIGHT_LIMIT = 20;

    const TRIM_RIGHT_LIMIT = 24;

    /**
     * @var array
     */
    private $params;

    /**
     * Visual3DManager constructor.
     */
    public function __construct(
        private VehicleVisualService $visualVehicleService
    ) {
        $this->params = [
            'client' => 'MyMarqueWEB',
        ];
    }

    public function setDefaultParameters()
    {
        $this->params['back'] = 0;
    }

    /**
     * @param $lcdv
     */
    private function setQueryParameters($lcdv)
    {
        $this->setVersion($lcdv);
        $this->setColor($lcdv);
        $this->setTrim($lcdv);
        $this->setDefaultParameters();
    }

    /**
     * @param $lcdv
     */
    private function setVersion($lcdv)
    {
        if (strlen($lcdv) >= self::VERSION_RIGHT_LIMIT) {
            $this->params['version'] = substr($lcdv, 0, self::VERSION_RIGHT_LIMIT);
        } else {
            $this->params['version'] = '';
        }
    }

    /**
     * @param $lcdv
     */
    private function setColor($lcdv)
    {
        if (strlen($lcdv) >= self::COLOR_RIGHT_LIMIT) {
            $this->params['color'] = '0M' . substr($lcdv, self::COLOR_RIGHT_LIMIT - 4, 2) . '0N' . substr($lcdv, self::COLOR_RIGHT_LIMIT - 2, 2);
        } else {
            $this->params['color'] = '0M0N';
        }
    }

    private function setTrim(string $lcdv)
    {
        if (strlen($lcdv) >= self::TRIM_RIGHT_LIMIT) {
            $this->params['trim'] = '0P' . substr($lcdv, self::TRIM_RIGHT_LIMIT - 4, 2) . '0R' . substr($lcdv, self::TRIM_RIGHT_LIMIT - 2, 2);
        } else {
            $this->params['trim'] = '0P0R';
        }
    }

    /**
     * @return array|mixed|ResponseInterface|null
     */
    public function loadImages(
        string $lcdv,
        string $brand,
        string $source,
        ?array $vehicleOptions = []
    ) {
        if (!BrandHelper::isOV($brand)) {
            $this->setQueryParameters($lcdv);
        }

        $visualResponse = $this->visualVehicleService->getVisualVehicle(
            $brand,
            $lcdv
        );

        $visualSettings = $visualResponse->getArrayFormat()['success']['visualSettings'] ?? [];
        $url = SecuredAccessor::getSecuredFromArray(
            $visualSettings,
            'visual',
            SecuredAccessor::getSecuredFromArray(
                $visualSettings,
                'defaultVisual',
                ''
            )
        );

        $url = $this->addQueriesParams($url, $visualSettings);
        $url = $this->addParams($url, $this->params);

        if (
            !BrandHelper::isOV($brand) &&
            $url &&
            $vehicleOptions &&
            (
                'WEB' == strtoupper($source) ||
                false !== strpos(parse_url($url, PHP_URL_QUERY), 'view=')
            )
        ) {
            $urlParams = [];
            foreach ($vehicleOptions as $key => $option) {
                $urlParams['OPT' . ++$key] = $option;
            }
            $url = $this->addParams($url, $urlParams);
        }

        return [
            "success" => true,
            "code" => Response::HTTP_OK,
            "data" => $url
        ];
    }

    private function addQueriesParams(string $url, array $data) 
    {
        $urlParams = SecuredAccessor::getArrayByKeys($data, 'view', 'width', 'height');
        $url = $urlParams ? $this->addParams($url, $urlParams) : $url;
        return $url;
    }

    private function addParams($url, $params)
    {
        if (str_contains($url, '?') != FALSE) {
            $url .= '&' . http_build_query($params);
        } else {
            $url .= '?' . http_build_query($params);
        }

        return $url;
    }
}
