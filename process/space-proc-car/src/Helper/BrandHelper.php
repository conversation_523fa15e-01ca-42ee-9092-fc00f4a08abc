<?php

declare(strict_types=1);

namespace App\Helper;

class BrandHelper
{
    public const OV_BRAND = ['OP', 'VX'];
    public function __construct(private array $xpBrands)
    {
    }

    public function isXpBrand(?string $brand): bool
    {
        return in_array(trim(strtoupper($brand ?? '')), $this->xpBrands);
    }

    public static function isOV($brand)
    {
        return in_array($brand, self::OV_BRAND);
    }
}
