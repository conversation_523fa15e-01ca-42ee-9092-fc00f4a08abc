<?php

namespace App\Helper;

class VehicleTypeEntities
{

    const CORVET_TYPES = [
        'DXD04CD' => 'ELECTRIC',
        'DXD03CD' => 'HYBRID',
        'DXD06CD' => 'HYDROGEN',
        'DXD05CD' => 'MHEV'
    ];

    const TYPES = [
        '00' => 'ICE',
        '02' => 'HEV',
        '03' => 'PHEV',
        '04' => 'BEV',
        '05' => 'MHEV',
        '06' => 'HFCV',
    ];

    // used for feature codes based on the above defined types
    const ENGINE_TYPES = [
        'ICE' => 'ICE',
        'HEV' => 'HEV',
        'PHEV' => 'PHEV',
        'BEV' => 'BEV',
        'MHEV' => 'MHEV',
        'HFCV' => 'HYDROGEN'
    ];

    public static function getType(string $attributCode, array $corvetAttributes = [])
    {
        foreach ($corvetAttributes as $value) {
            $rest = substr($value, 0, 3);
            if($rest === $attributCode){
                return substr($value, 3, 2);
            }
        }
        return -1;
    }

    public static function getTypes($corvetAttributes)
    {
        $types = [];
        array_walk($corvetAttributes, function($value) use (&$types) {
            if (isset(self::CORVET_TYPES[trim($value)])) {
                $types[] = self::CORVET_TYPES[trim($value)];
            }
        });
        return array_unique($types);
    }
}
