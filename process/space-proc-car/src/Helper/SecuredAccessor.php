<?php

namespace App\Helper;

class SecuredAccessor
{

    /**
     * Minimise the code complexity
     */
    public static function getSecuredFromArray($data, $key, $defaultValue = null)
    {
        if (!is_array($data)) {
            return $defaultValue;
        }
        return $data[$key] ?? $defaultValue;
    }

    public static function removeKeysIfExist(array $data, ...$keys)
    {
        foreach ($keys as $key) {
            if (isset($data[$key])) {
                unset($data[$key]);
            }
        }
        return $data;
    }

    public static function getArrayByKeys(array $data, ...$keys)
    {
        $keyData = [];
        foreach ($keys as $key) {
            if (isset($data[$key])) {
                $keyData[$key] = $data[$key];
            }
        }
        return $keyData;
    }
}
