<?php

namespace App\Tests\DataMapper;

use App\DataMapper\CatalogDataMapper;
use PHPUnit\Framework\TestCase;

class CatalogDataMapperTest extends TestCase
{
    private CatalogDataMapper $catalogDataMapper;

    protected function setUp(): void
    {
        $this->catalogDataMapper = new CatalogDataMapper();
    }

    public function testSetItem(): void
    {
        $item = ['id' => 'test-id'];
        $result = $this->catalogDataMapper->setItem($item);

        $this->assertInstanceOf(CatalogDataMapper::class, $result);
    }

    public function testTransform(): void
    {
        $item = [
            'id' => 'test-id',
            'marketingProductSheetId' => 'test-sheet-id',
            'type' => 'product',
            'isBundle' => true,
            'bundleId' => 'bundle-id',
            'offers' => [
                [
                    'pricingModel' => 'OneTime',
                    'fromPrice' => 100,
                    'prices' => [
                        [
                            'price' => 100,
                            'currency' => 'EUR',
                            'periodType' => 'monthly',
                            'typeDiscount' => 'none'
                        ]
                    ]
                ]
            ]
        ];

        $data = [
            'title' => 'Test Title',
            'shortDescription' => 'Test Short Description',
            'homePage' => 'Test Home Page',
            'productUrl' => 'Test Product URL',
            'productUrlSso' => 'Test Product URL SSO',
            'fullDescription' => 'Test Full Description',
            'topMainImage' => 'Test Top Main Image',
            'productUrlCvs' => 'Test Product URL CVS'
        ];

        $this->catalogDataMapper->setItem($item);
        $result = $this->catalogDataMapper->transform($data);

        $this->assertIsArray($result);
        $this->assertEquals('test-id', $result['catalogId']);
        $this->assertEquals('test-sheet-id', $result['marketingProductSheetId']);
        $this->assertEquals(100, $result['price']);
        $this->assertEquals('Test Title', $result['title']);
        $this->assertEquals('Test Short Description', $result['shortDescription']);
        $this->assertEquals('Test Home Page', $result['homePage']);
        $this->assertEquals('Test Product URL', $result['productUrl']);
        $this->assertEquals('Test Product URL SSO', $result['productUrlSso']);
        $this->assertEquals('Test Full Description', $result['fullDescription']);
        $this->assertEquals('Test Top Main Image', $result['topMainImage']);
        $this->assertEquals('Test Product URL CVS', $result['urlCvs']);
        $this->assertEquals('product', $result['type']);
        $this->assertTrue($result['isBundle']);
        $this->assertEquals('bundle-id', $result['bundleId']);
    }

    public function testTransformWithBundleType(): void
    {
        $item = [
            'id' => 'test-id',
            'marketingProductSheetId' => 'test-sheet-id',
            'type' => 'BUNDLE',
            'offers' => [
                [
                    'pricingModel' => 'OneTime',
                    'fromPrice' => 100,
                    'prices' => [
                        [
                            'price' => 100,
                            'currency' => 'EUR',
                            'periodType' => 'monthly',
                            'typeDiscount' => 'none'
                        ]
                    ]
                ]
            ]
        ];

        $data = [
            'title' => 'Test Title',
            'productUrl' => 'Test Product URL',
            'productUrlSso' => 'Test Product URL SSO',
            'fullDescription' => 'Test Full Description'
        ];

        $this->catalogDataMapper->setItem($item);
        $result = $this->catalogDataMapper->transform($data);

        $this->assertEquals('bundle', $result['type']);
    }

    public function testTransformWithMissingOptionalFields(): void
    {
        $item = [
            'id' => 'test-id',
            'marketingProductSheetId' => 'test-sheet-id',
            'type' => 'product',
            'offers' => [
                [
                    'pricingModel' => 'OneTime',
                    'fromPrice' => 0,
                    'prices' => []
                ]
            ]
        ];

        $data = [
            'title' => 'Test Title',
            'productUrl' => 'Test Product URL',
            'productUrlSso' => 'Test Product URL SSO',
            'fullDescription' => 'Test Full Description'
        ];

        $this->catalogDataMapper->setItem($item);
        $result = $this->catalogDataMapper->transform($data);

        $this->assertIsArray($result);
        $this->assertEquals('test-id', $result['catalogId']);
        $this->assertEquals('test-sheet-id', $result['marketingProductSheetId']);
        $this->assertEquals(0, $result['price']);
        $this->assertEquals('Test Title', $result['title']);
        $this->assertEquals('', $result['shortDescription']);
        $this->assertEquals('', $result['homePage']);
        $this->assertEquals('Test Product URL', $result['productUrl']);
        $this->assertEquals('Test Product URL SSO', $result['productUrlSso']);
        $this->assertEquals('Test Full Description', $result['fullDescription']);
        $this->assertEquals('', $result['topMainImage']);
        $this->assertEquals('', $result['urlCvs']);
        $this->assertEquals('product', $result['type']);
        $this->assertFalse($result['isBundle']);
        $this->assertNull($result['bundleId']);
    }

    public function testGetPriceWithArrayOffers(): void
    {
        $item = [
            'id' => 'test-id',
            'offers' => [
                [
                    'prices' => [
                        [
                            'price' => 100,
                            'currency' => 'EUR'
                        ]
                    ]
                ]
            ]
        ];

        $this->catalogDataMapper->setItem($item);

        // Use reflection to access private method
        $reflectionClass = new \ReflectionClass(CatalogDataMapper::class);
        $method = $reflectionClass->getMethod('getPrice');
        $method->setAccessible(true);

        $result = $method->invoke($this->catalogDataMapper);

        $this->assertIsArray($result);
        $this->assertEquals(100, $result['amount']);
        $this->assertEquals('EUR', $result['currency']);
    }

    public function testGetPriceWithObjectOffers(): void
    {
        $this->markTestSkipped('This test requires more complex mocking');
    }

    public function testGetPriceWithNoOffers(): void
    {
        $item = [
            'id' => 'test-id'
        ];

        $this->catalogDataMapper->setItem($item);

        // Use reflection to access private method
        $reflectionClass = new \ReflectionClass(CatalogDataMapper::class);
        $method = $reflectionClass->getMethod('getPrice');
        $method->setAccessible(true);

        $result = $method->invoke($this->catalogDataMapper);

        $this->assertIsArray($result);
        $this->assertEquals(0, $result['amount']);
        $this->assertEquals('', $result['currency']);
    }

    public function testGetPriceFromOfferWithArrayPrices(): void
    {
        $offer = [
            'prices' => [
                [
                    'price' => 100,
                    'currency' => 'EUR'
                ]
            ]
        ];

        // Use reflection to access private method
        $reflectionClass = new \ReflectionClass(CatalogDataMapper::class);
        $method = $reflectionClass->getMethod('getPriceFromOffer');
        $method->setAccessible(true);

        $result = $method->invoke($this->catalogDataMapper, $offer);

        $this->assertIsArray($result);
        $this->assertEquals(100, $result['amount']);
        $this->assertEquals('EUR', $result['currency']);
    }

    public function testGetPriceFromOfferWithObjectPrices(): void
    {
        $this->markTestSkipped('This test requires more complex mocking');
    }

    public function testGetPriceFromOfferWithNoPrices(): void
    {
        $offer = [];

        // Use reflection to access private method
        $reflectionClass = new \ReflectionClass(CatalogDataMapper::class);
        $method = $reflectionClass->getMethod('getPriceFromOffer');
        $method->setAccessible(true);

        $result = $method->invoke($this->catalogDataMapper, $offer);

        $this->assertIsArray($result);
        $this->assertEquals(0, $result['amount']);
        $this->assertEquals('', $result['currency']);
    }

    public function testGetOfferWithPriceWithPriorityOffers(): void
    {
        $item = [
            'id' => 'test-id',
            'offers' => [
                [
                    'priority' => 2,
                    'pricingModel' => 'Periodical',
                    'fromPrice' => 100,
                    'isFreetrial' => true,
                    'freeTrialDuration' => 30,
                    'freeTrialDurationType' => 'days',
                    'prices' => [
                        [
                            'periodType' => 'monthly',
                            'price' => 100,
                            'currency' => 'EUR',
                            'typeDiscount' => 'none'
                        ]
                    ]
                ],
                [
                    'priority' => 1,
                    'pricingModel' => 'OneTime',
                    'fromPrice' => 200,
                    'prices' => [
                        [
                            'periodType' => 'yearly',
                            'price' => 200,
                            'currency' => 'USD',
                            'typeDiscount' => 'discount'
                        ]
                    ]
                ]
            ]
        ];

        $this->catalogDataMapper->setItem($item);

        // Use reflection to access private method
        $reflectionClass = new \ReflectionClass(CatalogDataMapper::class);
        $method = $reflectionClass->getMethod('getOfferWithPrice');
        $method->setAccessible(true);

        $result = $method->invoke($this->catalogDataMapper);

        $this->assertIsArray($result);
        $this->assertEquals('OneTime', $result['pricingModel']);
        $this->assertEquals(200, $result['fromPrice']);
        $this->assertEquals('yearly', $result['price']['periodType']);
        $this->assertEquals(200, $result['price']['price']);
        $this->assertEquals('USD', $result['price']['currency']);
        $this->assertEquals('discount', $result['price']['typeDiscount']);
    }

    public function testGetOfferWithPriceWithPeriodicalOffer(): void
    {
        $item = [
            'id' => 'test-id',
            'offers' => [
                [
                    'pricingModel' => 'Periodical',
                    'fromPrice' => 100,
                    'prices' => [
                        [
                            'periodType' => 'monthly',
                            'price' => 100,
                            'currency' => 'EUR',
                            'typeDiscount' => 'none'
                        ]
                    ]
                ],
                [
                    'pricingModel' => 'OneTime',
                    'fromPrice' => 200,
                    'prices' => [
                        [
                            'periodType' => 'yearly',
                            'price' => 200,
                            'currency' => 'USD',
                            'typeDiscount' => 'discount'
                        ]
                    ]
                ]
            ]
        ];

        $this->catalogDataMapper->setItem($item);

        // Use reflection to access private method
        $reflectionClass = new \ReflectionClass(CatalogDataMapper::class);
        $method = $reflectionClass->getMethod('getOfferWithPrice');
        $method->setAccessible(true);

        $result = $method->invoke($this->catalogDataMapper);

        $this->assertIsArray($result);
        $this->assertEquals('Periodical', $result['pricingModel']);
        $this->assertEquals(100, $result['fromPrice']);
        $this->assertEquals('monthly', $result['price']['periodType']);
        $this->assertEquals(100, $result['price']['price']);
        $this->assertEquals('EUR', $result['price']['currency']);
        $this->assertEquals('none', $result['price']['typeDiscount']);
    }

    public function testGetOfferWithPriceWithFirstOffer(): void
    {
        $item = [
            'id' => 'test-id',
            'offers' => [
                [
                    'pricingModel' => 'OneTime',
                    'fromPrice' => 200,
                    'prices' => [
                        [
                            'periodType' => 'yearly',
                            'price' => 200,
                            'currency' => 'USD',
                            'typeDiscount' => 'discount'
                        ]
                    ]
                ]
            ]
        ];

        $this->catalogDataMapper->setItem($item);

        // Use reflection to access private method
        $reflectionClass = new \ReflectionClass(CatalogDataMapper::class);
        $method = $reflectionClass->getMethod('getOfferWithPrice');
        $method->setAccessible(true);

        $result = $method->invoke($this->catalogDataMapper);

        $this->assertIsArray($result);
        $this->assertEquals('OneTime', $result['pricingModel']);
        $this->assertEquals(200, $result['fromPrice']);
        $this->assertEquals('yearly', $result['price']['periodType']);
        $this->assertEquals(200, $result['price']['price']);
        $this->assertEquals('USD', $result['price']['currency']);
        $this->assertEquals('discount', $result['price']['typeDiscount']);
    }

    public function testGetOfferWithPriceWithNoOffers(): void
    {
        $item = [
            'id' => 'test-id'
        ];

        $this->catalogDataMapper->setItem($item);

        // Use reflection to access private method
        $reflectionClass = new \ReflectionClass(CatalogDataMapper::class);
        $method = $reflectionClass->getMethod('getOfferWithPrice');
        $method->setAccessible(true);

        $result = $method->invoke($this->catalogDataMapper);

        $this->assertIsArray($result);
        $this->assertEquals('', $result['pricingModel']);
        $this->assertEquals(0, $result['fromPrice']);
        $this->assertEquals('', $result['price']['periodType']);
        $this->assertEquals(0, $result['price']['price']);
        $this->assertEquals('', $result['price']['currency']);
        $this->assertEquals('', $result['price']['typeDiscount']);
    }

    public function testGetOffrePeriodical(): void
    {
        $offers = [
            [
                'pricingModel' => 'OneTime'
            ],
            [
                'pricingModel' => 'Periodical'
            ]
        ];

        // Use reflection to access private method
        $reflectionClass = new \ReflectionClass(CatalogDataMapper::class);
        $method = $reflectionClass->getMethod('getOffrePeriodical');
        $method->setAccessible(true);

        $result = $method->invoke($this->catalogDataMapper, $offers);

        $this->assertIsArray($result);
        $this->assertEquals('Periodical', $result['pricingModel']);
    }

    public function testGetOffrePeriodicalWithNoPeriodical(): void
    {
        $offers = [
            [
                'pricingModel' => 'OneTime'
            ],
            [
                'pricingModel' => 'AnotherModel'
            ]
        ];

        // Use reflection to access private method
        $reflectionClass = new \ReflectionClass(CatalogDataMapper::class);
        $method = $reflectionClass->getMethod('getOffrePeriodical');
        $method->setAccessible(true);

        $result = $method->invoke($this->catalogDataMapper, $offers);

        $this->assertNull($result);
    }

    public function testGetOfferPrice(): void
    {
        $offer = [
            'prices' => [
                [
                    'periodType' => 'monthly',
                    'price' => 100,
                    'currency' => 'EUR',
                    'typeDiscount' => 'none'
                ],
                [
                    'periodType' => 'yearly',
                    'price' => 1000,
                    'currency' => 'EUR',
                    'typeDiscount' => 'discount'
                ]
            ]
        ];

        // Use reflection to access private method
        $reflectionClass = new \ReflectionClass(CatalogDataMapper::class);
        $method = $reflectionClass->getMethod('getOfferPrice');
        $method->setAccessible(true);

        $result = $method->invoke($this->catalogDataMapper, $offer);

        $this->assertIsArray($result);
        $this->assertEquals('monthly', $result['periodType']);
        $this->assertEquals(100, $result['price']);
        $this->assertEquals('EUR', $result['currency']);
        $this->assertEquals('none', $result['typeDiscount']);
    }

    public function testGetOfferPriceWithNoPrices(): void
    {
        $offer = [];

        // Use reflection to access private method
        $reflectionClass = new \ReflectionClass(CatalogDataMapper::class);
        $method = $reflectionClass->getMethod('getOfferPrice');
        $method->setAccessible(true);

        $result = $method->invoke($this->catalogDataMapper, $offer);

        $this->assertIsArray($result);
        $this->assertEquals('', $result['periodType']);
        $this->assertEquals(0, $result['price']);
        $this->assertEquals('', $result['currency']);
        $this->assertEquals('', $result['typeDiscount']);
    }
}
