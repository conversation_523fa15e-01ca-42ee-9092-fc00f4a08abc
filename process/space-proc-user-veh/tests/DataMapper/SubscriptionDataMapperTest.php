<?php

namespace App\Tests\DataMapper;

use App\DataMapper\SubscriptionDataMapper;
use App\Model\ContractRemoteLev;
use App\Model\DateRange;
use App\Model\SubscriptionModel;
use PHPUnit\Framework\TestCase;
use <PERSON>ymfony\Component\Serializer\SerializerInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class SubscriptionDataMapperTest extends TestCase
{
    private SubscriptionDataMapper $subscriptionDataMapper;
    private ValidatorInterface $validator;
    private SerializerInterface $serializer;

    protected function setUp(): void
    {
        $this->validator = $this->createMock(ValidatorInterface::class);
        $this->serializer = $this->createMock(SerializerInterface::class);
        $this->subscriptionDataMapper = new SubscriptionDataMapper($this->validator, $this->serializer);
    }

    public function testConstructor(): void
    {
        $validator = $this->createMock(ValidatorInterface::class);
        $serializer = $this->createMock(SerializerInterface::class);
        $subscriptionDataMapper = new SubscriptionDataMapper($validator, $serializer);

        $this->assertInstanceOf(SubscriptionDataMapper::class, $subscriptionDataMapper);
    }

    public function testGetCategoryWithConnectedServices(): void
    {
        $familyNames = ['NAVCOZAR', 'TMTS', 'NAVCO', 'ZAR', 'LEV', 'PHEV', 'BEV', 'RACCESS', 'CONNECTEDALARM', 'DIGITALKEY', 'AE_CALL', 'EV_ROUTING_APP', 'PARTNERSERVICE', 'TRIPS_IN_THE_CLOUD', 'STOLEN_VEHICLE'];

        foreach ($familyNames as $familyName) {
            $result = $this->subscriptionDataMapper->getCategory($familyName);
            $this->assertEquals('CONNECTED_SERVICES', $result);
        }
    }

    public function testGetCategoryWithAfterSalesServices(): void
    {
        $familyNames = ['DIMBO', 'PRIVILEGE'];

        foreach ($familyNames as $familyName) {
            $result = $this->subscriptionDataMapper->getCategory($familyName);
            $this->assertEquals('AFTERSALES_SERVICES', $result);
        }
    }

    public function testGetCategoryWithOtherServices(): void
    {
        $familyName = 'OTHER_SERVICE';

        $result = $this->subscriptionDataMapper->getCategory($familyName);
        $this->assertEquals('OTHERS', $result);
    }

    public function testGetCategoryWithNullFamilyName(): void
    {
        $result = $this->subscriptionDataMapper->getCategory(null);
        $this->assertEquals('OTHERS', $result);
    }

    public function testCalculateDuration(): void
    {
        // Use reflection to access private method
        $reflectionClass = new \ReflectionClass(SubscriptionDataMapper::class);
        $method = $reflectionClass->getMethod('calculateDuration');
        $method->setAccessible(true);

        // Test with a value that is divisible by 12
        $result = $method->invoke($this->subscriptionDataMapper, '12');
        $this->assertEquals('per month', $result);

        // Test with a value that is not divisible by 12
        $result = $method->invoke($this->subscriptionDataMapper, '13');
        $this->assertEquals('per year', $result);

        // Test with a non-numeric value
        $result = $method->invoke($this->subscriptionDataMapper, 'non-numeric');
        $this->assertEquals('', $result);
    }

    public function testSamsStatusToMymStatus(): void
    {
        // Use reflection to access private static method
        $reflectionClass = new \ReflectionClass(SubscriptionDataMapper::class);
        $method = $reflectionClass->getMethod('samsStatusToMymStatus');
        $method->setAccessible(true);

        // Test all possible status values
        $this->assertEquals(ContractRemoteLev::CONTRACT_ACTIVE, $method->invoke(null, 'ACTIVATED'));
        $this->assertEquals(ContractRemoteLev::CONTRACT_SAMS_PENDING_ACTIVATION, $method->invoke(null, 'PENDING ACTIVATION'));
        $this->assertEquals(ContractRemoteLev::CONTRACT_SAMS_PENDING_IDENTIFICATION, $method->invoke(null, 'PENDING IDENTIFICATION'));
        $this->assertEquals(ContractRemoteLev::CONTRACT_SAMS_PENDING_SUBSCRIPTION, $method->invoke(null, 'PENDING SUBSCRIPTION'));
        $this->assertEquals(ContractRemoteLev::CONTRACT_SAMS_PENDING_SUBSCRIPTION, $method->invoke(null, 'EN ATTENTE DE SOUSCRIPTION'));
        $this->assertEquals(ContractRemoteLev::CONTRACT_SAMS_CANCELLED, $method->invoke(null, 'CANCELLED'));
        $this->assertEquals(ContractRemoteLev::CONTRACT_SAMS_PENDING_CANCELLATION, $method->invoke(null, 'PENDING CANCELLATION'));
        $this->assertEquals(ContractRemoteLev::CONTRACT_SAMS_EXPIRED, $method->invoke(null, 'EXPIRED'));
        $this->assertEquals(ContractRemoteLev::CONTRACT_SAMS_EXPIRED_IN, $method->invoke(null, 'EXPIRED IN'));
        $this->assertEquals(ContractRemoteLev::CONTRACT_SAMS_TERMINATED, $method->invoke(null, 'TERMINATED'));
        $this->assertEquals(ContractRemoteLev::CONTRACT_SAMS_PENDING_TERMINATION, $method->invoke(null, 'PENDING TERMINATION'));
        $this->assertEquals(ContractRemoteLev::CONTRACT_SAMS_FAILED_PAYMENT, $method->invoke(null, 'FAILED PAYMENT'));
        $this->assertEquals(ContractRemoteLev::CONTRACT_SAMS_DEACTIVATED, $method->invoke(null, 'DEACTIVATED'));
        $this->assertEquals(ContractRemoteLev::CONTRACT_SAMS_PENDING_DEACTIVATION, $method->invoke(null, 'PENDING DEACTIVATION'));

        // Test case insensitivity
        $this->assertEquals(ContractRemoteLev::CONTRACT_ACTIVE, $method->invoke(null, 'activated'));

        // Test all possible status values with different cases
        $this->assertEquals(ContractRemoteLev::CONTRACT_SAMS_PENDING_ACTIVATION, $method->invoke(null, 'pending activation'));
        $this->assertEquals(ContractRemoteLev::CONTRACT_SAMS_PENDING_IDENTIFICATION, $method->invoke(null, 'pending identification'));
        $this->assertEquals(ContractRemoteLev::CONTRACT_SAMS_PENDING_SUBSCRIPTION, $method->invoke(null, 'pending subscription'));
        $this->assertEquals(ContractRemoteLev::CONTRACT_SAMS_PENDING_SUBSCRIPTION, $method->invoke(null, 'en attente de souscription'));
        $this->assertEquals(ContractRemoteLev::CONTRACT_SAMS_CANCELLED, $method->invoke(null, 'cancelled'));
        $this->assertEquals(ContractRemoteLev::CONTRACT_SAMS_PENDING_CANCELLATION, $method->invoke(null, 'pending cancellation'));
        $this->assertEquals(ContractRemoteLev::CONTRACT_SAMS_EXPIRED, $method->invoke(null, 'expired'));
        $this->assertEquals(ContractRemoteLev::CONTRACT_SAMS_EXPIRED_IN, $method->invoke(null, 'expired in'));
        $this->assertEquals(ContractRemoteLev::CONTRACT_SAMS_TERMINATED, $method->invoke(null, 'terminated'));
        $this->assertEquals(ContractRemoteLev::CONTRACT_SAMS_PENDING_TERMINATION, $method->invoke(null, 'pending termination'));
        $this->assertEquals(ContractRemoteLev::CONTRACT_SAMS_FAILED_PAYMENT, $method->invoke(null, 'failed payment'));
        $this->assertEquals(ContractRemoteLev::CONTRACT_SAMS_DEACTIVATED, $method->invoke(null, 'deactivated'));
        $this->assertEquals(ContractRemoteLev::CONTRACT_SAMS_PENDING_DEACTIVATION, $method->invoke(null, 'pending deactivation'));

        // Test invalid status
        $this->assertFalse($method->invoke(null, 'INVALID_STATUS'));

        // Test with null value
        $this->assertFalse($method->invoke(null, null));

        // Test with empty string
        $this->assertFalse($method->invoke(null, ''));
    }

    public function testIsMorePrior(): void
    {
        // Use reflection to access private static method
        $reflectionClass = new \ReflectionClass(SubscriptionDataMapper::class);
        $method = $reflectionClass->getMethod('isMorePrior');
        $method->setAccessible(true);

        // Create test models
        $currentModel = new SubscriptionModel();
        $oldModel = new SubscriptionModel();

        // Test with null old model
        $this->assertTrue($method->invoke(null, $currentModel, null));

        // Test with same priority but different start dates
        $currentModel->setPriority('ACTIVATED'); // Priority 3
        $oldModel->setPriority('ACTIVATED'); // Priority 3

        $currentDateRange = new DateRange('2023-02-01', '2023-12-31');
        $oldDateRange = new DateRange('2023-01-01', '2023-12-31');

        $currentModel->setValidity($currentDateRange);
        $oldModel->setValidity($oldDateRange);

        // Old model has earlier start date, so current is more prior (based on the implementation)
        $this->assertTrue($method->invoke(null, $currentModel, $oldModel));

        // Test with same priority and same start date but different creation dates
        $currentDateRange = new DateRange('2023-01-01', '2023-12-31');
        $currentModel->setValidity($currentDateRange);

        $currentModel->setSubscriptionTechCreationDate('2023-02-01');
        $oldModel->setSubscriptionTechCreationDate('2023-01-01');

        // Old model has earlier creation date, so current is more prior (based on the implementation)
        $this->assertTrue($method->invoke(null, $currentModel, $oldModel));

        // Test with different priorities
        $currentModel->setPriority('PENDING IDENTIFICATION'); // Priority 1
        $oldModel->setPriority('ACTIVATED'); // Priority 3

        // Current model has higher priority (lower number), so it is more prior
        $this->assertTrue($method->invoke(null, $currentModel, $oldModel));
    }

    public function testMapWithPeriodicalPricingModel(): void
    {
        $subscriptionModel = new SubscriptionModel();
        $subscription = [
            'startDate' => '2023-01-01',
            'endDate' => '2023-12-31',
            'associationId' => 'test-association-id',
            'statusReason' => 'test-status-reason',
            'techCreationDate' => '2023-01-01',
            'topMainImage' => 'test-top-main-image',
            'productUrlSso' => 'test-product-url-sso',
            'productUrlCvs' => 'test-product-url-cvs',
            'subscription' => [
                'ratePlans' => [
                    [
                        'pricingModel' => 'Periodical',
                        'product' => [
                            'productFamily' => 'NAVCOZAR',
                            'productCommercialName' => 'Test Product'
                        ]
                    ]
                ],
                'isExtensible' => true,
                'hasFreeTrial' => 'true',
                'status' => 'ACTIVATED'
            ]
        ];

        $status = ContractRemoteLev::CONTRACT_ACTIVE;

        // Test instance method
        $result = $this->subscriptionDataMapper->map($subscriptionModel, $subscription, $status);

        $this->assertInstanceOf(SubscriptionModel::class, $result);
        $this->assertEquals('NAVCOZAR', $result->getType());
        $this->assertEquals('connected_services', $result->getCategory());
        $this->assertEquals($status, $result->getStatus());
        $this->assertInstanceOf(DateRange::class, $result->getValidity());
        $this->assertTrue($result->getIsExtensible());
        $this->assertEquals('Test Product', $result->getTitle());
        $this->assertTrue($result->getHasFreeTrial());
        $this->assertEquals('test-association-id', $result->getAssociationId());
        $this->assertEquals('test-status-reason', $result->getStatusReason());
        $this->assertEquals('2023-01-01', $result->getSubscriptionTechCreationDate());
        $this->assertEquals('test-top-main-image', $result->getTopMainImage());
        $this->assertEquals('test-product-url-sso', $result->getUrlSso());
        $this->assertStringContainsString('services-store.jeep.com', $result->getUrlCvs());

        // Test static method call through transform
        $samsSubscriptions = [$subscription];
        $result = $this->subscriptionDataMapper->transform($samsSubscriptions);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('nac', $result);
        $this->assertCount(1, $result['nac']);
        $this->assertInstanceOf(SubscriptionModel::class, $result['nac'][0]);
        $this->assertEquals('NAVCOZAR', $result['nac'][0]->getType());
    }

    public function testStaticMapMethodCall(): void
    {
        // Test the static call to map through transform
        $subscription = [
            'startDate' => '2023-01-01',
            'endDate' => '2023-12-31',
            'associationId' => 'test-association-id',
            'statusReason' => 'test-status-reason',
            'techCreationDate' => '2023-01-01',
            'topMainImage' => 'test-top-main-image',
            'productUrlSso' => 'test-product-url-sso',
            'productUrlCvs' => 'test-product-url-cvs',
            'subscription' => [
                'ratePlans' => [
                    [
                        'pricingModel' => 'Periodical',
                        'product' => [
                            'productFamily' => 'PHEV',
                            'productCommercialName' => 'Test Product'
                        ]
                    ]
                ],
                'isExtensible' => true,
                'hasFreeTrial' => 'true',
                'status' => 'ACTIVATED'
            ]
        ];

        // Test static method call through transform for PHEV
        $samsSubscriptions = [$subscription];
        $result = $this->subscriptionDataMapper->transform($samsSubscriptions);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('remoteLev', $result);
        $this->assertCount(1, $result['remoteLev']);
        $this->assertInstanceOf(ContractRemoteLev::class, $result['remoteLev'][0]);
        $this->assertEquals('REMOTELEV', $result['remoteLev'][0]->getType());

        // Test static method call through transform for other type
        $subscription['subscription']['ratePlans'][0]['product']['productFamily'] = 'OTHER_TYPE';
        $samsSubscriptions = [$subscription];
        $result = $this->subscriptionDataMapper->transform($samsSubscriptions);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('other_type', $result);
        $this->assertCount(1, $result['other_type']);
        $this->assertInstanceOf(SubscriptionModel::class, $result['other_type'][0]);
        $this->assertEquals('OTHER_TYPE', $result['other_type'][0]->getType());
    }

    public function testMapWithLevTrue(): void
    {
        $subscriptionModel = new ContractRemoteLev();
        $subscription = [
            'startDate' => '2023-01-01',
            'endDate' => '2023-12-31',
            'associationId' => 'test-association-id',
            'subscription' => [
                'ratePlans' => [
                    [
                        'product' => [
                            'productFamily' => 'PHEV'
                        ]
                    ]
                ],
                'status' => 'ACTIVATED'
            ]
        ];

        $status = ContractRemoteLev::CONTRACT_ACTIVE;

        $result = $this->subscriptionDataMapper->map($subscriptionModel, $subscription, $status, true);

        $this->assertInstanceOf(ContractRemoteLev::class, $result);
        $this->assertEquals('REMOTELEV', $result->getType());
        $this->assertEquals(['NCG01', 'NBM01', 'NAS01', 'NAO02'], $result->getFds());
        $this->assertEquals('test-association-id', $result->getAssociationId());
    }

    public function testMapWithLevTrueAndRACCESS(): void
    {
        $subscriptionModel = new ContractRemoteLev();
        $subscription = [
            'startDate' => '2023-01-01',
            'endDate' => '2023-12-31',
            'associationId' => 'test-association-id',
            'subscription' => [
                'ratePlans' => [
                    [
                        'product' => [
                            'productFamily' => 'RACCESS'
                        ]
                    ]
                ],
                'status' => 'ACTIVATED'
            ]
        ];

        $status = ContractRemoteLev::CONTRACT_ACTIVE;

        $result = $this->subscriptionDataMapper->map($subscriptionModel, $subscription, $status, true);

        $this->assertInstanceOf(ContractRemoteLev::class, $result);
        $this->assertEquals('RACCESS', $result->getType());
        $this->assertEquals(['NEE02', 'NEF02', 'NEF01'], $result->getFds());
        $this->assertEquals('test-association-id', $result->getAssociationId());
    }

    public function testTransformWithZARSubscription(): void
    {
        $samsSubscriptions = [
            [
                'startDate' => '2023-01-01',
                'endDate' => '2023-12-31',
                'associationId' => 'test-association-id',
                'subscription' => [
                    'ratePlans' => [
                        [
                            'product' => [
                                'productFamily' => 'ZAR'
                            ]
                        ]
                    ],
                    'status' => 'ACTIVATED'
                ]
            ]
        ];

        $result = $this->subscriptionDataMapper->transform($samsSubscriptions);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('nac', $result);
        $this->assertCount(1, $result['nac']);
        $this->assertInstanceOf(\App\Model\SubscriptionModel::class, $result['nac'][0]);
        $this->assertEquals('ZAR', $result['nac'][0]->getType());
    }

    public function testTransformWithPHEVSubscription(): void
    {
        $samsSubscriptions = [
            [
                'startDate' => '2023-01-01',
                'endDate' => '2023-12-31',
                'associationId' => 'test-association-id',
                'subscription' => [
                    'ratePlans' => [
                        [
                            'product' => [
                                'productFamily' => 'PHEV'
                            ]
                        ]
                    ],
                    'status' => 'ACTIVATED'
                ]
            ]
        ];

        $result = $this->subscriptionDataMapper->transform($samsSubscriptions);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('remoteLev', $result);
        $this->assertCount(1, $result['remoteLev']);
        $this->assertInstanceOf(\App\Model\ContractRemoteLev::class, $result['remoteLev'][0]);
        $this->assertEquals('REMOTELEV', $result['remoteLev'][0]->getType());
    }

    public function testTransformWithOtherSubscription(): void
    {
        $samsSubscriptions = [
            [
                'startDate' => '2023-01-01',
                'endDate' => '2023-12-31',
                'associationId' => 'test-association-id',
                'subscription' => [
                    'ratePlans' => [
                        [
                            'product' => [
                                'productFamily' => 'OTHER_SERVICE'
                            ]
                        ]
                    ],
                    'status' => 'ACTIVATED'
                ]
            ]
        ];

        $result = $this->subscriptionDataMapper->transform($samsSubscriptions);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('other_service', $result);
        $this->assertCount(1, $result['other_service']);
        $this->assertInstanceOf(\App\Model\SubscriptionModel::class, $result['other_service'][0]);
        $this->assertEquals('OTHER_SERVICE', $result['other_service'][0]->getType());
    }

    public function testTransformWithMultipleSubscriptions(): void
    {
        $samsSubscriptions = [
            [
                'startDate' => '2023-01-01',
                'endDate' => '2023-12-31',
                'associationId' => 'test-association-id-1',
                'subscription' => [
                    'ratePlans' => [
                        [
                            'product' => [
                                'productFamily' => 'ZAR'
                            ]
                        ]
                    ],
                    'status' => 'ACTIVATED'
                ]
            ],
            [
                'startDate' => '2023-01-01',
                'endDate' => '2023-12-31',
                'associationId' => 'test-association-id-2',
                'subscription' => [
                    'ratePlans' => [
                        [
                            'product' => [
                                'productFamily' => 'PHEV'
                            ]
                        ]
                    ],
                    'status' => 'ACTIVATED'
                ]
            ],
            [
                'startDate' => '2023-01-01',
                'endDate' => '2023-12-31',
                'associationId' => 'test-association-id-3',
                'subscription' => [
                    'ratePlans' => [
                        [
                            'product' => [
                                'productFamily' => 'OTHER_SERVICE'
                            ]
                        ]
                    ],
                    'status' => 'ACTIVATED'
                ]
            ]
        ];

        $result = $this->subscriptionDataMapper->transform($samsSubscriptions);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('nac', $result);
        $this->assertArrayHasKey('remoteLev', $result);
        $this->assertArrayHasKey('other_service', $result);
        $this->assertCount(1, $result['nac']);
        $this->assertCount(1, $result['remoteLev']);
        $this->assertCount(1, $result['other_service']);
    }

    public function testTransformWithInvalidStatus(): void
    {
        $samsSubscriptions = [
            [
                'startDate' => '2023-01-01',
                'endDate' => '2023-12-31',
                'associationId' => 'test-association-id',
                'subscription' => [
                    'ratePlans' => [
                        [
                            'product' => [
                                'productFamily' => 'ZAR'
                            ]
                        ]
                    ],
                    'status' => 'INVALID_STATUS'
                ]
            ]
        ];

        $result = $this->subscriptionDataMapper->transform($samsSubscriptions);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('nac', $result);
        $this->assertEmpty($result['nac']);
    }

    public function testTransformWithPendingIdentificationAndNoAssociationId(): void
    {
        $samsSubscriptions = [
            [
                'startDate' => '2023-01-01',
                'endDate' => '2023-12-31',
                'subscription' => [
                    'ratePlans' => [
                        [
                            'product' => [
                                'productFamily' => 'ZAR'
                            ]
                        ]
                    ],
                    'status' => 'PENDING IDENTIFICATION'
                ]
            ]
        ];

        $result = $this->subscriptionDataMapper->transform($samsSubscriptions);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('nac', $result);
        $this->assertCount(1, $result['nac']);
        $this->assertEquals(ContractRemoteLev::CONTRACT_SAMS_PENDING_SUBSCRIPTION, $result['nac'][0]->getStatus());
    }

    public function testTransformWithMultipleSubscriptionsOfSameType(): void
    {
        $samsSubscriptions = [
            [
                'startDate' => '2023-01-01',
                'endDate' => '2023-12-31',
                'techCreationDate' => '2023-01-01',
                'associationId' => 'test-association-id-1',
                'subscription' => [
                    'ratePlans' => [
                        [
                            'product' => [
                                'productFamily' => 'ZAR'
                            ]
                        ]
                    ],
                    'status' => 'ACTIVATED'
                ]
            ],
            [
                'startDate' => '2023-02-01',
                'endDate' => '2023-12-31',
                'techCreationDate' => '2023-02-01',
                'associationId' => 'test-association-id-2',
                'subscription' => [
                    'ratePlans' => [
                        [
                            'product' => [
                                'productFamily' => 'ZAR'
                            ]
                        ]
                    ],
                    'status' => 'ACTIVATED'
                ]
            ]
        ];

        $result = $this->subscriptionDataMapper->transform($samsSubscriptions);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('nac', $result);
        $this->assertCount(1, $result['nac']);
        $this->assertEquals('test-association-id-2', $result['nac'][0]->getAssociationId());
    }
}
