<?php

namespace App\Tests\MongoDB\UserData\UserDataDocument;

use App\MongoDB\UserData\UserDataDocument\Vehicle;
use App\MongoDB\UserData\UserDataDocument\VehicleOrder;
use PHPUnit\Framework\TestCase;

class VehicleTest extends TestCase
{
    private Vehicle $vehicle;

    protected function setUp(): void
    {
        $this->vehicle = new Vehicle();
    }

    public function testDefaultValues(): void
    {
        // Test default values
        $this->assertEquals('', $this->vehicle->id);
        $this->assertNull($this->vehicle->shortLabel);
        $this->assertNull($this->vehicle->nickName);
        $this->assertNull($this->vehicle->modelDescription);
        $this->assertNull($this->vehicle->versionId);
        $this->assertNull($this->vehicle->type);
        $this->assertNull($this->vehicle->culture);
        $this->assertNull($this->vehicle->brand);
        $this->assertNull($this->vehicle->picture);
        $this->assertNull($this->vehicle->sdp);
        $this->assertNull($this->vehicle->regTimeStamp);
        $this->assertNull($this->vehicle->enrollmentStatus);
        $this->assertNull($this->vehicle->connectorType);
        $this->assertNull($this->vehicle->make);
        $this->assertNull($this->vehicle->subMake);
        $this->assertNull($this->vehicle->market);
        $this->assertNull($this->vehicle->year);
        $this->assertNull($this->vehicle->warrantyStartDate);
        $this->assertNull($this->vehicle->isOrder);
        $this->assertNull($this->vehicle->vehicleOrder);
    }

    public function testPropertyAssignment(): void
    {
        // Set properties
        $this->vehicle->id = 'vehicle123';
        $this->vehicle->vin = 'VIN123456789';
        $this->vehicle->shortLabel = 'My Car';
        $this->vehicle->nickName = 'Speedy';
        $this->vehicle->modelDescription = 'Luxury Sedan';
        $this->vehicle->versionId = 'version123';
        $this->vehicle->type = 'sedan';
        $this->vehicle->culture = 'en-US';
        $this->vehicle->brand = 'Brand1';
        $this->vehicle->picture = 'car.jpg';
        $this->vehicle->sdp = 'sdp123';
        $this->vehicle->regTimeStamp = 1620000000;
        $this->vehicle->enrollmentStatus = 'active';
        $this->vehicle->connectorType = 'type1';
        $this->vehicle->make = 'Make1';
        $this->vehicle->subMake = 'SubMake1';
        $this->vehicle->market = 'US';
        $this->vehicle->year = '2023';
        $this->vehicle->lastUpdate = 1630000000;
        $this->vehicle->warrantyStartDate = '2023-01-01';
        $this->vehicle->isOrder = false;
        
        $vehicleOrder = new VehicleOrder();
        $this->vehicle->vehicleOrder = $vehicleOrder;

        // Verify properties were set correctly
        $this->assertEquals('vehicle123', $this->vehicle->id);
        $this->assertEquals('VIN123456789', $this->vehicle->vin);
        $this->assertEquals('My Car', $this->vehicle->shortLabel);
        $this->assertEquals('Speedy', $this->vehicle->nickName);
        $this->assertEquals('Luxury Sedan', $this->vehicle->modelDescription);
        $this->assertEquals('version123', $this->vehicle->versionId);
        $this->assertEquals('sedan', $this->vehicle->type);
        $this->assertEquals('en-US', $this->vehicle->culture);
        $this->assertEquals('Brand1', $this->vehicle->brand);
        $this->assertEquals('car.jpg', $this->vehicle->picture);
        $this->assertEquals('sdp123', $this->vehicle->sdp);
        $this->assertEquals(1620000000, $this->vehicle->regTimeStamp);
        $this->assertEquals('active', $this->vehicle->enrollmentStatus);
        $this->assertEquals('type1', $this->vehicle->connectorType);
        $this->assertEquals('Make1', $this->vehicle->make);
        $this->assertEquals('SubMake1', $this->vehicle->subMake);
        $this->assertEquals('US', $this->vehicle->market);
        $this->assertEquals('2023', $this->vehicle->year);
        $this->assertEquals(1630000000, $this->vehicle->lastUpdate);
        $this->assertEquals('2023-01-01', $this->vehicle->warrantyStartDate);
        $this->assertFalse($this->vehicle->isOrder);
        $this->assertSame($vehicleOrder, $this->vehicle->vehicleOrder);
    }

    public function testGetters(): void
    {
        // Set properties
        $this->vehicle->type = 'sedan';
        $this->vehicle->versionId = 'version123';
        $this->vehicle->brand = 'Brand1';

        // Test getters
        $this->assertEquals('sedan', $this->vehicle->getType());
        $this->assertEquals('version123', $this->vehicle->getVersionId());
        $this->assertEquals('Brand1', $this->vehicle->getBrand());
    }
}
