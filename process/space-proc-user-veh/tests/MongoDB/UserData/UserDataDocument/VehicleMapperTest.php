<?php

namespace App\Tests\MongoDB\UserData\UserDataDocument;

use App\Model\SystemVehicleData;
use App\MongoDB\UserData\UserDataDocument\Vehicle;
use App\MongoDB\UserData\UserDataDocument\VehicleMapper;
use PHPUnit\Framework\TestCase;

class VehicleMapperTest extends TestCase
{
    private SystemVehicleData $systemVehicleData;

    protected function setUp(): void
    {
        $this->systemVehicleData = new SystemVehicleData();
        
        // Set up the SystemVehicleData with test values
        $this->systemVehicleData->setVin('VIN123456789');
        $this->systemVehicleData->setBrandCode('Brand1');
        $this->systemVehicleData->setLabel('Test Vehicle');
        $this->systemVehicleData->setNickname('Speedy');
        $this->systemVehicleData->setImageUrl('car.jpg');
        $this->systemVehicleData->setSdp('sdp123');
        $this->systemVehicleData->setLcdv('lcdv123');
        $this->systemVehicleData->setType('sedan');
        $this->systemVehicleData->setRegTimestamp(1620000000);
        $this->systemVehicleData->setEnrollmentStatus('active');
        $this->systemVehicleData->setConnectorType('type1');
        $this->systemVehicleData->setMake('Make1');
        $this->systemVehicleData->setSubMake('SubMake1');
        $this->systemVehicleData->setMarket('US');
        $this->systemVehicleData->setLastUpdate('1630000000');
        $this->systemVehicleData->setYear('2023');
        $this->systemVehicleData->setWarrantyStartDate('2023-01-01');
    }

    public function testMapSystemVehicleDataWithNewVehicle(): void
    {
        // Map SystemVehicleData to a new Vehicle
        $vehicle = VehicleMapper::mapSystemVehicleData($this->systemVehicleData);
        
        // Verify mapping was done correctly
        $this->assertEquals('VIN123456789', $vehicle->vin);
        $this->assertEquals('BRAND1', $vehicle->brand);
        $this->assertEquals('Test Vehicle', $vehicle->shortLabel);
        $this->assertEquals('Speedy', $vehicle->nickname);
        $this->assertEquals('Test Vehicle', $vehicle->modelDescription);
        $this->assertEquals('car.jpg', $vehicle->picture);
        $this->assertEquals('sdp123', $vehicle->sdp);
        $this->assertEquals('lcdv123', $vehicle->versionId);
        $this->assertEquals('sedan', $vehicle->type);
        $this->assertEquals(1620000000, $vehicle->regTimeStamp);
        $this->assertEquals('active', $vehicle->enrollmentStatus);
        $this->assertEquals('type1', $vehicle->connectorType);
        $this->assertEquals('Make1', $vehicle->make);
        $this->assertEquals('SubMake1', $vehicle->subMake);
        $this->assertEquals('US', $vehicle->market);
        $this->assertEquals(1630000000, $vehicle->lastUpdate);
        $this->assertEquals('2023', $vehicle->year);
        $this->assertEquals('2023-01-01', $vehicle->warrantyStartDate);
    }

    public function testMapSystemVehicleDataWithExistingVehicle(): void
    {
        // Create an existing Vehicle
        $existingVehicle = new Vehicle();
        $existingVehicle->vin = 'VIN123456789';
        $existingVehicle->id = 'vehicle123';
        $existingVehicle->culture = 'en-US';
        $existingVehicle->isOrder = false;
        
        // Map SystemVehicleData to the existing Vehicle
        $updatedVehicle = VehicleMapper::mapSystemVehicleData($this->systemVehicleData, $existingVehicle);
        
        // Verify mapping was done correctly and existing properties were preserved
        $this->assertSame($existingVehicle, $updatedVehicle);
        $this->assertEquals('vehicle123', $updatedVehicle->id);
        $this->assertEquals('en-US', $updatedVehicle->culture);
        $this->assertEquals(false, $updatedVehicle->isOrder);
        
        // Verify updated properties
        $this->assertEquals('VIN123456789', $updatedVehicle->vin);
        $this->assertEquals('BRAND1', $updatedVehicle->brand);
        $this->assertEquals('Test Vehicle', $updatedVehicle->shortLabel);
        $this->assertEquals('Speedy', $updatedVehicle->nickname);
    }

    public function testMapSystemVehicleDataWithMismatchedVin(): void
    {
        // Create an existing Vehicle with a different VIN
        $existingVehicle = new Vehicle();
        $existingVehicle->vin = 'DIFFERENT_VIN';
        
        // Expect an exception when mapping with mismatched VIN
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('The vin of the used data vehicle (DIFFERENT_VIN) is different from the vin of the system vehicle data (VIN123456789)');
        
        VehicleMapper::mapSystemVehicleData($this->systemVehicleData, $existingVehicle);
    }
}
