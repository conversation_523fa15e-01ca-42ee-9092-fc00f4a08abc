<?php

namespace App\Tests\Service;

use App\Connector\SystemCorvetDataConnector;
use App\Helper\WSResponse;
use App\Service\CorvetService;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class CorvetServiceTest extends TestCase
{
    private CorvetService $corvetService;
    private SystemCorvetDataConnector $sysCorvetDataConnector;
    private LoggerInterface $logger;

    protected function setUp(): void
    {
        $this->sysCorvetDataConnector = $this->createMock(SystemCorvetDataConnector::class);
        $this->logger = $this->createMock(LoggerInterface::class);

        $this->corvetService = new CorvetService($this->sysCorvetDataConnector);
        $this->corvetService->setLogger($this->logger);
    }

    public function testGetDataSuccess(): void
    {
        $vin = 'TEST-VIN-123';
        $brand = 'AP';

        $expectedOptions = [
            'query' => ['brand' => $brand],
        ];

        $responseData = [
            'success' => [
                'VEHICULE' => [
                    'DONNEES_VEHICULE' => [
                        'VIN' => $vin,
                        'LCDV_BASE' => '1PTBSYHBM604A0F1M09P05FX',
                    ],
                    'LISTE_ATTRIBUTES_7' => [
                        'ATTRIBUT' => ['DCD06CD', 'DCW79CD', 'DCX01CD']
                    ]
                ]
            ]
        ];

        $wsResponse = new WSResponse(Response::HTTP_OK, $responseData);

        $this->sysCorvetDataConnector->expects($this->once())
            ->method('call')
            ->with(Request::METHOD_GET, "/v1/corvet/{$vin}/data", $expectedOptions)
            ->willReturn($wsResponse);

        $this->logger->expects($this->once())
            ->method('info')
            ->with($this->stringContains("Get vehicle from corvet api => vin : $vin ; brand : $brand"));

        $result = $this->corvetService->getData($vin, $brand);

        $this->assertIsArray($result);
        $this->assertEquals($responseData['success'], $result);
    }

    public function testGetDataWithErrorResponse(): void
    {
        $vin = 'TEST-VIN-123';
        $brand = 'AP';

        $expectedOptions = [
            'query' => ['brand' => $brand],
        ];

        $errorData = [
            'error' => [
                'message' => 'Invalid request'
            ]
        ];

        $wsResponse = new WSResponse(Response::HTTP_BAD_REQUEST, $errorData);

        $this->sysCorvetDataConnector->expects($this->once())
            ->method('call')
            ->with(Request::METHOD_GET, "/v1/corvet/{$vin}/data", $expectedOptions)
            ->willReturn($wsResponse);

        $this->logger->expects($this->once())
            ->method('info')
            ->with($this->stringContains("Get vehicle from corvet api => vin : $vin ; brand : $brand"));

        $result = $this->corvetService->getData($vin, $brand);

        $this->assertIsArray($result);
        $this->assertEquals($errorData['error'], $result['error']);
    }

    public function testGetDataWithGenericErrorResponse(): void
    {
        $vin = 'TEST-VIN-123';
        $brand = 'AP';

        $expectedOptions = [
            'query' => ['brand' => $brand],
        ];

        $errorData = 'Service unavailable';

        $wsResponse = new WSResponse(Response::HTTP_SERVICE_UNAVAILABLE, $errorData);

        $this->sysCorvetDataConnector->expects($this->once())
            ->method('call')
            ->with(Request::METHOD_GET, "/v1/corvet/{$vin}/data", $expectedOptions)
            ->willReturn($wsResponse);

        $this->logger->expects($this->once())
            ->method('info')
            ->with($this->stringContains("Get vehicle from corvet api => vin : $vin ; brand : $brand"));

        $result = $this->corvetService->getData($vin, $brand);

        $this->assertIsArray($result);
        $this->assertEquals($errorData, $result['error']);
    }

    public function testGetDataWsResponseSuccess(): void
    {
        $vin = 'TEST-VIN-123';
        $brand = 'AP';

        $expectedOptions = [
            'query' => ['brand' => $brand],
        ];

        $responseData = [
            'success' => [
                'VEHICULE' => [
                    'DONNEES_VEHICULE' => [
                        'VIN' => $vin,
                        'LCDV_BASE' => '1PTBSYHBM604A0F1M09P05FX',
                    ],
                    'LISTE_ATTRIBUTES_7' => [
                        'ATTRIBUT' => ['DCD06CD', 'DCW79CD', 'DCX01CD']
                    ]
                ]
            ]
        ];

        $wsResponse = new WSResponse(Response::HTTP_OK, $responseData);

        $this->sysCorvetDataConnector->expects($this->once())
            ->method('call')
            ->with(Request::METHOD_GET, "/v1/corvet/{$vin}/data", $expectedOptions)
            ->willReturn($wsResponse);

        $result = $this->corvetService->getDataWsResponse($vin, $brand);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }

    public function testGetDataWsResponseWithErrorResponse(): void
    {
        $vin = 'TEST-VIN-123';
        $brand = 'AP';

        $expectedOptions = [
            'query' => ['brand' => $brand],
        ];

        $errorData = [
            'error' => [
                'message' => 'Invalid request'
            ]
        ];

        $wsResponse = new WSResponse(Response::HTTP_BAD_REQUEST, $errorData);

        $this->sysCorvetDataConnector->expects($this->once())
            ->method('call')
            ->with(Request::METHOD_GET, "/v1/corvet/{$vin}/data", $expectedOptions)
            ->willReturn($wsResponse);

        $result = $this->corvetService->getDataWsResponse($vin, $brand);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->getCode());
        $this->assertEquals($errorData, $result->getData());
    }
}
