<?php

namespace App\Tests\Service;

use App\Connector\SysSamsDataConnector;
use App\Helper\WSResponse;
use App\Service\MongoAtlasQueryService;
use App\Service\SubscriptionService;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;

class SubscriptionServiceTest extends TestCase
{
    private SubscriptionService $subscriptionService;
    private SysSamsDataConnector $connector;
    private MongoAtlasQueryService $mongoService;

    protected function setUp(): void
    {
        $this->connector = $this->createMock(SysSamsDataConnector::class);
        $this->mongoService = $this->createMock(MongoAtlasQueryService::class);

        // Create a logger mock
        $loggerMock = $this->createMock(\Psr\Log\LoggerInterface::class);

        // Create a reflection class for SubscriptionService
        $subscriptionService = new SubscriptionService($this->connector, $this->mongoService);
        $reflectionClass = new \ReflectionClass(SubscriptionService::class);

        // Set the logger property
        $loggerProperty = $reflectionClass->getProperty('logger');
        $loggerProperty->setAccessible(true);
        $loggerProperty->setValue($subscriptionService, $loggerMock);

        $this->subscriptionService = $subscriptionService;
    }

    public function testGetSubscription(): void
    {
        $userDbId = 'test-user-db-id';
        $vin = 'VIN123456789';
        $target = 'test-target';

        $expectedOptions = [
            'query' => [
                'target' => $target
            ],
            'headers' => [
                'userId' => $userDbId,
                'vin' => $vin,
            ],
        ];

        $expectedResponse = new WSResponse(Response::HTTP_OK, ['success' => true]);

        $this->connector->expects($this->once())
            ->method('call')
            ->with('GET', '/v1/subscription', $expectedOptions)
            ->willReturn($expectedResponse);

        $result = $this->subscriptionService->getSubscription($userDbId, $vin, $target);

        $this->assertSame($expectedResponse, $result);
    }

    public function testGetSubscriptionWithError(): void
    {
        $userDbId = 'test-user-db-id';
        $vin = 'VIN123456789';
        $target = 'test-target';

        $expectedOptions = [
            'query' => [
                'target' => $target
            ],
            'headers' => [
                'userId' => $userDbId,
                'vin' => $vin,
            ],
        ];

        $expectedResponse = new WSResponse(Response::HTTP_BAD_REQUEST, ['error' => 'Bad request']);

        $this->connector->expects($this->once())
            ->method('call')
            ->with('GET', '/v1/subscription', $expectedOptions)
            ->willReturn($expectedResponse);

        $result = $this->subscriptionService->getSubscription($userDbId, $vin, $target);

        $this->assertSame($expectedResponse, $result);
    }
}
