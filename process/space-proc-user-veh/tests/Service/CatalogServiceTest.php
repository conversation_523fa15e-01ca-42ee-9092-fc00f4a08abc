<?php

namespace App\Tests\Service;

use App\Connector\SysSamsDataConnector;
use App\Helper\WSResponse;
use App\Service\CatalogService;
use App\Service\MongoAtlasQueryService;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class CatalogServiceTest extends TestCase
{
    private CatalogService $catalogService;
    private SysSamsDataConnector $connector;
    private MongoAtlasQueryService $mongoService;
    private LoggerInterface $logger;

    protected function setUp(): void
    {
        $this->connector = $this->createMock(SysSamsDataConnector::class);
        $this->mongoService = $this->createMock(MongoAtlasQueryService::class);
        $this->logger = $this->createMock(LoggerInterface::class);

        $this->catalogService = new CatalogService($this->connector, $this->mongoService);
        $this->catalogService->setLogger($this->logger);
    }

    public function testGetCatalogSuccess(): void
    {
        $params = [
            'brand' => 'AP',
            'language' => 'en',
            'country' => 'FR',
            'vin' => 'TEST-VIN-123',
            'userDbId' => 'test-user-id',
            'source' => 'APP'
        ];

        $expectedOptions = [
            'query' => [
                'brand' => 'AP',
                'country' => 'FR',
                'language' => 'en',
            ],
            'headers' => [
                'userId' => 'test-user-id',
                'vin' => 'TEST-VIN-123',
            ],
        ];

        $responseData = [
            'success' => [
                [
                    'id' => 'product1',
                    'groupName' => 'BUNDLE',
                    'groupMemberType' => 'Some Type',
                    'familyName' => 'Some Family',
                    'type' => 'BUNDLE',
                    'standaloneProducts' => ['product1', 'product2']
                ]
            ]
        ];

        $wsResponse = new WSResponse(Response::HTTP_OK, $responseData);

        $this->connector->expects($this->once())
            ->method('call')
            ->with(Request::METHOD_GET, '/v1/catalog', $expectedOptions)
            ->willReturn($wsResponse);

        $this->logger->expects($this->once())
            ->method('info')
            ->with($this->stringContains('=> Call API [/v1/catalog] with options'));

        $result = $this->catalogService->getCatalog($params);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }

    public function testGetCatalogWithErrorResponse(): void
    {
        $params = [
            'brand' => 'AP',
            'language' => 'en',
            'country' => 'FR',
            'vin' => 'TEST-VIN-123',
            'userDbId' => 'test-user-id',
            'source' => 'APP'
        ];

        $expectedOptions = [
            'query' => [
                'brand' => 'AP',
                'country' => 'FR',
                'language' => 'en',
            ],
            'headers' => [
                'userId' => 'test-user-id',
                'vin' => 'TEST-VIN-123',
            ],
        ];

        $errorData = [
            'error' => [
                'message' => 'Invalid request'
            ]
        ];

        $wsResponse = new WSResponse(Response::HTTP_BAD_REQUEST, $errorData);

        $this->connector->expects($this->once())
            ->method('call')
            ->with(Request::METHOD_GET, '/v1/catalog', $expectedOptions)
            ->willReturn($wsResponse);

        $this->logger->expects($this->once())
            ->method('info')
            ->with($this->stringContains('=> Call API [/v1/catalog] with options'));

        $result = $this->catalogService->getCatalog($params);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->getCode());
        $this->assertEquals($errorData, $result->getData());
    }
}
