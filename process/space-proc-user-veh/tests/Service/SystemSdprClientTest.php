<?php

namespace App\Tests\Service;

use App\Connector\SystemSdprConnector;
use App\Helper\WSResponse;
use App\Service\SystemSdprClient;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class SystemSdprClientTest extends TestCase
{
    private SystemSdprClient $systemSdprClient;
    private SystemSdprConnector $connector;
    private LoggerInterface $logger;

    protected function setUp(): void
    {
        $this->connector = $this->createMock(SystemSdprConnector::class);
        $this->logger = $this->createMock(LoggerInterface::class);
        
        $this->systemSdprClient = new SystemSdprClient($this->connector);
        $this->systemSdprClient->setLogger($this->logger);
    }

    public function testGetUserVehicles(): void
    {
        $userId = 'test-user-id';
        $expectedUri = '/v1/user/vehicles';
        $expectedOptions = [
            'headers' => [
                'userId' => $userId,
            ],
        ];
        
        $mockResponse = new WSResponse(Response::HTTP_OK, ['vehicles' => []]);
        
        $this->connector->expects($this->once())
            ->method('call')
            ->with(Request::METHOD_GET, $expectedUri, $expectedOptions)
            ->willReturn($mockResponse);
        
        $result = $this->systemSdprClient->getUserVehicles($userId);
        
        $this->assertSame($mockResponse, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
    }

    public function testGetAccountVehicleV4(): void
    {
        $userId = 'test-user-id';
        $brand = 'FT';
        $expectedUri = '/v4/account/'.$userId.'/vehicles';
        $expectedOptions = [
            'headers' => [
                'brand' => $brand,
            ],
        ];
        
        $mockResponse = new WSResponse(Response::HTTP_OK, ['vehicles' => []]);
        
        $this->connector->expects($this->once())
            ->method('call')
            ->with(Request::METHOD_GET, $expectedUri, $expectedOptions)
            ->willReturn($mockResponse);
        
        $result = $this->systemSdprClient->getAccountVehicleV4($userId, $brand);
        
        $this->assertSame($mockResponse, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
    }
}
