<?php

namespace App\Tests\Service;

use App\Connector\SystemIdpConnector;
use App\Helper\WSResponse;
use App\Model\FeatureCode;
use App\Service\FeatureCodeService;
use App\Service\UserDataService;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpClient\Response\MockResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Yaml\Yaml;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Contracts\HttpClient\ResponseInterface;

class FeatureCodeServiceTest extends TestCase
{
    private FeatureCodeService $featureCodeService;
    private UserDataService $userDataService;
    private HttpClientInterface $httpClient;
    private SystemIdpConnector $systemIdpConnector;
    private LoggerInterface $logger;
    private string $cdnUrl = 'https://example.com/cdn';

    protected function setUp(): void
    {
        $this->userDataService = $this->getMockBuilder(UserDataService::class)
            ->disableOriginalConstructor()
            ->getMock();

        $this->httpClient = $this->getMockBuilder(HttpClientInterface::class)
            ->disableOriginalConstructor()
            ->getMock();

        $this->systemIdpConnector = $this->getMockBuilder(SystemIdpConnector::class)
            ->disableOriginalConstructor()
            ->getMock();

        $this->logger = $this->getMockBuilder(LoggerInterface::class)
            ->disableOriginalConstructor()
            ->getMock();

        $this->featureCodeService = new FeatureCodeService(
            $this->userDataService,
            $this->httpClient,
            $this->systemIdpConnector,
            $this->cdnUrl
        );
        $this->featureCodeService->setLogger($this->logger);
    }

    public function testGetConsumerRightsDataSuccess(): void
    {
        $userDBId = 'test-user-id';
        $vin = 'TEST-VIN-123';

        $responseData = [
            'success' => [
                'services' => [
                    [
                        ['code' => 'NAE01'],
                        ['code' => 'NAK01'],
                        ['code' => 'NAL01']
                    ]
                ]
            ]
        ];

        $wsResponse = new WSResponse(Response::HTTP_OK, $responseData);

        $this->systemIdpConnector->expects($this->once())
            ->method('call')
            ->with('GET', '/v1/consumer-rights?vin=' . $vin . '&userDbId=' . $userDBId)
            ->willReturn($wsResponse);

        $result = $this->featureCodeService->getConsumerRightsData($userDBId, $vin);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }

    public function testGetConsumerRightsDataWithErrorResponse(): void
    {
        $userDBId = 'test-user-id';
        $vin = 'TEST-VIN-123';

        $errorData = [
            'error' => [
                'message' => 'Resource not found'
            ]
        ];

        $wsResponse = new WSResponse(Response::HTTP_NOT_FOUND, $errorData);

        $this->systemIdpConnector->expects($this->once())
            ->method('call')
            ->with('GET', '/v1/consumer-rights?vin=' . $vin . '&userDbId=' . $userDBId)
            ->willReturn($wsResponse);

        $this->logger->expects($this->exactly(2))
            ->method('error');

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Resource not found');

        $this->featureCodeService->getConsumerRightsData($userDBId, $vin);
    }

    public function testGetConsumerRightsDataWithException(): void
    {
        $userDBId = 'test-user-id';
        $vin = 'TEST-VIN-123';

        $this->systemIdpConnector->expects($this->once())
            ->method('call')
            ->with('GET', '/v1/consumer-rights?vin=' . $vin . '&userDbId=' . $userDBId)
            ->willThrowException(new \Exception('Connection error'));

        $this->logger->expects($this->any())
            ->method('error');

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Connection error');

        $this->featureCodeService->getConsumerRightsData($userDBId, $vin);
    }

    public function testGetServiceCodesByVinSuccess(): void
    {
        $userDBId = 'test-user-id';
        $vin = 'TEST-VIN-123';

        $responseData = [
            'success' => [
                'services' => [
                    [
                        ['code' => 'NAE01'],
                        ['code' => 'NAK01'],
                        ['code' => 'NAL01']
                    ]
                ]
            ]
        ];

        $wsResponse = new WSResponse(Response::HTTP_OK, $responseData);

        $this->systemIdpConnector->expects($this->once())
            ->method('call')
            ->with('GET', '/v1/consumer-rights?vin=' . $vin . '&userDbId=' . $userDBId)
            ->willReturn($wsResponse);

        $this->logger->expects($this->any())
            ->method('info');

        $result = $this->featureCodeService->getServiceCodesByVin($vin, $userDBId);

        $this->assertIsArray($result);
        $this->assertCount(3, $result);
        $this->assertContains('NAE01', $result);
        $this->assertContains('NAK01', $result);
        $this->assertContains('NAL01', $result);
    }

    public function testGetServiceCodesByVinWithException(): void
    {
        $userDBId = 'test-user-id';
        $vin = 'TEST-VIN-123';

        $this->systemIdpConnector->expects($this->once())
            ->method('call')
            ->with('GET', '/v1/consumer-rights?vin=' . $vin . '&userDbId=' . $userDBId)
            ->willThrowException(new \Exception('API error'));

        $this->logger->expects($this->any())
            ->method('error');

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('API error');

        $this->featureCodeService->getServiceCodesByVin($vin, $userDBId);
    }

    public function testDynamicStatusConfigCalculationWithJeepBrand(): void
    {
        // Mock the feature code config
        $featureCodeConfig = [
            'NAE01' => [
                'VEHICLE_INFO' => [
                    'code' => 'VEHICLE_INFO',
                    'status' => 'enable',
                    'value' => 'NAE01',
                    'config' => ['engine' => 'UNKNOWN']
                ]
            ],
            'NAK01' => [
                'SEND_TO_NAV' => [
                    'code' => 'SEND_TO_NAV',
                    'status' => 'enable',
                    'value' => 'NAK01',
                    'config' => ['protocol' => 'network']
                ]
            ],
            'NAW01' => [
                'TRIPS' => [
                    'code' => 'TRIPS',
                    'status' => 'enable',
                    'value' => 'NAW01',
                    'config' => ['protocol' => 'network']
                ],
                'VEHICLE_LOCATOR' => [
                    'code' => 'VEHICLE_LOCATOR',
                    'status' => 'enable',
                    'value' => 'VF_B',
                    'config' => [
                        'location' => ['trip', 'manual'],
                        'refresh' => false
                    ]
                ]
            ],
            'VEHICLE_DEEP_REFRESH' => [
                'code' => 'VEHICLE_DEEP_REFRESH',
                'status' => 'enable',
                'value' => 'NAW01'
            ],
            'NON_FDS_KEY' => [
                'GAS_STATION_LOCATOR' => [
                    'code' => 'GAS_STATION_LOCATOR',
                    'status' => 'enable',
                    'value' => '',
                    'config' => ['partner' => 'google']
                ],
                'SMARTPHONE_STATION' => [
                    'code' => 'SMARTPHONE_STATION',
                    'status' => 'enable',
                    'value' => '',
                    'config' => ['type' => 'dashboard']
                ]
            ]
        ];

        $serviceCodes = ['NAE01', 'NAK01', 'NAW01'];
        $lcdv = '1JJP12345678';  // Jeep brand
        $engineType = 'ICE';
        $corvetAttributes = ['DLX00', 'DRC00', 'DZZ01'];  // For SPS check

        $result = $this->featureCodeService->dynamicStatusConfigCalculation(
            $featureCodeConfig,
            $serviceCodes,
            $lcdv,
            $engineType,
            $corvetAttributes
        );

        $this->assertIsArray($result);
        $this->assertNotEmpty($result);

        // Check if NAE01 features are included
        $vehicleInfoFound = false;
        foreach ($result as $feature) {
            if ($feature['code'] === 'VEHICLE_INFO' && $feature['value'] === 'NAE01') {
                $vehicleInfoFound = true;
                $this->assertEquals('ICE', $feature['config']['engine']);
                break;
            }
        }
        $this->assertTrue($vehicleInfoFound, 'VEHICLE_INFO feature not found');

        // Check if NAK01 features are included
        $sendToNavFound = false;
        foreach ($result as $feature) {
            if ($feature['value'] === 'NAK01') {
                $sendToNavFound = true;
                $this->assertEquals(FeatureCode::J4U_NAV_PROTOCOL, $feature['config']['protocol']);
                break;
            }
        }
        $this->assertTrue($sendToNavFound, 'SEND_TO_NAV feature not found');

        // Check if NAW01 features are included
        $tripsFound = false;
        $vehicleLocatorFound = false;
        foreach ($result as $feature) {
            if ($feature['code'] === 'TRIPS' && $feature['value'] === 'NAW01') {
                $tripsFound = true;
                $this->assertEquals(FeatureCode::J4U_TRIPS_PROTOCOL, $feature['config']['protocol']);
            }
            if ($feature['code'] === 'VEHICLE_LOCATOR' && $feature['value'] === 'VF_B') {
                $vehicleLocatorFound = true;
                $this->assertEquals(FeatureCode::J4U_TRIPS_LOCATION, $feature['config']['location']);
                $this->assertFalse($feature['config']['refresh']);
            }
        }
        $this->assertTrue($tripsFound, 'TRIPS feature not found');
        $this->assertTrue($vehicleLocatorFound, 'VEHICLE_LOCATOR feature not found');

        // Check if GAS_STATION_LOCATOR is included for ICE engine
        $gasStationFound = false;
        foreach ($result as $feature) {
            if ($feature['code'] === 'GAS_STATION_LOCATOR') {
                $gasStationFound = true;
                break;
            }
        }
        $this->assertTrue($gasStationFound, 'GAS_STATION_LOCATOR feature not found');

        // Check if SMARTPHONE_STATION is included for SPS vehicle
        $smartphoneStationFound = false;
        foreach ($result as $feature) {
            if ($feature['code'] === 'SMARTPHONE_STATION') {
                $smartphoneStationFound = true;
                break;
            }
        }
        $this->assertTrue($smartphoneStationFound, 'SMARTPHONE_STATION feature not found');

        // Check if VEHICLE_DEEP_REFRESH is included
        $deepRefreshFound = false;
        foreach ($result as $feature) {
            if ($feature['code'] === 'VEHICLE_DEEP_REFRESH') {
                $deepRefreshFound = true;
                $this->assertEquals('NAW01', $feature['config']['value']);
                break;
            }
        }
        $this->assertTrue($deepRefreshFound, 'VEHICLE_DEEP_REFRESH feature not found');
    }

    public function testDynamicStatusConfigCalculationWithNonJeepBrand(): void
    {
        // Mock the feature code config
        $featureCodeConfig = [
            'NAM01' => [
                'DIGITAL_KEY' => [
                    'code' => 'DIGITAL_KEY',
                    'status' => 'enable',
                    'value' => 'NAM01',
                    'config' => ['type' => 'unknown']
                ]
            ],
            'NAO01' => [
                'VEHICLE_INFO' => [
                    'code' => 'VEHICLE_INFO',
                    'status' => 'enable',
                    'value' => 'NAO01',
                    'config' => ['engine' => 'BEV']
                ]
            ],
            'NAO02' => [
                'VEHICLE_INFO' => [
                    'code' => 'VEHICLE_INFO',
                    'status' => 'enable',
                    'value' => 'NAO02',
                    'config' => ['engine' => 'PHEV']
                ]
            ],
            'NON_NAW01' => [
                'VEHICLE_LOCATOR' => [
                    'code' => 'VEHICLE_LOCATOR',
                    'status' => 'enable',
                    'value' => '',
                    'config' => [
                        'location' => ['manual'],
                        'refresh' => false
                    ]
                ]
            ],
            'VEHICLE_DEEP_REFRESH' => [
                'code' => 'VEHICLE_DEEP_REFRESH',
                'status' => 'enable',
                'value' => 'NAO01'
            ],
            'NON_FDS_KEY' => [
                'CHARGE_STATION_LOCATOR' => [
                    'code' => 'CHARGE_STATION_LOCATOR',
                    'status' => 'enable',
                    'value' => '',
                    'config' => [
                        'type' => 'internal'
                    ]
                ]
            ]
        ];

        $serviceCodes = ['NAM01', 'NAO01'];
        $lcdv = '2ABC12345678';  // Non-Jeep brand
        $engineType = 'BEV';
        $corvetAttributes = [];

        $result = $this->featureCodeService->dynamicStatusConfigCalculation(
            $featureCodeConfig,
            $serviceCodes,
            $lcdv,
            $engineType,
            $corvetAttributes
        );

        $this->assertIsArray($result);
        $this->assertNotEmpty($result);

        // Check if NAM01 features are included but without Jeep-specific config
        $digitalKeyFound = false;
        foreach ($result as $feature) {
            if ($feature['code'] === 'DIGITAL_KEY' && $feature['value'] === 'NAM01') {
                $digitalKeyFound = true;
                $this->assertArrayNotHasKey('type', $feature['config']);
                break;
            }
        }
        $this->assertFalse($digitalKeyFound, 'DIGITAL_KEY feature should not be found for non-Jeep brand');

        // Check if NAO01 features are included
        $vehicleInfoFound = false;
        foreach ($result as $feature) {
            if ($feature['code'] === 'VEHICLE_INFO' && $feature['value'] === 'NAO01') {
                $vehicleInfoFound = true;
                $this->assertEquals('BEV', $feature['config']['engine']);
                break;
            }
        }
        $this->assertTrue($vehicleInfoFound, 'VEHICLE_INFO feature not found');

        // Check if NON_NAW01 features are included since NAW01 is not in serviceCodes
        $vehicleLocatorFound = false;
        foreach ($result as $feature) {
            if ($feature['code'] === 'VEHICLE_LOCATOR' && empty($feature['value'])) {
                $vehicleLocatorFound = true;
                $this->assertEquals(['manual'], $feature['config']['location']);
                $this->assertFalse($feature['config']['refresh']);
                break;
            }
        }
        $this->assertTrue($vehicleLocatorFound, 'VEHICLE_LOCATOR feature not found');

        // Check if CHARGE_STATION_LOCATOR is included for BEV engine
        $chargeStationFound = false;
        foreach ($result as $feature) {
            if ($feature['code'] === 'CHARGE_STATION_LOCATOR') {
                $chargeStationFound = true;
                $this->assertEquals('internal', $feature['config']['type']);
                break;
            }
        }
        $this->assertTrue($chargeStationFound, 'CHARGE_STATION_LOCATOR feature not found');

        // Check if VEHICLE_DEEP_REFRESH is included
        $deepRefreshFound = false;
        foreach ($result as $feature) {
            if ($feature['code'] === 'VEHICLE_DEEP_REFRESH') {
                $deepRefreshFound = true;
                $this->assertEquals('NAO01', $feature['config']['value']);
                break;
            }
        }
        $this->assertTrue($deepRefreshFound, 'VEHICLE_DEEP_REFRESH feature not found');
    }

    public function testGetFeaturesCodeWithValidData(): void
    {
        $userDBId = 'test-user-id';
        $vin = 'TEST-VIN-123';
        $lcdv = '1JJP12345678';  // Jeep brand
        $engineType = 'ICE';
        $corvetAttributes = ['DLX00', 'DRC00', 'DZZ01'];  // For SPS check

        // Mock the service codes response
        $responseData = [
            'success' => [
                'services' => [
                    [
                        ['code' => 'NAE01'],
                        ['code' => 'NAK01'],
                        ['code' => 'NAW01']
                    ]
                ]
            ]
        ];

        $wsResponse = new WSResponse(Response::HTTP_OK, $responseData);

        $this->systemIdpConnector->expects($this->once())
            ->method('call')
            ->with('GET', '/v1/consumer-rights?vin=' . $vin . '&userDbId=' . $userDBId)
            ->willReturn($wsResponse);

        $this->logger->expects($this->any())
            ->method('info');

        $result = $this->featureCodeService->getFeaturesCode($userDBId, $vin, $lcdv, $engineType, $corvetAttributes);

        $this->assertIsArray($result);
        $this->assertNotEmpty($result);

        // Since we can't mock the YAML file directly, we'll just check that the function returns an array
        // and doesn't throw an exception
    }

    public function testGetFeaturesCodeWithException(): void
    {
        $userDBId = 'test-user-id';
        $vin = 'TEST-VIN-123';
        $lcdv = '1JJP12345678';
        $engineType = 'ICE';
        $corvetAttributes = [];

        $this->systemIdpConnector->expects($this->once())
            ->method('call')
            ->with('GET', '/v1/consumer-rights?vin=' . $vin . '&userDbId=' . $userDBId)
            ->willThrowException(new \Exception('API error'));

        $this->logger->expects($this->any())
            ->method('error');

        $result = $this->featureCodeService->getFeaturesCode($userDBId, $vin, $lcdv, $engineType, $corvetAttributes);

        $this->assertIsArray($result);
        $this->assertEmpty($result);
    }

    public function testDynamicStatusConfigCalculationWithNAU01Success(): void
    {
        $featureCodeConfig = [
            'NAU01' => [
                'E_ROUTES' => [
                    'code' => 'E_ROUTES',
                    'status' => 'enable',
                    'value' => 'NAU01',
                    'config' => [
                        'linkAndroid' => '',
                        'linkIos' => ''
                    ]
                ]
            ]
        ];
        $serviceCodes = ['NAU01'];  // This will trigger the NAU01 case which makes an HTTP request
        $lcdv = '1JJP12345678';
        $engineType = 'ICE';
        $corvetAttributes = [];

        // Mock the HTTP client response
        $mockResponse = $this->createMock(ResponseInterface::class);
        $mockResponse->method('getStatusCode')->willReturn(200);
        $mockResponse->method('toArray')->willReturn([
            'eROUTES' => [
                'linkAndroid' => 'https://play.google.com/store/apps/details?id=com.example.eroutes',
                'linkIos' => 'https://apps.apple.com/app/eroutes/id123456789'
            ]
        ]);

        $this->httpClient->expects($this->once())
            ->method('request')
            ->with('GET', $this->cdnUrl)
            ->willReturn($mockResponse);

        $result = $this->featureCodeService->dynamicStatusConfigCalculation(
            $featureCodeConfig,
            $serviceCodes,
            $lcdv,
            $engineType,
            $corvetAttributes
        );

        $this->assertIsArray($result);
        $this->assertNotEmpty($result);

        // Check if E_ROUTES feature is included with correct links
        $eRoutesFound = false;
        foreach ($result as $feature) {
            if ($feature['code'] === 'E_ROUTES' && $feature['value'] === 'NAU01') {
                $eRoutesFound = true;
                $this->assertEquals('https://play.google.com/store/apps/details?id=com.example.eroutes', $feature['config']['linkAndroid']);
                $this->assertEquals('https://apps.apple.com/app/eroutes/id123456789', $feature['config']['linkIos']);
                break;
            }
        }
        $this->assertTrue($eRoutesFound, 'E_ROUTES feature not found');
    }

    public function testDynamicStatusConfigCalculationWithNAU01MissingERoutes(): void
    {
        $featureCodeConfig = [
            'NAU01' => [
                'E_ROUTES' => [
                    'code' => 'E_ROUTES',
                    'status' => 'enable',
                    'value' => 'NAU01',
                    'config' => [
                        'linkAndroid' => '',
                        'linkIos' => ''
                    ]
                ]
            ]
        ];
        $serviceCodes = ['NAU01'];
        $lcdv = '1JJP12345678';
        $engineType = 'ICE';
        $corvetAttributes = [];

        // Mock the HTTP client response with missing eROUTES key
        $mockResponse = $this->createMock(ResponseInterface::class);
        $mockResponse->method('getStatusCode')->willReturn(200);
        $mockResponse->method('toArray')->willReturn([
            'otherData' => 'value'
        ]);

        $this->httpClient->expects($this->once())
            ->method('request')
            ->with('GET', $this->cdnUrl)
            ->willReturn($mockResponse);

        $this->logger->expects($this->once())
            ->method('error')
            ->with($this->stringContains('eROUTES key is missing'));

        $result = $this->featureCodeService->dynamicStatusConfigCalculation(
            $featureCodeConfig,
            $serviceCodes,
            $lcdv,
            $engineType,
            $corvetAttributes
        );

        $this->assertIsArray($result);
        // E_ROUTES feature should not be included
        $eRoutesFound = false;
        foreach ($result as $feature) {
            if ($feature['code'] === 'E_ROUTES' && $feature['value'] === 'NAU01') {
                $eRoutesFound = true;
                break;
            }
        }
        $this->assertFalse($eRoutesFound, 'E_ROUTES feature should not be found');
    }

    public function testDynamicStatusConfigCalculationWithNAU01ErrorResponse(): void
    {
        $featureCodeConfig = [
            'NAU01' => [
                'E_ROUTES' => [
                    'code' => 'E_ROUTES',
                    'status' => 'enable',
                    'value' => 'NAU01',
                    'config' => [
                        'linkAndroid' => '',
                        'linkIos' => ''
                    ]
                ]
            ]
        ];
        $serviceCodes = ['NAU01'];
        $lcdv = '1JJP12345678';
        $engineType = 'ICE';
        $corvetAttributes = [];

        // Mock the HTTP client response with error status code
        $mockResponse = $this->createMock(ResponseInterface::class);
        $mockResponse->method('getStatusCode')->willReturn(404);

        $this->httpClient->expects($this->once())
            ->method('request')
            ->with('GET', $this->cdnUrl)
            ->willReturn($mockResponse);

        $this->logger->expects($this->once())
            ->method('error')
            ->with($this->stringContains('Failed to fetch data from CDN'));

        $result = $this->featureCodeService->dynamicStatusConfigCalculation(
            $featureCodeConfig,
            $serviceCodes,
            $lcdv,
            $engineType,
            $corvetAttributes
        );

        $this->assertIsArray($result);
        // E_ROUTES feature should not be included
        $eRoutesFound = false;
        foreach ($result as $feature) {
            if ($feature['code'] === 'E_ROUTES' && $feature['value'] === 'NAU01') {
                $eRoutesFound = true;
                break;
            }
        }
        $this->assertFalse($eRoutesFound, 'E_ROUTES feature should not be found');
    }

    public function testDynamicStatusConfigCalculationWithException(): void
    {
        $featureCodeConfig = [];
        $serviceCodes = ['NAU01'];  // This will trigger the NAU01 case which makes an HTTP request
        $lcdv = '1JJP12345678';
        $engineType = 'ICE';
        $corvetAttributes = [];

        // Mock the HTTP client to throw an exception
        $this->httpClient->expects($this->once())
            ->method('request')
            ->with('GET', $this->cdnUrl)
            ->willThrowException(new \Exception('HTTP error'));

        $this->logger->expects($this->any())
            ->method('error');

        $result = $this->featureCodeService->dynamicStatusConfigCalculation(
            $featureCodeConfig,
            $serviceCodes,
            $lcdv,
            $engineType,
            $corvetAttributes
        );

        $this->assertNull($result);
    }

    public function testIsSpsWithValidSpsVehicle(): void
    {
        // Use reflection to access private method
        $reflectionClass = new \ReflectionClass(FeatureCodeService::class);
        $method = $reflectionClass->getMethod('isSps');
        $method->setAccessible(true);

        // Test with valid SPS vehicle (1IK9 LCDV, DLX=00, DRC=00, DZZ=01)
        $lcdv = '1IK912345678';
        $attributes = ['DLX00', 'DRC00', 'DZZ01'];

        $result = $method->invoke($this->featureCodeService, $lcdv, $attributes);

        $this->assertTrue($result);
    }

    public function testIsSpsWithValidSpsVehicleAlternativeDrc(): void
    {
        // Use reflection to access private method
        $reflectionClass = new \ReflectionClass(FeatureCodeService::class);
        $method = $reflectionClass->getMethod('isSps');
        $method->setAccessible(true);

        // Test with valid SPS vehicle (2IK9 LCDV, DLX=00, DRC=71, DZZ=01)
        $lcdv = '2IK912345678';
        $attributes = ['DLX00', 'DRC71', 'DZZ01'];

        $result = $method->invoke($this->featureCodeService, $lcdv, $attributes);

        $this->assertTrue($result);
    }

    public function testIsSpsWithInvalidLcdv(): void
    {
        // Use reflection to access private method
        $reflectionClass = new \ReflectionClass(FeatureCodeService::class);
        $method = $reflectionClass->getMethod('isSps');
        $method->setAccessible(true);

        // Test with invalid LCDV (not 1IK9 or 2IK9)
        $lcdv = '3ABC12345678';
        $attributes = ['DLX00', 'DRC00', 'DZZ01'];

        $result = $method->invoke($this->featureCodeService, $lcdv, $attributes);

        $this->assertFalse($result);
    }

    public function testIsSpsWithInvalidAttributes(): void
    {
        // Use reflection to access private method
        $reflectionClass = new \ReflectionClass(FeatureCodeService::class);
        $method = $reflectionClass->getMethod('isSps');
        $method->setAccessible(true);

        // Test with invalid attributes
        $lcdv = '1IK912345678';
        $attributes = ['DLX01', 'DRC00', 'DZZ01'];  // DLX should be 00

        $result = $method->invoke($this->featureCodeService, $lcdv, $attributes);

        $this->assertFalse($result);

        // Test with another invalid combination
        $attributes = ['DLX00', 'DRC02', 'DZZ01'];  // DRC should be 00 or 71

        $result = $method->invoke($this->featureCodeService, $lcdv, $attributes);

        $this->assertFalse($result);

        // Test with another invalid combination
        $attributes = ['DLX00', 'DRC00', 'DZZ02'];  // DZZ should be 01

        $result = $method->invoke($this->featureCodeService, $lcdv, $attributes);

        $this->assertFalse($result);
    }
}
