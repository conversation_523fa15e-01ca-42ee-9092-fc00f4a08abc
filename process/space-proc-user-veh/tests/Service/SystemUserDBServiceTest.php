<?php

namespace App\Tests\Service;

use App\Connector\SystemUserDBConnector;
use App\Dto\AddVehicleInputDTO;
use App\Dto\EditVehicleInputDTO;
use App\Helper\WSResponse;
use App\Service\SystemUserDBService;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class SystemUserDBServiceTest extends TestCase
{
    private SystemUserDBService $systemUserDBService;
    private SystemUserDBConnector $connector;
    private LoggerInterface $logger;

    protected function setUp(): void
    {
        $this->connector = $this->createMock(SystemUserDBConnector::class);
        $this->logger = $this->createMock(LoggerInterface::class);
        
        $this->systemUserDBService = new SystemUserDBService($this->connector);
        $this->systemUserDBService->setLogger($this->logger);
    }

    public function testListVehicles(): void
    {
        $customerId = 'test-customer-id';
        $expectedUrl = '/v1/customer/' . $customerId . '/garage';
        $expectedOptions = [];
        
        $mockResponse = new WSResponse(Response::HTTP_OK, ['vehicles' => []]);
        
        $this->connector->expects($this->once())
            ->method('call')
            ->with(Request::METHOD_GET, $expectedUrl, $expectedOptions)
            ->willReturn($mockResponse);
        
        $this->logger->expects($this->once())
            ->method('info')
            ->with($this->stringContains("Call API [$expectedUrl] with options"), $expectedOptions);
        
        $result = $this->systemUserDBService->listVehicles($customerId);
        
        $this->assertSame($mockResponse, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
    }

    public function testAddVehicle(): void
    {
        $customerId = 'test-customer-id';
        $params = [
            'vin' => 'VIN123456789',
            'brand' => 'FT',
            'country' => 'FR'
        ];
        
        $expectedUrl = '/v1/customer/' . $customerId . '/garage';
        $expectedOptions = [
            'json' => $params
        ];
        
        $mockResponse = new WSResponse(Response::HTTP_OK, ['success' => true]);
        
        $this->connector->expects($this->once())
            ->method('call')
            ->with(Request::METHOD_POST, $expectedUrl, $expectedOptions)
            ->willReturn($mockResponse);
        
        $this->logger->expects($this->once())
            ->method('info')
            ->with($this->stringContains("Call API [$expectedUrl] with options"), $expectedOptions);
        
        $result = $this->systemUserDBService->addVehicle($customerId, $params);
        
        $this->assertSame($mockResponse, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
    }

    public function testUpdateVehicle(): void
    {
        $customerId = 'test-customer-id';
        $params = [
            'vin' => 'VIN123456789',
            'brand' => 'FT',
            'country' => 'FR',
            'nickname' => 'My Car'
        ];
        
        $expectedUrl = '/v1/customer/' . $customerId . '/garage';
        $expectedOptions = [
            'json' => $params
        ];
        
        $mockResponse = new WSResponse(Response::HTTP_OK, ['success' => true]);
        
        $this->connector->expects($this->once())
            ->method('call')
            ->with(Request::METHOD_PUT, $expectedUrl, $expectedOptions)
            ->willReturn($mockResponse);
        
        $this->logger->expects($this->once())
            ->method('info')
            ->with($this->stringContains("Call API [$expectedUrl] with options"), $expectedOptions);
        
        $result = $this->systemUserDBService->updateVehicle($customerId, $params);
        
        $this->assertSame($mockResponse, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
    }

    public function testUpdateCustomerGarage(): void
    {
        $vehicleDto = new EditVehicleInputDTO();
        $vehicleDto->setCustomerId('test-customer-id');
        $vehicleDto->setVin('VIN123456789');
        $vehicleDto->setBrand('FT');
        $vehicleDto->setCountry('FR');
        $vehicleDto->setNickName('My Car');
        $vehicleDto->setLicencePlate('ABC123');
        $vehicleDto->setCommercialName('Model X');
        $vehicleDto->setPictureUrl('http://example.com/image.jpg');
        
        $expectedUrl = '/v1/customer/test-customer-id/garage';
        $expectedOptions = [
            'json' => [
                'vin' => 'VIN123456789',
                'brand' => 'FT',
                'country' => 'FR',
                'VehicleNickName' => 'My Car',
                'plateName' => 'ABC123',
                'commercialName' => 'Model X',
                'pictureUrl' => 'http://example.com/image.jpg',
            ]
        ];
        
        $mockResponse = new WSResponse(Response::HTTP_OK, ['success' => true]);
        
        $this->connector->expects($this->once())
            ->method('call')
            ->with(Request::METHOD_PUT, $expectedUrl, $expectedOptions)
            ->willReturn($mockResponse);
        
        $this->logger->expects($this->once())
            ->method('info')
            ->with($this->stringContains("Call API [$expectedUrl] with options"), $expectedOptions);
        
        $result = $this->systemUserDBService->updateCustomerGarage($vehicleDto);
        
        $this->assertSame($mockResponse, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
    }

    public function testDeleteCustomerVehicle(): void
    {
        $userDbId = 'test-customer-id';
        $vin = 'VIN123456789';
        
        $expectedUrl = '/v1/customer/' . $userDbId . '/garage/' . $vin;
        
        $mockResponse = new WSResponse(Response::HTTP_OK, ['success' => true]);
        
        $this->connector->expects($this->once())
            ->method('call')
            ->with(Request::METHOD_DELETE, $expectedUrl)
            ->willReturn($mockResponse);
        
        $this->logger->expects($this->once())
            ->method('info')
            ->with($this->stringContains("Call API [$expectedUrl] with no options."));
        
        $result = $this->systemUserDBService->deleteCustomerVehicle($userDbId, $vin);
        
        $this->assertSame($mockResponse, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
    }
}
