<?php

namespace App\Tests\Service;

use App\Connector\SysSamsDataConnector;
use App\Helper\WSResponse;
use App\Service\ContribService;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class ContribServiceTest extends TestCase
{
    private ContribService $contribService;
    private SysSamsDataConnector $connector;
    private LoggerInterface $logger;

    protected function setUp(): void
    {
        $this->connector = $this->createMock(SysSamsDataConnector::class);
        $this->logger = $this->createMock(LoggerInterface::class);

        $this->contribService = new ContribService($this->connector);
        $this->contribService->setLogger($this->logger);
    }

    public function testGetContribSuccess(): void
    {
        $brand = 'AP';
        $culture = 'en-FR';
        $productId = 'PRODUCT1';
        $source = 'APP';

        $expectedOptions = [
            'query' => [
                'brand' => $brand,
                'culture' => $culture,
                'source' => $source,
            ]
        ];

        $responseData = [
            'success' => [
                'title' => 'Product 1',
                'description' => 'Description of Product 1'
            ]
        ];

        $wsResponse = new WSResponse(Response::HTTP_OK, $responseData);

        $this->connector->expects($this->once())
            ->method('call')
            ->with(Request::METHOD_GET, '/v1/sams/contrib/getInfoByProduct/' . $productId, $expectedOptions)
            ->willReturn($wsResponse);

        $this->logger->expects($this->once())
            ->method('info')
            ->with($this->stringContains('=> Call API [/v1/sams/contrib/getInfoByProduct/PRODUCT1] with options'));

        $result = $this->contribService->getContrib($brand, $culture, $productId, $source);

        $this->assertIsArray($result);
        $this->assertEquals($responseData['success'], $result);
    }

    public function testGetContribWithErrorResponse(): void
    {
        $brand = 'AP';
        $culture = 'en-FR';
        $productId = 'PRODUCT1';
        $source = 'APP';

        $expectedOptions = [
            'query' => [
                'brand' => $brand,
                'culture' => $culture,
                'source' => $source,
            ]
        ];

        $errorData = [
            'error' => [
                'message' => 'Invalid request',
                'code' => Response::HTTP_BAD_REQUEST
            ]
        ];

        $wsResponse = new WSResponse(Response::HTTP_BAD_REQUEST, $errorData);

        $this->connector->expects($this->once())
            ->method('call')
            ->with(Request::METHOD_GET, '/v1/sams/contrib/getInfoByProduct/' . $productId, $expectedOptions)
            ->willReturn($wsResponse);

        $this->logger->expects($this->once())
            ->method('info')
            ->with($this->stringContains('=> Call API [/v1/sams/contrib/getInfoByProduct/PRODUCT1] with options'));

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Error calling API: Invalid request');
        $this->expectExceptionCode(Response::HTTP_BAD_REQUEST);

        $this->contribService->getContrib($brand, $culture, $productId, $source);
    }

    public function testGetContribWithGenericErrorResponse(): void
    {
        $brand = 'AP';
        $culture = 'en-FR';
        $productId = 'PRODUCT1';
        $source = 'APP';

        $expectedOptions = [
            'query' => [
                'brand' => $brand,
                'culture' => $culture,
                'source' => $source,
            ]
        ];

        $errorData = 'Service unavailable';

        $wsResponse = new WSResponse(Response::HTTP_SERVICE_UNAVAILABLE, $errorData);

        $this->connector->expects($this->once())
            ->method('call')
            ->with(Request::METHOD_GET, '/v1/sams/contrib/getInfoByProduct/' . $productId, $expectedOptions)
            ->willReturn($wsResponse);

        $this->logger->expects($this->once())
            ->method('info')
            ->with($this->stringContains('=> Call API [/v1/sams/contrib/getInfoByProduct/PRODUCT1] with options'));

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Error calling API: Service unavailable');
        $this->expectExceptionCode(Response::HTTP_SERVICE_UNAVAILABLE);

        $this->contribService->getContrib($brand, $culture, $productId, $source);
    }

    public function testGetContribInfoByProductIdsSuccess(): void
    {
        $brand = 'AP';
        $culture = 'en-FR';
        $productIds = 'PRODUCT1,PRODUCT2';
        $source = 'APP';

        $expectedOptions = [
            'query' => [
                'brand' => $brand,
                'culture' => $culture,
                'source' => $source,
                'productIds' => $productIds
            ]
        ];

        $responseData = [
            'success' => [
                'PRODUCT1' => [
                    'title' => 'Product 1',
                    'description' => 'Description of Product 1'
                ],
                'PRODUCT2' => [
                    'title' => 'Product 2',
                    'description' => 'Description of Product 2'
                ]
            ]
        ];

        $wsResponse = new WSResponse(Response::HTTP_OK, $responseData);

        $this->connector->expects($this->once())
            ->method('call')
            ->with(Request::METHOD_GET, '/v1/sams/contrib/getInfoByProduct', $expectedOptions)
            ->willReturn($wsResponse);

        $this->logger->expects($this->once())
            ->method('info')
            ->with($this->stringContains('=> Call API [/v1/sams/contrib/getInfoByProduct] with options'));

        $result = $this->contribService->getContribInfoByProductIds($productIds, $brand, $culture, $source);

        $this->assertIsArray($result);
        $this->assertEquals($responseData['success'], $result);
    }

    public function testGetContribInfoByProductIdsWithErrorResponse(): void
    {
        $brand = 'AP';
        $culture = 'en-FR';
        $productIds = 'PRODUCT1,PRODUCT2';
        $source = 'APP';

        $expectedOptions = [
            'query' => [
                'brand' => $brand,
                'culture' => $culture,
                'source' => $source,
                'productIds' => $productIds
            ]
        ];

        $errorData = [
            'error' => [
                'message' => 'Invalid request',
                'code' => Response::HTTP_BAD_REQUEST
            ]
        ];

        $wsResponse = new WSResponse(Response::HTTP_BAD_REQUEST, $errorData);

        $this->connector->expects($this->once())
            ->method('call')
            ->with(Request::METHOD_GET, '/v1/sams/contrib/getInfoByProduct', $expectedOptions)
            ->willReturn($wsResponse);

        $this->logger->expects($this->once())
            ->method('info')
            ->with($this->stringContains('=> Call API [/v1/sams/contrib/getInfoByProduct] with options'));

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Error calling API: Invalid request');
        $this->expectExceptionCode(Response::HTTP_BAD_REQUEST);

        $this->contribService->getContribInfoByProductIds($productIds, $brand, $culture, $source);
    }

    public function testGetContribInfoByProductIdsWithGenericErrorResponse(): void
    {
        $brand = 'AP';
        $culture = 'en-FR';
        $productIds = 'PRODUCT1,PRODUCT2';
        $source = 'APP';

        $expectedOptions = [
            'query' => [
                'brand' => $brand,
                'culture' => $culture,
                'source' => $source,
                'productIds' => $productIds
            ]
        ];

        $errorData = 'Service unavailable';

        $wsResponse = new WSResponse(Response::HTTP_SERVICE_UNAVAILABLE, $errorData);

        $this->connector->expects($this->once())
            ->method('call')
            ->with(Request::METHOD_GET, '/v1/sams/contrib/getInfoByProduct', $expectedOptions)
            ->willReturn($wsResponse);

        $this->logger->expects($this->once())
            ->method('info')
            ->with($this->stringContains('=> Call API [/v1/sams/contrib/getInfoByProduct] with options'));

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Error calling API: Service unavailable');
        $this->expectExceptionCode(Response::HTTP_SERVICE_UNAVAILABLE);

        $this->contribService->getContribInfoByProductIds($productIds, $brand, $culture, $source);
    }
}
