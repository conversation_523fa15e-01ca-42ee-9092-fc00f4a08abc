<?php

namespace App\Tests\Model;

use App\Model\EditVehicleModel;
use PHPUnit\Framework\TestCase;

class EditVehicleModelTest extends TestCase
{
    private EditVehicleModel $editVehicleModel;

    protected function setUp(): void
    {
        $this->editVehicleModel = new EditVehicleModel();
    }

    public function testGettersAndSetters(): void
    {
        // Test value getter and setter
        $this->editVehicleModel->setValue(123);
        $this->assertEquals(123, $this->editVehicleModel->getValue());

        // Test timestamp getter and setter
        $timestamp = time();
        $this->editVehicleModel->setTimestamp($timestamp);
        $this->assertEquals($timestamp, $this->editVehicleModel->getTimestamp());

        // Test source getter and setter
        $this->editVehicleModel->setSource(1);
        $this->assertEquals(1, $this->editVehicleModel->getSource());

        // Test nickName getter and setter
        $this->editVehicleModel->setNickName('My Car');
        $this->assertEquals('My Car', $this->editVehicleModel->getNickName());

        // Test licencePlate getter and setter
        $this->editVehicleModel->setLicencePlate('ABC123');
        $this->assertEquals('ABC123', $this->editVehicleModel->getLicencePlate());
    }

    public function testFluentInterface(): void
    {
        // Test fluent interface (method chaining)
        $result = $this->editVehicleModel
            ->setValue(123)
            ->setTimestamp(time())
            ->setSource(1)
            ->setNickName('My Car')
            ->setLicencePlate('ABC123');

        // Verify that each setter returns $this
        $this->assertSame($this->editVehicleModel, $result);
    }

    public function testNullValue(): void
    {
        // Test with null value for nullable property
        $this->editVehicleModel->setValue(null);
        
        // Verify getter returns null
        $this->assertNull($this->editVehicleModel->getValue());
    }
}
