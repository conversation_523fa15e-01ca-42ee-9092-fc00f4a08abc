<?php

namespace App\Tests\Model;

use App\Model\EditVehicleModel;
use App\Model\EditVehicleModelSuccess;
use PHPUnit\Framework\TestCase;

class EditVehicleModelSuccessTest extends TestCase
{
    private EditVehicleModelSuccess $editVehicleModelSuccess;
    private EditVehicleModel $editVehicleModel;

    protected function setUp(): void
    {
        $this->editVehicleModelSuccess = new EditVehicleModelSuccess();
        $this->editVehicleModel = new EditVehicleModel();
        
        // Set up the EditVehicleModel with test values
        $this->editVehicleModel->setNickName('My Car')
            ->setLicencePlate('ABC123')
            ->setTimestamp(time())
            ->setSource(1)
            ->setValue(123);
    }

    public function testGetterAndSetter(): void
    {
        // Test setter
        $result = $this->editVehicleModelSuccess->setEditVehicle($this->editVehicleModel);
        
        // Test getter
        $retrievedModel = $this->editVehicleModelSuccess->getEditVehicle();
        
        // Verify the model was set correctly
        $this->assertSame($this->editVehicleModel, $retrievedModel);
        
        // Verify fluent interface (method chaining)
        $this->assertSame($this->editVehicleModelSuccess, $result);
    }
}
