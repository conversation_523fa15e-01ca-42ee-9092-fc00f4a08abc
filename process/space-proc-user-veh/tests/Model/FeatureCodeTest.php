<?php

namespace App\Tests\Model;

use App\Model\FeatureCode;
use PHPUnit\Framework\TestCase;

class FeatureCodeTest extends TestCase
{
    private FeatureCode $featureCode;

    protected function setUp(): void
    {
        $this->featureCode = new FeatureCode();
    }

    public function testGettersAndSetters(): void
    {
        // Test code getter and setter
        $this->featureCode->setCode('VEHICLE_INFO');
        $this->assertEquals('VEHICLE_INFO', $this->featureCode->getCode());

        // Test status getter and setter
        $this->featureCode->setStatus('enable');
        $this->assertEquals('enable', $this->featureCode->getStatus());

        // Test value getter and setter
        $this->featureCode->setValue('NAE01');
        $this->assertEquals('NAE01', $this->featureCode->getValue());

        // Test config getter and setter
        $config = ['engine' => 'BEV'];
        $this->featureCode->setConfig($config);
        $this->assertEquals($config, $this->featureCode->getConfig());
    }

    public function testToArray(): void
    {
        // Set up the feature code with test values
        $this->featureCode->setCode('VEHICLE_INFO');
        $this->featureCode->setStatus('enable');
        $this->featureCode->setValue('NAE01');
        $this->featureCode->setConfig(['engine' => 'BEV']);

        // Convert to array
        $array = $this->featureCode->__toArray();

        // Verify the array contains all the expected properties
        $this->assertIsArray($array);
        $this->assertArrayHasKey('code', $array);
        $this->assertArrayHasKey('status', $array);
        $this->assertArrayHasKey('value', $array);
        $this->assertArrayHasKey('config', $array);

        // Verify the values in the array
        $this->assertEquals('VEHICLE_INFO', $array['code']);
        $this->assertEquals('enable', $array['status']);
        $this->assertEquals('NAE01', $array['value']);
        $this->assertEquals(['engine' => 'BEV'], $array['config']);
    }

    public function testEmptyValues(): void
    {
        // Test with empty values
        $this->featureCode->setCode('');
        $this->featureCode->setStatus('');
        $this->featureCode->setValue('');
        $this->featureCode->setConfig([]);

        // Verify getters return empty values
        $this->assertEquals('', $this->featureCode->getCode());
        $this->assertEquals('', $this->featureCode->getStatus());
        $this->assertEquals('', $this->featureCode->getValue());
        $this->assertEquals([], $this->featureCode->getConfig());
    }

    public function testConstants(): void
    {
        // Test constants
        $this->assertEquals('JE', FeatureCode::BRAND_JEEP);
        $this->assertEquals('AR', FeatureCode::BRAND_ALFA);
        $this->assertEquals('ble', FeatureCode::J4U_NAV_PROTOCOL);
        $this->assertEquals('BEV', FeatureCode::BATTERY_ELECTRIC_VEHICLE);
        $this->assertEquals('NEA', FeatureCode::J4U_DIGITAL_KEY_TYPE);
        $this->assertEquals('Brain', FeatureCode::A5U_DIGITAL_KEY);
        $this->assertEquals('Atlantis', FeatureCode::LEGACY_XF_DIGITAL_KEY_TYPE);
        $this->assertEquals('network', FeatureCode::J4U_TRIPS_PROTOCOL);
        $this->assertEquals(['trip', 'manual'], FeatureCode::J4U_TRIPS_LOCATION);
        $this->assertEquals('NON_NAW01', FeatureCode::NON_NAW01);
        $this->assertEquals('ADD_VEHICLE', FeatureCode::NON_FDS_KEY);
    }

    public function testFluentInterface(): void
    {
        // Test fluent interface (method chaining)
        $result = $this->featureCode
            ->setCode('VEHICLE_INFO')
            ->setStatus('enable')
            ->setValue('NAE01')
            ->setConfig(['engine' => 'BEV']);

        // Verify that each setter returns $this
        $this->assertSame($this->featureCode, $result);
    }
}
