<?php

namespace App\Tests\Model;

use App\Model\OfferModel;
use App\Model\PriceModel;
use PHPUnit\Framework\TestCase;

class OfferModelTest extends TestCase
{
    private OfferModel $offerModel;

    protected function setUp(): void
    {
        $this->offerModel = new OfferModel();
    }

    public function testGettersAndSetters(): void
    {
        // Test pricingModel getter and setter
        $this->offerModel->setPricingModel('subscription');
        $this->assertEquals('subscription', $this->offerModel->getPricingModel());

        // Test fromPrice getter and setter
        $this->offerModel->setFromPrice(1000);
        $this->assertEquals(1000, $this->offerModel->getFromPrice());

        // Test price getter and setter
        $priceModel = new PriceModel();
        $this->offerModel->setPrice($priceModel);
        $this->assertSame($priceModel, $this->offerModel->getPrice());

        // Test isFreetrial getter and setter
        $this->offerModel->setIsFreetrial(1);
        $this->assertEquals(1, $this->offerModel->getIsFreetrial());
    }

    public function testNullValues(): void
    {
        // Test with null values
        $this->offerModel->setPricingModel(null);
        $this->offerModel->setFromPrice(null);
        $this->offerModel->setPrice(null);
        $this->offerModel->setIsFreetrial(null);

        // Verify getters return null
        $this->assertNull($this->offerModel->getPricingModel());
        $this->assertNull($this->offerModel->getFromPrice());
        $this->assertNull($this->offerModel->getPrice());
        $this->assertNull($this->offerModel->getIsFreetrial());
    }
}
