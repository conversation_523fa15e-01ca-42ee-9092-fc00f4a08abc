<?php

namespace App\Tests\Model;

use App\Model\PriceModel;
use PHPUnit\Framework\TestCase;

class PriceModelTest extends TestCase
{
    private PriceModel $priceModel;

    protected function setUp(): void
    {
        $this->priceModel = new PriceModel();
    }

    public function testGettersAndSetters(): void
    {
        // Test currency getter and setter
        $this->priceModel->setCurrency('EUR');
        $this->assertEquals('EUR', $this->priceModel->getCurrency());

        // Test price getter and setter
        $this->priceModel->setPrice(1000);
        $this->assertEquals(1000, $this->priceModel->getPrice());

        // Test periodType getter and setter
        $this->priceModel->setPeriodType('monthly');
        $this->assertEquals('monthly', $this->priceModel->getPeriodType());

        // Test typeDiscount getter and setter
        $this->priceModel->setTypeDiscount('percentage');
        $this->assertEquals('percentage', $this->priceModel->getTypeDiscount());
    }

    public function testNullValues(): void
    {
        // Test with null values
        $this->priceModel->setCurrency(null);
        $this->priceModel->setPrice(null);
        $this->priceModel->setPeriodType(null);
        $this->priceModel->setTypeDiscount(null);

        // Verify getters return null
        $this->assertNull($this->priceModel->getCurrency());
        $this->assertNull($this->priceModel->getPrice());
        $this->assertNull($this->priceModel->getPeriodType());
        $this->assertNull($this->priceModel->getTypeDiscount());
    }

    public function testDefaultValues(): void
    {
        $priceModel = new PriceModel();
        
        // Check default values
        $this->assertEquals('', $priceModel->getCurrency());
        $this->assertEquals(0, $priceModel->getPrice());
        $this->assertEquals('', $priceModel->getPeriodType());
        $this->assertEquals('', $priceModel->getTypeDiscount());
    }
}
