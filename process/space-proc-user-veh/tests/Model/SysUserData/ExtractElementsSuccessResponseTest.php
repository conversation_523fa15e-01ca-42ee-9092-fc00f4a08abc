<?php

namespace App\Tests\Model\SysUserData;

use App\Model\SysUserData\ExtractElementsSuccessResponse;
use App\Model\SysUserData\SuccessResponseBlock;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpKernel\Profiler\Profile;

class ExtractElementsSuccessResponseTest extends TestCase
{
    public function testConstructor(): void
    {
        // Create mock for Profile
        $profileMock = $this->createMock(Profile::class);
        
        // Create SuccessResponseBlock
        $successResponseBlock = new SuccessResponseBlock(
            $profileMock,
            'dealer123',
            [['vin' => 'VIN123']]
        );
        
        // Create instance with constructor
        $extractElementsSuccessResponse = new ExtractElementsSuccessResponse($successResponseBlock);
        
        // Verify property was set correctly
        $this->assertSame($successResponseBlock, $extractElementsSuccessResponse->success);
    }
}
