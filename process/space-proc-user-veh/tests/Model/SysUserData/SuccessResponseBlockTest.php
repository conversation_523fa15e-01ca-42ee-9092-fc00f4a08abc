<?php

namespace App\Tests\Model\SysUserData;

use App\Model\SysUserData\SuccessResponseBlock;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpKernel\Profiler\Profile;

class SuccessResponseBlockTest extends TestCase
{
    public function testConstructor(): void
    {
        // Create mock for Profile
        $profileMock = $this->createMock(Profile::class);
        
        // Test data
        $favoriteDealer = 'dealer123';
        $vehicles = [
            ['vin' => 'VIN123', 'brand' => 'Brand1'],
            ['vin' => 'VIN456', 'brand' => 'Brand2']
        ];
        
        // Create instance with constructor
        $successResponseBlock = new SuccessResponseBlock(
            $profileMock,
            $favoriteDealer,
            $vehicles
        );
        
        // Verify properties were set correctly
        $this->assertSame($profileMock, $successResponseBlock->profile);
        $this->assertSame($favoriteDealer, $successResponseBlock->favorite_dealer);
        $this->assertSame($vehicles, $successResponseBlock->vehicles);
    }
}
