<?php

namespace App\Tests\Model;

use App\Model\SamsRatePlanModel;
use PHPUnit\Framework\TestCase;

class SamsRatePlanModelTest extends TestCase
{
    private SamsRatePlanModel $samsRatePlanModel;

    protected function setUp(): void
    {
        $this->samsRatePlanModel = new SamsRatePlanModel(
            'Test Family',
            'TEST123',
            'subscription',
            '9.99',
            'EUR',
            'monthly',
            '12'
        );
    }

    public function testConstructor(): void
    {
        // Test that constructor sets properties correctly
        $this->assertEquals('Test Family', $this->samsRatePlanModel->getProductFamily());
        $this->assertEquals('TEST123', $this->samsRatePlanModel->getProductCode());
        $this->assertEquals('subscription', $this->samsRatePlanModel->getType());
        $this->assertEquals('9.99', $this->samsRatePlanModel->getPrice());
        $this->assertEquals('EUR', $this->samsRatePlanModel->getCurrency());
        $this->assertEquals('monthly', $this->samsRatePlanModel->getPeriod());
        $this->assertEquals('12', $this->samsRatePlanModel->getDuration());
    }

    public function testGettersAndSetters(): void
    {
        // Test productFamily getter and setter
        $this->samsRatePlanModel->setProductFamily('New Family');
        $this->assertEquals('New Family', $this->samsRatePlanModel->getProductFamily());

        // Test productCode getter and setter
        $this->samsRatePlanModel->setProductCode('NEW456');
        $this->assertEquals('NEW456', $this->samsRatePlanModel->getProductCode());

        // Test type getter and setter
        $this->samsRatePlanModel->setType('one-time');
        $this->assertEquals('one-time', $this->samsRatePlanModel->getType());

        // Test price getter and setter
        $this->samsRatePlanModel->setPrice('19.99');
        $this->assertEquals('19.99', $this->samsRatePlanModel->getPrice());

        // Test currency getter and setter
        $this->samsRatePlanModel->setCurrency('USD');
        $this->assertEquals('USD', $this->samsRatePlanModel->getCurrency());

        // Test period getter and setter
        $this->samsRatePlanModel->setPeriod('yearly');
        $this->assertEquals('yearly', $this->samsRatePlanModel->getPeriod());

        // Test duration getter and setter
        $this->samsRatePlanModel->setDuration('24');
        $this->assertEquals('24', $this->samsRatePlanModel->getDuration());
    }

    public function testNullPeriod(): void
    {
        // Test with null period
        $this->samsRatePlanModel->setPeriod(null);
        
        // Verify getter returns null
        $this->assertNull($this->samsRatePlanModel->getPeriod());
    }
}
