<?php

namespace App\Tests\Model;

use App\Model\CatalogModel;
use App\Model\OfferModel;
use PHPUnit\Framework\TestCase;

class CatalogModelTest extends TestCase
{
    private CatalogModel $catalogModel;

    protected function setUp(): void
    {
        $this->catalogModel = new CatalogModel();
    }

    public function testGettersAndSetters(): void
    {
        // Test title getter and setter
        $this->catalogModel->setTitle('Test Title');
        $this->assertEquals('Test Title', $this->catalogModel->getTitle());

        // Test category getter and setter
        $this->catalogModel->setCategory('Test Category');
        $this->assertEquals('Test Category', $this->catalogModel->getCategory());

        // Test description getter and setter
        $this->catalogModel->setDescription('Test Description');
        $this->assertEquals('Test Description', $this->catalogModel->getDescription());

        // Test homePage getter and setter
        $this->catalogModel->setHomePage('https://example.com');
        $this->assertEquals('https://example.com', $this->catalogModel->getHomePage());

        // Test urlSso getter and setter
        $this->catalogModel->setUrlSso('https://sso.example.com');
        $this->assertEquals('https://sso.example.com', $this->catalogModel->getUrlSso());

        // Test urlCvs getter and setter
        $this->catalogModel->setUrlCvs('https://cvs.example.com');
        $this->assertEquals('https://cvs.example.com', $this->catalogModel->getUrlCvs());

        // Test topMainImage getter and setter
        $this->catalogModel->setTopMainImage('image.jpg');
        $this->assertEquals('image.jpg', $this->catalogModel->getTopMainImage());

        // Test price getter and setter
        $this->catalogModel->setPrice(1000);
        $this->assertEquals(1000, $this->catalogModel->getPrice());

        // Test currency getter and setter
        $this->catalogModel->setCurrency('EUR');
        $this->assertEquals('EUR', $this->catalogModel->getCurrency());

        // Test offer getter and setter
        $offer = new OfferModel();
        $this->catalogModel->setOffer($offer);
        $this->assertSame($offer, $this->catalogModel->getOffer());

        // Test isBundle getter and setter
        $this->catalogModel->setIsBundle(true);
        $this->assertTrue($this->catalogModel->getIsBundle());

        // Test type getter and setter
        $this->catalogModel->setType('subscription');
        $this->assertEquals('subscription', $this->catalogModel->getType());

        // Test productGroupName getter and setter
        $this->catalogModel->setProductGroupName('Test Group');
        $this->assertEquals('Test Group', $this->catalogModel->getProductGroupName());
    }

    public function testNullValues(): void
    {
        // Test with null values
        $this->catalogModel->setTitle(null);
        $this->catalogModel->setCategory(null);
        $this->catalogModel->setDescription(null);
        $this->catalogModel->setHomePage(null);
        $this->catalogModel->setUrlSso(null);
        $this->catalogModel->setUrlCvs(null);
        $this->catalogModel->setTopMainImage(null);
        $this->catalogModel->setPrice(null);
        $this->catalogModel->setCurrency(null);
        $this->catalogModel->setOffer(null);
        $this->catalogModel->setIsBundle(null);
        $this->catalogModel->setType(null);
        $this->catalogModel->setProductGroupName(null);

        // Verify getters return null
        $this->assertNull($this->catalogModel->getTitle());
        $this->assertNull($this->catalogModel->getCategory());
        $this->assertNull($this->catalogModel->getDescription());
        $this->assertNull($this->catalogModel->getHomePage());
        $this->assertNull($this->catalogModel->getUrlSso());
        $this->assertNull($this->catalogModel->getUrlCvs());
        $this->assertNull($this->catalogModel->getTopMainImage());
        $this->assertNull($this->catalogModel->getPrice());
        $this->assertNull($this->catalogModel->getCurrency());
        $this->assertNull($this->catalogModel->getOffer());
        $this->assertNull($this->catalogModel->getIsBundle());
        $this->assertNull($this->catalogModel->getType());
        $this->assertNull($this->catalogModel->getProductGroupName());
    }

    public function testDefaultValues(): void
    {
        $catalogModel = new CatalogModel();
        
        // Check default values
        $this->assertEquals('', $catalogModel->getTopMainImage());
        $this->assertEquals(0, $catalogModel->getPrice());
        $this->assertEquals('', $catalogModel->getCurrency());
    }
}
