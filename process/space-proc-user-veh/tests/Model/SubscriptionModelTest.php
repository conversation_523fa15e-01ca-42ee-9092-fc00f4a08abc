<?php

namespace App\Tests\Model;

use App\Model\DateRange;
use App\Model\SubscriptionModel;
use PHPUnit\Framework\TestCase;

class SubscriptionModelTest extends TestCase
{
    private SubscriptionModel $subscriptionModel;

    protected function setUp(): void
    {
        $this->subscriptionModel = new SubscriptionModel();
    }

    public function testConstructor(): void
    {
        $this->assertNull($this->subscriptionModel->getType());
        $this->assertEquals(0, $this->subscriptionModel->getStatus());
        
        $subscriptionModel = new SubscriptionModel('test-type');
        $this->assertEquals('test-type', $subscriptionModel->getType());
    }
    
    public function testGetSetType(): void
    {
        $this->subscriptionModel->setType('test-type');
        $this->assertEquals('test-type', $this->subscriptionModel->getType());
    }
    
    public function testGetSetCategory(): void
    {
        $this->subscriptionModel->setCategory('TEST_CATEGORY');
        $this->assertEquals('test_category', $this->subscriptionModel->getCategory());
    }
    
    public function testGetSetCode(): void
    {
        $this->subscriptionModel->setCode('test-code');
        $this->assertEquals('test-code', $this->subscriptionModel->getCode());
    }
    
    public function testGetSetStatus(): void
    {
        $this->subscriptionModel->setStatus(SubscriptionModel::CONTRACT_ACTIVE);
        $this->assertEquals(SubscriptionModel::CONTRACT_ACTIVE, $this->subscriptionModel->getStatus());
    }
    
    public function testIsActive(): void
    {
        $this->subscriptionModel->setStatus(SubscriptionModel::CONTRACT_ACTIVE);
        $this->assertTrue($this->subscriptionModel->isActive());
        
        $this->subscriptionModel->setStatus(SubscriptionModel::CONTRACT_STANDBY);
        $this->assertFalse($this->subscriptionModel->isActive());
    }
    
    public function testGetSetValidity(): void
    {
        $dateRange = new DateRange('2023-01-01', '2023-12-31');
        $this->subscriptionModel->setValidity($dateRange);
        $this->assertSame($dateRange, $this->subscriptionModel->getValidity());
    }
    
    public function testGetSetIsExtensible(): void
    {
        $this->subscriptionModel->setIsExtensible(true);
        $this->assertTrue($this->subscriptionModel->getIsExtensible());
        
        $this->subscriptionModel->setIsExtensible(false);
        $this->assertFalse($this->subscriptionModel->getIsExtensible());
    }
    
    public function testGetSetTitle(): void
    {
        $this->subscriptionModel->setTitle('test-title');
        $this->assertEquals('test-title', $this->subscriptionModel->getTitle());
    }
    
    public function testGetSetStatusReason(): void
    {
        $this->subscriptionModel->setStatusReason('test-status-reason');
        $this->assertEquals('test-status-reason', $this->subscriptionModel->getStatusReason());
    }
    
    public function testGetSetSubscriptionTechCreationDate(): void
    {
        $this->subscriptionModel->setSubscriptionTechCreationDate('2023-01-01');
        $this->assertEquals('2023-01-01', $this->subscriptionModel->getSubscriptionTechCreationDate());
    }
    
    public function testGetSetHasFreeTrial(): void
    {
        $this->subscriptionModel->setHasFreeTrial(true);
        $this->assertTrue($this->subscriptionModel->getHasFreeTrial());
        
        $this->subscriptionModel->setHasFreeTrial(false);
        $this->assertFalse($this->subscriptionModel->getHasFreeTrial());
    }
    
    public function testGetSetAssociationId(): void
    {
        $this->subscriptionModel->setAssociationId('test-association-id');
        $this->assertEquals('test-association-id', $this->subscriptionModel->getAssociationId());
    }
    
    public function testGetSetTopMainImage(): void
    {
        $this->subscriptionModel->setTopMainImage('test-top-main-image');
        $this->assertEquals('test-top-main-image', $this->subscriptionModel->getTopMainImage());
    }
    
    public function testGetSetUrlSso(): void
    {
        $this->subscriptionModel->setUrlSso('test-url-sso');
        $this->assertEquals('test-url-sso', $this->subscriptionModel->getUrlSso());
    }
    
    public function testGetSetUrlCvs(): void
    {
        $this->subscriptionModel->setUrlCvs('test-url-cvs');
        $this->assertEquals('test-url-cvs', $this->subscriptionModel->getUrlCvs());
    }
    
    public function testSetPriorityWithPendingActivation(): void
    {
        $this->subscriptionModel->setPriority('PENDING ACTIVATION');
        $this->assertEquals(2, $this->subscriptionModel->getPriority());
    }
    
    public function testSetPriorityWithPendingIdentification(): void
    {
        $this->subscriptionModel->setPriority('PENDING IDENTIFICATION');
        $this->assertEquals(1, $this->subscriptionModel->getPriority());
    }
    
    public function testSetPriorityWithActivated(): void
    {
        $this->subscriptionModel->setPriority('ACTIVATED');
        $this->assertEquals(3, $this->subscriptionModel->getPriority());
    }
    
    public function testSetPriorityWithPendingSubscription(): void
    {
        $this->subscriptionModel->setPriority('PENDING SUBSCRIPTION');
        $this->assertEquals(4, $this->subscriptionModel->getPriority());
        
        $this->subscriptionModel->setPriority('EN ATTENTE DE SOUSCRIPTION');
        $this->assertEquals(4, $this->subscriptionModel->getPriority());
    }
    
    public function testSetPriorityWithCancelled(): void
    {
        $this->subscriptionModel->setPriority('CANCELLED');
        $this->assertEquals(8, $this->subscriptionModel->getPriority());
    }
    
    public function testSetPriorityWithPendingCancellation(): void
    {
        $this->subscriptionModel->setPriority('PENDING CANCELLATION');
        $this->assertEquals(7, $this->subscriptionModel->getPriority());
    }
    
    public function testSetPriorityWithExpired(): void
    {
        $this->subscriptionModel->setPriority('EXPIRED');
        $this->assertEquals(6, $this->subscriptionModel->getPriority());
    }
    
    public function testSetPriorityWithExpiredIn(): void
    {
        $this->subscriptionModel->setPriority('EXPIRED IN');
        $this->assertEquals(5, $this->subscriptionModel->getPriority());
    }
    
    public function testSetPriorityWithTerminated(): void
    {
        $this->subscriptionModel->setPriority('TERMINATED');
        $this->assertEquals(10, $this->subscriptionModel->getPriority());
    }
    
    public function testSetPriorityWithPendingTermination(): void
    {
        $this->subscriptionModel->setPriority('PENDING TERMINATION');
        $this->assertEquals(9, $this->subscriptionModel->getPriority());
    }
    
    public function testSetPriorityWithFailedPayment(): void
    {
        $this->subscriptionModel->setPriority('FAILED PAYMENT');
        $this->assertEquals(11, $this->subscriptionModel->getPriority());
    }
    
    public function testSetPriorityWithDeactivated(): void
    {
        $this->subscriptionModel->setPriority('DEACTIVATED');
        $this->assertEquals(13, $this->subscriptionModel->getPriority());
    }
    
    public function testSetPriorityWithPendingDeactivation(): void
    {
        $this->subscriptionModel->setPriority('PENDING DEACTIVATION');
        $this->assertEquals(12, $this->subscriptionModel->getPriority());
    }
    
    public function testSetPriorityWithUnknownStatus(): void
    {
        $this->subscriptionModel->setPriority('UNKNOWN_STATUS');
        $this->assertEquals(20, $this->subscriptionModel->getPriority());
    }
}
