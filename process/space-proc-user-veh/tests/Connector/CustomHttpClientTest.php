<?php

namespace App\Tests\Connector;

use App\Connector\CustomHttpClient;
use App\Connector\InternalHttpClientService;
use App\Helper\WSResponse;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\HttpClient\ResponseInterface;

class CustomHttpClientTest extends TestCase
{
    private $httpClient;
    private $logger;
    private $customHttpClient;
    private $response;

    protected function setUp(): void
    {
        $this->httpClient = $this->createMock(InternalHttpClientService::class);
        $this->response = $this->createMock(ResponseInterface::class);
        $this->logger = $this->createMock(LoggerInterface::class);
        
        $this->customHttpClient = new CustomHttpClient($this->httpClient);
        $this->customHttpClient->setLogger($this->logger);
    }

    public function testConstructor(): void
    {
        // Test that the constructor properly sets the client
        $httpClient = $this->createMock(InternalHttpClientService::class);
        $customHttpClient = new CustomHttpClient($httpClient);
        
        // Use reflection to check if the client property is set correctly
        $reflection = new \ReflectionClass($customHttpClient);
        $property = $reflection->getProperty('client');
        $property->setAccessible(true);
        
        $this->assertSame($httpClient, $property->getValue($customHttpClient));
    }

    public function testRequestSuccessWithContent(): void
    {
        // Prepare test data
        $method = 'GET';
        $url = 'http://example.com/api';
        $options = ['headers' => ['Content-Type' => 'application/json']];
        $responseData = ['foo' => 'bar'];
        $statusCode = Response::HTTP_OK;
        
        // Configure mocks
        $this->response->method('getStatusCode')->willReturn($statusCode);
        $this->response->method('toArray')->willReturn($responseData);
        
        $this->httpClient->expects($this->once())
            ->method('request')
            ->with($method, $url, $options)
            ->willReturn($this->response);
        
        $this->logger->expects($this->once())
            ->method('info')
            ->with($this->stringContains("Call URL: {$url} {$method}"));
        
        // Execute the method
        $result = $this->customHttpClient->request($method, $url, $options);
        
        // Assert the result
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals($statusCode, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }

    public function testRequestSuccessWithNoContent(): void
    {
        // Prepare test data
        $method = 'DELETE';
        $url = 'http://example.com/api/resource/123';
        $options = [];
        $statusCode = Response::HTTP_NO_CONTENT;
        
        // Configure mocks
        $this->response->method('getStatusCode')->willReturn($statusCode);
        $this->response->method('toArray')->willThrowException(new \Exception('No content'));
        
        $this->httpClient->expects($this->once())
            ->method('request')
            ->with($method, $url, $options)
            ->willReturn($this->response);
        
        $this->logger->expects($this->once())
            ->method('info')
            ->with($this->stringContains("Call URL: {$url} {$method}"));
        
        // Execute the method
        $result = $this->customHttpClient->request($method, $url, $options);
        
        // Assert the result
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals($statusCode, $result->getCode());
        $this->assertEquals([], $result->getData());
    }

    public function testRequestWithException(): void
    {
        // Prepare test data
        $method = 'POST';
        $url = 'http://example.com/api/resource';
        $options = ['json' => ['name' => 'test']];
        $exceptionMessage = 'Connection error';
        $exceptionCode = Response::HTTP_BAD_REQUEST;
        $exception = new \Exception($exceptionMessage, $exceptionCode);
        
        // Configure mocks
        $this->httpClient->expects($this->once())
            ->method('request')
            ->with($method, $url, $options)
            ->willThrowException($exception);
        
        $this->logger->expects($this->once())
            ->method('info')
            ->with($this->stringContains("Call URL: {$url} {$method}"));
        
        $this->logger->expects($this->once())
            ->method('error')
            ->with($this->stringContains('Cached Exception'), $this->anything());
        
        // Execute the method and expect exception
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage($exceptionMessage);
        $this->expectExceptionCode($exceptionCode);
        
        $this->customHttpClient->request($method, $url, $options);
    }
}
