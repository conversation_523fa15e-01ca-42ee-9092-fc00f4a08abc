<?php

namespace App\Tests\Connector;

use App\Connector\CustomHttpClient;
use App\Connector\SysSamsDataConnector;
use App\Helper\WSResponse;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;

class SysSamsDataConnectorTest extends TestCase
{
    private $customHttpClient;
    private $logger;
    private $sysSamsDataConnector;
    private $url = 'https://example.com/sams-api';

    protected function setUp(): void
    {
        $this->customHttpClient = $this->createMock(CustomHttpClient::class);
        $this->logger = $this->createMock(LoggerInterface::class);
        
        $this->sysSamsDataConnector = new SysSamsDataConnector(
            $this->customHttpClient,
            $this->url
        );
        $this->sysSamsDataConnector->setLogger($this->logger);
    }

    public function testConstructor(): void
    {
        // Test that the constructor properly sets the client and url
        $customHttpClient = $this->createMock(CustomHttpClient::class);
        $url = 'https://example.com/sams-api';
        
        $sysSamsDataConnector = new SysSamsDataConnector($customHttpClient, $url);
        
        // Use reflection to check if the properties are set correctly
        $reflection = new \ReflectionClass($sysSamsDataConnector);
        
        $clientProperty = $reflection->getProperty('client');
        $clientProperty->setAccessible(true);
        $this->assertSame($customHttpClient, $clientProperty->getValue($sysSamsDataConnector));
        
        $urlProperty = $reflection->getProperty('url');
        $urlProperty->setAccessible(true);
        $this->assertSame($url, $urlProperty->getValue($sysSamsDataConnector));
    }

    public function testCallSuccess(): void
    {
        // Prepare test data
        $method = 'GET';
        $uri = '/v1/sams/data';
        $options = ['headers' => ['Content-Type' => 'application/json']];
        $expectedUrl = $this->url . $uri;
        $responseData = ['success' => true, 'data' => ['foo' => 'bar']];
        $statusCode = 200;
        $wsResponse = new WSResponse($statusCode, $responseData);
        
        // Configure mocks
        $this->logger->expects($this->once())
            ->method('info')
            ->with($this->stringContains('::call ' . $expectedUrl));
        
        $this->customHttpClient->expects($this->once())
            ->method('request')
            ->with($method, $expectedUrl, $options)
            ->willReturn($wsResponse);
        
        // Execute the method
        $result = $this->sysSamsDataConnector->call($method, $uri, $options);
        
        // Assert the result
        $this->assertSame($wsResponse, $result);
        $this->assertEquals($statusCode, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }

    public function testCallWithException(): void
    {
        // Prepare test data
        $method = 'POST';
        $uri = '/v1/sams/data';
        $options = ['json' => ['name' => 'test']];
        $expectedUrl = $this->url . $uri;
        $exceptionMessage = 'Connection error';
        $exceptionCode = 400;
        $exception = new \Exception($exceptionMessage, $exceptionCode);
        
        // Configure mocks
        $this->logger->expects($this->once())
            ->method('info')
            ->with($this->stringContains('::call ' . $expectedUrl));
        
        $this->customHttpClient->expects($this->once())
            ->method('request')
            ->with($method, $expectedUrl, $options)
            ->willThrowException($exception);
        
        $this->logger->expects($this->once())
            ->method('error')
            ->with($this->stringContains('::call : Cached Exception'));
        
        // Execute the method
        $result = $this->sysSamsDataConnector->call($method, $uri, $options);
        
        // Assert the result
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals($exceptionCode, $result->getCode());
        $this->assertEquals($exceptionMessage, $result->getData());
    }

    public function testCallWithNullOptions(): void
    {
        // Prepare test data
        $method = 'GET';
        $uri = '/v1/sams/data';
        $options = null;
        $expectedUrl = $this->url . $uri;
        $responseData = ['success' => true, 'data' => ['foo' => 'bar']];
        $statusCode = 200;
        $wsResponse = new WSResponse($statusCode, $responseData);
        
        // Configure mocks
        $this->logger->expects($this->once())
            ->method('info')
            ->with($this->stringContains('::call ' . $expectedUrl));
        
        $this->customHttpClient->expects($this->once())
            ->method('request')
            ->with($method, $expectedUrl, [])
            ->willReturn($wsResponse);
        
        // Execute the method
        $result = $this->sysSamsDataConnector->call($method, $uri, $options);
        
        // Assert the result
        $this->assertSame($wsResponse, $result);
        $this->assertEquals($statusCode, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }
}
