<?php

namespace App\Tests\Connector;

use App\Connector\InternalHttpClientService;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Contracts\HttpClient\ResponseInterface;
use Symfony\Contracts\HttpClient\ResponseStreamInterface;

class InternalHttpClientServiceTest extends TestCase
{
    private $httpClient;
    private $requestStack;
    private $request;
    private $internalHttpClientService;

    protected function setUp(): void
    {
        $this->httpClient = $this->createMock(HttpClientInterface::class);
        $this->requestStack = $this->createMock(RequestStack::class);
        $this->request = $this->createMock(Request::class);
        
        $this->requestStack->method('getCurrentRequest')
            ->willReturn($this->request);
        
        $this->internalHttpClientService = new InternalHttpClientService(
            $this->httpClient,
            $this->requestStack
        );
    }

    public function testConstructor(): void
    {
        // Test that the constructor properly sets the client and request
        $httpClient = $this->createMock(HttpClientInterface::class);
        $requestStack = $this->createMock(RequestStack::class);
        $request = $this->createMock(Request::class);
        
        $requestStack->expects($this->once())
            ->method('getCurrentRequest')
            ->willReturn($request);
        
        $internalHttpClientService = new InternalHttpClientService($httpClient, $requestStack);
        
        // Use reflection to check if the properties are set correctly
        $reflection = new \ReflectionClass($internalHttpClientService);
        
        $clientProperty = $reflection->getProperty('client');
        $clientProperty->setAccessible(true);
        $this->assertSame($httpClient, $clientProperty->getValue($internalHttpClientService));
        
        $requestProperty = $reflection->getProperty('request');
        $requestProperty->setAccessible(true);
        $this->assertSame($request, $requestProperty->getValue($internalHttpClientService));
    }

    public function testRequest(): void
    {
        // Prepare test data
        $method = 'GET';
        $url = 'http://example.com/api';
        $options = ['headers' => ['Content-Type' => 'application/json']];
        $response = $this->createMock(ResponseInterface::class);
        
        // Configure mocks
        $this->httpClient->expects($this->once())
            ->method('request')
            ->with($method, $url, $options)
            ->willReturn($response);
        
        // Execute the method
        $result = $this->internalHttpClientService->request($method, $url, $options);
        
        // Assert the result
        $this->assertSame($response, $result);
    }

    public function testStream(): void
    {
        // Prepare test data
        $responses = [$this->createMock(ResponseInterface::class)];
        $timeout = 10.0;
        $responseStream = $this->createMock(ResponseStreamInterface::class);
        
        // Configure mocks
        $this->httpClient->expects($this->once())
            ->method('stream')
            ->with($responses, $timeout)
            ->willReturn($responseStream);
        
        // Execute the method
        $result = $this->internalHttpClientService->stream($responses, $timeout);
        
        // Assert the result
        $this->assertSame($responseStream, $result);
    }

    public function testWithOptions(): void
    {
        // Prepare test data
        $options = ['timeout' => 30];
        
        // Execute the method
        $result = $this->internalHttpClientService->withOptions($options);
        
        // Assert the result is the same instance
        $this->assertSame($this->internalHttpClientService, $result);
    }
}
