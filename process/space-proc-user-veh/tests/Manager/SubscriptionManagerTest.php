<?php

namespace App\Tests\Manager;

use App\DataMapper\SubscriptionDataMapper;
use App\Helper\ErrorResponse;
use App\Helper\SuccessResponse;
use App\Helper\WSResponse;
use App\Manager\SubscriptionManager;
use App\Service\ContribService;
use App\Service\SubscriptionService;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class SubscriptionManagerTest extends TestCase
{
    private SubscriptionManager $subscriptionManager;
    private SubscriptionService $subscriptionService;
    private ContribService $contribService;
    private ValidatorInterface $validator;
    private SubscriptionDataMapper $subscriptionDataMapper;
    private SerializerInterface $serializer;
    private LoggerInterface $logger;

    protected function setUp(): void
    {
        $this->subscriptionService = $this->createMock(SubscriptionService::class);
        $this->contribService = $this->createMock(ContribService::class);
        $this->validator = $this->createMock(ValidatorInterface::class);
        $this->subscriptionDataMapper = $this->createMock(SubscriptionDataMapper::class);
        $this->serializer = $this->createMock(SerializerInterface::class);
        $this->logger = $this->createMock(LoggerInterface::class);

        $this->subscriptionManager = new SubscriptionManager(
            $this->subscriptionService,
            $this->contribService,
            $this->validator,
            $this->subscriptionDataMapper,
            $this->serializer
        );
        $this->subscriptionManager->setLogger($this->logger);
    }

    public function testGetSubscriptionSuccess(): void
    {
        $userDbId = 'test-user-id';
        $vin = 'TEST-VIN-123';
        $target = 'B2C';
        $brand = 'AP';
        $country = 'FR';
        $language = 'en';
        $source = 'APP';

        // Mock SubscriptionService::getSubscription
        $subscriptionData = [
            'success' => [
                'vehicleProvisionings' => [
                    [
                        'subscription' => [
                            'ratePlans' => [
                                [
                                    'product' => [
                                        'productCode' => 'PRODUCT1'
                                    ]
                                ]
                            ]
                        ]
                    ],
                    [
                        'subscription' => [
                            'ratePlans' => [
                                [
                                    'product' => [
                                        'productCode' => 'PRODUCT2'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];
        $subscriptionResponse = new WSResponse(Response::HTTP_OK, $subscriptionData);
        $this->subscriptionService->expects($this->once())
            ->method('getSubscription')
            ->with($userDbId, $vin, $target)
            ->willReturn($subscriptionResponse);

        // Mock ContribService::getContribInfoByProductIds
        $contribData = [
            'PRODUCT1' => [
                'title' => 'Product 1',
                'description' => 'Description of Product 1'
            ],
            'PRODUCT2' => [
                'title' => 'Product 2',
                'description' => 'Description of Product 2'
            ]
        ];
        $this->contribService->expects($this->once())
            ->method('getContribInfoByProductIds')
            ->with('PRODUCT1,PRODUCT2', $brand, 'en-FR', $source)
            ->willReturn($contribData);

        // Mock SubscriptionDataMapper::transform
        $transformedData = [
            'subscriptions' => [
                [
                    'type' => 'remotelev_phev',
                    'status' => 'active'
                ],
                [
                    'type' => 'navco',
                    'status' => 'active'
                ]
            ]
        ];
        $this->subscriptionDataMapper->expects($this->once())
            ->method('transform')
            ->willReturn($transformedData);

        // Mock SerializerInterface::serialize
        $this->serializer->expects($this->exactly(2))
            ->method('serialize')
            ->willReturnMap([
                [[
                    'type' => 'remotelev_phev',
                    'status' => 'active'
                ], 'json', [], '{"type":"remotelev_phev","status":"active"}'],
                [[
                    'type' => 'navco',
                    'status' => 'active'
                ], 'json', [], '{"type":"navco","status":"active"}']
            ]);

        // Mock logger
        $this->logger->expects($this->exactly(2))
            ->method('info')
            ->withConsecutive(
                [$this->stringContains('=> Call Subscription API')],
                [$this->stringContains('Success: [function getSubscription]')]
            );

        $result = $this->subscriptionManager->getSubscription($userDbId, $vin, $target, $brand, $country, $language, $source);

        $this->assertInstanceOf(SuccessResponse::class, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
        $this->assertIsArray($result->getData());
        $this->assertCount(2, $result->getData());
    }

    public function testGetSubscriptionWithErrorResponse(): void
    {
        $userDbId = 'test-user-id';
        $vin = 'TEST-VIN-123';
        $target = 'B2C';
        $brand = 'AP';
        $country = 'FR';
        $language = 'en';
        $source = 'APP';

        // Mock SubscriptionService::getSubscription with error response
        $errorData = [
            'error' => [
                'message' => 'Invalid request'
            ]
        ];
        $errorResponse = new WSResponse(Response::HTTP_BAD_REQUEST, $errorData);
        $this->subscriptionService->expects($this->once())
            ->method('getSubscription')
            ->with($userDbId, $vin, $target)
            ->willReturn($errorResponse);

        // Mock logger
        $this->logger->expects($this->once())
            ->method('info')
            ->with($this->stringContains('=> Call Subscription API'));
        $this->logger->expects($this->any()) // Change to any() to allow multiple calls
            ->method('error');

        $result = $this->subscriptionManager->getSubscription($userDbId, $vin, $target, $brand, $country, $language, $source);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->getCode());
        $this->assertEquals('Invalid request', $result->getMessage());
    }

    public function testGetSubscriptionWithException(): void
    {
        $userDbId = 'test-user-id';
        $vin = 'TEST-VIN-123';
        $target = 'B2C';
        $brand = 'AP';
        $country = 'FR';
        $language = 'en';
        $source = 'APP';

        // Mock SubscriptionService::getSubscription to throw exception
        $this->subscriptionService->expects($this->once())
            ->method('getSubscription')
            ->with($userDbId, $vin, $target)
            ->willThrowException(new \Exception('Service unavailable', Response::HTTP_SERVICE_UNAVAILABLE));

        // Mock logger
        $this->logger->expects($this->once())
            ->method('info')
            ->with($this->stringContains('=> Call Subscription API'));
        $this->logger->expects($this->once())
            ->method('error')
            ->with($this->stringContains('Catched Exception SubscriptionManager::getSubscription'));

        $result = $this->subscriptionManager->getSubscription($userDbId, $vin, $target, $brand, $country, $language, $source);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals(Response::HTTP_SERVICE_UNAVAILABLE, $result->getCode());
        $this->assertEquals('Service unavailable', $result->getMessage());
    }

    public function testAddContribDataSuccess(): void
    {
        $userDbId = 'test-user-id';
        $brand = 'AP';
        $country = 'FR';
        $language = 'en';
        $source = 'APP';

        $contracts = [
            [
                'subscription' => [
                    'ratePlans' => [
                        [
                            'product' => [
                                'productCode' => 'PRODUCT1'
                            ]
                        ]
                    ]
                ]
            ],
            [
                'subscription' => [
                    'ratePlans' => [
                        [
                            'product' => [
                                'productCode' => 'PRODUCT2'
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $contribData = [
            'PRODUCT1' => [
                'title' => 'Product 1',
                'description' => 'Description of Product 1'
            ],
            'PRODUCT2' => [
                'title' => 'Product 2',
                'description' => 'Description of Product 2'
            ]
        ];

        $this->contribService->expects($this->once())
            ->method('getContribInfoByProductIds')
            ->with('PRODUCT1,PRODUCT2', $brand, 'en-FR', $source)
            ->willReturn($contribData);

        $result = $this->subscriptionManager->addContribData($userDbId, $contracts, $brand, $country, $language, $source);

        $this->assertIsArray($result);
        $this->assertCount(2, $result);
        $this->assertEquals('Product 1', $result[0]['title']);
        $this->assertEquals('Description of Product 1', $result[0]['description']);
        $this->assertEquals('Product 2', $result[1]['title']);
        $this->assertEquals('Description of Product 2', $result[1]['description']);
    }

    public function testAddContribDataWithInvalidContribData(): void
    {
        $userDbId = 'test-user-id';
        $brand = 'AP';
        $country = 'FR';
        $language = 'en';
        $source = 'APP';

        $contracts = [
            [
                'subscription' => [
                    'ratePlans' => [
                        [
                            'product' => [
                                'productCode' => 'PRODUCT1'
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $contribData = [
            'PRODUCT1' => 'invalid-data' // This should be an array, not a string
        ];

        $this->contribService->expects($this->once())
            ->method('getContribInfoByProductIds')
            ->with('PRODUCT1', $brand, 'en-FR', $source)
            ->willReturn($contribData);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Contrib data is not an array');
        $this->expectExceptionCode(Response::HTTP_BAD_REQUEST);

        $this->subscriptionManager->addContribData($userDbId, $contracts, $brand, $country, $language, $source);
    }
}
