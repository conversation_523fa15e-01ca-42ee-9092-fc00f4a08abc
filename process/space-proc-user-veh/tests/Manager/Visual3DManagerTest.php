<?php

namespace App\Tests\Manager;

use App\Manager\Visual3DManager;
use App\Service\VehicleVisualService;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;

class Visual3DManagerTest extends TestCase
{
    private Visual3DManager $visual3DManager;
    private VehicleVisualService $vehicleVisualService;

    protected function setUp(): void
    {
        $this->vehicleVisualService = $this->createMock(VehicleVisualService::class);
        $this->visual3DManager = new Visual3DManager($this->vehicleVisualService);
    }

    public function testLoadImagesWithNonOVBrand(): void
    {
        $lcdv = '1PTBSYHBM604A0F1M09P05FX';
        $brand = 'AP'; // Non-OV brand
        $source = 'APP';
        $vehicleOptions = ['VD09', 'WLWF', 'ZD09', 'ZH47'];

        $visualResponse = [
            'visualSettings' => [
                'visual' => 'https://visuel3d-secure.peugeot.com/V3DImage.ashx',
                'defaultVisual' => 'https://default-image.com/image.jpg',
                'view' => '001',
                'width' => '1000',
                'height' => '800'
            ]
        ];

        $this->vehicleVisualService->expects($this->once())
            ->method('getVisualVehicle')
            ->with($lcdv, $brand)
            ->willReturn($visualResponse);

        $result = $this->visual3DManager->loadImages($lcdv, $brand, $source, $vehicleOptions);

        $this->assertTrue($result['success']);
        $this->assertEquals(Response::HTTP_OK, $result['code']);
        $this->assertStringContainsString('https://visuel3d-secure.peugeot.com/V3DImage.ashx', $result['data']);
        $this->assertStringContainsString('version=1PTBSYHBM604A0F1', $result['data']);
        $this->assertStringContainsString('color=0MM00N9P', $result['data']);
        $this->assertStringContainsString('trim=0P050R', $result['data']);
        $this->assertStringContainsString('view=001', $result['data']);
        $this->assertStringContainsString('width=1000', $result['data']);
        $this->assertStringContainsString('height=800', $result['data']);
        $this->assertStringContainsString('client=MyMarqueWEB', $result['data']);
        $this->assertStringContainsString('back=0', $result['data']);
        $this->assertStringContainsString('OPT1=VD09', $result['data']);
        $this->assertStringContainsString('OPT2=WLWF', $result['data']);
        $this->assertStringContainsString('OPT3=ZD09', $result['data']);
        $this->assertStringContainsString('OPT4=ZH47', $result['data']);
    }

    public function testLoadImagesWithOVBrand(): void
    {
        $lcdv = '1PTBSYHBM604A0F1M09P05FX';
        $brand = 'VX'; // OV brand
        $source = 'APP';
        $vehicleOptions = ['VD09', 'WLWF', 'ZD09', 'ZH47'];

        $visualResponse = [
            'visualSettings' => [
                'visual' => 'https://visuel3d-secure.vauxhall.com/V3DImage.ashx',
                'defaultVisual' => 'https://default-image.com/image.jpg',
                'view' => '001',
                'width' => '1000',
                'height' => '800'
            ]
        ];

        $this->vehicleVisualService->expects($this->once())
            ->method('getVisualVehicle')
            ->with($lcdv, $brand)
            ->willReturn($visualResponse);

        $result = $this->visual3DManager->loadImages($lcdv, $brand, $source, $vehicleOptions);

        $this->assertTrue($result['success']);
        $this->assertEquals(Response::HTTP_OK, $result['code']);
        $this->assertStringContainsString('https://visuel3d-secure.vauxhall.com/V3DImage.ashx', $result['data']);
        // For OV brands, we don't set version, color, and trim parameters
        $this->assertStringNotContainsString('version=', $result['data']);
        $this->assertStringNotContainsString('color=', $result['data']);
        $this->assertStringNotContainsString('trim=', $result['data']);
        $this->assertStringContainsString('view=001', $result['data']);
        $this->assertStringContainsString('width=1000', $result['data']);
        $this->assertStringContainsString('height=800', $result['data']);
        // For OV brands with APP source, we don't add vehicle options
        $this->assertStringNotContainsString('OPT1=', $result['data']);
    }

    public function testLoadImagesWithWebSource(): void
    {
        $lcdv = '1PTBSYHBM604A0F1M09P05FX';
        $brand = 'AP';
        $source = 'WEB'; // Web source
        $vehicleOptions = ['VD09', 'WLWF', 'ZD09', 'ZH47'];

        $visualResponse = [
            'visualSettings' => [
                'visual' => 'https://visuel3d-secure.peugeot.com/V3DImage.ashx',
                'defaultVisual' => 'https://default-image.com/image.jpg',
                'view' => '001',
                'width' => '1000',
                'height' => '800'
            ]
        ];

        $this->vehicleVisualService->expects($this->once())
            ->method('getVisualVehicle')
            ->with($lcdv, $brand)
            ->willReturn($visualResponse);

        $result = $this->visual3DManager->loadImages($lcdv, $brand, $source, $vehicleOptions);

        $this->assertTrue($result['success']);
        $this->assertEquals(Response::HTTP_OK, $result['code']);
        $this->assertStringContainsString('https://visuel3d-secure.peugeot.com/V3DImage.ashx', $result['data']);
        $this->assertStringContainsString('version=1PTBSYHBM604A0F1', $result['data']);
        $this->assertStringContainsString('color=0MM00N9P', $result['data']);
        $this->assertStringContainsString('trim=0P050R', $result['data']);
        $this->assertStringContainsString('view=001', $result['data']);
        $this->assertStringContainsString('width=1000', $result['data']);
        $this->assertStringContainsString('height=800', $result['data']);
        $this->assertStringContainsString('client=MyMarqueWEB', $result['data']);
        $this->assertStringContainsString('back=0', $result['data']);
        // For WEB source, we add vehicle options
        $this->assertStringContainsString('OPT1=VD09', $result['data']);
        $this->assertStringContainsString('OPT2=WLWF', $result['data']);
        $this->assertStringContainsString('OPT3=ZD09', $result['data']);
        $this->assertStringContainsString('OPT4=ZH47', $result['data']);
    }

    public function testLoadImagesWithShortLcdv(): void
    {
        $lcdv = '1PTB'; // Short LCDV
        $brand = 'AP';
        $source = 'APP';
        $vehicleOptions = [];

        $visualResponse = [
            'visualSettings' => [
                'visual' => 'https://visuel3d-secure.peugeot.com/V3DImage.ashx',
                'defaultVisual' => 'https://default-image.com/image.jpg',
                'view' => '001',
                'width' => '1000',
                'height' => '800'
            ]
        ];

        $this->vehicleVisualService->expects($this->once())
            ->method('getVisualVehicle')
            ->with($lcdv, $brand)
            ->willReturn($visualResponse);

        $result = $this->visual3DManager->loadImages($lcdv, $brand, $source, $vehicleOptions);

        $this->assertTrue($result['success']);
        $this->assertEquals(Response::HTTP_OK, $result['code']);
        $this->assertStringContainsString('https://visuel3d-secure.peugeot.com/V3DImage.ashx', $result['data']);
        // For short LCDV, we set empty version and default color and trim
        $this->assertStringContainsString('version=', $result['data']);
        $this->assertStringContainsString('color=0M0N', $result['data']);
        $this->assertStringContainsString('trim=0P0R', $result['data']);
        $this->assertStringContainsString('view=001', $result['data']);
        $this->assertStringContainsString('width=1000', $result['data']);
        $this->assertStringContainsString('height=800', $result['data']);
        $this->assertStringContainsString('client=MyMarqueWEB', $result['data']);
        $this->assertStringContainsString('back=0', $result['data']);
    }

    public function testLoadImagesWithNoVisualSettings(): void
    {
        $lcdv = '1PTBSYHBM604A0F1M09P05FX';
        $brand = 'AP';
        $source = 'APP';
        $vehicleOptions = [];

        $visualResponse = []; // No visualSettings

        $this->vehicleVisualService->expects($this->once())
            ->method('getVisualVehicle')
            ->with($lcdv, $brand)
            ->willReturn($visualResponse);

        $result = $this->visual3DManager->loadImages($lcdv, $brand, $source, $vehicleOptions);

        $this->assertTrue($result['success']);
        $this->assertEquals(Response::HTTP_OK, $result['code']);
        // When no visualSettings, we use empty string for URL
        $this->assertStringContainsString('version=1PTBSYHBM604A0F1', $result['data']);
        $this->assertStringContainsString('color=0MM00N9P', $result['data']);
        $this->assertStringContainsString('trim=0P050R', $result['data']);
        $this->assertStringContainsString('client=MyMarqueWEB', $result['data']);
        $this->assertStringContainsString('back=0', $result['data']);
    }

    public function testLoadImagesWithDefaultVisual(): void
    {
        $lcdv = '1PTBSYHBM604A0F1M09P05FX';
        $brand = 'AP';
        $source = 'APP';
        $vehicleOptions = [];

        $visualResponse = [
            'visualSettings' => [
                'defaultVisual' => 'https://default-image.com/image.jpg',
                'view' => '001',
                'width' => '1000',
                'height' => '800'
            ]
        ];

        $this->vehicleVisualService->expects($this->once())
            ->method('getVisualVehicle')
            ->with($lcdv, $brand)
            ->willReturn($visualResponse);

        $result = $this->visual3DManager->loadImages($lcdv, $brand, $source, $vehicleOptions);

        $this->assertTrue($result['success']);
        $this->assertEquals(Response::HTTP_OK, $result['code']);
        $this->assertStringContainsString('https://default-image.com/image.jpg', $result['data']);
        $this->assertStringContainsString('version=1PTBSYHBM604A0F1', $result['data']);
        $this->assertStringContainsString('color=0MM00N9P', $result['data']);
        $this->assertStringContainsString('trim=0P050R', $result['data']);
        $this->assertStringContainsString('view=001', $result['data']);
        $this->assertStringContainsString('width=1000', $result['data']);
        $this->assertStringContainsString('height=800', $result['data']);
        $this->assertStringContainsString('client=MyMarqueWEB', $result['data']);
        $this->assertStringContainsString('back=0', $result['data']);
    }

    public function testLoadImagesWithNoVisualAndNoDefaultVisual(): void
    {
        $lcdv = '1PTBSYHBM604A0F1M09P05FX';
        $brand = 'AP';
        $source = 'APP';
        $vehicleOptions = [];

        $visualResponse = [
            'visualSettings' => [
                'view' => '001',
                'width' => '1000',
                'height' => '800'
            ]
        ];

        $this->vehicleVisualService->expects($this->once())
            ->method('getVisualVehicle')
            ->with($lcdv, $brand)
            ->willReturn($visualResponse);

        $result = $this->visual3DManager->loadImages($lcdv, $brand, $source, $vehicleOptions);

        $this->assertTrue($result['success']);
        $this->assertEquals(Response::HTTP_OK, $result['code']);
        // When no visual and no defaultVisual, we use empty string for URL
        $this->assertStringContainsString('version=1PTBSYHBM604A0F1', $result['data']);
        $this->assertStringContainsString('color=0MM00N9P', $result['data']);
        $this->assertStringContainsString('trim=0P050R', $result['data']);
        $this->assertStringContainsString('view=001', $result['data']);
        $this->assertStringContainsString('width=1000', $result['data']);
        $this->assertStringContainsString('height=800', $result['data']);
        $this->assertStringContainsString('client=MyMarqueWEB', $result['data']);
        $this->assertStringContainsString('back=0', $result['data']);
    }
}
