<?php

namespace App\Tests\Manager;

use App\Helper\WSResponse;
use App\Manager\SystemUserDBManager;
use App\Service\SystemUserDBService;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;

class SystemUserDBManagerTest extends TestCase
{
    private SystemUserDBManager $systemUserDBManager;
    private SystemUserDBService $systemUserDBService;
    private LoggerInterface $logger;

    protected function setUp(): void
    {
        $this->systemUserDBService = $this->createMock(SystemUserDBService::class);
        $this->logger = $this->createMock(LoggerInterface::class);
        
        $this->systemUserDBManager = new SystemUserDBManager($this->systemUserDBService);
        $this->systemUserDBManager->setLogger($this->logger);
    }

    public function testListVehiclesSuccess(): void
    {
        $customerId = 'test-customer-id';
        $expectedResponse = new WSResponse(
            Response::HTTP_OK,
            [
                'vehicles' => [
                    [
                        'vin' => 'VIN123456789',
                        'brand' => 'FT',
                        'country' => 'FR'
                    ]
                ]
            ]
        );
        
        $this->systemUserDBService->expects($this->once())
            ->method('listVehicles')
            ->with($customerId)
            ->willReturn($expectedResponse);
        
        $this->logger->expects($this->exactly(2))
            ->method('info')
            ->withConsecutive(
                [$this->stringContains("Calling list vehicles API for customer --> $customerId")],
                [$this->stringContains("Response from list vehicles API --> " . $expectedResponse->getCode())]
            );
        
        $result = $this->systemUserDBManager->listVehicles($customerId);
        
        $this->assertSame($expectedResponse, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
    }

    public function testListVehiclesError(): void
    {
        $customerId = 'test-customer-id';
        $errorResponse = new WSResponse(
            Response::HTTP_BAD_REQUEST,
            [
                'error' => [
                    'message' => 'Invalid customer ID'
                ]
            ]
        );
        
        $this->systemUserDBService->expects($this->once())
            ->method('listVehicles')
            ->with($customerId)
            ->willReturn($errorResponse);
        
        $this->logger->expects($this->exactly(2))
            ->method('info')
            ->withConsecutive(
                [$this->stringContains("Calling list vehicles API for customer --> $customerId")],
                [$this->stringContains("Response from list vehicles API --> " . $errorResponse->getCode())]
            );
        
        $result = $this->systemUserDBManager->listVehicles($customerId);
        
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->getCode());
        $this->assertEquals('Invalid customer ID', $result->getData());
    }

    public function testAddVehicleSuccess(): void
    {
        $customerId = 'test-customer-id';
        $params = [
            'vin' => 'VIN123456789',
            'brand' => 'FT',
            'country' => 'FR'
        ];
        
        $expectedResponse = new WSResponse(
            Response::HTTP_OK,
            [
                'success' => true
            ]
        );
        
        $this->systemUserDBService->expects($this->once())
            ->method('addVehicle')
            ->with($customerId, $params)
            ->willReturn($expectedResponse);
        
        $this->logger->expects($this->exactly(2))
            ->method('info')
            ->withConsecutive(
                [$this->stringContains("Calling add vehicle API for customer --> $customerId")],
                [$this->stringContains("Response from add vehicle API --> " . $expectedResponse->getCode())]
            );
        
        $result = $this->systemUserDBManager->addVehicle($customerId, $params);
        
        $this->assertSame($expectedResponse, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
    }

    public function testAddVehicleWithBadRequestFallsBackToUpdate(): void
    {
        $customerId = 'test-customer-id';
        $params = [
            'vin' => 'VIN123456789',
            'brand' => 'FT',
            'country' => 'FR'
        ];
        
        $addResponse = new WSResponse(
            Response::HTTP_BAD_REQUEST,
            [
                'error' => [
                    'message' => 'Vehicle already exists'
                ]
            ]
        );
        
        $updateResponse = new WSResponse(
            Response::HTTP_OK,
            [
                'success' => true
            ]
        );
        
        $this->systemUserDBService->expects($this->once())
            ->method('addVehicle')
            ->with($customerId, $params)
            ->willReturn($addResponse);
        
        $this->systemUserDBService->expects($this->once())
            ->method('updateVehicle')
            ->with($customerId, $params)
            ->willReturn($updateResponse);
        
        $this->logger->expects($this->exactly(3))
            ->method('info')
            ->withConsecutive(
                [$this->stringContains("Calling add vehicle API for customer --> $customerId")],
                [$this->stringContains("Response from add vehicle API --> " . $addResponse->getCode())],
                [$this->stringContains("Response from update vehicle API --> " . $updateResponse->getCode())]
            );
        
        $result = $this->systemUserDBManager->addVehicle($customerId, $params);
        
        $this->assertSame($updateResponse, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
    }

    public function testAddVehicleWithOtherError(): void
    {
        $customerId = 'test-customer-id';
        $params = [
            'vin' => 'VIN123456789',
            'brand' => 'FT',
            'country' => 'FR'
        ];
        
        $errorResponse = new WSResponse(
            Response::HTTP_INTERNAL_SERVER_ERROR,
            [
                'error' => [
                    'message' => 'Server error'
                ]
            ]
        );
        
        $this->systemUserDBService->expects($this->once())
            ->method('addVehicle')
            ->with($customerId, $params)
            ->willReturn($errorResponse);
        
        $this->systemUserDBService->expects($this->never())
            ->method('updateVehicle');
        
        $this->logger->expects($this->exactly(2))
            ->method('info')
            ->withConsecutive(
                [$this->stringContains("Calling add vehicle API for customer --> $customerId")],
                [$this->stringContains("Response from add vehicle API --> " . $errorResponse->getCode())]
            );
        
        $result = $this->systemUserDBManager->addVehicle($customerId, $params);
        
        $this->assertSame($errorResponse, $result);
        $this->assertEquals(Response::HTTP_INTERNAL_SERVER_ERROR, $result->getCode());
    }
}
