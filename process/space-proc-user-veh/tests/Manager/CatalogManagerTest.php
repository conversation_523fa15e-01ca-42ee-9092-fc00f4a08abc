<?php

namespace App\Tests\Manager;

use App\Helper\ErrorResponse;
use App\Helper\SuccessResponse;
use App\Helper\WSResponse;
use App\Manager\CatalogManager;
use App\Service\CatalogService;
use App\Service\ContribService;
use App\Service\CorvetService;
use App\Service\ImageCheckerService;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class CatalogManagerTest extends TestCase
{
    private CatalogManager $catalogManager;
    private CatalogService $catalogService;
    private SerializerInterface $serializer;
    private ContribService $contribService;
    private ImageCheckerService $imageCheckerService;
    private ValidatorInterface $validator;
    private CorvetService $corvetService;
    private LoggerInterface $logger;

    protected function setUp(): void
    {
        $this->catalogService = $this->createMock(CatalogService::class);
        $this->serializer = $this->createMock(SerializerInterface::class);
        $this->contribService = $this->createMock(ContribService::class);
        $this->imageCheckerService = $this->createMock(ImageCheckerService::class);
        $this->validator = $this->createMock(ValidatorInterface::class);
        $this->corvetService = $this->createMock(CorvetService::class);
        $this->logger = $this->createMock(LoggerInterface::class);

        $this->catalogManager = new CatalogManager(
            $this->catalogService,
            $this->serializer,
            $this->contribService,
            $this->imageCheckerService,
            $this->validator,
            $this->corvetService
        );
        $this->catalogManager->setLogger($this->logger);
    }

    public function testGetCategoryConnectedServices(): void
    {
        $this->assertEquals('CONNECTED_SERVICES', $this->catalogManager->getCategory('NAVCOZAR'));
        $this->assertEquals('CONNECTED_SERVICES', $this->catalogManager->getCategory('TMTS'));
        $this->assertEquals('CONNECTED_SERVICES', $this->catalogManager->getCategory('NAVCO'));
        $this->assertEquals('CONNECTED_SERVICES', $this->catalogManager->getCategory('ZAR'));
        $this->assertEquals('CONNECTED_SERVICES', $this->catalogManager->getCategory('LEV'));
        $this->assertEquals('CONNECTED_SERVICES', $this->catalogManager->getCategory('PHEV'));
        $this->assertEquals('CONNECTED_SERVICES', $this->catalogManager->getCategory('BEV'));
        $this->assertEquals('CONNECTED_SERVICES', $this->catalogManager->getCategory('RACCESS'));
        $this->assertEquals('CONNECTED_SERVICES', $this->catalogManager->getCategory('CONNECTEDALARM'));
        $this->assertEquals('CONNECTED_SERVICES', $this->catalogManager->getCategory('DIGITALKEY'));
        $this->assertEquals('CONNECTED_SERVICES', $this->catalogManager->getCategory('AE_CALL'));
        $this->assertEquals('CONNECTED_SERVICES', $this->catalogManager->getCategory('EV_ROUTING_APP'));
        $this->assertEquals('CONNECTED_SERVICES', $this->catalogManager->getCategory('PARTNERSERVICE'));
        $this->assertEquals('CONNECTED_SERVICES', $this->catalogManager->getCategory('TRIPS_IN_THE_CLOUD'));
        $this->assertEquals('CONNECTED_SERVICES', $this->catalogManager->getCategory('STOLEN_VEHICLE'));
    }

    public function testGetCategoryAfterSalesServices(): void
    {
        $this->assertEquals('AFTERSALES_SERVICES', $this->catalogManager->getCategory('DIMBO'));
        $this->assertEquals('AFTERSALES_SERVICES', $this->catalogManager->getCategory('PRIVILEGE'));
    }

    public function testGetCategoryOthers(): void
    {
        $this->assertEquals('OTHERS', $this->catalogManager->getCategory('UNKNOWN'));
        $this->assertEquals('OTHERS', $this->catalogManager->getCategory(null));
    }

    public function testExtractCatalogueGroupNameNavco(): void
    {
        $item = [
            'groupName' => 'NAVCO',
            'groupMemberType' => 'Base Products',
            'familyName' => 'Some Family'
        ];

        $result = $this->invokePrivateMethod($this->catalogManager, 'extractCatalogueGroupName', [$item]);
        $this->assertEquals('NAVCO', $result);
    }

    public function testExtractCatalogueGroupNameZar(): void
    {
        $item = [
            'groupName' => 'NAVCO',
            'groupMemberType' => 'Add On Services',
            'familyName' => 'ZAR'
        ];

        $result = $this->invokePrivateMethod($this->catalogManager, 'extractCatalogueGroupName', [$item]);
        $this->assertEquals('ZAR', $result);
    }

    public function testExtractCatalogueGroupNameNavcozar(): void
    {
        $item = [
            'groupName' => 'NAVCOZAR',
            'groupMemberType' => 'Some Type',
            'familyName' => 'NAVCOZAR'
        ];

        $result = $this->invokePrivateMethod($this->catalogManager, 'extractCatalogueGroupName', [$item]);
        $this->assertEquals('NAVCOZAR', $result);
    }

    public function testExtractCatalogueGroupNameTmts(): void
    {
        $item = [
            'groupName' => 'TMTS',
            'groupMemberType' => 'Some Type',
            'familyName' => 'Some Family'
        ];

        $result = $this->invokePrivateMethod($this->catalogManager, 'extractCatalogueGroupName', [$item]);
        $this->assertEquals('TMTS', $result);
    }

    public function testExtractCatalogueGroupNameLev(): void
    {
        $item = [
            'groupName' => 'LEV',
            'groupMemberType' => 'Some Type',
            'familyName' => 'BEV'
        ];

        $result = $this->invokePrivateMethod($this->catalogManager, 'extractCatalogueGroupName', [$item]);
        $this->assertEquals('LEV', $result);

        $item['familyName'] = 'PHEV';
        $result = $this->invokePrivateMethod($this->catalogManager, 'extractCatalogueGroupName', [$item]);
        $this->assertEquals('LEV', $result);
    }

    public function testExtractCatalogueGroupNameDimbo(): void
    {
        $item = [
            'groupName' => 'DIMBO',
            'groupMemberType' => 'Some Type',
            'familyName' => 'DIMBO'
        ];

        $result = $this->invokePrivateMethod($this->catalogManager, 'extractCatalogueGroupName', [$item]);
        $this->assertEquals('DIMBO', $result);
    }

    public function testExtractCatalogueGroupNameRaccess(): void
    {
        $item = [
            'groupName' => 'RACCESS',
            'groupMemberType' => 'Some Type',
            'familyName' => 'Some Family'
        ];

        $result = $this->invokePrivateMethod($this->catalogManager, 'extractCatalogueGroupName', [$item]);
        $this->assertEquals('RACCESS', $result);
    }

    public function testExtractCatalogueGroupNamePrivilege(): void
    {
        $item = [
            'groupName' => 'DSCP',
            'groupMemberType' => 'Some Type',
            'familyName' => 'DSCP'
        ];

        $result = $this->invokePrivateMethod($this->catalogManager, 'extractCatalogueGroupName', [$item]);
        $this->assertEquals('DSCP', $result);
    }

    public function testExtractCatalogueGroupNameConnectedAlarm(): void
    {
        $item = [
            'groupName' => 'CONNECTEDALARM',
            'groupMemberType' => 'Some Type',
            'familyName' => 'Some Family'
        ];

        $result = $this->invokePrivateMethod($this->catalogManager, 'extractCatalogueGroupName', [$item]);
        $this->assertEquals('CONNECTEDALARM', $result);
    }

    public function testExtractCatalogueGroupNameEvRoutingApp(): void
    {
        $item = [
            'groupName' => 'EV_ROUTING_APP',
            'groupMemberType' => 'Some Type',
            'familyName' => 'Some Family'
        ];

        $result = $this->invokePrivateMethod($this->catalogManager, 'extractCatalogueGroupName', [$item]);
        $this->assertEquals('EV_ROUTING_APP', $result);
    }

    public function testExtractCatalogueGroupNameDigitalKey(): void
    {
        $item = [
            'groupName' => 'DIGITALKEY',
            'groupMemberType' => 'Some Type',
            'familyName' => 'Some Family'
        ];

        $result = $this->invokePrivateMethod($this->catalogManager, 'extractCatalogueGroupName', [$item]);
        $this->assertEquals('DIGITALKEY', $result);
    }

    public function testExtractCatalogueGroupNameAeCall(): void
    {
        $item = [
            'groupName' => 'AE_CALL',
            'groupMemberType' => 'Some Type',
            'familyName' => 'Some Family'
        ];

        $result = $this->invokePrivateMethod($this->catalogManager, 'extractCatalogueGroupName', [$item]);
        $this->assertEquals('AE_CALL', $result);
    }

    public function testExtractCatalogueGroupNamePartnerService(): void
    {
        $item = [
            'groupName' => 'PARTNERSERVICE',
            'groupMemberType' => 'Some Type',
            'familyName' => 'Some Family'
        ];

        $result = $this->invokePrivateMethod($this->catalogManager, 'extractCatalogueGroupName', [$item]);
        $this->assertEquals('PARTNERSERVICE', $result);
    }

    public function testExtractCatalogueGroupNameTripsInTheCloud(): void
    {
        $item = [
            'groupName' => 'TripsintheCloud',
            'groupMemberType' => 'Some Type',
            'familyName' => 'Some Family'
        ];

        $result = $this->invokePrivateMethod($this->catalogManager, 'extractCatalogueGroupName', [$item]);
        $this->assertEquals('TripsintheCloud', $result);
    }

    public function testExtractCatalogueGroupNameStolenVehicle(): void
    {
        $item = [
            'groupName' => 'SECURITY',
            'groupMemberType' => 'Some Type',
            'familyName' => 'Some Family'
        ];

        $result = $this->invokePrivateMethod($this->catalogManager, 'extractCatalogueGroupName', [$item]);
        $this->assertEquals('SECURITY', $result);
    }

    public function testExtractCatalogueGroupNameEmergency(): void
    {
        $item = [
            'groupName' => 'EMERGENCY',
            'groupMemberType' => 'Some Type',
            'familyName' => 'Some Family'
        ];

        $result = $this->invokePrivateMethod($this->catalogManager, 'extractCatalogueGroupName', [$item]);
        $this->assertEquals('EMERGENCY', $result);
    }

    public function testExtractCatalogueGroupNameBundle(): void
    {
        $item = [
            'groupName' => 'Some Group',
            'groupMemberType' => 'Some Type',
            'familyName' => 'Some Family',
            'type' => 'BUNDLE',
            'standaloneProducts' => ['product1', 'product2']
        ];

        $result = $this->invokePrivateMethod($this->catalogManager, 'extractCatalogueGroupName', [$item]);
        $this->assertEquals('BUNDLE', $result);
    }

    public function testExtractCatalogueGroupNameUnknown(): void
    {
        $item = [
            'groupName' => 'UNKNOWN',
            'groupMemberType' => 'Some Type',
            'familyName' => 'Some Family',
            'type' => 'UNKNOWN'
        ];

        $result = $this->invokePrivateMethod($this->catalogManager, 'extractCatalogueGroupName', [$item]);
        $this->assertNull($result);
    }

    public function testGetManagedAttributes(): void
    {
        $attributes = [
            'DCX123', 'DXD456', 'DCD789', 'DRE012', 'DRC345', 'DMW678',
            'DVQ901', 'DJY234', 'D7K567', 'DME890', 'DE2123', 'DZZ456',
            'DLX789', 'DO9012', 'D32345', 'DYR17678', 'UNKNOWN'
        ];

        $result = $this->invokePrivateMethod($this->catalogManager, 'getManagedAttributes', [$attributes]);

        $this->assertIsArray($result);
        $this->assertCount(16, $result);
        $this->assertContains('DCX123', $result);
        $this->assertContains('DXD456', $result);
        $this->assertContains('DCD789', $result);
        $this->assertContains('DRE012', $result);
        $this->assertContains('DRC345', $result);
        $this->assertContains('DMW678', $result);
        $this->assertContains('DVQ901', $result);
        $this->assertContains('DJY234', $result);
        $this->assertContains('D7K567', $result);
        $this->assertContains('DME890', $result);
        $this->assertContains('DE2123', $result);
        $this->assertContains('DZZ456', $result);
        $this->assertContains('DLX789', $result);
        $this->assertContains('DO9012', $result);
        $this->assertContains('D32345', $result);
        $this->assertContains('DYR17678', $result);
        $this->assertNotContains('UNKNOWN', $result);
    }

    public function testGetCatalogSuccess(): void
    {
        $this->markTestSkipped('This test requires more complex mocking');
    }

    public function testGetCatalogWithErrorResponse(): void
    {
        $this->markTestSkipped('This test requires more complex mocking');
    }

    public function testGetCatalogWithException(): void
    {
        $this->markTestSkipped('This test requires more complex mocking');
    }

    /**
     * Helper method to invoke private methods for testing
     */
    private function invokePrivateMethod($object, $methodName, array $parameters = [])
    {
        $reflection = new \ReflectionClass(get_class($object));
        $method = $reflection->getMethod($methodName);
        $method->setAccessible(true);
        return $method->invokeArgs($object, $parameters);
    }
}
