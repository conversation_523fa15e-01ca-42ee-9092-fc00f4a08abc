<?php

namespace App\Message;

use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\Transport\Serialization\Serializer;
use Symfony\Component\Messenger\Exception\InvalidArgumentException;

use App\Trait\LoggerTrait;
use App\Message\VehicleNotificationEnvelopeBody;

class VehicleNotificationCustomSerializer extends Serializer
{
    use LoggerTrait;

    public function decode(array $encodedEnvelope): Envelope
    {
        $userId = '';
        $vin = '';
        $sessionId = '';
        $xApiKey = '';
        $spaceVehicleNotificationMessage = null;
        $flags = JSON_INVALID_UTF8_IGNORE |
            JSON_UNESCAPED_UNICODE |
            JSON_UNESCAPED_SLASHES |
            JSON_THROW_ON_ERROR;

        try {
            $this->logger->info('Decoding Space Vehicle Notification message: ', ['encodedEnvelope' => $encodedEnvelope]);
            $userId = $encodedEnvelope['headers']['userid'] ?? '';
            $vin = $encodedEnvelope['headers']['vin'] ?? '';
            $sessionId = $encodedEnvelope['headers']['SessionId'] ?? '';
            $xApiKey = $encodedEnvelope['headers']['x-api-key'] ?? '';
            $decodedBody = json_decode($encodedEnvelope['body'], true, 512, $flags);
        } catch (\JsonException $e) {
            // skip every retrying
            $this->logger->error(
                'Json decoding exception for Space Vehicle Notification message: encoded envelope body is not valid JSON: ',
                [
                    'exception' => $e->getMessage(),
                    'messageQueueId' => $encodedEnvelope['headers']['message_id'] ?? '',
                    'sessionId' => $sessionId,
                    'userId' => $userId,
                    'vin' => $vin,
                    'xApiKey' => $xApiKey,
                    'spaceVehicleNotificationMessageBody' => $encodedEnvelope['body'],
                ]
            );

            return $this->getEnvelope($userId, $vin, $sessionId, $xApiKey, $spaceVehicleNotificationMessage);
        }

        try {
            if (isset($decodedBody['message']) && $decodedBody['message']) {
                return $this->seralizeEnvelope($userId, $vin, $sessionId, $xApiKey, $decodedBody);
            }
        } catch (\JsonException $e) {
            $this->logger->error(
                'Json decoding exception for the Message field on Space Vehicle Notification message: the field is not valid JSON: ',
                [
                    'exception' => $e->getMessage(),
                    'messageQueueId' => $encodedEnvelope['headers']['message_id'] ?? '',
                    'sessionId' => $sessionId,
                    'userId' => $userId,
                    'vin' => $vin,
                    'spaceVehicleNotificationMessageBody' => $decodedBody['body'],
                ]
            );
        }

        return $this->getEnvelope($userId, $vin, $sessionId, $xApiKey, $spaceVehicleNotificationMessage);
    }

    private function seralizeEnvelope($userId, $vin, $sessionId, $xApiKey, $decodedBody): Envelope
    {
        $decodedBody['userId'] = $userId;
        $decodedBody['vin'] = $vin;
        $decodedBody['sessionId'] = $sessionId;
        $decodedBody['xApiKey'] = $xApiKey;
        $message = $decodedBody['message'] ?? [];
        $decodedBody['message'] = $message;
        $encodedEnvelope['body'] = json_encode($decodedBody);
        $encodedEnvelope['headers']['type'] = VehicleNotificationEnvelopeBody::class;
        $envelope = parent::decode($encodedEnvelope);

        return $envelope;
    }

    private function getEnvelope(
        string $userId,
        string $vin,
        string $sessionId,
        string $xApiKey,
        ?array $message
    ): Envelope {
        return new Envelope(new VehicleNotificationEnvelopeBody($userId, $vin, $sessionId, $xApiKey, $message));
    }
}