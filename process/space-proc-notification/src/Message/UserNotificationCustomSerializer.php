<?php

namespace App\Message;

use Symfony\Component\Messenger\Exception\UnrecoverableMessageHandlingException;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\Transport\Serialization\Serializer;
use Symfony\Component\Messenger\Exception\InvalidArgumentException;

use App\Trait\LoggerTrait;

class UserNotificationCustomSerializer extends Serializer
{
    use LoggerTrait;

    public function decode(array $encodedEnvelope): Envelope
    {
        $userId = '';
        $sessionId = '';
        $xApiKey = '';
        $spaceUserNotificationMessage = null;
        $flags = JSON_INVALID_UTF8_IGNORE |
            JSON_UNESCAPED_UNICODE |
            JSON_UNESCAPED_SLASHES |
            JSON_THROW_ON_ERROR;

        try {
            $this->logger->info('Decoding Space User Notification message: ', ['encodedEnvelope' => $encodedEnvelope]);
            $userId = $encodedEnvelope['headers']['userid'] ?? '';
            $sessionId = $encodedEnvelope['headers']['SessionId'] ?? '';
            $xApiKey = $encodedEnvelope['headers']['x-api-key'] ?? '';
            $decodedBody = json_decode($encodedEnvelope['body'], true, 512, $flags);
        } catch (\JsonException $e) {
            // skip every retrying
            $this->logger->error(
                'Json decoding exception for Space User Notification message: encoded envelope body is not valid JSON: ',
                [
                    'exception' => $e->getMessage(),
                    'messageQueueId' => $encodedEnvelope['headers']['message_id'] ?? '',
                    'userId' => $userId,
                    'sessionId' => $sessionId,
                    'xApiKey' => $xApiKey,
                    'spaceUserNotificationMessageBody' => $encodedEnvelope['body'],
                ]
            );

            return $this->getEnvelope($userId, $sessionId, $xApiKey, $spaceUserNotificationMessage);
        }

        try {
            if (isset($decodedBody['message']) && $decodedBody['message']) {
                return $this->seralizeEnvelope($userId, $sessionId, $xApiKey, $decodedBody);
            }
        } catch (\JsonException $e) {
            $this->logger->error(
                'Json decoding exception for the Message field on Space User Notification message: the field is not valid JSON: ',
                [
                    'exception' => $e->getMessage(),
                    'messageQueueId' => $encodedEnvelope['headers']['message_id'] ?? '',
                    'userId' => $userId,
                    'sessionId' => $sessionId,
                    'xApiKey' => $xApiKey,
                    'spaceUserNotificationMessageBody' => $decodedBody['body'],
                ]
            );
        }
        return $this->getEnvelope($userId, $sessionId, $xApiKey, $spaceUserNotificationMessage);
    }

    private function seralizeEnvelope($userId, $sessionId, $xApiKey, $decodedBody): Envelope
    {
        $decodedBody['userId'] = $userId;
        $decodedBody['sessionId'] = $sessionId;
        $decodedBody['xApiKey'] = $xApiKey;
        $message = $decodedBody['message'] ?? [];
        $decodedBody['message'] = $message;
        // encoding all togheter just one time
        $encodedEnvelope['body'] = json_encode($decodedBody);
        // add rule to enable deserialization for this message
        $encodedEnvelope['headers']['type'] = UserNotificationEnvelopeBody::class;
        $envelope = parent::decode($encodedEnvelope);

        return $envelope;
    }

    private function getEnvelope(
        string $userId,
        string $sessionId,
        string $xApiKey,
        ?array $message
    ): Envelope {
        return new Envelope(new UserNotificationEnvelopeBody($userId, $sessionId, $xApiKey, $message));
    }
}