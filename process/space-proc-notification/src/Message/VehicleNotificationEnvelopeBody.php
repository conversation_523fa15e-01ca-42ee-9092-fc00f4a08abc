<?php

namespace App\Message;

use Symfony\Component\Serializer\Annotation\SerializedName;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Context\ExecutionContextInterface;

class VehicleNotificationEnvelopeBody
{

    public function __construct(
        #[SerializedName('userId')]
        #[Assert\NotBlank]
        private string $userId,
        #[SerializedName('vin')]
        #[Assert\NotBlank]
        private string $vin,
        #[SerializedName('sessionId')]
        #[Assert\NotBlank]
        private string $sessionId,
        #[SerializedName('xApiKey')]
        #[Assert\NotBlank]
        private string $xApiKey,
        #[SerializedName('message')]
        #[Assert\NotBlank]
        #[Assert\Valid]
        private ?array $message
    ) {
    }

    #[Assert\Callback]
    public function validateUserEnvelopeBody(ExecutionContextInterface $context): void
    {
        if ($this->getMessage() === null or !is_array($this->getMessage())) {
            $context->buildViolation('The message should not be empty and it should be array.')
                ->atPath('message')
                ->addViolation();
        }
        
        if ($this->userId === '') {
            $context->buildViolation('The userId should not be empty.')
                ->atPath('userId')
                ->addViolation();
        }

        if ($this->vin === '') {
            $context->buildViolation('The vin should not be empty.')
                ->atPath('vin')
                ->addViolation();
        }

        if ($this->sessionId === '') {
            $context->buildViolation('The sessionId should not be empty.')
                ->atPath('sessionId')
                ->addViolation();
        } 
    }

    public function getUserId(): string
    {
        return $this->userId;
    }

    public function getVin(): string
    {
        return $this->vin;
    }

    public function getSessionId(): string
    {
        return $this->sessionId;
    }

    public function getXApiKey(): string
    {
        return $this->xApiKey;
    }

    public function getMessage(): ?array
    {
        return $this->message;
    }
}
