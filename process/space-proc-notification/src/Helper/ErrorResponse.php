<?php

namespace App\Helper;

use Symfony\Component\HttpFoundation\Response;

/**
 * Error response class.
 */
class ErrorResponse implements ResponseArrayFormat
{
    private ?int $code;

    private string $message;

    private mixed $errors;

    public function __construct(mixed $errors, ?int $code = Response::HTTP_BAD_REQUEST)
    {
        $this->code = $code;
        if (is_string($errors)) {
            $this->message = $errors;
            $this->errors = $errors;
        } else {
            $this->message = 'error occured '.json_encode($errors);
            $this->errors = $errors;
        }
    }

    public function setCode(?int $code): self
    {
        $this->code = $code;

        return $this;
    }

    public function setMessage(string $message): self
    {
        $this->message = $message;

        return $this;
    }

    public function setErrors(mixed $errors): self
    {
        $this->errors = $errors;

        return $this;
    }

    public function toArray(): array
    {
        $response = [];
        if ($this->message) {
            $response['message'] = $this->message;
        }
        if ($this->errors) {
            $response['errors'] = $this->errors;
        }

        return [
            'code' => $this->code < 400 ? Response::HTTP_BAD_REQUEST : $this->code,
            'content' => ['error' => $response],
        ];
    }
}
