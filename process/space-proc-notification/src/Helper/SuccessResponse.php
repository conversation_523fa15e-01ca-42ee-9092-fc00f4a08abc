<?php

namespace App\Helper;

use Symfony\Component\HttpFoundation\Response;

/**
 * Success response class.
 */
class SuccessResponse implements ResponseArrayFormat
{
    private ?int $code;

    private mixed $data;

    public function __construct(mixed $data, ?int $code = Response::HTTP_OK)
    {
        $this->code = $code;
        $this->data = $data;
    }

    public function setCode(?int $code): self
    {
        $this->code = $code;

        return $this;
    }

    public function getCode(): ?int
    {
        return $this->code;
    }

    public function setData(mixed $data): self
    {
        $this->data = $data;

        return $this;
    }

    public function getData(): mixed
    {
        return $this->data;
    }

    public function toArray(): array
    {
        $this->data = !is_array($this->data) ? json_decode($this->data, true) : $this->data;

        return [
            'content' => ['success' => $this->data],
            'code' => $this->code,
        ];
    }
}
