<?php

namespace App\Helper;

use Symfony\Component\Yaml\Yaml;

/**
 *  Brand provider.
 */
class BrandProvider
{
    private const FILE_PATH = __DIR__.'/../../config/space_params.yaml';

    /**
     * get all available Brands.
     */
    public static function getBrands(): array
    {
        $params = Yaml::parseFile(self::FILE_PATH);
        $brands = $params['brands'];

        return $brands;
    }
}