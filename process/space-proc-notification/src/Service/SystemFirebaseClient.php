<?php

namespace App\Service;

use Symfony\Component\HttpFoundation\Request;

use App\Connector\SystemFirebaseConnector;
use App\Helper\WSResponse;
use App\Trait\LoggerTrait;


class SystemFirebaseClient
{
    use LoggerTrait;

    public function __construct(
        private SystemFirebaseConnector $connector
    ) {
        $this->connector = $connector;
    }

    public function sendNotification(array $users, ?array $message)
    {
        $uri = '/v1/push/notification';
        $this->logger->info('Sending notification', ['uri' => $uri, 'users' => $users, 'message' => $message]);
        foreach ($users as $user) {
            if (isset($user['push']) && is_array($user['push'])) {
                foreach ($user['push'] as $pushDetails) {
                    $options = [
                        'headers' => [
                            'pushToken' => $pushDetails['pushToken'] ?? '',
                        ],
                        'json' => [
                            'message' => $message
                        ],
                    ];
                    $this->logger->info('Sending notification', ['uri' => $uri, 'options' => $options]);
                    $response = $this->connector->call(Request::METHOD_POST, $uri, $options);
                    $this->logger->info('Notification sent status', ['response' => $response->getData()]);
                }
            }
        }
        return true;
    }
}
