<?php

namespace App\Model;

use Symfony\Component\Validator\Constraints as Assert;
use App\Helper\BrandProvider;


class FcmTokenRegisterModel
{
    
    public function __construct(
        #[Assert\NotBlank(groups: ['fcmTokenRegister'])]
        private string $deviceId,

        #[Assert\NotBlank(groups: ['fcmTokenRegister'])]
        #[Assert\Choice(callback: [BrandProvider::class, 'getBrands'], message: 'Choose a valid brand.', groups: ['fcmTokenRegister'])]
        private string $brand,

        #[Assert\NotBlank(groups: ['fcmTokenRegister'])]
        private string $country,

        #[Assert\NotBlank(groups: ['fcmTokenRegister'])]
        private string $pushToken,

        #[Assert\NotBlank(groups: ['fcmTokenRegister'])]
        private string $appId,

        #[Assert\NotBlank(groups: ['timestamp'])]
        private int $timestamp,
    ) {}
    public function getDeviceId(): string
    {
        return $this->deviceId;
    }

    public function setDeviceID(string $deviceId): void
    {
        $this->deviceId = $deviceId;
    }

    public function getBrand(): string
    {
        return $this->brand;
    }

    public function setBrand(string $brand): void
    {
        $this->brand = $brand;
    }

    public function getCountry(): string
    {
        return $this->country;
    }

    public function setCountry(string $country): void
    {
        $this->country = $country;
    }

    public function getPushToken(): string
    {
        return $this->pushToken;
    }

    public function setPushToken(string $pushToken): void
    {
        $this->pushToken = $pushToken;
    }

    public function getAppId(): string
    {
        return $this->appId;
    }

    public function setAppId(string $appId): void
    {
        $this->appId = $appId;
    }

    public function getTimestamp(): int
    {
        return $this->timestamp;
    }

    public function setTimestamp(int $timestamp): void
    {
        $this->timestamp = $timestamp;
    }
}