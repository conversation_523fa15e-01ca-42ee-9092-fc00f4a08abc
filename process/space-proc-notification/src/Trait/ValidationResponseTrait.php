<?php

namespace App\Trait;

use App\Helper\ErrorResponse;
use Composer\Semver\Constraint\Constraint;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\ConstraintViolationInterface;

trait ValidationResponseTrait
{
    /**
     * Get the validation error messages.
     */
    protected static function getValidationMessages(ConstraintViolationListInterface $errors): array
    {
        $messages = [];

        if (0 === $errors->count()) {
            return $messages;
        }

        foreach ($errors as $error) {
            /** @var ConstraintViolationInterface $error */
            $name = str_replace(['[', ']'], '', $error->getPropertyPath());
            $messages[$name] = $error->getMessage();
        }

        return $messages;
    }

    /**
     * Get the validation error response.
     */
    protected static function getValidationErrorResponse(array $messages): ErrorResponse
    {
        return (new ErrorResponse($messages, Response::HTTP_UNPROCESSABLE_ENTITY))->setMessage('validation_failed');
    }
}
