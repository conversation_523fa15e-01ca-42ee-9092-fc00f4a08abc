<?php

namespace App\MessageHandler;

use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Component\Messenger\Exception\UnrecoverableMessageHandlingException;

use App\Manager\UserManager;
use App\Message\VehicleNotificationEnvelopeBody;
use App\Message\spaceVehicleNotificationMessage;
use App\Service\SystemFirebaseClient;
use App\Trait\ValidationResponseTrait;
use Psr\Log\LoggerInterface;
use App\Service\NotificationService;


/**
 * Processor class to handle spaceVehicleNotificationMessage messge.
 */
#[AsMessageHandler]
class VehicleNotificationEnvelopeBodyHandler
{
    public const NOTIFICATION_TYPE = 'vehicle';

    // use InParametersValidator;
    use ValidationResponseTrait;

    /**
     * Construct.
     */
    public function __construct(
        private ValidatorInterface $validator,
        private LoggerInterface $logger,
        private UserManager $userManager,
        private SystemFirebaseClient $sysFirebaseClient,
        private NotificationService $notificationService 

    ) {
    }

    /**
     * Process method that implement message handling.
     *
     * @return void
     */
    public function __invoke(VehicleNotificationEnvelopeBody $envelopeBody)
    {
        $this->logger->info('Starting reading a new message', ['message' => $envelopeBody->getMessage()]);
        $errors = $this->validator->validate($envelopeBody);
        $errorMessages = $this->getValidationMessages($errors);
        if (!empty($errorMessages)) {
            $response = $this->getValidationErrorResponse($errorMessages)->toArray();
            throw new UnrecoverableMessageHandlingException(json_encode($response['content']));
        }
        $message = $envelopeBody->getMessage();
        $users = $this->userManager->getUsersBySessionId($envelopeBody->getUserId(), $envelopeBody->getVin(), $envelopeBody->getSessionId());
        $users = (array) json_decode($users->getData(), true)['documents'] ?? [];
        if (empty($users)) {
            $this->logger->error('No users found', ['userId' => $envelopeBody->getUserId(), 'vin' => $envelopeBody->getVin(), 'sessionId' => $envelopeBody->getSessionId()]);
            throw new UnrecoverableMessageHandlingException('No users found');
        }
        $this->logger->info('Users fetched', ['users' => $users]);

        //Validate if the title and body fields in the notification are not empty
        if (!isset($envelopeBody->getMessage()['notification']['title']) || trim($envelopeBody->getMessage()['notification']['title']) === '') {
            $this->logger->error( "Vehicle Notification error: Title is required and cannot be empty.");
        } elseif (!isset($envelopeBody->getMessage()['notification']['body']) || trim($envelopeBody->getMessage()['notification']['body']) === '') {
            $this->logger->error( "Vehicle Notification error: Notification body is required and cannot be empty.");
        } else {
            $this->sysFirebaseClient->sendNotification($users, $envelopeBody->getMessage());

            // Saving notification in DB
            $this->notificationService->saveNotification(self::NOTIFICATION_TYPE, $envelopeBody->getUserId(), $envelopeBody->getMessage(), $envelopeBody->getVin());
        }
    }
}