<?php

namespace App\MessageHandler;

use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Component\Messenger\Exception\UnrecoverableMessageHandlingException;
use Symfony\Component\Validator\Validator\ValidatorInterface;

use App\Manager\UserManager;
use App\Message\UserNotificationEnvelopeBody;
use App\Service\SystemFirebaseClient;
use App\Trait\ValidationResponseTrait;
use Psr\Log\LoggerInterface;
use App\Service\NotificationService;


/**
 * Processor class to handle spaceUserNotificationMessage messge.
 */
#[AsMessageHandler]
class UserNotificationEnvelopeBodyHandler
{
    public const NOTIFICATION_TYPE = 'user';

    // use InParametersValidator;
    use ValidationResponseTrait;

    /**
     * Construct.
     */
    public function __construct(
        private ValidatorInterface $validator,
        private LoggerInterface $logger,
        private UserManager $userManager,
        private SystemFirebaseClient $sysFirebaseClient,
        private NotificationService $notificationService
    ) {
    }

    /**
     * Process method that implement message handling.
     *
     * @return void
     */
    public function __invoke(UserNotificationEnvelopeBody $envelopeBody)
    {
        $this->logger->info('Starting reading a new message', ['message' => $envelopeBody->getMessage()]);
        $errors = $this->validator->validate($envelopeBody);
        $errorMessages = $this->getValidationMessages($errors);
        if (!empty($errorMessages)) {
            $response = $this->getValidationErrorResponse($errorMessages)->toArray();
            throw new UnrecoverableMessageHandlingException(json_encode($response['content']));
        }
        $message = $envelopeBody->getMessage();
        $users = $this->userManager->getUserByUserId($envelopeBody->getUserId());
        $users = (array) json_decode($users->getData(), true)['documents'] ?? [];
        if (empty($users)) {
            $this->logger->error('No users found', ['userId' => $envelopeBody->getUserId()]);
            throw new UnrecoverableMessageHandlingException('No users found');
        }
        
        //Validate if the title and body fields in the notification are not empty
        if (!isset($envelopeBody->getMessage()['notification']['title']) || trim($envelopeBody->getMessage()['notification']['title']) === '') {
            $this->logger->error( message: "Vehicle Notification error: Title is required and cannot be empty.");
        } elseif (!isset($envelopeBody->getMessage()['notification']['body']) || trim($envelopeBody->getMessage()['notification']['body']) === '') {
            $this->logger->error( "Vehicle Notification error: Notification body is required and cannot be empty.");
        } else {
            $this->sysFirebaseClient->sendNotification($users, $envelopeBody->getMessage());

            // Saving notification in DB
            $this->notificationService->saveNotification(self::NOTIFICATION_TYPE, $envelopeBody->getUserId(), $envelopeBody->getMessage());
        }
        $this->logger->info('Users fetched', ['users' => $users]);
    }
}