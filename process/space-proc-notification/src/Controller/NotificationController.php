<?php

namespace App\Controller;

use OpenApi\Attributes as OA;
use OpenApi\Attributes\JsonContent;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\{Request, Response, JsonResponse};
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use App\Trait\ValidationResponseTrait;
use App\Manager\NotificationManager;


#[Route('', name: 'notification_')]
class NotificationController extends AbstractController
{
    use ValidationResponseTrait;

    #[Route('/v1/accounts/{userid}/vehicles/{vin}/notifications', name: 'notification_v1', methods: ['GET'])]
    #[OA\Tag(name: 'Notifications History')]
    #[OA\Parameter(
        name: "since",
        in: "query",
        description: "Filter notifications since a specific timestamp",
        required: false,
        schema: new OA\Schema(type: "string", format: "date-time")
    )]
    #[OA\Parameter(
        name: "till",
        in: "query",
        description: "Filter notifications up to a specific timestamp",
        required: false,
        schema: new OA\Schema(type: "string", format: "date-time")
    )]
    #[OA\Parameter(
        name: "limit",
        in: "query",
        description: "Limit the number of notifications returned",
        required: false,
        schema: new OA\Schema(type: "integer", minimum: 1)
    )]
    #[OA\Parameter(
        name: "offset",
        in: "query",
        description: "Offset for pagination",
        required: false,
        schema: new OA\Schema(type: "integer", minimum: 0)
    )]
    #[OA\Parameter(
        name: "includeLocalNotif",
        in: "query",
        description: "Include local notifications (1 for true, 0 for false)",
        required: false,
        schema: new OA\Schema(type: "integer", enum: [0, 1])
    )]

    #[OA\Response(
        response: 200,
        description: "Successful response with user notifications",
        content: new OA\JsonContent(
            type: "object",
            properties: [
                new OA\Property(property: "userId", type: "string", example: "6f5853d0f9e2442c9c818fecf0eec7cf"),
                new OA\Property(property: "startTS", type: "integer", example: 0),
                new OA\Property(property: "endTS", type: "integer", example: 0),
                new OA\Property(
                    property: "notifications",
                    type: "object",
                    properties: [
                        new OA\Property(property: "totalResults", type: "integer", example: 6),
                        new OA\Property(property: "offset", type: "integer", example: 0),
                        new OA\Property(property: "count", type: "integer", example: 1),
                        new OA\Property(property: "vin", type: "string", example: "VR3UPHNKSKT101603"),
                        new OA\Property(
                            property: "items",
                            type: "array",
                            items: new OA\Items(
                                type: "object",
                                properties: [
                                    new OA\Property(property: "id", type: "string", example: "9954eab6-c81f-481c-8597-ae80809bffdd"),
                                    new OA\Property(
                                        property: "notification",
                                        type: "object",
                                        properties: [
                                            new OA\Property(
                                                property: "context",
                                                type: "object",
                                                properties: [
                                                    new OA\Property(property: "brandMarketingName", type: "string", example: "FORD FIGO"),
                                                    new OA\Property(property: "firstName", type: "string", example: "Tilak"),
                                                    new OA\Property(property: "model", type: "string", example: "FIAT"),
                                                    new OA\Property(property: "nickname", type: "string", example: "QA_EMEA_TS_VIN"),
                                                ]
                                            ),
                                            new OA\Property(
                                                property: "in-app",
                                                type: "object",
                                                properties: [
                                                    new OA\Property(property: "criticality", type: "string", example: "LOW"),
                                                    new OA\Property(property: "body", type: "string", example: "IMI_S12_BODY_15"),
                                                    new OA\Property(property: "subtitle", type: "string", example: "IMI_S12_SUB_15"),
                                                    new OA\Property(property: "title", type: "string", example: "IMI_S12_TITLE_15"),
                                                ]
                                            ),
                                            new OA\Property(
                                                property: "data",
                                                type: "object",
                                                properties: [
                                                    new OA\Property(property: "notificationId", type: "string", example: "S12_RO_START_FAILED_TIMEOUT"),
                                                    new OA\Property(property: "userId", type: "string", example: "100000000f0b4dce874859657ae00100")
                                                ]
                                            ),
                                            new OA\Property(property: "click_action", type: "string", example: "OPEN_ACTIVITY_1"),
                                        ]
                                    ),
                                    new OA\Property(property: "eventName", type: "string", example: "HORN_AND_LIGHTS"),
                                    new OA\Property(property: "timestamp", type: "integer", example: 1549457649131),
                                    new OA\Property(property: "vin", type: "string", example: "VR3UPHNKSKT101603")
                                ]
                            )
                        )
                    ]
                )
            ]
        )
    )]     
    
    #[OA\Response(
        response: 422,
        description: 'Validation failed',
        content: new OA\JsonContent(
            type: 'object',
            properties: [
                new OA\Property(
                    property: 'error',
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'message', type: 'string', example: 'validation_failed'),
                        new OA\Property(
                            property: 'errors',
                            type: 'object',
                            properties: [
                                new OA\Property(property: 'userId', type: 'string', example: 'This value should not be blank.')
                            ]
                        )
                    ]
                )
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: "Bad Request",
        content: new OA\JsonContent(
            type: "object",
            properties: [
                new OA\Property(
                    property: "error",
                    type: "object",
                    properties: [
                        new OA\Property(property: "message", type: "string", example: "Unknown error occurred")
                    ]
                )
            ]
        )
    )]
    
    #[OA\Response(
        response: 500,
        description: "Internal Server Error",
        content: new OA\JsonContent(
            type: "object",
            properties: [
                new OA\Property(
                    property: "error",
                    type: "object",
                    properties: [
                        new OA\Property(property: "message", type: "string", example: "Unknown error occurred")
                    ]
                )
            ]
        )
    )]
    public function vehicleNotifications(string $userid, string $vin, Request $request, NotificationManager $notificationManager, ValidatorInterface $validator): JsonResponse
    {
        try {
            $data['since'] = $request->query->get('since') ?? null;
            $data['till'] = $request->query->get('till') ?? null;
            $data['limit'] = $request->query->get('limit') ?? null;
            $data['offset'] = $request->query->get('offset') ?? null;
            $data['includeLocalNotif'] = $request->query->get('includeLocalNotif') ?? 0;

            $errors = $validator->validate(
                compact('userid', 'vin'),
                new Assert\Collection([
                    'userid' => new Assert\NotBlank(),
                    'vin' => new Assert\NotBlank(),
                ])
            );

            $messages = $this->getValidationMessages($errors);

            if (!empty($messages)) {
                $response = $this->getValidationErrorResponse($messages)->toArray();
                return $this->json($response['content'], $response['code']);
            }

            $response = $notificationManager->getVehicleNotifications($userid, $data, $vin);
            return $this->json($response['content'], $response['code']);
        } catch (\Throwable $e) {
            return $this->json(['error' => ['message' => $e->getMessage()]], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }


    #[Route('/v2/accounts/{userid}/notifications', name: 'notification_v2', methods: ['GET'])]
    #[OA\Tag(name: 'Notifications History')]
    #[OA\Parameter(
        name: "since",
        in: "query",
        description: "Filter notifications since a specific timestamp",
        required: false,
        schema: new OA\Schema(type: "string", format: "date-time")
    )]
    #[OA\Parameter(
        name: "till",
        in: "query",
        description: "Filter notifications up to a specific timestamp",
        required: false,
        schema: new OA\Schema(type: "string", format: "date-time")
    )]
    #[OA\Parameter(
        name: "limit",
        in: "query",
        description: "Limit the number of notifications returned",
        required: false,
        schema: new OA\Schema(type: "integer", minimum: 1)
    )]
    #[OA\Parameter(
        name: "offset",
        in: "query",
        description: "Offset for pagination",
        required: false,
        schema: new OA\Schema(type: "integer", minimum: 0)
    )]
    #[OA\Parameter(
        name: "includeLocalNotif",
        in: "query",
        description: "Include local notifications (1 for true, 0 for false)",
        required: false,
        schema: new OA\Schema(type: "integer", enum: [0, 1])
    )]
    #[OA\Response(
        response: 422,
        description: 'Validation failed',
        content: new OA\JsonContent(
            type: 'object',
            properties: [
                new OA\Property(
                    property: 'error',
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'message', type: 'string', example: 'validation_failed'),
                        new OA\Property(
                            property: 'errors',
                            type: 'object',
                            properties: [
                                new OA\Property(property: 'userId', type: 'string', example: 'This value should not be blank.')
                            ]
                        )
                    ]
                )
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: "Bad Request",
        content: new OA\JsonContent(
            type: "object",
            properties: [
                new OA\Property(
                    property: "error",
                    type: "object",
                    properties: [
                        new OA\Property(property: "message", type: "string", example: "Unknown error occurred")
                    ]
                )
            ]
        )
    )]
    
    #[OA\Response(
        response: 500,
        description: "Internal Server Error",
        content: new OA\JsonContent(
            type: "object",
            properties: [
                new OA\Property(
                    property: "error",
                    type: "object",
                    properties: [
                        new OA\Property(property: "message", type: "string", example: "Unknown error occurred")
                    ]
                )
            ]
        )
    )]

    #[OA\Response(
        response: 200,
        description: "Successful response with user notifications",
        content: new OA\JsonContent(
            type: "object",
            properties: [
                new OA\Property(property: "userId", type: "string", example: "6f5853d0f9e2442c9c818fecf0eec7cf"),
                new OA\Property(property: "startTS", type: "integer", example: 0),
                new OA\Property(property: "endTS", type: "integer", example: 0),
                new OA\Property(
                    property: "notifications",
                    type: "array",
                    items: new OA\Items(
                        type: "object",
                        properties: [
                            new OA\Property(property: "count", type: "integer", example: 2),
                            new OA\Property(
                                property: "items",
                                type: "array",
                                items: new OA\Items(
                                    type: "object",
                                    properties: [
                                        new OA\Property(property: "id", type: "string", example: "c2454d1e-0799-4efc-b0f4-c5bfbe820e87"),
                                        new OA\Property(
                                            property: "notification",
                                            type: "object",
                                            properties: [
                                                new OA\Property(
                                                    property: "context",
                                                    type: "object",
                                                    properties: [
                                                        new OA\Property(property: "brandMarketingName", type: "string", example: "ALFA ROMEO"),
                                                        new OA\Property(property: "firstName", type: "string", example: "John"),
                                                        new OA\Property(property: "model", type: "string", example: "AR"),
                                                        new OA\Property(property: "nickname", type: "string", example: "MY CAR")
                                                    ]
                                                ),
                                                new OA\Property(
                                                    property: "in-app",
                                                    type: "object",
                                                    properties: [
                                                        new OA\Property(property: "criticality", type: "string", example: "HIGH"),
                                                        new OA\Property(property: "body", type: "string", example: "VEHICLE"),
                                                        new OA\Property(property: "subtitle", type: "string", example: "VEHICLE"),
                                                        new OA\Property(property: "title", type: "string", example: "TEST NEW STRUCTURE")
                                                    ]
                                                ),
                                                new OA\Property(
                                                    property: "data",
                                                    type: "object",
                                                    properties: [
                                                        new OA\Property(property: "notificationId", type: "string", example: "S12_RO_START_FAILED_TIMEOUT"),
                                                        new OA\Property(property: "userId", type: "string", example: "100000000f0b4dce874859657ae00100")
                                                    ]
                                                ),
                                                new OA\Property(property: "click_action", type: "string", example: "OPEN_ACTIVITY_1")
                                            ]
                                        ),
                                        new OA\Property(property: "eventName", type: "string", example: "HORN_AND_LIGHTS"),
                                        new OA\Property(property: "timestamp", type: "integer", example: 1539179391201),
                                        new OA\Property(property: "vin", type: "string", example: "VR3UPHNKSKT101603ZZ")
                                    ]
                                )
                            ),
                            new OA\Property(property: "vin", type: "string", example: "VR3UPHNKSKT101603ZZ")
                        ]
                    )
                )
            ]
        )
    )]
    
    public function userVehicleNotifications(string $userid, Request $request, NotificationManager $notificationManager, ValidatorInterface $validator): JsonResponse
    {
        try {
            $data['since'] = $request->query->get('since') ?? null;
            $data['till'] = $request->query->get('till') ?? null;
            $data['limit'] = $request->query->get('limit') ?? null;
            $data['offset'] = $request->query->get('offset') ?? null;
            $data['includeLocalNotif'] = $request->query->get('includeLocalNotif') ?? 0;

            $errors = $validator->validate(
                compact('userid'),
                new Assert\Collection([
                    'userid' => new Assert\NotBlank(),
                ])
            );

            $messages = $this->getValidationMessages($errors);

            if (!empty($messages)) {
                $response = $this->getValidationErrorResponse($messages)->toArray();
                return $this->json($response['content'], $response['code']);
            }

            $response = $notificationManager->getUserVehicleNotifications($userid, $data);
            return $this->json($response['content'], $response['code']);
        } catch (\Throwable $e) {
            return $this->json(['error' => ['message' => $e->getMessage()]], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}