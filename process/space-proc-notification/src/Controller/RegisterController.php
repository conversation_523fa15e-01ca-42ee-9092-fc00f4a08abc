<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use OpenApi\Attributes as OA;
use OpenApi\Attributes\JsonContent;

use App\Trait\ValidationResponseTrait;
use App\Helper\ErrorResponse;
use App\Model\FcmTokenRegisterModel;
use App\Manager\RegisterManager;

#[Route('v1', name: 'register_')]
class RegisterController extends AbstractController
{
    use ValidationResponseTrait;

    #[Route('/notification/enrollment', methods: ['POST'])]
    #[OA\Parameter(
        name: 'userId',
        in: 'header',
        description: 'user Id',
        schema: new OA\Schema(type: 'string'),
        required: true
    )]
    #[OA\Tag(name: 'Register FCM Token')]
    #[OA\RequestBody(
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'deviceID', type: 'string'),
                new OA\Property(property: 'brand', type: 'string'),
                new OA\Property(property: 'country', type: 'string'),
                new OA\Property(property: 'pushToken', type: 'string'),
                new OA\Property(property: 'appId', type: 'string'),
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Bad Request',
    )]
    #[OA\Response(
        response: 200,
        description: 'Successful response',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'success', properties: [
                    new OA\Property(property: 'message'),
                ])
            ]
        )
    )]
    public function registerFcmToken(Request $request, ValidatorInterface $validator, RegisterManager $registerManager): JsonResponse
    {
        try {
            $content = json_decode($request->getContent(), true);
            $userId = $request->headers->get('userId');
            $model = new FcmTokenRegisterModel(
                $content['deviceID'] ?? '',
                $content['brand'] ?? '',
                $content['country'] ?? '',
                $content['pushToken'] ?? '',
                $content['appId'] ?? '',
                time()
            );
            $errors = $validator->validate(compact('userId'), [
                new Assert\Collection([
                    'userId' => new Assert\NotBlank(),
                ])
            ]);
            $messages = $this->getValidationMessages($errors);
            if (!empty($messages)) {
                $response = $this->getValidationErrorResponse($messages)->toArray();

                return $this->json($response['content'], $response['code']);
            }
            $response = $registerManager->registerFcmToken($model, $userId)->toArray();
            
            return $this->json($response['content'], $response['code']);    
        } 
        catch (\Throwable $e) {
            return $this->json(['error' => ['message' => $e->getMessage()]], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }


    #[Route('/remote/session', methods: ['POST'])]
    #[OA\Parameter(
        name: 'userId',
        in: 'header',
        description: 'user Id',
        schema: new OA\Schema(type: 'string'),
        required: true
    )]
    #[OA\Tag(name: 'Register FCM Token')]
    #[OA\RequestBody(
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'remoteSessionId', type: 'string'),
                new OA\Property(property: 'deviceID', type: 'string'),
                new OA\Property(property: 'brand', type: 'string'),
                new OA\Property(property: 'country', type: 'string'),
                new OA\Property(property: 'pushToken', type: 'string'),
                new OA\Property(property: 'appId', type: 'string'),
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Bad Request',
    )]
    #[OA\Response(
        response: 200,
        description: 'Successful response',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'success', properties: [
                    new OA\Property(property: 'message'),
                ])
            ]
        )
    )]
    public function registerRemoteSession(Request $request, ValidatorInterface $validator, RegisterManager $registerManager): JsonResponse
    {
        try {
            $content = json_decode($request->getContent(), true);
            $userId = $request->headers->get('userId');
            $remoteSessionId = $content['remoteSessionId'] ?? '';
            $model = new FcmTokenRegisterModel(
                $content['deviceID'] ?? '',
                $content['brand'] ?? '',
                $content['country'] ?? '',
                $content['pushToken'] ?? '',
                $content['appId'] ?? '',
                time()
            );
            $errors = $validator->validate(compact('userId', 'remoteSessionId'), [
                new Assert\Collection([
                    'userId' => new Assert\NotBlank(),
                    'remoteSessionId' => new Assert\NotBlank(),
                ])
            ]);
            $messages = $this->getValidationMessages($errors);
            if (!empty($messages)) {
                $response = $this->getValidationErrorResponse($messages)->toArray();

                return $this->json($response['content'], $response['code']);
            }
            $response = $registerManager->registerSessionId($model, $userId, $remoteSessionId)->toArray();
            
            return $this->json($response['content'], $response['code']);    
        } 
        catch (\Throwable $e) {
            return $this->json(['error' => ['message' => $e->getMessage()]], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }  
}