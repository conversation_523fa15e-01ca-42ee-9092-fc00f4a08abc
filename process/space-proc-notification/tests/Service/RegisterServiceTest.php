<?php

namespace App\Tests\Service;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use App\Service\RegisterService;
use App\Helper\WSResponse;
use App\Helper\SuccessResponse;
use App\Model\FcmTokenRegisterModel;
use App\Service\MongoAtlasQueryService;
use Symfony\Component\Serializer\SerializerInterface;

class RegisterServiceTest extends TestCase
{
    private RegisterService $registerService;
    private MongoAtlasQueryService $mongoService;
    private SerializerInterface $serializer;
    private LoggerInterface $loggerMock;
    private FcmTokenRegisterModel $fcmTokenRegisterModel;

    public function setUp(): void
    {
        $this->mongoService = $this->createMock(MongoAtlasQueryService::class);
        $this->serializer = $this->createMock(SerializerInterface::class);
        $this->loggerMock = $this->createMock(LoggerInterface::class);
        $this->fcmTokenRegisterModel = $this->createMock(FcmTokenRegisterModel::class);
        $this->registerService = new RegisterService($this->mongoService, $this->serializer);
        $this->registerService->setLogger($this->loggerMock);
    }

    public function testRegisterFcmTokenSuccess(): void
    {
        $this->mongoService->expects($this->once())
            ->method('find')
            ->willReturn(new WSResponse(Response::HTTP_OK, '{"documents":[{"_id":"objectId"}]}'));
        $this->mongoService->expects($this->once())
            ->method('aggregate')
            ->willReturn(new WSResponse(Response::HTTP_OK, '{"documents":[]}'));
        $this->mongoService->expects($this->once())
            ->method('updatePush')
            ->willReturn(new WSResponse(Response::HTTP_OK, 'fcm token registered successfully'));
        $this->serializer->expects($this->once())
            ->method('serialize')
            ->willReturn('{"deviceID":"fcmToken","brand":"deviceType","country":"deviceModel","pushToken":"device","appId":"appVersion"}');
        
        $response = $this->registerService->registerFcmToken($this->fcmTokenRegisterModel, 'userId');
        $this->assertInstanceOf(WSResponse::class, $response);
    }

    public function testUpdateFcmTokenSuccess(): void
    {
        $this->mongoService->expects($this->once())
            ->method('find')
            ->willReturn(new WSResponse(Response::HTTP_OK, '{"documents":[{"_id":"objectId"}]}'));
        $this->mongoService->expects($this->once())
            ->method('aggregate')
            ->willReturn(new WSResponse(Response::HTTP_OK, '{"documents":[{"push":[{"deviceID":"fcmToken","brand":"deviceType","country":"deviceModel","pushToken":"device","appId":"appVersion","timestamp":1630000000}]}]}'));
        $this->mongoService->expects($this->once())
            ->method('updateOne')
            ->willReturn(new WSResponse(Response::HTTP_OK, 'fcm token updated successfully'));
        $this->serializer->expects($this->once())
            ->method('serialize')
            ->willReturn('{"deviceID":"fcmToken","brand":"deviceType","country":"deviceModel","pushToken":"device","appId":"appVersion"}');
        
        $response = $this->registerService->registerFcmToken($this->fcmTokenRegisterModel, 'userId');
        $this->assertInstanceOf(WSResponse::class, $response);
    }

    public function testRegisterFcmTokenFailure(): void
    {
        $this->mongoService->expects($this->once())
            ->method('find')
            ->willReturn(new WSResponse(404, 'User not found'));
        
        $response = $this->registerService->registerFcmToken($this->fcmTokenRegisterModel, 'userId');
        $this->assertInstanceOf(WSResponse::class, $response);
    }

    public function testRegisterSessionIdSuccess(): void
    {
        $this->mongoService->expects($this->once())
            ->method('find')
            ->willReturn(new WSResponse(Response::HTTP_OK, '{"documents":[{"_id":"objectId"}]}'));
        $this->mongoService->expects($this->once())
            ->method('aggregate')
            ->willReturn(new WSResponse(Response::HTTP_OK, '{"documents":[{"push":[{"deviceID":"fcmToken","brand":"deviceType","country":"deviceModel","pushToken":"device","appId":"appVersion","timestamp":1630000000}]}]}'));
        $this->mongoService->expects($this->once())
            ->method('updateOne')
            ->willReturn(new WSResponse(Response::HTTP_OK, 'sessionId registered successfully'));
        
        $response = $this->registerService->registerSessionId($this->fcmTokenRegisterModel, 'userId', 'sessionId');
        $this->assertInstanceOf(WSResponse::class, $response);
    }

    public function testUpdateSessionIdSuccess(): void
    {
        $this->mongoService->expects($this->once())
            ->method('find')
            ->willReturn(new WSResponse(Response::HTTP_OK, '{"documents":[{"_id":"objectId"}]}'));
        $this->mongoService->expects($this->once())
            ->method('aggregate')
            ->willReturn(new WSResponse(Response::HTTP_OK, '{"documents":[{"push":[{"deviceID":"fcmToken","brand":"deviceType","country":"deviceModel","pushToken":"device","appId":"appVersion","timestamp":1630000000, "commandSessionId":[{"remoteSessionId":"sessionId","timestamp":1630000000}]}]}]}'));
        $this->mongoService->expects($this->once())
            ->method('updateOne')
            ->willReturn(new WSResponse(Response::HTTP_OK, 'sessionId updated successfully'));
        
        $response = $this->registerService->registerSessionId($this->fcmTokenRegisterModel, 'userId', 'sessionId');
        $this->assertInstanceOf(WSResponse::class, $response);
    }

    public function testInsertFcmTokenAndSessionIdSuccess(): void
    {
        $this->mongoService->expects($this->once())
            ->method('find')
            ->willReturn(new WSResponse(Response::HTTP_OK, '{"documents":[{"_id":"objectId"}]}'));
        $this->mongoService->expects($this->once())
            ->method('aggregate')
            ->willReturn(new WSResponse(Response::HTTP_OK, '{"documents":[]}'));
        $this->mongoService->expects($this->once())
            ->method('updatePush')
            ->willReturn(new WSResponse(Response::HTTP_OK, 'sessionId updated successfully'));
        $this->serializer->expects($this->once())
            ->method('serialize')
            ->willReturn('{"deviceID":"fcmToken","brand":"deviceType","country":"deviceModel","pushToken":"device","appId":"appVersion"}');
        
        $response = $this->registerService->registerSessionId($this->fcmTokenRegisterModel, 'userId', 'sessionId');
        $this->assertInstanceOf(WSResponse::class, $response);
    }

    public function testRegisterSessionIdFailure(): void
    {
        $this->mongoService->expects($this->once())
            ->method('find')
            ->willReturn(new WSResponse(404, 'User not found'));
        
        $response = $this->registerService->registerSessionId($this->fcmTokenRegisterModel, 'userId', 'sessionId');
        $this->assertInstanceOf(WSResponse::class, $response);
    }
}