<?php

namespace App\Tests\Service;

use App\Connector\MongoAtlasApiConnector;
use App\Helper\WSResponse;
use App\Model\MongoQueryModel;
use App\Service\MongoAtlasQueryService;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;

class MongoAtlasQueryServiceTest extends TestCase
{
    private MongoAtlasQueryService $service;
    private MongoAtlasApiConnector $connector;
    private NormalizerInterface $normalizer;
    private LoggerInterface $logger;

    public function setUp(): void
    {
        parent::setUp();

        $this->connector = $this->createMock(MongoAtlasApiConnector::class);
        $this->logger = $this->createMock(LoggerInterface::class);
        $this->logger->expects($this->any())
            ->method('info')
        ;
        $this->logger->expects($this->any())
            ->method('error')
        ;
        $this->connector->setLogger($this->logger);

        $this->normalizer = $this->createMock(NormalizerInterface::class);

        $this->service = new MongoAtlasQueryService(
            $this->normalizer,
            $this->connector,
            'testDataSource',
            'testDatabase'
        );
        $this->service->setLogger($this->logger);
    }

    public function testBuildQuery(): void
    {
        $query = MongoAtlasQueryService::buildQuery('testDataSource', 'testDatabase');

        $this->assertInstanceOf(MongoQueryModel::class, $query);
        $this->assertEquals('testDataSource', $query->getDataSource());
        $this->assertEquals('testDatabase', $query->getDatabase());
    }

    public function testFind(): void
    {
        $this->connector
            ->expects($this->once())
            ->method('getEndpoint')
            ->with('find')
            ->willReturn('testEndpoint');

        $this->normalizer
            ->expects($this->once())
            ->method('normalize')
            ->with($this->isInstanceOf(MongoQueryModel::class), 'array', ['groups' => ['default', 'find']])
            ->willReturn([
                'dataSource' => 'testDataSource',
                'database' => 'testDatabase',
                'collection' => 'testCollection',
                'filter' => [
                    'field1' => 'value1',
                    'field2' => 'value2',
                ],
            ]);

        $this->connector
            ->expects($this->once())
            ->method('call')
            ->with('POST', 'testEndpoint', ['json' => [
                'dataSource' => 'testDataSource',
                'database' => 'testDatabase',
                'collection' => 'testCollection',
                'filter' => [
                    'field1' => 'value1',
                    'field2' => 'value2',
                ],
            ]])
            ->willReturn(new WSResponse(Response::HTTP_OK, 'testResponse'));

        $response = $this->service->find('testCollection', [
            'field1' => 'value1',
            'field2' => 'value2',
        ]);

        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals('testResponse', $response->getData());
    }

    public function testAggregateOKResponse(): void
    {
        $this->connector
            ->expects($this->once())
            ->method('getEndpoint')
            ->with('aggregate')
            ->willReturn('aggregate');

        $this->normalizer
            ->expects($this->once())
            ->method('normalize')
            ->with($this->isInstanceOf(MongoQueryModel::class), 'array', ['groups' => ['default', 'aggregate']])
            ->willReturn([
                'dataSource' => 'testDataSource',
                'database' => 'testDatabase',
                'collection' => 'testCollection',
                'filter' => [
                    'field1' => 'value1',
                    'field2' => 'value2',
                ],
            ]);

        $this->connector
            ->expects($this->once())
            ->method('call')
            ->with('POST', 'aggregate', ['json' => [
                'dataSource' => 'testDataSource',
                'database' => 'testDatabase',
                'collection' => 'testCollection',
                'filter' => [
                    'field1' => 'value1',
                    'field2' => 'value2',
                ],
            ]])
            ->willReturn(new WSResponse(Response::HTTP_OK, 'testResponse'));

        $response = $this->service->aggregate('testCollection', [
            'field1' => 'value1',
            'field2' => 'value2',
        ]);

        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals('testResponse', $response->getData());
    }

    public function testUpdateMany(): void
    {
        $wsResponse = new WSResponse(Response::HTTP_OK, 'Success response payload');
        $this->mockMethodExecute('updateMany', $wsResponse);

        $result = $this->service->updateMany('', [], [], true);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertSame(Response::HTTP_OK, $result->getCode());
        $this->assertSame('Success response payload', $result->getData());
    }

    public function testUpdateOne(): void
    {
        $wsResponse = new WSResponse(Response::HTTP_OK, 'Success response payload');
        $this->mockMethodExecute('updateOne', $wsResponse);

        $result = $this->service->updateOne('', [], [], true);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertSame(Response::HTTP_OK, $result->getCode());
        $this->assertSame('Success response payload', $result->getData());
    }

    public function testUpdatePush(): void
    {
        $wsResponse = new WSResponse(Response::HTTP_OK, 'Success response payload');
        $this->mockMethodExecute('updateOne', $wsResponse);

        $result = $this->service->updatePush('', [], [], true);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertSame(Response::HTTP_OK, $result->getCode());
        $this->assertSame('Success response payload', $result->getData());
    }

    public function testInsertOne(): void
    {
        $wsResponse = new WSResponse(Response::HTTP_OK, 'Success response payload');
        $this->mockMethodExecute('insertOne', $wsResponse);

        $result = $this->service->insertOne('', []);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertSame(Response::HTTP_OK, $result->getCode());
        $this->assertSame('Success response payload', $result->getData());
    }

    public function testInsertMany(): void
    {
        $wsResponse = new WSResponse(Response::HTTP_OK, 'Success response payload');
        $this->mockMethodExecute('insertMany', $wsResponse);

        $result = $this->service->insertMany('', []);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertSame(Response::HTTP_OK, $result->getCode());
        $this->assertSame('Success response payload', $result->getData());
    }

    public function testInsertManyReturnManagedExceptionResult(): void
    {
        $exception = new \Exception('Exception message', 1234);
        $this->mockMethodExecuteWithException($exception);

        $result = $this->service->insertMany('', []);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertSame(1234, $result->getCode());
        $this->assertSame('Exception message', $result->getData());
    }

    private function mockMethodExecute(string $action, WSResponse $wsResponse): void
    {
        $this->logger
            ->expects($this->once())
            ->method('info')
            ->with($this->stringContains('MongoAtlasQueryService::execute'));

        $this->connector
            ->expects($this->once())
            ->method('getEndpoint')
            ->with($action)
            ->willReturn('endpoint');

        $this->normalizer
            ->expects($this->once())
            ->method('normalize')
            ->willReturn([]);

        $this->connector
            ->expects($this->once())
            ->method('call')
            ->willReturn($wsResponse);
    }

    private function mockMethodExecuteWithException(\Exception $e): void
    {
        $this->logger
            ->expects($this->once())
            ->method('info')
            ->willThrowException($e);

        $this->logger
            ->expects($this->once())
            ->method('error')
            ->with($this->stringContains('MongoAtlasQueryService::execute Catched Exception'));
    }
}
