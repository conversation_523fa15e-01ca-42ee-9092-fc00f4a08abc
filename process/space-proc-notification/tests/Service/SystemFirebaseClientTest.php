<?php

namespace App\Tests\Manager;

use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;
use Psr\Log\LoggerInterface;

use App\Connector\SystemFirebaseConnector;
use App\Service\SystemFirebaseClient;
use App\Helper\WSResponse;
use App\Trait\LoggerTrait;
use App\Message\Field\VehicleNotificationField;
use App\Message\Field\UserNotificationField;

class SystemFirebaseClientTest extends TestCase
{
    private LoggerInterface $logger;
    private SystemFirebaseConnector $connector;
    private SystemFirebaseClient $systemFirebaseClient;

    public function setUp(): void
    {
        $this->connector = $this->createMock(SystemFirebaseConnector::class);
        $this->logger = $this->createMock(LoggerInterface::class);
        $this->logger->expects($this->any())
            ->method('info')
        ;
        $this->logger->expects($this->any())
            ->method('error')
        ;
        $this->systemFirebaseClient = new SystemFirebaseClient($this->connector);
        $this->systemFirebaseClient->setLogger($this->logger);
    }

    public function testSendNotificationWithVehicleNotificationField(): void
    {
        $message = [
            'notification' => ['title' => 'Vehicle location', 'body' => 'Vehicle is in India'], 
            'data' => ['serviceData' => ['EventID' => 'GenericNotificationEvent','Version' => '1.0','VehicleId' => 'v38uihwnjsfns','Timestamp' => 1539179391201,'Data' => ['notificationId' => 'S12_RO_START_FAILED_TIMEOUT','eventName' => 'HORN_AND_LIGHTS','flatAttrA' => '18.58483868','flatAttrB' => 'Boundary 9 Oct','flatAttrC' => '3bca9be2-fb5d-430a-9bb7-4ce6c7f9a474','flatAttrD' => '73.73466107']],'context' => ['nickname' => 'MY CAR','model' => 'AR','brandMarketingName' => 'ALFA ROMEO','firstName' => 'John']],
            'criticality' => 'high',
            'apiPushConfig' => ['staticConfig' => ['silent' => true, 'inApp' => true, 'push' => true, 'android' => ['ttl' => 'string', 'notification' => ['click_action' => 'string'], 'priority' => 'string'], 'apns' => ['headers' => ['apns-priority' => 'string'], 'payload' => ['aps' => ['category' => 'string']]]]]
        ];
        $users = [
            [
                '_id' => 'pushDetails',
                'push' => [
                    [
                        "devideId" => "AZERR",
                        "pushToken" => "1234566",
                        "appId" => "appspace",
                        "timestamp" => "123456677",
                        "commandSession" => [
                            [
                                "id" => "12345",
                                "timestamp" => "2344"
                            ],
                            [
                                "id" => "12345",
                                "timestamp" => "123333"
                            ]
                        ]
                    ]
                ],
            ],
        ];
        $this->connector->expects($this->exactly(1))
            ->method('call')
            ->willReturn(new WSResponse(Response::HTTP_OK, ''));
        $response = $this->systemFirebaseClient->sendNotification($users, $message);

        $this->assertTrue($response);
    }

    public function testSendNotificationWithUserNotificationField(): void
    {
        $message = [
            'notification' => ['title' => 'Vehicle location', 'body' => 'Vehicle is in India'], 
            'data' => ['serviceData' => ['EventID' => 'GenericNotificationEvent','Version' => '1.0','VehicleId' => 'v38uihwnjsfns','Timestamp' => 1539179391201,'Data' => ['notificationId' => 'S12_RO_START_FAILED_TIMEOUT','eventName' => 'HORN_AND_LIGHTS','flatAttrA' => '18.58483868','flatAttrB' => 'Boundary 9 Oct','flatAttrC' => '3bca9be2-fb5d-430a-9bb7-4ce6c7f9a474','flatAttrD' => '73.73466107']],'context' => ['nickname' => 'MY CAR','model' => 'AR','brandMarketingName' => 'ALFA ROMEO','firstName' => 'John']],
            'criticality' => 'high',
            'apiPushConfig' => ['staticConfig' => ['silent' => true, 'inApp' => true, 'push' => true, 'android' => ['ttl' => 'string', 'notification' => ['click_action' => 'string'], 'priority' => 'string'], 'apns' => ['headers' => ['apns-priority' => 'string'], 'payload' => ['aps' => ['category' => 'string']]]]]
        ];
        $users = [
            [
                '_id' => 'pushDetails',
                'push' => [
                    [
                        "devideId" => "AZERR",
                        "pushToken" => "1234566",
                        "appId" => "appspace",
                        "timestamp" => "123456677",
                        "commandSession" => [
                            [
                                "id" => "12345",
                                "timestamp" => "2344"
                            ],
                            [
                                "id" => "12345",
                                "timestamp" => "123333"
                            ]
                        ]
                    ]
                ],
            ],
        ];
        $this->connector->expects($this->exactly(1))
            ->method('call')
            ->willReturn(new WSResponse(Response::HTTP_OK, ''));
        $response = $this->systemFirebaseClient->sendNotification($users, $message);

        $this->assertTrue($response);
    }
}
