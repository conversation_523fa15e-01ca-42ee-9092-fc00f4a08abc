<?php

namespace App\Tests\Service;

use App\Helper\WSResponse;
use App\Service\MongoAtlasQueryService;
use App\Service\UserService;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;

class UserServiceTest extends TestCase
{
    private MongoAtlasQueryService $mongoService;
    private LoggerInterface $logger;
    private UserService $userService;

    public function setUp(): void
    {
        $this->mongoService = $this->createMock(MongoAtlasQueryService::class);
        $this->userService = new UserService($this->mongoService);
        $this->logger = $this->createMock(LoggerInterface::class);
        $this->logger->expects($this->any())
            ->method('info')
        ;
        $this->logger->expects($this->any())
            ->method('error')
        ;
        $this->userService->setLogger($this->logger);
    }

    public function testGetUsersBySessionId(): void
    {
        $this->mongoService->expects($this->any())
            ->method('aggregate')
            ->willReturn(new WSResponse(Response::HTTP_OK, '{"documents":[{"sessionId":"44dscsbf5d4dsdsdfsasa9657aefgdxc2","name":"John Doe"}]}'));
        
        $response = $this->userService->getUsersBySessionId('100000000f0b4dce874859657ae00100','VR3UPHNKSKT101603', '44dscsbf5d4dsdsdfsasa9657aefgdxc2');

        $this->assertInstanceOf(WSResponse::class, $response);
    }

    public function testGetUsersBySessionIdWithInvalidSessionId(): void
    {
        $this->mongoService->expects($this->any())
            ->method('aggregate')
            ->willReturn(new WSResponse(Response::HTTP_OK, '{"documents":[]}'));
        
        $response = $this->userService->getUsersBySessionId('100000000f0b4dce874859657ae00100', 'VR3UPHNKSKT101603', '44dscsbf5d4dsdsdfsasa9657aefgdxc2');

        $this->assertInstanceOf(WSResponse::class, $response);
    }

    public function testGetUsersByUserId(): void
    {
        $this->mongoService->expects($this->any())
            ->method('aggregate')
            ->willReturn(new WSResponse(Response::HTTP_OK, '{"documents":[{"sessionId":"44dscsbf5d4dsdsdfsasa9657aefgdxc2","name":"John Doe"}]}'));
        
        $response = $this->userService->getUserByUserId('100000000f0b4dce874859657ae00100');

        $this->assertInstanceOf(WSResponse::class, $response);
    }

    public function testGetUsersByUserIdWithInvalidUserId(): void
    {
        $this->mongoService->expects($this->any())
            ->method('aggregate')
            ->willReturn(new WSResponse(Response::HTTP_OK, '{"documents":[]}'));
        
        $response = $this->userService->getUserByUserId('100000000f0b4dce874859657ae00100');

        $this->assertInstanceOf(WSResponse::class, $response);
    }
}