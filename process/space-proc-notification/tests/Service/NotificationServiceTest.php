<?php

namespace App\Tests\Service;

use App\Service\NotificationService;
use App\Helper\SuccessResponse;
use App\Helper\ErrorResponse;
use App\Service\MongoAtlasQueryService;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use <PERSON>ymfony\Component\Serializer\SerializerInterface;
use App\Helper\WSResponse;

class NotificationServiceTest extends TestCase
{
    private $mongoServiceMock;
    private $serializerMock;
    private $notificationService;
    private $loggerMock;

    protected function setUp(): void
    {
        $this->mongoServiceMock = $this->createMock(MongoAtlasQueryService::class);
        $this->serializerMock = $this->createMock(SerializerInterface::class);
        $this->loggerMock = $this->createMock(LoggerInterface::class);

        $this->notificationService = new NotificationService(
            $this->serializerMock,
            $this->mongoServiceMock
        );

        $this->notificationService->setLogger($this->loggerMock);
    }

    public function testSaveNotificationSuccess(): void
    {
        $userId = '111ef5d49f0b4dce874859657ae98122';
        $message = [
            'notification' => [
                'title' => 'Vehicle location',
                'body' => 'Vehicle is in India',
                'subtitle' => 'Vehicle Status',
            ],
            'criticality' => 'high',
            'date' => time(),
        ];

        $type = 'vehicle';
        $vin = '1HGBH41JXMN109186';

        // Create a mock of WSResponse for the updatePush return value
        $wsResponseMock = $this->createMock(WSResponse::class);
        $wsResponseMock->method('getData')
            ->willReturn(true);  // This simulates a successful operation returning true

        // Expecting the mongoService updatePush method to return a WSResponse mock
        $this->mongoServiceMock
            ->expects($this->once())
            ->method('updatePush')
            ->with(
                NotificationService::COLLECTION,
                ['userId' => $userId],
                ['messages' => [
                    'title' => 'Vehicle location',
                    'body' => 'Vehicle is in India',
                    'subtitle' => 'Vehicle Status',
                    'criticality' => 'high',
                    'date' => $message['date'],
                    'type' => $type,
                    'vin' => $vin,
                    'expiresAt' => strtotime('+45 days')
                ]],
                true
            )
            ->willReturn($wsResponseMock);

        $result = $this->notificationService->saveNotification($type, $userId, $message, $vin);
    }

    public function testSaveNotificationFailure(): void
    {
        $userId = '111ef5d49f0b4dce874859657ae98122';
        $message = [
            'notification' => [
                'title' => 'Vehicle location',
                'body' => 'Vehicle is in India',
                'subtitle' => 'Vehicle Status',
            ],
            'criticality' => 'high',
            'date' => time(),
        ];

        $type = 'vehicle';
        $vin = '1HGBH41JXMN109186';

        // Simulate an exception in the mongoService updatePush method
        $this->mongoServiceMock
            ->expects($this->once())
            ->method('updatePush')
            ->willThrowException(new \Exception('Database error'));

        $this->loggerMock
            ->expects($this->once())
            ->method('error')
            ->with('Failed to save notifications due to an exception: Database error', [
                'userId' => $userId,
                'messages' => $message,
            ]);

        $this->notificationService->saveNotification($type, $userId, $message, $vin);
    }
}
