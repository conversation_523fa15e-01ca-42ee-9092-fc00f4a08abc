<?php

namespace App\Tests\MessageHandler;

use App\Manager\UserManager;
use App\Message\Field\VehicleNotificationField;
use App\Message\VehicleNotificationEnvelopeBody;
use App\MessageHandler\VehicleNotificationEnvelopeBodyHandler;
use App\Service\SystemFirebaseClient;
use App\Helper\WSResponse;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Messenger\Exception\UnrecoverableMessageHandlingException;
use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\HttpFoundation\Response;
use App\Service\NotificationService;

class VehicleNotificationEnvelopeBodyHandlerTest extends KernelTestCase
{
    private LoggerInterface $logger;
    private ValidatorInterface $validatorMock;
    private UserManager $userManager;
    private ConstraintViolationListInterface $constraintListMock ;
    private SystemFirebaseClient $sysFirebaseClient;
    private NotificationService $notificationService;

    protected function setUp(): void
    {
        $this->logger = $this->createMock(LoggerInterface::class);
        $this->validatorMock = $this->createMock(ValidatorInterface::class); // $this->getContainer()->get(ValidatorInterface::class);
        $this->constraintListMock = $this->createMock(ConstraintViolationListInterface::class);
        $this->userManager = $this->createMock(UserManager::class);
        $this->sysFirebaseClient = $this->createMock(SystemFirebaseClient::class);
        $this->notificationService = $this->createMock(NotificationService::class);
    }

    public function testHandleMessageWithSuccess(): void
    {
        $serviceResult = new WSResponse(
            Response::HTTP_OK,
            '{"documents":[{"_id":"66b5eca07bbc4739adc88342","userId":"100000000f0b4dce874859657ae00100","push":{"pushToken":["APA91bEjICQ"]}}]}'
        );
        $this->userManager->expects($this->once())
            ->method('getUsersBySessionId')
            ->willReturn($serviceResult);

        $this->logger
            ->expects($this->never())
            ->method('error');

        $handler = new VehicleNotificationEnvelopeBodyHandler(
            $this->validatorMock,
            $this->logger,
            $this->userManager,
            $this->sysFirebaseClient,
            notificationService: $this->notificationService
        );

        // Call the process method on the message handler
        $message = $this->getVehicleNotificationEnvelopeBodyFixture();
        $handler->__invoke($message);
    }

    public function testHandleMessageWithValidationException(): void
    {
        $serviceResult = new WSResponse(
            Response::HTTP_OK,
            '{"documents":[]}'
        );
        $this->userManager->expects($this->once())
            ->method('getUsersBySessionId')
            ->willReturn($serviceResult);

        $handler = new VehicleNotificationEnvelopeBodyHandler(
            $this->validatorMock,
            $this->logger,
            $this->userManager,
            $this->sysFirebaseClient,
            notificationService: $this->notificationService
        );

        // Call the process method on the message handler
        $message = $this->getVehicleNotificationEnvelopeBodyFixture();
        $this->expectException(UnrecoverableMessageHandlingException::class);
        $handler->__invoke($message);
    }

    private function getVehicleNotificationEnvelopeBodyFixture(): VehicleNotificationEnvelopeBody
    {
        $message = [
            'notification' => ['title' => 'Vehicle location', 'body' => 'Vehicle is in India'], 
            'data' => ['serviceData' => ['EventID' => 'GenericNotificationEvent','Version' => '1.0','VehicleId' => 'v38uihwnjsfns','Timestamp' => 1539179391201,'Data' => ['notificationId' => 'S12_RO_START_FAILED_TIMEOUT','eventName' => 'HORN_AND_LIGHTS','flatAttrA' => '18.58483868','flatAttrB' => 'Boundary 9 Oct','flatAttrC' => '3bca9be2-fb5d-430a-9bb7-4ce6c7f9a474','flatAttrD' => '73.73466107']],'context' => ['nickname' => 'MY CAR','model' => 'AR','brandMarketingName' => 'ALFA ROMEO','firstName' => 'John']],
            'criticality' => 'high',
            'apiPushConfig' => ['staticConfig' => ['silent' => true, 'inApp' => true, 'push' => true, 'android' => ['ttl' => 'string', 'notification' => ['click_action' => 'string'], 'priority' => 'string'], 'apns' => ['headers' => ['apns-priority' => 'string'], 'payload' => ['aps' => ['category' => 'string']]]]]
        ];

        $envelope = new VehicleNotificationEnvelopeBody(
            userId: '66b5eca07bbc4739adc88342',
            vin: '1C4RJFAG0CC',
            sessionId: 'APA91bEjICQ',
            xApiKey: '1PP2A5HMT1B0A4B0',
            message: $message,
        );

        return $envelope;
    }
}