<?php

namespace App\Tests\Message;

use App\Message\VehicleNotificationCustomSerializer;
use App\Message\VehicleNotificationEnvelopeBody;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Envelope;

class VehicleNotificationCustomSerializerTest extends TestCase
{
    private VehicleNotificationCustomSerializer $serializer;
    private LoggerInterface $logger;

    public function setup(): void
    {
        $this->serializer = new VehicleNotificationCustomSerializer();
        $this->logger = $this->createMock(LoggerInterface::class);
        $this->serializer->setLogger($this->logger);
    }

    public function testDecode(): void
    {
        $sqsMessage = $this->getBodyFromRealSqsMessage();
        $encodedEnvelope = [
            'body' => $sqsMessage,
            'headers' => [
                "userid" => "66b5eca07bbc4739adc88342",
                "vin" => "VR3UPHNKSKT101603",
                "sessionId" => "APA91bEjICQ",
                "x-api-key" => "1PP2A5HMT1B0A4B0"
            ],
        ];

        $envelope = $this->serializer->decode($encodedEnvelope);

        $this->assertInstanceOf(Envelope::class, $envelope);
        $this->assertInstanceOf(VehicleNotificationEnvelopeBody::class, $envelope->getMessage());
    }

    private function getBodyFromRealSqsMessage(): string
    {
        $message = '{"message": {"notification": {"title": "OMI001","body": "OMI002","subtitle": "OMI003","bannerTitle": "string","bannerDesc": "string"},"criticality": "LOW","data": {"serviceData": {"EventID": "S42_CPA_DELETE_SUCCESS","Version": "1.0","Timestamp": 1539179391201,"Data": {"notificationId": "S42_CPA_DELETE_SUCCESS","eventName": "HORN_AND_LIGHTS","flatAttrA": "18.58483868","flatAttrB": "Boundary 9 Oct","flatAttrC": "3bca9be2-fb5d-430a-9bb7-4ce6c7f9a474","flatAttrD": "73.73466107"}},"context": {"firstName": "John"}},"apiPushConfig": {"staticConfig": {"silent": true,"push": true,"inApp": true,"android": {"ttl": "86400s","notification": {"click_action": "OPEN_ACTIVITY_1"},"priority": "normal"},"apns": {"headers": {"apns-priority": "5"},"payload": {"aps": {"category": "NEW_MESSAGE_CATEGORY"}}}}}}}';
        return $message;
    }

    public function testDecodeBadCodedMessageReturnEmptyEnvelpe(): void
    {
        $encodedEnvelope = [
            'body' => 'bad encoded body',
            'headers' => [
                "userId" => "66b5eca07bbc4739adc88342",
                "vin" => "VR3UPHNKSKT101603",
                "sessionId" => "APA91bEjICQ",
                "xApiKey" => "1PP2A5HMT1B0A4B0"
            ],
        ];

        $this->logger
            ->expects($this->once())
            ->method('error')
            ->with(
                $this->stringContains('Json decoding exception for Space Vehicle Notification message: encoded envelope body is not valid JSON:'),
            );

        $envelope = $this->serializer->decode($encodedEnvelope);

        $this->assertInstanceOf(Envelope::class, $envelope);
        $this->assertInstanceOf(VehicleNotificationEnvelopeBody::class, $envelope->getMessage());
        $message = $envelope->getMessage();
        $this->assertNull($message->getMessage());
        $this->assertEquals('',$message->getUserId());
        $this->assertEquals('',$message->getSessionId());
        $this->assertEquals('',$message->getXApiKey());
    }
}