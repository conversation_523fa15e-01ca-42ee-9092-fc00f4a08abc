<?php

namespace App\Tests\Message;

use App\Message\UserNotificationEnvelopeBody;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Serializer\Encoder\JsonEncoder;
use Symfony\Component\Serializer\Normalizer\ObjectNormalizer;
use Symfony\Component\Serializer\Serializer;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class UserNotificationEnvelopeBodyTest extends KernelTestCase
{
    private ?ValidatorInterface $validator;

    protected function setUp(): void
    {
        $this->validator = $this->getContainer()->get(ValidatorInterface::class);
    }

    public function testDeserializationFromRealMessage(): void
    {
        $encoded = $this->getEncodedMessageFixture();
        $serializer = new Serializer([new ObjectNormalizer()], [new JsonEncoder()]);

        $envelope = $serializer->deserialize($encoded, UserNotificationEnvelopeBody::class, 'json');

        $this->assertInstanceOf(UserNotificationEnvelopeBody::class, $envelope);
        $message = $envelope->getMessage();
        $this->assertIsArray($message);
    }

    public function testValidationFromRealMessage(): void
    {
        $validator = $this->getContainer()->get(ValidatorInterface::class);

        $encoded = $this->getEncodedMessageFixture();
        $serializer = new Serializer([new ObjectNormalizer()], [new JsonEncoder()]);
        $envelope = $serializer->deserialize($encoded, UserNotificationEnvelopeBody::class, 'json');
        $this->assertInstanceOf(UserNotificationEnvelopeBody::class, $envelope);

        $violations = $validator->validate($envelope);
        $this->assertCount(0, $violations);
    }


    public function testValidationFromRealMessageUserNotificationWithoutUserId(): void
    {
        // xF Brand without mvs
        $fixture = $this->getMessageFixture();
        $fixture['userId']='';
        $encoded = json_encode($fixture);

        $serializer = new Serializer([new ObjectNormalizer()], [new JsonEncoder()]);
        $envelope = $serializer->deserialize($encoded, UserNotificationEnvelopeBody::class, 'json');
        $this->assertInstanceOf(UserNotificationEnvelopeBody::class, $envelope);

        $violations = $this->validator->validate($envelope);
        $this->assertCount(2, $violations);
        $this->assertSame('The userId should not be empty.', $violations[0]->getMessage());
        $this->assertSame('userId', $violations[0]->getPropertyPath());
    }

    private function getEncodedMessageFixture(array $body = [], array $envelope = []): string
    {
        $template = $this->getMessageFixture();
        $message = $this->mergeEnvelope($template, $body, $envelope);
        $encoded = json_encode($message);
        if (false === $encoded) {
            $encoded = '';
        }

        return $encoded;
    }

    private function getMessageFixture(array $body = [], array $envelope = []): array
    {
        $encoded = '{"userId":"ca970d679a29a87aa396dff00029cee3","sessionId":"1YYYYYYYY1EzNzhhOGMyZG555", "xApiKey":"ndfskjnsnknnc", "message": {"notification": {"title": "OMI001","body": "OMI002","subtitle": "OMI003"}}}';
        $template = json_decode($encoded, true);

        $message = $this->mergeEnvelope($template, $body, $envelope);

        return $message;
    }

    private function mergeEnvelope(array $template, array $body = [], array $envelope = []): array
    {
        $template['message'] = array_merge($template['message'], $body);
        $message = array_merge($template, $envelope);

        return $message;
    }
}
