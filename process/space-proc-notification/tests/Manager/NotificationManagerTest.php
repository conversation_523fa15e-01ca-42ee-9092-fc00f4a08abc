<?php

namespace App\Tests\Manager;

use App\Manager\NotificationManager;
use App\Service\NotificationService;
use App\Manager\UserManager;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;
use App\Helper\SuccessResponse;
use App\Helper\ErrorResponse;
use Symfony\Component\Messenger\Exception\UnrecoverableMessageHandlingException;
use Psr\Log\LoggerInterface;

class NotificationManagerTest extends TestCase
{
    private $notificationService;
    private $userManager;
    private $notificationManager;

    protected function setUp(): void
    {
        
        $this->notificationService = $this->createMock(NotificationService::class);
        $this->userManager = $this->createMock(UserManager::class);
        
        $logger = $this->createMock(LoggerInterface::class);
        

        $this->notificationManager = new NotificationManager(
            $this->notificationService,
            $this->userManager
        );
        $this->notificationManager->setLogger($logger);
    }

    public function testGetNotificationsSuccess(): void
    {
        $userId = '111ef5d49f0b4dce874859657ae98122';
        $criticality = "high";
        $type = 'vehicle';

        $this->userManager
            ->expects($this->once())
            ->method('getUserByUserId')
            ->with($userId)
            ->willReturn((new SuccessResponse(json_encode(['documents' => [['userId' => $userId]]]))))
        ;

        $this->notificationService
            ->expects($this->once())
            ->method('getNotifications')
            ->with($userId, $criticality, $type)
            ->willReturn((new SuccessResponse(json_encode([
                'documents' => [[
                    'messages' => [
                        [
                            'title' => 'Vehicle location',
                            'body' => 'Vehicle is in India',
                            'criticality' => 'high',
                            'type' => 'user',
                            'date' => 1734524690,
                            'vin' => null
                        ]
                    ]
                ]]
            ])))
            );

        $result = $this->notificationManager->getNotifications($userId, $criticality, $type);

        $this->assertInstanceOf(SuccessResponse::class, $result);
        $this->assertEquals([
            [
                'title' => 'Vehicle location',
                'body' => 'Vehicle is in India',
                'criticality' => 'high',
                'type' => 'user',
                'date' => 1734524690,
                'vin' => null,
            ]
        ], $result->getData());
    }

}
