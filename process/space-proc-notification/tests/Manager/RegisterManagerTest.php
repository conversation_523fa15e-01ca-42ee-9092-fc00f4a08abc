<?php

namespace App\Tests\Manager;

use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use App\Service\RegisterService;
use App\Helper\ErrorResponse;
use App\Helper\SuccessResponse;
use App\Helper\WSResponse;
use App\Manager\RegisterManager;
use App\Model\FcmTokenRegisterModel;

class RegisterManagerTest extends TestCase
{
    public function testRegisterFcmTokenSuccess(): void
    {
        $registerMockService = $this->createMock(RegisterService::class);
        $fcmTokenRegisterModel = $this->createMock(FcmTokenRegisterModel::class);
        $validator = $this->createMock(ValidatorInterface::class);
        $logger = $this->createMock(LoggerInterface::class);
        $registerMockService->expects($this->once())
            ->method('registerFcmToken')
            ->willReturn(new WSResponse(200, 'fcm token registered successfully'));
        
        $registerManager = new RegisterManager($validator, $registerMockService);
        $registerManager->setLogger($logger);
        $response = $registerManager->registerFcmToken($fcmTokenRegisterModel, 'userId');

        $this->assertInstanceOf(SuccessResponse::class, $response);
    }

    public function testRegisterFcmTokenFailure(): void
    {
        $registerMockService = $this->createMock(RegisterService::class);
        $fcmTokenRegisterModel = $this->createMock(FcmTokenRegisterModel::class);
        $validator = $this->createMock(ValidatorInterface::class);
        $logger = $this->createMock(LoggerInterface::class);
        $registerMockService->expects($this->once())
            ->method('registerFcmToken')
            ->willReturn(new WSResponse(404, 'User not found'));
        
        $registerManager = new RegisterManager($validator, $registerMockService);
        $registerManager->setLogger($logger);
        $response = $registerManager->registerFcmToken($fcmTokenRegisterModel, 'userId');

        $this->assertInstanceOf(ErrorResponse::class, $response);
    }

    public function testRegisterSessionIdSuccess(): void
    {
        $registerMockService = $this->createMock(RegisterService::class);
        $fcmTokenRegisterModel = $this->createMock(FcmTokenRegisterModel::class);
        $validator = $this->createMock(ValidatorInterface::class);
        $logger = $this->createMock(LoggerInterface::class);
        $registerMockService->expects($this->once())
            ->method('registerSessionId')
            ->willReturn(new WSResponse(200, 'session ID registered successfully'));
        
        $registerManager = new RegisterManager($validator, $registerMockService);
        $registerManager->setLogger($logger);
        $response = $registerManager->registerSessionId($fcmTokenRegisterModel, 'userId', 'sessionId');

        $this->assertInstanceOf(SuccessResponse::class, $response);
    }

    public function testRegisterSessionIdFailure(): void
    {
        $registerMockService = $this->createMock(RegisterService::class);
        $fcmTokenRegisterModel = $this->createMock(FcmTokenRegisterModel::class);
        $validator = $this->createMock(ValidatorInterface::class);
        $logger = $this->createMock(LoggerInterface::class);
        $registerMockService->expects($this->once())
            ->method('registerSessionId')
            ->willReturn(new WSResponse(404, 'User not found'));
        
        $registerManager = new RegisterManager($validator, $registerMockService);
        $registerManager->setLogger($logger);
        $response = $registerManager->registerSessionId($fcmTokenRegisterModel, 'userId', 'sessionId');

        $this->assertInstanceOf(ErrorResponse::class, $response);
    }

}