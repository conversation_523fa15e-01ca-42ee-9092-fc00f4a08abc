<?php

namespace App\Tests\Controller;

use Symfony\Component\HttpFoundation\HeaderBag;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\HttpFoundation\JsonResponse;
use App\Controller\NotificationController;
use App\Manager\NotificationManager;

class NotificationControllerTest extends KernelTestCase
{
    private $validator;
    private $notificationManager;

    protected function setUp(): void
    {
        self::bootKernel();
        $container = static::getContainer();

        $this->validator = $container->get('validator');
        $this->notificationManager = $container->get(NotificationManager::class);
    }

     public function testGetNotificationsWithMissingUserId(): void
    {
        $controller = new NotificationController();
        $controller->setContainer(static::getContainer());

        $request = Request::create('/v1/notifications', 'GET');
        $request->headers = new HeaderBag(); // userId missing

        $response = $controller->getNotifications($request, $this->notificationManager, $this->validator);

        $this->assertSame(422, $response->getStatusCode()); 

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
    }

    public function testGetNotificationsWithInvalidUserId(): void
    {
        $controller = new NotificationController();
        $controller->setContainer(static::getContainer());

        $request = Request::create('/v1/notifications', 'GET');
        $request->headers->set('userId', '111ef5d49f0b4dce874859657ae98122');

        $response = $controller->getNotifications($request, $this->notificationManager, $this->validator);

        $this->assertSame(400, $response->getStatusCode()); 

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
    }

    public function testGetNotificationsWithValidUserId(): void
    {
        $controller = new NotificationController();
        $controller->setContainer(static::getContainer());

        $request = Request::create('/v1/notifications', 'GET');
        $request->headers->set('userId', '111ef5d49f0b4dce874859657ae98122');

        $response = $controller->getNotifications($request, $this->notificationManager, $this->validator);
        $this->assertSame(200, $response->getStatusCode()); 

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('success', $responseData);
    }

    public function testGetNotificationsWithValidTypeHighCriticality(): void
    {
        $controller = new NotificationController();
        $controller->setContainer(static::getContainer());

        $request = Request::create('/v1/notifications', 'GET');
        $request->headers->set('userId', '111ef5d49f0b4dce874859657ae98122');
        $request->headers->set('criticality', values: 'high');
        $request->headers->set('type', 'vehicle');
        
        $response = $controller->getNotifications($request, $this->notificationManager, $this->validator);
        $this->assertSame(200, $response->getStatusCode()); 

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('success', $responseData);
    }


    public function testGetNotificationsWithInvalidType(): void
    {
        $controller = new NotificationController();
        $controller->setContainer(static::getContainer());

        $request = Request::create('/v1/notifications', 'GET');
        $request->headers->set('userId', '111ef5d49f0b4dce874859657ae98122');
        $request->headers->set('criticality', 'high');
        $request->headers->set('type', 'unknownType');
        
        $response = $controller->getNotifications($request, $this->notificationManager, $this->validator);
        $this->assertSame(422, $response->getStatusCode()); 

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
    }

    public function testGetNotificationsWithNoCriticality(): void
    {
        $controller = new NotificationController();
        $controller->setContainer(static::getContainer());

        $request = Request::create('/v1/notifications', 'GET');
        $request->headers->set('userId', '111ef5d49f0b4dce874859657ae98122');
        $request->headers->set('type', 'user');
        
        $response = $controller->getNotifications($request, $this->notificationManager, $this->validator);
        $this->assertSame(200, $response->getStatusCode()); 

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('success', $responseData);
    }
}