<?php

namespace App\Tests\Controller;

use Symfony\Component\HttpFoundation\HeaderBag;
use Symfony\Component\HttpFoundation\ParameterBag;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Component\Validator\ConstraintViolationListInterface;

use App\Controller\RegisterController;
use App\Helper\SuccessResponse;
use App\Manager\RegisterManager;
use App\Helper\ErrorResponse;

class RegisterControllerTest extends KernelTestCase
{
    private $validator;
    private $registerManager;

    protected function setUp(): void
    {
        self::bootKernel();
        $container = static::getContainer();

        $this->validator = $container->get(ValidatorInterface::class);
        $this->registerManager = $container->get(RegisterManager::class);
    }

    public function testRegisterFcmTokenWithInvalidData()
    {
        $content = json_encode([
            'deviceID' => 'F0CE5BD1-DFA1-458A-8449-1832C8EA9047',
            'brand' => 'Citroen',
            'country' => 'India',
            'pushToken' => 'AIzaSyDzpKEgI5Lcri5Zf5Wg5lRz',
            'appId' => 'spaceapp',
        ]);

        $controller = new RegisterController();
        $controller->setContainer(static::getContainer());
        $headerBag = $this->createMock(HeaderBag::class);

        $request = Request::create('/v1/notification/enrollment', 'POST', [], [], [], [], $content);
        $request->headers = $headerBag;
        $response = $controller->registerFcmToken($request, $this->validator, $this->registerManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertSame(422, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
    }

    public function testRegisterFcmTokenWithValidData()
    {
        $content = json_encode([
            'deviceID' => 'F0CE5BD1-DFA1-458A-8449-1832C8EA9047',
            'brand' => 'Citroen',
            'country' => 'India',
            'pushToken' => 'AIzaSyDzpKEgI5Lcri5Zf5Wg5lRz',
            'appId' => 'spaceapp',
        ]);
        $registerManager = $this->createMock(RegisterManager::class);
        $controller = new RegisterController();
        $controller->setContainer(static::getContainer());

        $request = Request::create('/v1/notification/enrollment', 'POST', [], [], [], [], $content);
        $request->headers = new HeaderBag(['userId' => '6736320116974042a60293d16xasd2y2823']);
        $registerManager->expects($this->once())
            ->method('registerFcmToken')
            ->willReturn(new SuccessResponse(['message' => 'fcm token registered successfully'], 200));
        $response = $controller->registerFcmToken($request, $this->validator, $registerManager);
        
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertSame(200, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('success', $responseData);
    }

    public function testRegisterFcmTokenWithInvalidUserId()
    {
        $content = json_encode([
            'deviceID' => 'F0CE5BD1-DFA1-458A-8449-1832C8EA9047',
            'brand' => 'Citroen',
            'country' => 'India',
            'pushToken' => 'AIzaSyDzpKEgI5Lcri5Zf5Wg5lRz',
            'appId' => 'spaceapp',
        ]);
        $registerManager = $this->createMock(RegisterManager::class);
        $controller = new RegisterController();
        $controller->setContainer(static::getContainer());

        $request = Request::create('/v1/notification/enrollment', 'POST', [], [], [], [], $content);
        $request->headers = new HeaderBag(['userId' => '6736320116974042a60293d16xasd2y2823']);
        $registerManager->expects($this->once())
            ->method('registerFcmToken')
            ->willReturn(new ErrorResponse('User not found', 404));
        $response = $controller->registerFcmToken($request, $this->validator, $registerManager);
        
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertSame(404, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
    }

    public function testRegisterSessionIdWithInvalidData(): void
    {
        $content = json_encode([
            'deviceID' => 'F0CE5BD1-DFA1-458A-8449-1832C8EA9047',
            'brand' => 'Citroen',
            'country' => 'India',
            'pushToken' => 'AIzaSyDzpKEgI5Lcri5Zf5Wg5lRz',
            'appId' => 'spaceapp',
        ]);

        $controller = new RegisterController();
        $controller->setContainer(static::getContainer());
        $headerBag = $this->createMock(HeaderBag::class);

        $request = Request::create('/v1/remote/session', 'POST', [], [], [], [], $content);
        $request->headers = $headerBag;
        $response = $controller->registerRemoteSession($request, $this->validator, $this->registerManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertSame(422, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
    }

    public function testRegisterSessionIdWithValidData(): void
    {
        $content = json_encode([
            'remoteSessionId' => '2C8EA9CE5BD1FA1',
            'deviceID' => 'F0CE5BD1-DFA1-458A-8449-1832C8EA9047',
            'brand' => 'Citroen',
            'country' => 'India',
            'pushToken' => 'AIzaSyDzpKEgI5Lcri5Zf5Wg5lRz',
            'appId' => 'spaceapp',
        ]);
        $registerManager = $this->createMock(RegisterManager::class);
        $controller = new RegisterController();
        $controller->setContainer(static::getContainer());

        $request = Request::create('/v1/remote/session', 'POST', [], [], [], [], $content);
        $request->headers = new HeaderBag(['userId' => '6736320116974042a60293d16xasd2y2823']);
        $registerManager->expects($this->once())
            ->method('registerSessionId')
            ->willReturn(new SuccessResponse(['message' => 'sessionId registered successfully'], 200));
        $response = $controller->registerRemoteSession($request, $this->validator, $registerManager);
        
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertSame(200, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('success', $responseData);
    }
}