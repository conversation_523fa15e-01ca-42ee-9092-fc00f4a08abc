<?php

namespace App\Tests\Connector;

use App\Connector\CustomHttpClient;
use App\Connector\SystemFirebaseConnector;
use App\Helper\WSResponse;
use Exception;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;

class SystemFirebaseConnectorTest extends TestCase
{
    private CustomHttpClient $client;
    private SystemFirebaseConnector $connector;

    public function setUp(): void
    {
        $this->client = $this->createMock(CustomHttpClient::class);
        $this->connector = new systemFirebaseConnector($this->client, 'https://example.com');
        $logger = $this->createMock(LoggerInterface::class);
        $logger->expects($this->any())
            ->method('info');
        $logger->expects($this->any())
            ->method('error');
        $this->connector->setLogger($logger);
    }

    public function testCallReturnsSuccess(): void
    {
        $this->client->expects($this->once())
            ->method('request')
            ->willReturn(new WSResponse(200, ['success' => ["message" => "success"]]));
        $message = [
            'notification' => ['title' => 'Vehicle location', 'body' => 'Vehicle is in India'], 
            'data' => ['serviceData' => ['EventID' => 'GenericNotificationEvent','Version' => '1.0','VehicleId' => 'v38uihwnjsfns','Timestamp' => 1539179391201,'Data' => ['notificationId' => 'S12_RO_START_FAILED_TIMEOUT','eventName' => 'HORN_AND_LIGHTS','flatAttrA' => '18.58483868','flatAttrB' => 'Boundary 9 Oct','flatAttrC' => '3bca9be2-fb5d-430a-9bb7-4ce6c7f9a474','flatAttrD' => '73.73466107']],'context' => ['nickname' => 'MY CAR','model' => 'AR','brandMarketingName' => 'ALFA ROMEO','firstName' => 'John']],
            'criticality' => 'high',
            'apiPushConfig' => ['staticConfig' => ['silent' => true, 'inApp' => true, 'push' => true, 'android' => ['ttl' => 'string', 'notification' => ['click_action' => 'string'], 'priority' => 'string'], 'apns' => ['headers' => ['apns-priority' => 'string'], 'payload' => ['aps' => ['category' => 'string']]]]]
        ];
        $options = [
            'headers' => [
                'pushToken' => "token",
            ],
            'json' => [
                'message' => $message
            ],
        ];

        $response = $this->connector->call('POST', '/v1/push/notification', $options);
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(200, $response->getCode());
        $this->assertEquals(['success' => ["message" => "success"]], $response->getData());
    }

    public function testCallThrowException(): void
    {
        $this->client->expects($this->once())
            ->method('request')
            ->willThrowException(new Exception('Unprocessable Content', 422));
        $options = [
            'headers' => [
                'pushToken' => "token",
            ],
            'json' => [
                'title' => 'notification',
            ],
        ];

        $wsResponse = $this->connector->call('POST', '/v1/push/notification', []);

        $this->assertInstanceOf(WSResponse::class, $wsResponse);
        $this->assertEquals(422, $wsResponse->getCode());
        $this->assertEquals('Unprocessable Content', $wsResponse->getData());
    }
}
