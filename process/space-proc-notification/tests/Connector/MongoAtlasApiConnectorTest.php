<?php

namespace App\Tests\Connector;

use App\Connector\MongoAtlasApiConnector;
use App\Helper\WSResponse;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpClient\Response\MockResponse;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class MongoAtlasApiConnectorTest extends TestCase
{
    private HttpClientInterface $client;
    private string $mongoApp;
    private MongoAtlasApiConnector $connector;
    private LoggerInterface $logger;

    protected function setUp(): void
    {
        $this->client = $this->createMock(HttpClientInterface::class);
        $this->mongoApp = 'test-app';
        $this->connector = new MongoAtlasApiConnector($this->client, $this->mongoApp);
        $this->logger = $this->createMock(LoggerInterface::class);
        $this->logger->expects($this->any())
            ->method('info')
        ;
        $this->logger->expects($this->any())
            ->method('error')
        ;
        $this->connector->setLogger($this->logger);
    }

    public function testCallMethodReturnsWSResponse(): void
    {
        // Mock the HTTP response
        $response = new MockResponse('{"status": "success"}', [
            'http_code' => 200,
        ]);

        $this->client->expects($this->once())
            ->method('request')
            ->willReturn($response);

        $wsResponse = $this->connector->call('GET', '/test', ['query' => 'param']);

        $this->assertInstanceOf(WSResponse::class, $wsResponse);
    }

    public function testCallMethodThrowException(): void
    {
        // Mock the HTTP client to throw an exception
        $this->client->expects($this->once())
            ->method('request')
            ->willThrowException(new \Exception('API error', 500));

        $wsResponse = $this->connector->call('GET', '/test', ['query' => 'param']);

        // Assert that WSResponse with error status and error message is returned
        $this->assertInstanceOf(WSResponse::class, $wsResponse);
        $this->assertEquals(500, $wsResponse->getCode());
        $this->assertEquals('API error', $wsResponse->getData());
    }

    public function testGetEndpointMethodReturnsCorrectEndpoint(): void
    {
        $action = 'find';
        $expectedEndpoint = sprintf('/app/%s/endpoint/data/v1/action/%s', $this->mongoApp, $action);

        $endpoint = $this->connector->getEndpoint($action);

        $this->assertEquals($expectedEndpoint, $endpoint);
    }
}
