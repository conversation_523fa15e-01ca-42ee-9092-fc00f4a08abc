framework:
    messenger:
        # reset services after consuming messages
        reset_on_message: true

        transports:
            space_vehicle_notification_sqs_queue: 
                dsn: 'sqs://sqs.%env(resolve:AWS_SQS_REGION)%.amazonaws.com'
                options:                
                    access_key: '%env(AWS_SQS_ACCESS_KEY)%'
                    secret_key: '%env(AWS_SQS_SECRET_KEY)%'
                    queue_name: '%env(AWS_VEHICLE_NOTIFICATION_SQS_QUEUE_NAME)%'
                    debug: false
                #serializer: messenger.transport.symfony_serializer
                serializer: App\Message\VehicleNotificationCustomSerializer
                retry_strategy:
                    max_retries: 0

            space_user_notification_sqs_queue: 
                dsn: 'sqs://sqs.%env(resolve:AWS_SQS_REGION)%.amazonaws.com'
                options:                
                    access_key: '%env(AWS_SQS_ACCESS_KEY)%'
                    secret_key: '%env(AWS_SQS_SECRET_KEY)%'
                    queue_name: '%env(AWS_USER_NOTIFICATION_SQS_QUEUE_NAME)%'
                    debug: false
                #serializer: messenger.transport.symfony_serializer
                serializer: App\Message\UserNotificationCustomSerializer
                retry_strategy:
                    max_retries: 0

            # failed: 'doctrine://default?queue_name=failed'
            # sync: 'sync://'

        routing:
            # Route your messages to the transports
            'App\Message\VehicleNotificationEnvelopeBody': space_vehicle_notification_sqs_queue
            'App\Message\UserNotificationEnvelopeBody': space_user_notification_sqs_queue


# when@test:
#    framework:
#        messenger:
#            transports:
#                # replace with your transport name here (e.g., my_transport: 'in-memory://')
#                # For more Messenger testing tools, see https://github.com/zenstruck/messenger-test
#                async: 'in-memory://'
