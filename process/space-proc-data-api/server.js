const express = require('express');
const dotenv = require('dotenv');
const app = express();
dotenv.config();
const PORT = process.env.SERVER_PORT || 80;

// Sample route
app.get('/', (req, res) => {
    res.send("Hello, I'm Data API server !!!!");
});

// Sample route with a parameter
app.get('/hello/:name', (req, res) => {
    res.send(`Hello, ${req.params.name}!`);
});

// Start the server
app.listen(PORT, () => {
    console.log(`Server is running at http://localhost:${PORT}`);
});

module.exports = app; // Export app for testing
