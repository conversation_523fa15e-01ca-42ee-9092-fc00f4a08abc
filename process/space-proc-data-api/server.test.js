const request = require('supertest');
const app = require('./server'); // Import the server

describe('GET /', () => {
    it('should return Hello, World!', async () => {
        const response = await request(app).get('/');
        expect(response.status).toBe(200);
        expect(response.text).toBe('Hello, World!');
    });
});

describe('GET /hello/:name', () => {
    it('should return a personalized greeting', async () => {
        const name = '<PERSON>';
        const response = await request(app).get(`/hello/${name}`);
        expect(response.status).toBe(200);
        expect(response.text).toBe(`Hello, ${name}!`);
    });
});
