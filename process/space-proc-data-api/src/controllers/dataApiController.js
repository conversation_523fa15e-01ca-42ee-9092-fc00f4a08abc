const dataApiService = require('../services/dataApiService');

class dataApiController {

  async findOne(req, res) {
    try {
      const data = await dataApiService.findOne(req.body) ?? {};
      console.log(data);
      res.json({ "document": data });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async find(req, res) {
    try {
      console.log(req.body);
      const data = await dataApiService.find(req.body);
      res.json({ "documents": data });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async insertOne(req, res) {

    try {
      const data = await dataApiService.insertOne(req.body);
      if (data.acknowledged) {
        res.status(200).send(
          {
            "insertedId": data.insertedId
          }
        );
      } else {
        res.status(400).send(
          {
            "error": data.error,
            "error_code": data.error_code
          }
        );
      }
    } catch (error) {
      res.status(500).json(
        {
          "error": error.message,
          "error_code": error.error_code
        });
    }
  }

  async insertMany(req, res) {

    try {
      const data = await dataApiService.insertMany(req.body);
      if (data.acknowledged) {
        res.status(200).send({
          "insertedIds": Object.values(data.insertedIds)
        });
      } else {
        res.status(400).send(
          {
            "error": data.error,
            "error_code": data.error_code
          }
        );
      }
    } catch (error) {
      res.status(500).json(
        {
          "error": error.message,
          "error_code": error.error_code ?? "Internal Error"
        }
      );
    }

  }

  async updateOne(req, res) {
    try {
      const data = await dataApiService.updateOne(req.body);
      if (data.acknowledged) {
        res.status(200).send({
          "matchedCount": data.matchedCount,
          "modifiedCount": data.modifiedCount
        });
      } else {
        res.status(400).send(
          {
            "error": data.error,
            "error_code": data.error_code
          }
        );
      }
    } catch (error) {
      res.status(500).json(
        {
          "error": error.message,
          "error_code": error.error_code ?? "Internal Error"
        }
      );
    }

  }

  async updateMany(req, res) {
    try {
      //const data = await dataApiService.updateMany(req.body);
      const data = await dataApiService.updateMany(req.body);
      if (data.acknowledged) {
        res.status(200).send({
          "matchedCount": data.matchedCount,
          "modifiedCount": data.modifiedCount
        });
      } else {
        res.status(400).send(
          {
            "error": data.error,
            "error_code": data.error_code
          }
        );
      }
    } catch (error) {
      res.status(500).json(
        {
          "error": error.message,
          "error_code": error.error_code ?? "Internal Error"
        }
      );
    }
  }

  async deleteOne(req, res) {
    try {
      const data = await dataApiService.deleteOne(req.body);
      res.json(data);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async deleteMany(req, res) {
    try {
      const data = await dataApiService.deleteMany(req.body);
      res.json(data);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }
  async aggregate(req, res) {
    try {
      const data = await dataApiService.aggregate(req.body);
      res.json(data);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }
}

module.exports = new dataApiController();