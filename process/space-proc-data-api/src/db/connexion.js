
const { MongoClient } = require('mongodb');

const dbConnexion = (async  function () {
  let instance;
  const MONGO_URI = 'mongodb+srv://space-mid-integ:<EMAIL>/SpaceDb?retryWrites=true&w=majority';
  const DATABASE_NAME = 'SpaceDb';

  console.log("dbConnexion : Database connexion .... ");
    const client = new MongoClient(MONGO_URI);
 
    try {
        // Connect to the MongoDB cluster
        await client.connect();
         // Make the appropriate DB calls
        return client.db(DATABASE_NAME);
    } catch (e) {
        console.error(e);
    } 
   

  /*
  function createInstance() {
    const client = new MongoClient(MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    return client;
  }
*/

})();


module.exports = dbConnexion;