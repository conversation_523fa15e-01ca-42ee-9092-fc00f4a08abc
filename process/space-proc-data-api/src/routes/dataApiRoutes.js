const express = require('express');
const apiController = require('../controllers/dataApiController');

const router = express.Router();

router.post('/findOne', apiController.findOne);
router.post('/find', apiController.find);
router.post('/insertOne', apiController.insertOne);
router.post('/insertMany', apiController.insertMany);
router.post('/updateOne', apiController.updateOne);
router.post('/updateMany', apiController.updateMany);
router.post('/deleteOne', apiController.deleteOne);
router.post('/deleteMany', apiController.deleteMany);
router.post('/aggregate', apiController.aggregate);

/*
router.get('/:id', apiController.getUserById);
router.post('/', apiController.createUser);
router.put('/:id', apiController.updateUser);
router.delete('/:id', apiController.deleteUser);
*/
module.exports = router;
