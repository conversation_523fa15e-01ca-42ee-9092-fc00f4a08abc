const dataApiRepository = require('../repositories/dataApiRepository');

class dataApiService {
  async findOne(requestData) {
    return dataApiRepository.findOne(requestData);
  }

  async find(requestData) {
    return dataApiRepository.find(requestData);
  }

  async insertOne(requestData) {
    return dataApiRepository.insertOne(requestData)
  }

  async insertMany(requestData) {
    return dataApiRepository.insertMany(requestData);
  }

  async updateOne(requestData) {
    return dataApiRepository.updateOne(requestData);
  }

  async updateMany(requestData) {
    return dataApiRepository.updateMany(requestData);
  }

  async deleteOne(requestData) {
    return dataApiRepository.deleteOne(requestData);
  }

  async deleteMany(requestData) {
    return dataApiRepository.deleteMany(requestData);
  }
  async aggregate(requestData) {
    return dataApiRepository.aggregate(requestData);
  }
}

module.exports = new dataApiService();
