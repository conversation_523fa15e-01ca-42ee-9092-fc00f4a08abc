const dotenv = require('dotenv'); // Load the dotenv package to manage environment variables

module.exports = async (req, res, next) => {
    dotenv.config(); // Load environment variables from the .env file

    if( process.env.API_KEY === req.headers["api-key"]){
        next()
    }else{
        res.status(401).send(
            {
                "error": "invalid session: error finding user for endpoint",
                "error_code": "InvalidSession"
            }
        ); 
    }

}
