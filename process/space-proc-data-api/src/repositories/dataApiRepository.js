const dotenv = require('dotenv'); // Load the dotenv package to manage environment variables
const { MongoClient } = require('mongodb'); // Import the MongoClient class from the mongodb package

dotenv.config(); // Load environment variables from the .env file

// Define the dataApiRepository class for interacting with the MongoDB database
class dataApiRepository {

  // Constructor function to set up the database connection
  constructor() {
    const mongoDatabaseUri = process.env.MONGO_CONNEXION_URL; // Get the MongoDB URI from environment variables
    const databaseName = process.env.MONGO_BD_NAME; // Get the database name from environment variables
    const client = new MongoClient(mongoDatabaseUri); // Create a new MongoClient instance with the URI
    try {
      // Connect to the MongoDB cluster
      client.connect();
      // Store the database connection in this.dbConnexion
      this.dbConnexion = client.db(databaseName)
      console.log("dbConnexion : Database connexion .... ", databaseName); // Log the database connection message;
    } catch (e) { 
      console.error(e); // Log any connection errors
    }
  }

  // Method to find a single document in the specified collection
  async findOne(req) {
    const collection = this.dbConnexion.collection(req.collection); // Get the collection
    const options = {
      sort: req.sort, // Apply sorting options
      projection: req.projection // Apply projection options
    };
    return await collection.findOne(req.filter, options); // Find and return one document matching the filter
  }

  // Method to find multiple documents in the specified collection
  async find(req) {
    const collection = this.dbConnexion.collection(req.collection); // Get the collection
    const options = {
      sort: req.sort, // Apply sorting options
      limit: req.limit, // Limit the number of documents returned
      skip: req.skip, // Skip the specified number of documents
      projection: req.projection // Apply projection options
    };
    return await collection.find(req.filter, options).toArray(); // Find and return documents as an array
  }

  // Method to insert a single document into the specified collection
  async insertOne(req) {
    const collection = this.dbConnexion.collection(req.collection); // Get the collection
    return await collection.insertOne(req.document); // Insert one document
  }

  // Method to insert multiple documents into the specified collection
  async insertMany(req) {
    const collection = this.dbConnexion.collection(req.collection); // Get the collection
    return await collection.insertMany(req.documents); // Insert liste of document

  }

  // Method to update a single document in the specified collection
  async updateOne(req) {
    const collection = this.dbConnexion.collection(req.collection); // Get the collection
    return await collection.updateOne(req.filter, req.update, req.upsert ); 
  }

  // Method to update multiple documents in the specified collection
  async updateMany(req) {
    const collection = this.dbConnexion.collection(req.collection); // Get the collection
    return await collection.updateMany(req.filter, req.update, req.upsert ); 
  }
  
  // Method to perform an aggregation operation on the specified collection
  async aggregate(req) {
    const collection = this.dbConnexion.collection(req.collection); // Get the collection
    return await collection.aggregate(req.pipeline).toArray(); 
  }

  // Method to delete a single document from the specified collection
  async deleteOne(requestData) {
    const collection = this.dbConnexion.collection(req.collection); // Get the collection
    return await collection.deleteOne(req.filter); 
  }

  // Method to delete multiple documents from the specified collection
  async deleteMany(requestData) {
    const collection = this.dbConnexion.collection(req.collection); // Get the collection
    return await collection.deleteMany(req.filter); 
  }
}

// Export an instance of the dataApiRepository class
module.exports = new dataApiRepository();
