#index:
#    path: /
#    controller: App\Controller\DefaultController::index
controllers:
    resource:
        path: ../src/Controller/
        namespace: App\Controller
    type: attribute

app.swagger_ui:
    path: /
    methods: GET
    defaults: { _controller: nelmio_api_doc.controller.swagger_ui }

app.swagger_ui_doc:
    path: /doc
    methods: GET
    defaults: { _controller: nelmio_api_doc.controller.swagger_ui }

app.swagger:
    path: /doc.json
    methods: GET
    defaults: { _controller: nelmio_api_doc.controller.swagger }