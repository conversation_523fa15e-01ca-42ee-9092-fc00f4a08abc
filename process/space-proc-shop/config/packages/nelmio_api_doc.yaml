nelmio_api_doc:
    areas:
        path_patterns: # an array of regexps (document only routes under /, except /doc)
            - ^/v[0-9]*(?!/$)
        # host_patterns: # document only routes with a host of the form api.*
        #     - ^\.
    documentation:
        # servers:
        #   - url: http://api.example.com/unsafe
        #     description: API over HTTP
        #   - url: https://api.example.com/secured
        #     description: API over HTTPS
        info:
            title: MS PROC-SHOP
            description: This is an awesome app!
            version: 1.0.0
        components:
            securitySchemes:
                Bearer:
                    type: http
                    scheme: bearer
                    bearerFormat: JWT
        security:
            - Bearer: []