<?php

namespace App\DtoResponse;


class XfEmeaDealerDto
{
    private string $dealerId;
    private string $name;
    private string $address;
    private string $phoneNumber;
    private string $latitude;
    private string $longitude;
    private string $locationSeq;
    private bool $preferred;
    private string $ossDealerId;
    private string $website;
    private bool $serviceScheduling;

    /**
     * Get the value of dealerId
     */ 
    public function getDealerId(): string
    {
        return $this->dealerId;
    }

    /**
     * Set the value of dealerId
     *
     * @return  self
     */ 
    public function setDealerId(string $dealerId): self
    {
        $this->dealerId = $dealerId;

        return $this;
    }

    /**
     * Get the value of name
     */ 
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * Set the value of name
     *
     * @return  self
     */ 
    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    /**
     * Get the value of address
     */ 
    public function getAddress(): string
    {
        return $this->address;
    }

    /**
     * Set the value of address
     *
     * @return  self
     */ 
    public function setAddress(string $address): self
    {
        $this->address = $address;

        return $this;
    }

    /**
     * Get the value of phoneNumber
     */ 
    public function getPhoneNumber(): string
    {
        return $this->phoneNumber;
    }

    /**
     * Set the value of phoneNumber
     *
     * @return  self
     */ 
    public function setPhoneNumber(string $phoneNumber): self
    {
        $this->phoneNumber = $phoneNumber;

        return $this;
    }

    /**
     * Get the value of latitude
     */ 
    public function getLatitude(): string
    {
        return $this->latitude;
    }

    /**
     * Set the value of latitude
     *
     * @return  self
     */ 
    public function setLatitude($latitude): self
    {
        $this->latitude = $latitude;

        return $this;
    }

    /**
     * Get the value of longitude
     */ 
    public function getLongitude(): string
    {
        return $this->longitude;
    }

    /**
     * Set the value of longitude
     *
     * @return  self
     */ 
    public function setLongitude(string $longitude): self
    {
        $this->longitude = $longitude;

        return $this;
    }

    /**
     * Get the value of locationSeq
     */ 
    public function getLocationSeq(): string
    {
        return $this->locationSeq;
    }

    /**
     * Set the value of locationSeq
     *
     * @return  self
     */ 
    public function setLocationSeq(string $locationSeq): self
    {
        $this->locationSeq = $locationSeq;

        return $this;
    }

    /**
     * Get the value of preferred
     */ 
    public function getPreferred(): bool
    {
        return $this->preferred;
    }

    /**
     * Set the value of preferred
     *
     * @return  self
     */ 
    public function setPreferred(bool $preferred): self
    {
        $this->preferred = $preferred;

        return $this;
    }

    /**
     * Get the value of ossDealerId
     */ 
    public function getOssDealerId(): string
    {
        return $this->ossDealerId;
    }

    /**
     * Set the value of ossDealerId
     *
     * @return  self
     */ 
    public function setOssDealerId(string $ossDealerId): self
    {
        $this->ossDealerId = $ossDealerId;

        return $this;
    }

    /**
     * Get the value of website
     */ 
    public function getWebsite(): string
    {
        return $this->website;
    }

    /**
     * Set the value of website
     *
     * @return  self
     */ 
    public function setWebsite(string $website): self
    {
        $this->website = $website;

        return $this;
    }

    /**
     * Get the value of serviceScheduling
     */ 
    public function getServiceScheduling(): bool
    {
        return $this->serviceScheduling;
    }

    /**
     * Set the value of serviceScheduling
     *
     * @return  self
     */ 
    public function setServiceScheduling($serviceScheduling): self
    {
        $this->serviceScheduling = $serviceScheduling;

        return $this;
    }
}
