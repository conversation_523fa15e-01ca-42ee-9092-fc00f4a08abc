<?php

namespace App\DtoResponse;

use App\Model\DealerV2Model;

class DealerResponseDto
{
    private array $success = [];

    public function getSuccess(): array
    {
        return $this->success;
    }

    public function setSuccess(array $success): self
    {
        $this->success = $success;

        return $this;
    }

    public function addDealer(DealerV2Model $dealer): self
    {
        $this->success[] = $dealer;

        return $this;
    }
}
