<?php

namespace App\DataMapper;

use App\Model\DealerModel;
use Symfony\Component\Serializer\SerializerInterface;

class DealerDataMapper
{
    public function __construct(private SerializerInterface $serializer)
    {
    }

    public function createList(array $dealerData): array
    {
        $dealerData = $dealerData['Dealers'] ?? null;

        if (is_null($dealerData) || !is_array($dealerData)) {
            return [];
        }

        $dealersFull = [];
        foreach ($dealerData as $dealerItem) {
            $dealersFull[] = $this->mapData($dealerItem);
        }

        return ['Dealers' => $dealersFull];
    }

    private function mapData(array $dealerData): DealerModel
    {
        $dealerData['distanceFromPoint'] = floatval($dealerData['distanceFromPoint']) ?? null;

        return $this->serializer->denormalize($dealerData, DealerModel::class);
    }
}
