<?php

namespace App\DataMapper;

use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Component\Serializer\SerializerInterface;

use App\Trait\ValidationResponseTrait;
use App\Trait\LoggerTrait;
use App\Model\SubscriptionModel;
use App\Model\SamsRatePlanModel;
use App\Helper\ErrorResponse;

class SubscriptionDataMapper
{

    use LoggerTrait;
    use ValidationResponseTrait;

    public function __construct(
        private ValidatorInterface $validator,
        private SerializerInterface $serializer,
    )
    {
    }

    private function calculateDuration(string $samsDuration): string
    {
        $duration = '';
        if (intval($samsDuration)) {
            ($samsDuration % 12) >= 1 ? $duration = 'per year' : $duration = 'per month';
        }

        return $duration;
    }

    public function createList(array $subscription, array $vehicleProvisionning, array $contribData): array|ErrorResponse
    {
        $samsRatePlan = new SamsRatePlanModel(
            $subscription['ratePlans'][0]['product']['productFamily'] ?? "",
            $subscription['ratePlans'][0]['product']['productCode'] ?? "",
            $subscription['ratePlans'][0]['ratePlanCharges'][0]['type'] ?? "",
            $subscription['ratePlans'][0]['ratePlanCharges'][0]['rateplanChargeTiers'][0]['price'] ?? "",
            $subscription['ratePlans'][0]['ratePlanCharges'][0]['ratePlanCurrency'] ?? "",
            $this->calculateDuration($subscription['ratePlans'][0]['duration']),
            $subscription['ratePlans'][0]['duration'] ?? ""
        );
        $violations = $this->validator->validate($samsRatePlan, null, ['subscriptionResponse']);
        if (0 < count($violations)) {
            $violationsDescription = $this->getValidationMessages($violations);
            $missingKeys = array_keys($violationsDescription);
            $keysString = implode(', ', $missingKeys);
            $message = "The following fields are missing data from the SAMS API response: $keysString.";
            $this->logger->error(__METHOD__ . ' Validation fails for incoming request', [
                'model' => $samsRatePlan,
                'desc' => $violationsDescription,
            ]);
            return new ErrorResponse($message, 500);
        }
        $subscription = new SubscriptionModel(
            $subscription['subscriptionName'] ?? "",
            $contribData['title'] ?? "",
            $contribData['fullDescription'] ?? "",
            'SAMS',
            $vehicleProvisionning['subscriberAccount']['externalId'] ?? "",
            $vehicleProvisionning['subscriberAccount']['systemType'] ?? "",
            $vehicleProvisionning['vehicle'] ?? "",
            $subscription['ratePlans'][0]['product']['type'] ?? "",
            $subscription['status'] ?? "",
            $vehicleProvisionning['statusReason'] ?? null,
            $vehicleProvisionning['brand'] ?? "",
            $vehicleProvisionning['countryCode'] ?? "",
            $subscription['cultureCode'] ?? "",
            $vehicleProvisionning['startDate'] ?? "",
            $vehicleProvisionning['endDate'] ?? "cvcfgdgs",
            $vehicleProvisionning['associationId'] ?? null,
            $vehicleProvisionning['associationLevel'] ?? null,
            $subscription['isExtensible'] ?? null,
            $subscription['hasFreeTrial'] ?? null,
            $contribData['topMainImage'] ?? "",
            $contribData['homePageSso'] ?? "",
            $samsRatePlan
        );
        $violations = $this->validator->validate($subscription, null, ['subscriptionResponse']);
        if (0 < count($violations)) {
            $violationsDescription = $this->getValidationMessages($violations);
            $missingKeys = array_keys($violationsDescription);
            $keysString = implode(', ', $missingKeys);
            $message = "The following fields are missing data from the SAMS API response: $keysString.";
            $this->logger->error(__METHOD__ . ' Validation fails for incoming request', [
                'model' => $subscription,
                'desc' => $violationsDescription,
            ]);
            return new ErrorResponse($message, 500);
        }
        $subscription = json_decode($this->serializer->serialize($subscription, 'json'), true);
        return $subscription;
    }
}