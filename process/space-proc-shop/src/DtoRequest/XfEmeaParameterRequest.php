<?php

namespace App\DtoRequest;

use Symfony\Component\Validator\Constraints as Assert;

class XfEmeaParameterRequest
{
    #[Assert\NotBlank]
    #[Assert\Type('integer')]
    private ?int $market;

    #[Assert\NotBlank]
    #[Assert\Type('integer')]
    private ?int $brand;
    private ?string $brandCode;
    private ?string $service;
    #[Assert\NotBlank]
    #[Assert\Regex(
        pattern: '/^\d+(\.\d+)?/'
    )]
    private string $latitude;

    #[Assert\NotBlank]
    #[Assert\Regex(
        pattern: '/^\d+(\.\d+)?/'
    )]
    private string $longitude;

    #[Assert\NotBlank]
    #[Assert\Regex(array(
        'pattern' => '/^[1-9]\d*$/'
        )
    )]
    private string $raduis;
    private string $language;

    public function __toArray(){
        return call_user_func('get_object_vars', $this);
    }

    public function getMarket(): int
    {
        return $this->market;
    }

    public function setMarket(?int $market): self
    {
        $this->market = $market;
        return $this;
    }

    public function getBrand(): int
    {
        return $this->brand;
    }

    public function setBrand(?int $brand): self
    {
        $this->brand = $brand;
        return $this;
    }

    public function getService(): ?string
    {
        return $this->service;
    }

    public function setService(?string $service): self
    {
        $this->service = $service;
        return $this;
    }

    public function getLatitude(): string
    {
        return $this->latitude;
    }

    public function setLatitude(string $latitude): self
    {
        $this->latitude = $latitude;
        return $this;
    }

    public function getLongitude(): string
    {
        return $this->longitude;
    }

    public function setLongitude(string $longitude): self
    {
        $this->longitude = $longitude;
        return $this;
    }

    public function getRaduis(): string
    {
        return $this->raduis;
    }

    public function setRaduis(string $raduis): self
    {
        $this->raduis = $raduis;
        return $this;
    }

    public function getBrandCode(): string
    {
        return $this->brandCode;
    }
 
    public function setBrandCode(string $brandCode): self
    {
        $this->brandCode = $brandCode;

        return $this;
    }

    public function getLanguage(): string
    {
        return $this->language;
    }

    public function setLanguage(string $language): self
    {
        $this->language = $language;

        return $this;
    }
}
