<?php

namespace App\Controller;

use Nelmio\ApiDocBundle\Annotation\Model;
use OpenApi\Attributes as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use OpenApi\Attributes\JsonContent;

use App\Model\CatalogModel;
use App\Trait\ValidationResponseTrait;
use App\Validator\VinValidator;
use App\Model\SubscriptionModel;
use App\Manager\SubscriptionManager;

#[Route('/v1/subscription', name: 'Subscription_')]
class SubscriptionController extends AbstractController
{
    use ValidationResponseTrait;

    #[Route('', name: 'getSubscription', methods: ['GET'])]
    #[OA\Response(
        response: 200,
        description: 'Successful Response',
        content: new Model(type: SubscriptionModel::class)
    )]
    #[OA\Response(
        response: 400,
        description: 'Bad Request',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 404,
        description: 'Bad Request',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 422,
        description: 'Bad Request::Validation Error: Required Fields Missing',
        content: new JsonContent(type: 'object', properties: [
            new OA\Property(property: 'error', properties: [
                new OA\Property(property: 'message', type: 'string', example: 'validation_failed'),
                new OA\Property(property: 'errors', type: 'object', properties: [
                    new OA\Property(property: 'userId', type: 'string', example: 'This value should not be blank.')
                ])
            ])
        ])
    )]    
    #[OA\Response(
        response: 500,
        description: 'Internal Server Error',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Parameter(
        name: 'userId',
        in: 'header',
        description: 'User ID',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'vin',
        in: 'header',
        description: 'VIN',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'target',
        in: 'query',
        description: 'target parameter',
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Tag(name: 'SUBSCRIPTION API')]
    public function getSubscription(Request $request, ValidatorInterface $validator, SubscriptionManager $subscriptionManager): JsonResponse
    {
        $userId = $request->headers->get('userId') ?? '';
        $vin = $request->headers->get('vin') ?? '';
        $target = $request->query->get('target') ?? '<string>';
        $errors = $validator->validate(
            compact('vin', 'userId'),
            new Assert\Collection([
                'userId' => new Assert\NotBlank(),
                'vin' => VinValidator::getConstraints()
            ])
        );
        $messages = $this->getValidationMessages($errors);
        if (!empty($messages)) {
            $messages = $this->getValidationErrorResponse($messages)->toArray();
            return $this->json($messages['content'], $messages['code']);
        }
        $response = $subscriptionManager->getSubscription($userId, $vin, $target)->toArray();
        return $this->json($response['content'], $response['code']);
    }
}
