<?php

namespace App\Controller;

use Nelmio\ApiDocBundle\Annotation\Model;
use OpenApi\Attributes as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use OpenApi\Attributes\JsonContent;

use App\Manager\CatalogManager;
use App\Model\CatalogModel;
use App\Trait\ValidationResponseTrait;
use App\Validator\BrandValidator;
use App\Validator\VinValidator;


#[Route('/v1/catalog', name: 'catalog_')]
class CatalogController extends AbstractController
{
    use ValidationResponseTrait;

    #[Route('', name: 'index', methods: ['GET'])]
    #[OA\Parameter(
        name: 'userId',
        in: 'header',
        description: 'User ID',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'vin',
        in: 'header',
        description: 'VIN',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'brand',
        in: 'query',
        description: 'Brand',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'country',
        in: 'query',
        description: 'Country',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'language',
        in: 'query',
        description: 'Language',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Response(
        response: 200,
        description: 'Successful Response',
        content: new Model(type: CatalogModel::class)
    )]
    #[OA\Response(
        response: 400,
        description: 'Bad Request',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 404,
        description: 'Bad Request',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 422,
        description: 'Bad Request::Validation Error: Required Fields Missing',
        content: new JsonContent(type: 'object', properties: [
            new OA\Property(property: 'error', properties: [
                new OA\Property(property: 'message', type: 'string', example: 'validation_failed'),
                new OA\Property(property: 'errors', type: 'object', properties: [
                    new OA\Property(property: 'userId', type: 'string', example: 'This value should not be blank.')
                ])
            ])
        ])
    )]    
    #[OA\Response(
        response: 500,
        description: 'Internal Server Error',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Tag(name: 'API CATALOG')]
    public function index(ValidatorInterface $validator, CatalogManager $manager, Request $request): JsonResponse
    {
        $userId = $request->headers->get('userId');
        $vin = $request->headers->get('vin');
        $brand = $request->query->get('brand');
        $country = $request->query->get('country');
        $language = $request->query->get('language');

        $errors = $validator->validate(
            compact('userId', 'vin', 'brand', 'country', 'language'),
            new Assert\Collection([
                    'userId' => new Assert\NotBlank(),
                    'brand'    => BrandValidator::getConstraints(),
                    'vin' => VinValidator::getConstraints(),
                    'country' => new Assert\NotBlank(),
                    'language' => new Assert\NotBlank(),
                ])
        );
        $messages = $this->getValidationMessages($errors);
        if (!empty($messages)) {
            $response = $this->getValidationErrorResponse($messages)->toArray();

            return $this->json($response['content'], $response['code']);
        }
        $params = [
            'userId' => $userId,
            'vin' => $vin,
            'brand' => $brand,
            'country' => $country,
            'language' => $language,
        ];

        $response = $manager->getCatalog($params)->toArray();

        return $this->json($response['content'], $response['code']);
    }
}
