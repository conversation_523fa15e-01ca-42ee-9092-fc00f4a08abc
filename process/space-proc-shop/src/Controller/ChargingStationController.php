<?php

namespace App\Controller;

use App\Manager\F2mManager;
use App\Model\ChargeStationModel;
use App\Trait\ValidationResponseTrait;
use App\Validator\BrandValidator;
use Nelmio\ApiDocBundle\Annotation\Model;
use OpenApi\Attributes as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Validator\ValidatorInterface;

#[Route('/v1', name: 'charging_station_')]
class ChargingStationController extends AbstractController
{
    use ValidationResponseTrait;

    #[Route('/f2m_locator/search', name: 'list', methods: ['GET'])]
    #[OA\Parameter(
        name: 'brand',
        in: 'query',
        description: 'Brand',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'latitude',
        in: 'query',
        description: 'latitude',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'longitude',
        in: 'query',
        description: 'longitude',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Response(
        response: 400,
        description: 'Bad Request',
    )]
    #[OA\Response(
        response: 200,
        description: 'Successful Response',
        content: new Model(type: ChargeStationModel::class)
    )]
    #[OA\Tag(name: 'Charging Station API')]
    
    public function list(ValidatorInterface $validator, F2mManager $manager, Request $request): JsonResponse
    {
        $brand = $request->query->get('brand');
        $language = $request->query->get('language');
        $latitude = $request->query->get('latitude');
        $longitude = $request->query->get('longitude');

            $errors = $validator->validate(
                compact('brand', 'latitude', 'longitude'),
                new Assert\Collection([
                    'brand' => BrandValidator::getConstraints(),
                    'latitude' => new Assert\NotBlank(),
                    'longitude' => new Assert\NotBlank(),
                ])
            );

            $messages = $this->getValidationMessages($errors);
            if (!empty($messages)) {
                $response = $this->getValidationErrorResponse($messages)->toArray();

                return $this->json($response['content'], $response['code']);
            }
            $params = [
                'brand' => $brand,
                'language' => $language,
                'latitude' => $latitude,
                'longitude' => $longitude
            ];

            $response = $manager->search($params)->toArray();
        
       
        return $this->json($response['content'], $response['code']);
    }
}
