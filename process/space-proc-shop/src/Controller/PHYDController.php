<?php

namespace App\Controller;

use App\Trait\ValidationResponseTrait;
use Nelmio\ApiDocBundle\Annotation\Model;
use OpenApi\Attributes as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\{Request, Response, JsonResponse};
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use OpenApi\Attributes\JsonContent;
use App\Service\PHYDService;
use App\Manager\PHYDManager;

#[Route('/v1/vehicle', name: 'phyd_')]
class PHYDController extends AbstractController
{
    use ValidationResponseTrait;

    #[Route('/driving-score', name: 'driving_score', methods: ['GET'])]
    #[OA\Tag(name: 'PHYD-Driving Score Data')]

    #[OA\Response(
        response: 200,
        description: 'Success Response',
        content: new JsonContent(type: 'object', properties: [
            new OA\Property(property: 'success', properties: [
                new OA\Property(property: 'featureCodes', type: 'string', example: 'UBI_PHYD'),
                new OA\Property(property: 'featureCodeStatus', type: 'string', example: 'enable'),
                new OA\Property(property: 'data', properties: [
                    new OA\Property(property: '_id', type: 'string', example: '677f9567fafbc59bbc51bccd'),
                    new OA\Property(property: 'vin', type: 'string', example: 'VR3UPHNKSKT101603'),
                    new OA\Property(property: 'updateDate', type: 'string', format: 'date', example: '2024-12-10'),
                    new OA\Property(property: 'overallScore', properties: [
                        new OA\Property(property: 'value', type: 'string', example: '36.28')
                    ]),
                    new OA\Property(property: 'dailyScore', properties: [
                        new OA\Property(property: 'value', type: 'string', example: '47.73'),
                        new OA\Property(property: 'subScore', properties: [
                            new OA\Property(property: 'dynamincs', properties: [
                                new OA\Property(property: 'percentageOfGood', type: 'string', example: '47.99'),
                                new OA\Property(property: 'percentageOfAverage', type: 'string', example: '77.35'),
                                new OA\Property(property: 'percentageOfBad', type: 'string', example: '45.75'),
                                new OA\Property(property: 'tips', type: 'string', example: '')
                            ]),
                            new OA\Property(property: 'decelaration', properties: [
                                new OA\Property(property: 'percentageOfGood', type: 'string', example: '72.15'),
                                new OA\Property(property: 'percentageOfAverage', type: 'string', example: '5.92'),
                                new OA\Property(property: 'percentageOfBad', type: 'string', example: '55.46'),
                                new OA\Property(property: 'tips', type: 'string', example: '')
                            ]),
                            new OA\Property(property: 'cornering', properties: [
                                new OA\Property(property: 'percentageOfGood', type: 'string', example: '26.02'),
                                new OA\Property(property: 'percentageOfAverage', type: 'string', example: '70.34'),
                                new OA\Property(property: 'percentageOfBad', type: 'string', example: '17.39'),
                                new OA\Property(property: 'tips', type: 'string', example: '')
                            ])
                        ])
                    ]),
                    new OA\Property(property: 'isValid', type: 'boolean', example: true),
                    new OA\Property(property: 'stliPolicyNumber', type: 'string', example: 'CN123321')
                ])
            ])
        ])
    )]

    #[OA\Response(
        response: 404,
        description: 'Error: Service Not Available for VIN',
        content: new JsonContent(type: 'object', properties: [
            new OA\Property(property: 'error', properties: [
                new OA\Property(property: 'message', type: 'string', example: 'Driving Score service not available for the vin'),
                new OA\Property(property: 'featureCode', type: 'string', example: 'UBI_PHYD'),
                new OA\Property(property: 'featureCodeStatus', type: 'string', example: 'disable')
            ])
        ])
    )]

    #[OA\Response(
        response: 422,
        description: 'Bad Request::Validation Error: Required Fields Missing',
        content: new JsonContent(type: 'object', properties: [
            new OA\Property(property: 'error', properties: [
                new OA\Property(property: 'message', type: 'string', example: 'validation_failed'),
                new OA\Property(property: 'errors', type: 'object', properties: [
                    new OA\Property(property: 'userId', type: 'string', example: 'This value should not be blank.')
                ])
            ])
        ])
    )]    

    #[OA\Parameter(
        name: 'userId',
        in: 'header',
        description: 'userId',
        schema: new OA\Schema(type: 'string'),
        required: true
    )]

    #[OA\Parameter(
        name: 'vin',
        in: 'header',
        description: 'vin',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    
    public function getDrivingScore (Request $request, PHYDManager $PHYDManager, ValidatorInterface $validator): JsonResponse
    {
        try {
            $userId = $request->headers->get('userId');
            $vin = $request->headers->get('vin');

            $errors = $validator->validate(
                compact('userId', 'vin'),
                new Assert\Collection([
                    'userId' => new Assert\NotBlank(),
                    'vin' => new Assert\NotBlank(),
                ])
            );

            $messages = $this->getValidationMessages($errors);

            if (!empty($messages)) {
                $response = $this->getValidationErrorResponse($messages)->toArray();
                return $this->json($response['content'], $response['code']);
            }

            $response = $PHYDManager->getDrivingScore($userId, $vin)->toArray();
            return $this->json($response['content'], $response['code']);
        } catch (\Throwable $e) {
            return $this->json(['error' => ['message' => $e->getMessage()]], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    #[Route('/driving-score/activation', name: 'driving_score_activation', methods: ['POST'])]
    #[OA\Tag(name: 'PHYD-Driving Score Activation')]

    #[OA\Response(
        response: 200,
        description: 'Success: Service Successfully Activated',
        content: new JsonContent(type: 'object', properties: [
            new OA\Property(property: 'success', properties: [
                new OA\Property(property: 'featureCode', type: 'string', example: 'UBI_PHYD'),
                new OA\Property(property: 'featureCodeStatus', type: 'string', example: 'enable'),
                new OA\Property(property: 'message', type: 'string', example: 'the service is successfully activated')
            ])
        ])
    )]

    #[OA\Response(
        response: 404,
        description: 'Error: Service Not Available for VIN',
        content: new JsonContent(type: 'object', properties: [
            new OA\Property(property: 'error', properties: [
                new OA\Property(property: 'featureCode', type: 'string', example: 'UBI_PHYD'),
                new OA\Property(property: 'featureCodeStatus', type: 'string', example: 'disable'),
                new OA\Property(property: 'message', type: 'string', example: 'Driving Score service not available for the vin')
            ])
        ])
    )]

    #[OA\Response(
        response: 422,
        description: 'Validation Error: Required Fields Missing',
        content: new JsonContent(type: 'object', properties: [
            new OA\Property(property: 'error', properties: [
                new OA\Property(property: 'message', type: 'string', example: 'validation_failed'),
                new OA\Property(property: 'errors', type: 'object', properties: [
                    new OA\Property(property: 'userId', type: 'string', example: 'This value should not be blank.')
                ])
            ])
        ])
    )]    

    #[OA\Response(
        response: 422,
        description: 'Unporcessable Content',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]

    #[OA\Parameter(
        name: 'userId',
        in: 'header',
        description: 'userId',
        schema: new OA\Schema(type: 'string'),
        required: true
    )]

    #[OA\RequestBody(
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'vin', type: 'string'),
                new OA\Property(property: 'stliPolicyNumber', type: 'string'), //(Insurance Contract Number (ST'LI Policy number))
            ]
        )
    )]

    public function saveDrivingScoreActivation (Request $request, PHYDManager $PHYDManager, ValidatorInterface $validator): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true);
            $vin = $data['vin'];
            $stliPolicyNumber = $data['stliPolicyNumber'];
            $userId = $request->headers->get('userId');
            $data['userId'] = $userId;

            $errors = $validator->validate(
                $data,
                new Assert\Collection([
                    'userId' => new Assert\NotBlank(),
                    'vin' => new Assert\NotBlank(),
                    'stliPolicyNumber' => new Assert\NotBlank()
                ])
            );

            $messages = $this->getValidationMessages($errors);

            if (!empty($messages)) {
                $response = $this->getValidationErrorResponse($messages)->toArray();
                return $this->json($response['content'], $response['code']);
            }

            $response = $PHYDManager->activateDrivingScore($userId, $vin, $stliPolicyNumber)->toArray();
            return $this->json($response['content'], $response['code']);
        } catch (\Throwable $e) {
            return $this->json(['error' => ['message' => $e->getMessage()]], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
