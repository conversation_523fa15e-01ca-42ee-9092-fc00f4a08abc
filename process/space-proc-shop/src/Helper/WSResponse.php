<?php

namespace App\Helper;

use Symfony\Component\HttpFoundation\Response;

/**
 * Manage rest responses.
 */
class WSResponse
{
    public function __construct(
        private ?int $code,
        private mixed $data
    ) {
        $this->code = $code ?? Response::HTTP_OK;
    }

    public function setCode(int $code): self
    {
        $this->code = $code;

        return $this;
    }

    public function getCode(): ?int
    {
        return $this->code;
    }

    public function setData(mixed $data): self
    {
        $this->data = $data;

        return $this;
    }

    public function getData(): mixed
    {
        return $this->data;
    }
}
