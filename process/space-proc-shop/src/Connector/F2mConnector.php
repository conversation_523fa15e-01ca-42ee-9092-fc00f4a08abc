<?php

namespace App\Connector;

use App\Helper\WSResponse;
use App\Trait\LoggerTrait;

class F2mConnector
{
    use LoggerTrait;

    public function __construct(
        private CustomHttpClient $client,
        private string $url
    ) {
    }

    public function call(string $method, string $uri, ?array $options = []): WSResponse
    {
        try {
            $this->logger->info(__METHOD__.'::call '.$this->url.$uri);

            return $this->client->request($method, $this->url.$uri, $options ?? []);
        } catch (\Exception $e) {
            $this->logger->error(__METHOD__.'::call : Cached Exception'.$e->getMessage());

            return new WSResponse($e->getCode(), $e->getMessage());
        }
    }
}
