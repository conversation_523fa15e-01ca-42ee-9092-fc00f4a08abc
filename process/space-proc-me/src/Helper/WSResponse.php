<?php

namespace App\Helper;

use Symfony\Component\HttpFoundation\Response;

/**
 * Manage rest responses
 */
class WSResponse
{
    private $code = Response::HTTP_OK;
    private $data;

    public function __construct(int $code, mixed $data)
    {
        $this->code = $code;
        $this->data = $data;
    }

    public function setCode(int $code): static
    {
        $this->code = $code;

        return $this;
    }

    public function getCode(): int
    {
        return $this->code;
    }

    public function setData(mixed $data): static
    {
        $this->data = $data;

        return $this;
    }

    public function getData(): mixed
    {
        return $this->data;
    }
}
