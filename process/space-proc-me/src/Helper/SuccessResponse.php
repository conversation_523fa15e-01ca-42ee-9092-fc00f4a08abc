<?php

namespace App\Helper;

use Symfony\Component\HttpFoundation\Response;

/**
 * Success response class
 */
class SuccessResponse implements ResponseArrayFormat
{
    public function __construct(
        private mixed $data,
        private ?int $code = Response::HTTP_OK)
    {
    }

    public function setCode(int $code): static
    {
        $this->code = $code;

        return $this;
    }

    public function getCode(): int
    {
        return $this->code;
    }

    public function setData(mixed $data): static
    {
        $this->data = $data;

        return $this;
    }

    public function getData(): mixed
    {
        return $this->data;
    }

    public function toArray(): array
    {
        $this->data = ! is_array($this->data) ? json_decode($this->data, true) : $this->data;

        return [
            'content' => ['success' => $this->data],
            'code'    => $this->code,
        ];
    }
}
