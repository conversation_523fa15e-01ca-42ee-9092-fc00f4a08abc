<?php

namespace App\Helper;

use Symfony\Component\Yaml\Yaml;

/**
 * BrandHelper class.
 */
class BrandHelper
{

    private const FILE_PATH =  __DIR__ . '/../../config/parametrage/brandCode.yml';
    private const XF = [
        'SP',
        'FT',
        'FO',
        'AH',
        'AR',
        'CY',
        'DG',
        'JE',
        'LA',
        'RM',
        'MA',
    ];

    private const XP = [
        'AC',
        'AP',
        'DS',
        'OP',
        'VX',
    ];

    public static function isXf(?string $brand): bool
    {
        if ($brand === null) {
            return false;
        }
        return in_array(strtoupper($brand), self::XF);
    }

    public static function isXP(?string $brand): bool
    {
        if ($brand === null) {
            return false;
        }
        return in_array(strtoupper($brand), self::XP);
    }

    public static function getBrandCode(?string $brand): ?string
    {
        if ($brand === null) {
            return null;
        }

        try {
            $brandCodes = Yaml::parseFile(self::FILE_PATH);
            $brandCode = null;

            foreach ($brandCodes as $key => $value) {
                if ($key == $brand) {
                    $brandCode = $value;
                    break;
                }
            }

            return $brandCode;
        } catch (\Exception $e) {
            return null;
        }
    }

    public static function getBrandId(?string $brand): ?string
    {
        if ($brand === null) {
            return null;
        }

        try {
            $brandCodes = Yaml::parseFile(self::FILE_PATH);
            $code = null;

            foreach ($brandCodes as $key => $value) {
                if ($value == $brand) {
                    $code = $key;
                    break;
                }
            }

            return $code;
        } catch (\Exception $e) {
            return null;
        }
    }
}
