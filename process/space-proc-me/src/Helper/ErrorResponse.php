<?php

namespace App\Helper;

use Symfony\Component\HttpFoundation\Response;

/**
 * Error response class
 */
class ErrorResponse implements ResponseArrayFormat
{
    private string $message;
    private mixed $errors;

    public function __construct(mixed $data, private ?int $code = Response::HTTP_BAD_REQUEST)
    {
        if (is_string($data)) {
            $this->message = $data;
        } else {
            $this->message = json_encode($data);
        }
    }

    public function setCode(?int $code): self
    {
        $this->code = $code;

        return $this;
    }

    public function setMessage(string $message): self
    {
        $this->message = $message;

        return $this;
    }

    public function setErrors(mixed $errors): self
    {
        $this->errors = $errors;

        return $this;
    }

    public function getCode(): ?int
    {
        return $this->code;
    }

    public function toArray(): array
    {
        $response = [];
        if ($this->message) {
            $response['message'] = $this->message;
        }
        if (isset($this->errors)) {
            $response['errors'] = $this->errors;
        }

        return [
            'code'    => 0 == $this->code ? Response::HTTP_BAD_REQUEST : $this->code,
            'content' => ['error' => $response],
        ];
    }
}
