<?php

namespace App\Controller;

use App\Manager\ConsentManager;
use App\Trait\ValidationResponseTrait;
use OpenApi\Attributes as OA;
use OpenApi\Attributes\JsonContent;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Validator\ValidatorInterface;

#[Route('v1', name: 'consent_')]
class ConsentController extends AbstractController
{
    use ValidationResponseTrait;

    #[Route('/consents', name: 'get_consents_info', methods: ['GET'])]
    #[OA\Parameter(
        name: 'userId',
        in: 'header',
        description: 'userId',
        schema: new OA\Schema(type: 'string'),
        required: true
    )]
    #[OA\Tag(name: 'Consents')]
    #[OA\Response(
        response: 200,
        description: 'Successful response',
        content: new OA\JsonContent(
            type: 'object',
            properties: [
                new OA\Property(
                    property: 'success',
                    type: 'object',
                    properties: [
                        new OA\Property(
                            property: 'preferences',
                            type: 'object',
                            properties: [
                                new OA\Property(
                                    property: 'marketing',
                                    type: 'object',
                                    properties: [
                                        new OA\Property(
                                            property: 'isConsentGranted',
                                            type: 'boolean',
                                            example: true
                                        )
                                    ]
                                ),
                                new OA\Property(
                                    property: 'profiling',
                                    type: 'object',
                                    properties: [
                                        new OA\Property(
                                            property: 'isConsentGranted',
                                            type: 'boolean',
                                            example: true
                                        )
                                    ]
                                ),
                                new OA\Property(
                                    property: 'thirdPartiesMarketing',
                                    type: 'object',
                                    properties: [
                                        new OA\Property(
                                            property: 'isConsentGranted',
                                            type: 'boolean',
                                            example: true
                                        )
                                    ]
                                ),
                                new OA\Property(
                                    property: 'thirdPartiesProfiling',
                                    type: 'object',
                                    properties: [
                                        new OA\Property(
                                            property: 'isConsentGranted',
                                            type: 'boolean',
                                            example: true
                                        )
                                    ]
                                ),
                            ]
                        )
                    ]
                )
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Bad Request',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 401,
        description: 'Unauthorized',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 403,
        description: 'Forbidden',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    public function getConsentsInfo(ValidatorInterface $validator, ConsentManager $consentManager, Request $request): JsonResponse
    {
        $userId = $request->headers->get('userId');
        $errors = $validator->validate(
            compact('userId'),
            new Assert\Collection([
                'userId' => new Assert\NotBlank()
            ])
        );
        $messages = $this->getValidationMessages($errors);

        if (!empty($messages)) {
            $response = $this->getValidationErrorResponse($messages)->toArray();
            return $this->json($response['content'], $response['code']);
        }

        $response = $consentManager->getConsentsInfo($userId)->toArray();

        return $this->json($response['content'], $response['code']);
    }

    #[Route('/consents', name: 'post_consents', methods: ['POST'])]
    #[OA\Parameter(
        name: 'userId',
        in: 'header',
        description: 'userId',
        schema: new OA\Schema(type: 'string'),
        required: true
    )]
    #[OA\RequestBody(
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(
                    property: 'preferences',
                    type: 'object',
                    properties: [
                        new OA\Property(
                            property: 'marketing',
                            type: 'object',
                            properties: [
                                new OA\Property(property: 'isConsentGranted', type: 'boolean')
                            ]
                        ),
                        new OA\Property(
                            property: 'profiling',
                            type: 'object',
                            properties: [
                                new OA\Property(property: 'isConsentGranted', type: 'boolean')
                            ]
                        ),
                        new OA\Property(
                            property: 'thirdPartiesMarketing',
                            type: 'object',
                            properties: [
                                new OA\Property(property: 'isConsentGranted', type: 'boolean')
                            ]
                        ),
                        new OA\Property(
                            property: 'thirdPartiesProfiling',
                            type: 'object',
                            properties: [
                                new OA\Property(property: 'isConsentGranted', type: 'boolean')
                            ]
                        ),
                    ]
                ),
            ]
        )
    )]
    #[OA\Tag(name: 'Consents')]
    #[OA\Response(
        response: 200,
        description: 'Successful response',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'success', properties: [
                    new OA\Property(property: 'statusCode'),
                    new OA\Property(property: 'errorCode'),
                    new OA\Property(property: 'statusReason'),
                    new OA\Property(property: 'callId'),
                    new OA\Property(property: 'time'),
                    new OA\Property(property: 'apiVersion')
                ])
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Bad Request',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 401,
        description: 'Unauthorized',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 403,
        description: 'Forbidden',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    public function putConsentsInfo(ValidatorInterface $validator, ConsentManager $consentManager, Request $request): JsonResponse
    {
        $userId = $request->headers->get('userId');
        $payload = json_decode($request->getContent(), true);
        $constraints = new Assert\Collection([
            'userId' => new Assert\NotBlank(),
            'payload' => [
                new Assert\NotBlank(),  // Ensures payload is not null or empty
                new Assert\Type('array'),  // Ensures payload is an array before deeper validation
                new Assert\Collection([
                    'preferences' => new Assert\Collection([
                        'marketing' => new Assert\Collection([
                            'isConsentGranted' => [new Assert\NotNull(), new Assert\Type('boolean')],
                        ]),
                        'profiling' => new Assert\Collection([
                            'isConsentGranted' => [new Assert\NotNull(), new Assert\Type('boolean')],
                        ]),
                        'thirdPartiesMarketing' => new Assert\Collection([
                            'isConsentGranted' => [new Assert\NotNull(), new Assert\Type('boolean')],
                        ]),
                        'thirdPartiesProfiling' => new Assert\Collection([
                            'isConsentGranted' => [new Assert\NotNull(), new Assert\Type('boolean')],
                        ]),
                    ]),
                ]),
            ],
        ]);
        $errors = $validator->validate(compact('userId', 'payload'), $constraints);
        $messages = $this->getValidationMessages($errors);
        if (!empty($messages)) {
            $response = $this->getValidationErrorResponse($messages)->toArray();
            return $this->json($response['content'], $response['code']);
        }
        $response = $consentManager->putConsentsInfo($userId, $payload)->toArray();

        return $this->json($response['content'], $response['code']);
    }
}