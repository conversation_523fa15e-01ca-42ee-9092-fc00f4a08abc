<?php

namespace App\Controller;

use Symfony\Component\HttpFoundation\JsonResponse;
use OpenApi\Attributes as OA;
use OpenApi\Attributes\JsonContent;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use App\Trait\ValidationResponseTrait;
use App\Model\ConsentArbitrationModel;
use App\Manager\ConsentArbitrationManager;

#[Route('v1', name: 'consentArbitration_')]
class ConsentArbitrationController extends AbstractController
{
    use ValidationResponseTrait;

    #[Route('/consents/arbitration', methods: ['POST'])]
    #[OA\Parameter(
        name: 'userId',
        in: 'header',
        description: 'userId',
        schema: new OA\Schema(type: 'string'),
        required: true
    )]
    #[OA\Parameter(
        name: 'brand',
        in: 'query',
        description: 'Brand',
        schema: new OA\Schema(type: 'string'),
        required: false
    )]
    #[OA\Parameter(
        name: 'country',
        in: 'query',
        description: 'Country',
        schema: new OA\Schema(type: 'string'),
        required: true
    )]
    #[OA\Parameter(
        name: 'source',
        in: 'query',
        description: 'Source',
        schema: new OA\Schema(
            type: 'string',
            enum: ['APP', 'WEB', 'SPACEWEB']
        ),
        required: false
    )]
    #[OA\Tag(name: 'Consents')]
    #[OA\RequestBody(
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'vin', type: 'string'),
                new OA\Property(property: 'consent', type: 'boolean'),
                new OA\Property(property: 'warrantyDocumentLink', type: 'string'),
            ]
        )
    )]
    #[OA\Response(
        response: 200,
        description: 'Successful response',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'success', properties: [
                    new OA\Property(property: 'message'),
                ])
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Bad Request',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 422,
        description: 'Unporcessable Content',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    public function saveConsentArbitration(Request $request, ValidatorInterface $validator, ConsentArbitrationManager $consentArbitrationManager): JsonResponse
    {
        try {
            $content = json_decode($request->getContent(), true);
            $userId = $request->headers->get('userId') ?? '';
            $model = new ConsentArbitrationModel(
                $userId,
                $content['vin'] ?? '',
                $content['consent'] ?? null,
                $request->query->get('brand', null),
                $request->query->get('country', null),
                $request->query->get('source', null),
                $content['warrantyDocumentLink'] ?? null
            );
            $violations = $validator->validate($model, null, ['consentArbitration']);
            if (!isset($content['consent'])) {
                $violations[] = new \Symfony\Component\Validator\ConstraintViolation(
                    'The consent field is required.',
                    'The consent field is required.',
                    [],
                    null,
                    'consent',
                    null
                );
            } else {
                // Check if 'consent' is a boolean
                if (!is_bool($content['consent'])) {
                    $violations[] = new \Symfony\Component\Validator\ConstraintViolation(
                        'The consent field should be boolean.',
                        'The consent field should be boolean.',
                        [],
                        null,
                        'consent',
                        $content['consent']
                    );
                }
            }
            if (0 < count($violations)) {
                $violationsDescription = $this->getValidationMessages($violations);
                $response = $this->getValidationErrorResponse($violationsDescription)->toArray();
                return $this->json($response['content'], $response['code']);
            }
            $response = $consentArbitrationManager->saveConsentArbitration($model)->toArray();
            return $this->json($response['content'], $response['code']);
        }
        catch (\Throwable $e) {
            return $this->json(['error' => ['message' => $e->getMessage()]], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
