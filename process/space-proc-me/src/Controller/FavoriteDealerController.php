<?php

namespace App\Controller;

use Symfony\Component\Serializer\Normalizer\AbstractObjectNormalizer;
use App\Helper\BrandHelper;
use Symfony\Component\HttpFoundation\JsonResponse;
use OpenApi\Attributes as OA;
use OpenApi\Attributes\JsonContent;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use App\Validator\CountryValidator;
use App\Validator\LanguageValidator;
use App\Trait\ValidationResponseTrait;
use App\Manager\FavoriteDealerManager;
use App\Validator\BrandValidator;
use App\Validator\VinValidator;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use App\Helper\ErrorResponse;

#[Route('v1/preferences', name: 'favorite_dealer_')]
class FavoriteDealerController extends AbstractController
{
    use ValidationResponseTrait;

    public function __construct(
        private ValidatorInterface $validator
    ) {}

    #[Route('/favorite-dealer', methods: ['POST'])]
    #[OA\Parameter(
        name: 'userId',
        in: 'header',
        description: 'userId',
        schema: new OA\Schema(type: 'string'),
        required: true
    )]
    #[OA\Parameter(
        name: 'vin',
        in: 'header',
        description: 'VIN',
        required: false,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'country',
        in: 'query',
        description: 'Country',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'language',
        in: 'query',
        description: 'Language',
        required: false,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'brand',
        in: 'query',
        description: 'Brand',
        schema: new OA\Schema(type: 'string'),
        required: true
    )]
    #[OA\RequestBody(
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'site_geo', type: 'string'),
            ]
        )
    )]
    #[OA\Tag(name: 'Favorite Dealer')]
    #[OA\Response(
        response: Response::HTTP_OK,
        description: 'Successful response',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'success', properties: [
                    new OA\Property(property: 'message'),
                ])
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Bad Request',
    )]
    #[OA\Response(
        response: Response::HTTP_FORBIDDEN,
        description: 'Request Forbidden',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: Response::HTTP_UNPROCESSABLE_ENTITY,
        description: 'Unprocessable Content',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    public function saveFavoriteDealer(
        Request $request,
        FavoriteDealerManager $favoriteDealerManager
    ): JsonResponse {
        $content = $request->toArray();
        $siteGeo = $content['site_geo'] ?? '';
        $userId = $request->headers->get('userId');
        $vin = $request->headers->get('vin');
        $brand = $request->query->get('brand');
        $country = $request->query->get('country');
        $language = $request->query->get('language');
        $params = [
            'brand' => $brand,
            'siteGeo' => $siteGeo,
            'userId' => $userId,
            'country' => $country,
        ];
        $listConstraints = [
            'brand' => BrandValidator::getConstraints(),
            'userId' => new Assert\NotBlank(),
            'siteGeo' => new Assert\NotBlank(),
            'country' => CountryValidator::getConstraints()
        ];

        if(BrandHelper::isXf($brand)) {
            $listConstraints['vin'] = new Assert\NotBlank();
            $params['vin'] = $vin;
            $errors = $this->validateConstraint(
                $listConstraints,
                $params
            );
        } else {
            $listConstraints['language'] = LanguageValidator::getConstraintsForLanguage();
            $params['language'] = $language;
            $errors = $this->validateConstraint(
                $listConstraints,
                $params
            );
        }

        $messages = $this->getValidationMessages($errors);
        if (!empty($messages)) {
            $response = $this->getValidationErrorResponse($messages)->toArray();

            return $this->json($response['content'], $response['code']);
        }

        $response = $favoriteDealerManager->saveFavoriteDealer($params)->toArray();
        if ($response['code'] == 200) {
            return $this->json(["success" => "Favourite dealer successfully added!"], $response['code']);
        }
        return $this->json($response['content'], $response['code']);
    }

    #[OA\Parameter(
        name: 'userId',
        in: 'header',
        description: 'userId',
        schema: new OA\Schema(type: 'string'),
        required: true
    )]
    #[OA\Parameter(
        name: 'vin',
        in: 'header',
        description: 'VIN',
        required: false,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'brand',
        in: 'query',
        description: 'Brand',
        schema: new OA\Schema(type: 'string'),
        required: true
    )]
    #[OA\Parameter(
        name: 'source',
        in: 'query',
        description: 'source',
        required: false,
        schema: new OA\Schema(
            type: 'string',
            enum: ['APP', 'WEB', 'SPACEWEB']
        )
    )]
    #[OA\Response(
        response: Response::HTTP_OK,
        description: 'Successful response',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'success', type:'object')
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Bad Request',
    )]
    #[OA\Response(
        response: Response::HTTP_FORBIDDEN,
        description: 'Forbidden',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: Response::HTTP_NOT_FOUND,
        description: 'Error: Dealer Not Available',
        content: new JsonContent(type: 'object', properties: [
            new OA\Property(property: 'error', properties: [
                new OA\Property(property: 'message', type: 'string', example: 'Dealer not found'),
                new OA\Property(property: 'errors', properties: [])
            ])
        ])
    )]
    #[OA\Response(
        response: Response::HTTP_UNPROCESSABLE_ENTITY,
        description: 'Unprocessable Content',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Tag(name: 'Favorite Dealer')]
    #[Route('/favorite-dealer',  name: 'get_favorite_dealer', methods: ['GET'])]
    public function getFavoriteDealer(
        Request $request,
        FavoriteDealerManager $favoriteDealerManager
    ): JsonResponse {
        $userId = $request->headers->get('userId');
        $vin = $request->headers->get('vin');
        $brand = $request->query->get('brand');
        $source = $request->query->get('source');
        $context = [
            AbstractObjectNormalizer::PRESERVE_EMPTY_OBJECTS => true,
            AbstractObjectNormalizer::SKIP_NULL_VALUES => TRUE,
        ];

        $params = [
            'brand' => $brand,
            'userId' => $userId,
            'source' => $source
        ];
        $listConstraints = [
            'brand' => BrandValidator::getConstraints(),
            'userId' => new Assert\NotBlank(),
            'source' => new Assert\Choice(['APP', 'SPACEWEB', 'WEB']),
        ];

        if(BrandHelper::isXf($brand)) {
            $listConstraints['vin'] = VinValidator::getConstraints();
            $params['vin'] = $vin;
            $errors = $this->validateConstraint(
                $listConstraints,
                $params
            );
        } else {
            $errors = $this->validateConstraint(
                $listConstraints,
                $params
            );
        }

        $messages = $this->getValidationMessages($errors);
        if (!empty($messages)) {
            $response = $this->getValidationErrorResponse($messages)->toArray();

            return $this->json($response['content'], $response['code']);
        }

        $response = $favoriteDealerManager->getFavoriteDealer($params)->toArray();

        return $this->json(
            $response['content'],
            $response['code'],
            [],
            $context
        );
    }


    #[Route('/favorite-dealer', methods: ['DELETE'])]
    #[OA\Parameter(
        name: 'userId',
        in: 'header',
        description: 'userId',
        schema: new OA\Schema(type: 'string'),
        required: true
    )]
    #[OA\Parameter(
        name: 'vin',
        in: 'header',
        description: 'VIN',
        required: false,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'brand',
        in: 'query',
        description: 'Brand',
        schema: new OA\Schema(type: 'string'),
        required: true
    )]
    #[OA\Parameter(
        name: 'country',
        in: 'query',
        description: 'Country',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Tag(name: 'Favorite Dealer')]
    #[OA\Response(
        response: Response::HTTP_OK,
        description: 'Successful response',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'success', properties: [
                    new OA\Property(property: 'message'),
                ])
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Bad Request',
    )]
    #[OA\Response(
        response: Response::HTTP_FORBIDDEN,
        description: 'Request Forbidden',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: Response::HTTP_UNPROCESSABLE_ENTITY,
        description: 'Unprocessable Content',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    public function deleteFavoriteDealer(
        Request $request,
        FavoriteDealerManager $favoriteDealerManager
    ): JsonResponse {
        $userId = $request->headers->get('userId');
        $vin = $request->headers->get('vin');
        $brand = $request->query->get('brand');
        $country = $request->query->get('country');
        $params = [
            'brand' => $brand,
            'userId' => $userId,
            'country' => $country
        ];
        $listConstraints = [
            'brand' => BrandValidator::getConstraints(),
            'userId' => new Assert\NotBlank(),
            'country' => CountryValidator::getConstraints()
        ];

        if(BrandHelper::isXf($brand)) {
            $listConstraints['vin'] = new Assert\NotBlank();
            $params['vin'] = $vin;
            $errors = $this->validateConstraint(
                $listConstraints,
                $params
            );
        } else {
            $errors = $this->validateConstraint(
                $listConstraints,
                $params
            );
        }

        $messages = $this->getValidationMessages($errors);
        if (!empty($messages)) {
            $response = $this->getValidationErrorResponse($messages)->toArray();

            return $this->json($response['content'], $response['code']);
        }

        $response = $favoriteDealerManager->deleteFavoriteDealer($params)->toArray();
        if ($response['code'] == 200) {
            return $this->json(["success" => "successfully deleted!"], $response['code']);
        }
        return $this->json($response['content'], $response['code']);
    }

    private function validateConstraint(
        array $listConstraints,
        array $params
    ): ConstraintViolationListInterface {
        return $this->validator->validate(
            $params,
            new Assert\Collection($listConstraints)
        );
    }
}
