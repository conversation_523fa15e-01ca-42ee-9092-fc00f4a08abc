<?php

namespace App\Validator;

use Symfony\Component\Validator\Constraints as Assert;

class BrandValidator
{
    /**
     * Get all the brands.
     */
    public static function getBrands(): array
    {
        return ['AP', 'AC', 'DS', 'OP', 'VX', 'FT', 'FO', 'AH', 'AR', 'AL', 'CY', 'DG', 'JE', 'LA', 'RM', 'MA'];
    }

    /**
     * Get brand constraints.
     */
    public static function getConstraints(): array
    {
        return [
            new Assert\NotBlank(),
            new Assert\Choice(BrandValidator::getBrands()),
        ];
    }
}
