<?php

namespace App\Validator;

use Symfony\Component\Validator\Constraints as Assert;

class VinValidator
{
    /**
     * Get Country constraints.
     *
     * @return array
     */
    public static function getConstraints(): array
    {
        return [
            new Assert\Type([
                'type' => "alnum"
            ]),
            new Assert\NotBlank,
            new Assert\Length(17),
        ];
    }
}
