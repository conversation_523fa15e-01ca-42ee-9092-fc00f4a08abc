<?php

namespace App\Transformer;

use App\DtoRequest\XfEmeaParameterRequest;
use App\DtoResponse\DealerResponseDto;
use App\Helper\CultureHelper;
use App\Model\AddressV2Model;
use App\Model\CoordinateV2Model;
use App\Model\DataModel;
use App\Model\DealerServiceModel;
use App\Model\DealerV2Model;
use App\Model\EmailV2Model;
use App\Model\OpenHourV2Model;
use App\Model\PhoneV2Model;
use App\Model\WebSiteV2Model;

class XfEmeaResponseTransformer
{
    public static function mapper(
        ?array $data,
        XfEmeaParameterRequest $params
    ): DealerResponseDto {
        $sincomIds = [];
        foreach ($data as $value) {
            $sincom = $value['SINCOM'] ?? null;
            self::extractServicesAndActivities($sincomIds, $sincom, $value);
        }
        return self::buildResponse($params, $sincomIds);
    }

    public static function buildResponse(XfEmeaParameterRequest $params, array $dealers = []): DealerResponseDto
    {
        $xfEmeaResponse = new DealerResponseDto();
        foreach ($dealers as $dealer) {
            $phoneNumber = $dealer['TEL_1'] ?? '';
            $sincom = $dealer['SINCOM'] ?? '';
            $siteCode = $dealer['SITECODE'] ?? '';
            $phone = new PhoneV2Model();
            $phone->setPhoneNumber($phoneNumber);
            $email = new EmailV2Model();
            $email->setEmail($dealer['EMAIL'] ?? '');
            $address = new AddressV2Model();
            $address->setAddress1($dealer['ADDRESS'] ?? '');
            $address->setCity($dealer['TOWN'] ?? '');
            $address->setCountry($dealer['NATION'] ?? '');
            $address->setZipCode($dealer['ZIPCODE'] ?? '');
            $address->setDepartment($dealer['PROVINCE'] ?? '');
            $coordinates = new CoordinateV2Model();
            $coordinates->setLatitude($dealer['YCOORD'] ?? '');
            $coordinates->setLongitude($dealer['XCOORD'] ?? '');
            $website = new WebSiteV2Model();
            $website->setPublic($dealer['WEBSITE'] ?? '');
            $xfEmeaDealer = new DealerV2Model();
            $xfEmeaDealer->setWebSites($website);
            $xfEmeaDealer->setRrdi($dealer['MAINCODE'] ?? '');
            //$xfEmeaDealer->setId($sincom . '|' . $siteCode);
            $xfEmeaDealer->setSiteGeo($siteCode . '|' . $sincom);
            $xfEmeaDealer->setName($dealer['COMPANYNAM'] ?? '');
            $xfEmeaDealer->setPhone($phone);
            $xfEmeaDealer->setEmail($email);
            $xfEmeaDealer->setTypeOperateur($params->getBrandCode());
            $xfEmeaDealer->setCulture(CultureHelper::getCulture($params->getBrandCode(), $params->getLanguage()));
            $distance = $dealer['DISTANCE'] ?? null;
            $xfEmeaDealer->setDistance(is_numeric($distance) && !is_string($distance) ? (float)$distance : null);
            $xfEmeaDealer->setCoordinates($coordinates);
            $xfEmeaDealer->setAddress($address);
            $openHoursList = [];
            foreach ($dealer['ACTIVITY'] as $activity) {
                $openHours = new OpenHourV2Model();
                $openHours->setLabel(self::getOpenTimes($activity));
                $openHours->setType($activity['SERVICE'] ?? '');
                $openHoursList[] = $openHours;
            }
            $xfEmeaDealer->setOpenHours($openHoursList);
            usort($dealer['SERVICES'], function ($item1, $item2) {
                return $item1->getCode() > $item2->getCode();
            });
            $dealerData = new DataModel();
            $dealerData->setRaisonSocial($dealer['LEGAL_COMPANY_NAME'] ?? '');
            $dealerData->setBrand($params->getBrandCode() ?? '');
            $dealerData->setCommercialRegister($dealer['REGISTRATI'] ?? '');
            $dealerData->setFaxNumber($dealer['FAX'] ?? '');
            $xfEmeaDealer->setData($dealerData);
            $xfEmeaDealer->setBusiness($dealer['SERVICES']);
            $xfEmeaResponse->addDealer($xfEmeaDealer);
        }

        return $xfEmeaResponse;
    }

    private static function extractServicesAndActivities(array &$sincomIds, string $sincom, array $dealer): void
    {
        $dealerService = $dealer['SERVICE'] ?? null;
        $service = new DealerServiceModel();
        $service->setCode($dealerService);
        $dealer['ACTIVITY'][0]['SERVICE'] = $dealerService;
        $dealer['SERVICES'][] = $service;
        if (!isset($sincomIds[$sincom])) {
            $sincomIds[$sincom] = $dealer;
        } else {
            if (self::isServiceExist($sincomIds[$sincom]['SERVICES'], $dealerService)) {
                return;
            }
            $storedDealer = $sincomIds[$sincom];
            $sincomIds[$sincom]['ACTIVITY'] = array_merge($dealer['ACTIVITY'], $storedDealer['ACTIVITY']);
            $sincomIds[$sincom]['SERVICES'] = array_merge($dealer['SERVICES'], $storedDealer['SERVICES']);
        }
    }

    private static function isServiceExist(array $data, string $key): bool
    {
        return count(array_filter($data, function ($item) use ($key) {
            return $item->getCode() == $key;
        })) ? true : false;
    }

    public static function getOpenTimes(array $activities): string
    {
        if (empty($activities)) {
            return '';
        }

        $openTime = '';
        usort($activities, function ($prevItem, $nextItem) {
            return (int)($prevItem['DATE'] ?? 0) - (int)($nextItem['DATE'] ?? 0);
        });

        foreach ($activities as $activity) {
            $dayData = $activity;
            $day = $dayData['DATEWEEK'] ?? '';
            $morningFrom = self::getFormatedTime($dayData['MORNING_FROM'] ?? '');
            $morningTo = self::getFormatedTime($dayData['MORNING_TO'] ?? '');
            $afternoonFrom = self::getFormatedTime($dayData['AFTERNOON_FROM'] ?? '');
            $afternoonTo = self::getFormatedTime($dayData['AFTERNOON_TO'] ?? '');

            if ($morningFrom && $morningTo || $afternoonFrom && $afternoonTo) {
                $hours = trim(
                    ($morningFrom && $morningTo ? "$morningFrom-$morningTo " : '') .
                        ($afternoonFrom && $afternoonTo ? "$afternoonFrom-$afternoonTo" : '')
                );
                $openTime .= "$day: $hours<br /> ";
            }
        }

        return $openTime;
    }

    private static function getFormatedTime(?string $time): ?string
    {
        if (!$time || strlen($time) !== 4) {
            return null;
        }
        return substr($time, 0, 2) . ':' . substr($time, 2, 2);
    }
}