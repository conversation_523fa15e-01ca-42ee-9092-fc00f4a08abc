<?php

namespace App\Transformer;

use App\DtoRequest\XfEmeaParameterRequest;
use App\Enum\DealerFilterEnum;

class xfEmeaParameterRequestTransformer
{
    public static function mapper(
        array $data,
        ?int $codeMarket,
        ?int $brandCode,
    ): XfEmeaParameterRequest
    {
        $xfEmeaParameterRequest = new XfEmeaParameterRequest();
        $xfEmeaParameterRequest->setBrand($brandCode);
        $xfEmeaParameterRequest->setBrandCode($data['brand'] ?? '');
        $xfEmeaParameterRequest->setLanguage($data['language'] ?? '');
        $xfEmeaParameterRequest->setMarket($codeMarket);
        $xfEmeaParameterRequest->setLatitude($data['latitude'] ?? '');
        $xfEmeaParameterRequest->setLongitude($data['longitude'] ?? '');
        $xfEmeaParameterRequest->setRaduis($data['resultmax'] ?? DealerFilterEnum::FILTER_RESULTMAX);
        return $xfEmeaParameterRequest;
    }
}