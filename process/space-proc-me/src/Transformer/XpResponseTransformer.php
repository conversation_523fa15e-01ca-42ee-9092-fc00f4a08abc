<?php

namespace App\Transformer;

use App\Helper\CultureHelper;
use App\Model\AddressV2Model;
use App\Model\AdrLivVnListModel;
use App\Model\BusinessModel;
use App\Model\CodeActorModel;
use App\Model\CodeRegionModel;
use App\Model\ContratVlModel;
use App\Model\CoordinateV2Model;
use App\Model\DataModel;
use App\Model\DealerV2Model;
use App\Model\EmailV2Model;
use App\Model\GroupModel;
use App\Model\ImporterModel;
use App\Model\IndicatorModel;
use App\Model\OpenHourV2Model;
use App\Model\PdvImporterModel;
use App\Model\PhoneV2Model;
use App\Model\PrincipalModel;
use App\Model\UrlPageV2Model;
use App\Model\WebSiteV2Model;

class XpResponseTransformer
{
    public static function mapper(
        ?array $value,
        array $params,
        bool $withXXcall = false
    ): DealerV2Model   {
        $o2xSettings = $params['o2x'] ?? null;
        $brand = $params['brand'] ?? '';
        $dataBusinessO2X = $o2xSettings['licenceCode'] ?? null;
        $dataCodeValet = $o2xSettings['codeValet'] ?? null;
        $xpDealer = new DealerV2Model();
        $siteGeo = $value['SiteGeo'] ?? "";
        $xpDealer->setSiteGeo($siteGeo);
        $xpDealer->setRrdi($value['RRDI'] ?? "");
        $xpDealer->setName($value['Name'] ?? "");

        $phone = new PhoneV2Model();
        $phoneApv = $value['Phones']['PhoneAPV'] ?? "";
        $phoneNumber = $value['Phones']['PhoneNumber'] ?? "";
        $defaultPhoneNumber = $withXXcall ? $phoneApv : $phoneNumber;
        $phone->setPhoneNumber($defaultPhoneNumber);
        $phone->setPhoneApv($phoneApv ?? "");
        $phone->setPhonePr($value['Phones']['PhonePR'] ?? "");
        $phone->setPhoneVn($value['Phones']['PhoneVN'] ?? "");
        $phone->setPhoneVo($value['Phones']['PhoneVO'] ?? "");
        $xpDealer->setPhone($phone);

        $email = new EmailV2Model();
        $email->setEmail($value['Emails']['Email'] ?? "");
        $email->setEmailApv($value['Emails']['EmailAPV'] ?? "");
        $email->setEmailAgent($value['Emails']['EmailAgent'] ?? "");
        $email->setEmailGer($value['Emails']['EmailGER'] ?? "");
        $email->setEmailGrc($value['Emails']['EmailGRC'] ?? "");
        $email->setEmailPr($value['Emails']['EmailPR'] ?? "");
        $email->setEmailSales($value['Emails']['EmailSales'] ?? "");
        $email->setEmailVo($value['Emails']['EmailVO'] ?? "");
        $xpDealer->setEmail($email);

        $address = new AddressV2Model();
        $address->setAddress1($value['Address']['Line1'] ?? "");
        $address->setAddress2($value['Address']['Line2'] ?? "");
        $address->setAddress3($value['Address']['Line3'] ?? "");
        $address->setCity($value['Address']['City'] ?? "");
        $address->setRegion($value['Address']['Region'] ?? "");
        $address->setCountry($value['Address']['Country'] ?? "");
        $address->setZipCode($value['Address']['ZipCode'] ?? "");
        $xpDealer->setAddress($address);

        $coordinate = new CoordinateV2Model();
        $coordinate->setLatitude($value['Coordinates']['Latitude'] ?? "");
        $coordinate->setLongitude($value['Coordinates']['Longitude'] ?? "");
        $xpDealer->setCoordinates($coordinate);

        $xpDealer->setDistance($value['DistanceFromPoint'] ?? "");
        $xpDealer->setIsAgent($value['IsAgent'] ?? null);
        $xpDealer->setIsAgentAP($value['IsAgentAP'] ?? null);
        $xpDealer->setIsSecondary($value['IsSecondary'] ?? null);
        $xpDealer->setIsSuccursale($value['IsSuccursale'] ?? null);

        $services = [];
        $webSite = $value['WebSites']['Public'] ?? "";
        $businessList = $value['BusinessList'] ?? [];
        $isO2x = false;
        $isJocKey = false;
        foreach ($businessList as $service) {
            if ($service['Code']) {
                $serviceModel = new BusinessModel();
                $serviceModel->setLabel($service['Label'] ?? "");
                $serviceModel->setCode($service['Code']);
                $serviceModel->setType($service['Type'] ?? "");
                $services[] = $serviceModel;
                if ($withXXcall && $webSite) {
                    if (self::isFoundBusinessCode($service['Code'], $o2xSettings ?? [])) {
                        $xpDealer->setTypeOperateur($service['Label']);
                        $webSite = self::calculateUrl($webSite, $service['Label'] ?? '', $siteGeo ?? '', $params['country']);
                    }
                }

                if ($dataBusinessO2X == $service['Code']) {
                    $isO2x = true;
                }

                if ($dataCodeValet == $service['Code']) {
                    $isO2x = true;
                    $isJocKey = true;
                }
            }
        }
        $xpDealer->setBusiness($services);

        $openHours = $value['OpeningHoursList'] ?? [];
        $listOpenHours = [];
        foreach ($openHours as $openHour) {
            $openHourModel = new OpenHourV2Model();
            $openHourModel->setLabel($openHour['Label'] ?? "");
            $openHourModel->setType($openHour['Type'] ?? "");
            $listOpenHours[] = $openHourModel;
        }
        $xpDealer->setOpenHours($listOpenHours);

        $principal = new PrincipalModel();
        $principal->setIsPrincipalAG($value['Principal']['IsPrincipalAG'] ?? null);
        $principal->setIsPrincipalPR($value['Principal']['IsPrincipalPR'] ?? null);
        $principal->setIsPrincipalRA($value['Principal']['IsPrincipalRA'] ?? null);
        $principal->setIsPrincipalVN($value['Principal']['IsPrincipalVN'] ?? null);
        $principal->setIsPrincipalVO($value['Principal']['IsPrincipalVO'] ?? null);
        $xpDealer->setPrincipal($principal);

        $urlPages = new UrlPageV2Model();
        $urlPages->setUrlApvForm($value['UrlPages']['UrlAPVForm'] ?? "");
        $urlPages->setUrlContact($value['UrlPages']['UrlContact'] ?? "");
        $urlPages->setUrlNewCarStock($value['UrlPages']['UrlNewCarStock'] ?? "");
        $urlPages->setUrlUsedCarStock($value['UrlPages']['UrlUsedCarStock'] ?? "");
        $urlPages->setUrlUsefullInformation($value['UrlPages']['UrlUsefullInformation'] ?? "");
        $xpDealer->setUrlPages($urlPages);

        $webSites = new WebSiteV2Model();
        $webSites->setPublic($value['WebSites']['Public'] ?? "");
        $webSites->setPrivate($value['WebSites']['Private'] ?? "");
        $xpDealer->setWebSites($webSites);

        $xpDealer->setCulture(CultureHelper::getCulture($value['CountryId'], $value['Culture']));
        
        $dealerData = new DataModel();
        $dealerData->setBenefitList($value['BenefitList'] ?? []);

        $codeActor = new CodeActorModel();
        $codeActor->setCodeActorAddressPr($value['CodesActors']['CodeActorAddressPR'] ?? "");
        $codeActor->setCodeActorAddressRa($value['CodesActors']['CodeActorAddressRA'] ?? "");
        $codeActor->setCodeActorAddressVn($value['CodesActors']['CodeActorAddressVN'] ?? "");
        $codeActor->setCodeActorAddressVo($value['CodesActors']['CodeActorAddressVO'] ?? "");
        $codeActor->setCodeActorCcAg($value['CodesActors']['CodeActorCC_AG'] ?? "");
        $codeActor->setCodeActorCcPr($value['CodesActors']['CodeActorCC_PR'] ?? "");
        $codeActor->setCodeActorCcRa($value['CodesActors']['CodeActorCC_RA'] ?? "");
        $codeActor->setCodeActorCcVn($value['CodesActors']['CodeActorCC_VN'] ?? "");
        $codeActor->setCodeActorCcVo($value['CodesActors']['CodeActorCC_VO'] ?? "");
        $codeActor->setCodeActorSearch($value['CodesActors']['CodeActorSearch'] ?? "");
        $dealerData->setCodesActors($codeActor ?? null);

        $codeRegion = new CodeRegionModel();
        $codeRegion->setCodeRegionAg($value['CodesRegions']['CodeRegionAG'] ?? "");
        $codeRegion->setCodeRegionPr($value['CodesRegions']['CodeRegionPR'] ?? "");
        $codeRegion->setCodeRegionRa($value['CodesRegions']['CodeRegionRA'] ?? "");
        $codeRegion->setCodeRegionVn($value['CodesRegions']['CodeRegionVN'] ?? "");
        $codeRegion->setCodeRegionVo($value['CodesRegions']['CodeRegionVO'] ?? "");
        $dealerData->setCodesRegions($codeRegion);

        $dealerData->setFaxNumber($value['FaxNumber'] ?? "");

        $group = new GroupModel();
        $group->setGroupId($value['Group']['GroupId'] ?? "");
        $group->setIsLeader($value["Group"]["IsLeader"] ?? null);
        $group->setSubGroupId($value['Group']['SubGroupId'] ?? "");
        $group->setSubGrouplabel($value['Group']['SubGrouplabel'] ?? "");
        $dealerData->setGroup($group);

        $indicator = new IndicatorModel();
        $indicator->setCode($value['Indicator']['Code'] ?? "");
        $indicator->setLabel($value['Indicator']['label'] ?? "");
        $dealerData->setIndicator($indicator);

        $dealerData->setWelcomeMessage($value['WelcomeMessage'] ?? "");

        $importer = new ImporterModel();
        $importer->setImporterCode($value['Importer']['ImporterCode'] ?? "");
        $importer->setCorporateName($value['Importer']['CorporateName'] ?? "");
        $importer->setImporterName($value['Importer']['ImporterName'] ?? "");
        $importer->setAddress($value['Importer']['Address'] ?? "");
        $importer->setCity($value['Importer']['City'] ?? "");
        $importer->setManagementCountry($value['Importer']['ManagementCountry'] ?? "");
        $importer->setCountry($value['Importer']['Country'] ?? "");
        $importer->setSubsidiary($value['Importer']['Subsidiary'] ?? "");
        $importer->setSubsidiaryName($value['Importer']['SubsidiaryName'] ?? "");
        $dealerData->setImporter($importer);

        $pdvImporter = new PdvImporterModel();
        $pdvImporter->setPdvCode($value['PDVImporter']['PDVCode'] ?? "");
        $pdvImporter->setPdvName($value['PDVImporter']['PDVName'] ?? "");
        $pdvImporter->setPdvContact($value['PDVImporter']['PDVContact'] ?? "");
        $dealerData->setPdvImporter($pdvImporter);

        $dealerData->setNumSiret($value['NumSiret'] ?? "");
        $dealerData->setLegalStatus($value['LegalStatus'] ?? "");
        $dealerData->setCapital($value['Capital'] ?? "");
        $dealerData->setCommercialRegister($value['CommercialRegister'] ?? "");
        $dealerData->setIntracommunityTva($value['IntracommunityTVA'] ?? "");
        $dealerData->setBrand($value['Brand'] ?? "");
        $dealerData->setParentSiteGeo($value['ParentSiteGeo'] ?? "");
        $dealerData->setRaisonSocial($value['RaisonSocial'] ?? "");
        $dealerData->setRcsNumber($value['RCSNumber'] ?? "");
        $dealerData->setGmCodeList($value['GmCodeList'] ?? []);
        $dealerData->setLienVoList($value['LienVoList'] ?? []);
        $dealerData->setBqCaptive($value['bqCaptive'] ?? "");
        $dealerData->setCaracRdvi($value['carac_rdvi'] ?? "");
        $dealerData->setFtcCodeList($value['FtcCodeList'] ?? []);
        
        $adrLivVNLists = [];
        foreach (($value['AdrLivVNList'] ?? []) as $adrLiv) {
            $adrLivVNList = new AdrLivVnListModel();
            $adrLivVNList->setActorAdrCode($adrLiv['ActorAdrCode'] ?? "");
            $adrLivVNList->setCity($adrLiv['City'] ?? "");
            $adrLivVNList->setCommercialName($adrLiv['CommercialName'] ?? "");
            $adrLivVNList->setLine1($adrLiv['Line1'] ?? "");
            $adrLivVNList->setLine2($adrLiv['Line2'] ?? "");
            $adrLivVNList->setIsDefault($adrLiv['isDefault'] ?? null);
            $adrLivVNList->setType($adrLiv['Type'] ?? "");
            $adrLivVNLists[] = $adrLivVNList;
        }
        if($adrLivVNLists) {
            $dealerData->setAdrLivVnList($adrLivVNLists ?? null);
        }

        $contractV1 = new ContratVlModel();
        $contractV1->setCodeActorAddressVl($value['ContratVl']['CodeActorAddressVL'] ?? "");
        $contractV1->setCodeActorCcVl($value['ContratVl']['CodeActorCC_VL'] ?? "");
        $contractV1->setCodeRegionVl($value['ContratVl']['CodeRegionVL'] ?? "");
        $contractV1->setEmailVl($value['ContratVl']['EmailVL'] ?? "");
        $contractV1->setPhoneVl($value['ContratVl']['PhoneVL'] ?? "");
        $contractV1->setIsPrincipalVl($value['ContratVl']['IsPrincipalVL'] ?? null);
        $dealerData->setContratVl($contractV1);
        $xpDealer->setData($dealerData);

        $xpDealer->setIsCaracRdvi($value['caracRdvi'] ?? false);
        $xpDealer->setTypeOperateur($brand);
        $xpDealer->setUrlPrdvErcs($webSite);
        $xpDealer->setO2x($isO2x);
        $xpDealer->setJockey($isJocKey);

        return $xpDealer;
    }

    private static function calculateUrl(string $webSite, string $label, string $siteGeo, string $country): ?string
    {
        if ($label == 'ERCS') {
            return $webSite . '?xcode=' . $siteGeo . '&locale=' . $country;
        }
        return $webSite;
    }

    private static function isFoundBusinessCode(string $businessCode, array $o2xSettings): bool
    {
        return in_array($businessCode, self::getParamsO2C($o2xSettings));
    }

    private static function getParamsO2C($o2xSettings): array
    {
        $params = [];
        if (isset($o2xSettings['licenceRI'])) {
            $params[] = $o2xSettings['licenceRI'];
        }
        if (isset($o2xSettings['licenceERCS'])) {
            $params[] = $o2xSettings['licenceERCS'];
        }

        return $params;
    }
}