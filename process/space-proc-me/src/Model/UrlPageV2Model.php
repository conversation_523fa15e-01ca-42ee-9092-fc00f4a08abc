<?php

namespace App\Model;

use Symfony\Component\Serializer\Attribute\SerializedName;

class UrlPageV2Model
{
    #[SerializedName('UrlAPVForm')]
    private ?string $urlApvForm = "";
    #[SerializedName('UrlContact')]
    private ?string $urlContact = "";
    #[SerializedName('UrlNewCarStock')]
    private ?string $urlNewCarStock = "";
    #[SerializedName('UrlUsedCarStock')]
    private ?string $urlUsedCarStock = "";
    #[SerializedName('UrlUsefullInformation')]
    private ?string $urlUsefullInformation = "";

    public function getUrlApvForm(): ?string
    {
        return $this->urlApvForm;
    }

    public function getUrlContact(): ?string
    {
        return $this->urlContact;
    }

    public function getUrlNewCarStock(): ?string
    {
        return $this->urlNewCarStock;
    }

    public function getUrlUsedCarStock(): ?string
    {
        return $this->urlUsedCarStock;
    }

    public function getUrlUsefullInformation(): ?string
    {
        return $this->urlUsefullInformation;
    }

    public function setUrlApvForm(?string $urlApvForm): self
    {
        $this->urlApvForm = $urlApvForm;

        return $this;
    }

    public function setUrlContact(?string $urlContact): self
    {
        $this->urlContact = $urlContact;

        return $this;
    }

    public function setUrlNewCarStock(?string $urlNewCarStock): self
    {
        $this->urlNewCarStock = $urlNewCarStock;

        return $this;
    }

    public function setUrlUsedCarStock(?string $urlUsedCarStock): self
    {
        $this->urlUsedCarStock = $urlUsedCarStock;

        return $this;
    }

    public function setUrlUsefullInformation(?string $urlUsefullInformation): self
    {
        $this->urlUsefullInformation = $urlUsefullInformation;

        return $this;
    }
}