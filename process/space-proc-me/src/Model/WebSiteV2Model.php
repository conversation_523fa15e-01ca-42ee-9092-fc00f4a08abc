<?php

namespace App\Model;

use Symfony\Component\Serializer\Attribute\SerializedName;

class WebSiteV2Model
{
    #[SerializedName('Private')]
    private ?string $private = "";
    #[SerializedName('Public')]
    private ?string $public = "";

    public function getPrivate(): ?string
    {
        return $this->private;
    }

    public function getPublic(): ?string
    {
        return $this->public;
    }

    public function setPrivate(?string $private): self
    {
        $this->private = $private;

        return $this;
    }

    public function setPublic(?string $public): self
    {
        $this->public = $public;

        return $this;
    }
}