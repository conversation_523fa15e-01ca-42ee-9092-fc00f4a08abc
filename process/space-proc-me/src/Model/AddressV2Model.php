<?php

namespace App\Model;

use Symfony\Component\Serializer\Attribute\SerializedName;

class AddressV2Model
{
    private ?string $address1 = "";
    private ?string $address2 = "";
    private ?string $address3 = "";
    private ?string $department = "";
    private ?string $city = "";
    private ?string $region = "";
    #[SerializedName('zip_code')]
    private ?string $zipCode = "";
    private ?string $country = "";

    public function getAddress1(): ?string
    {
        return $this->address1;
    }
 
    public function setAddress1(?string $address1): self
    {
        $this->address1 = $address1;

        return $this;
    }

    public function getAddress2(): ?string
    {
        return $this->address2;
    }

    public function setAddress2(?string $address2): self
    {
        $this->address2 = $address2;

        return $this;
    }

    public function getAddress3(): ?string
    {
        return $this->address3;
    }

    public function setAddress3(?string $address3): self
    {
        $this->address3 = $address3;

        return $this;
    }

    public function getDepartment(): ?string
    {
        return $this->department;
    }

    public function setDepartment(?string $department): self
    {
        $this->department = $department;

        return $this;
    }

    public function getCity(): ?string
    {
        return $this->city;
    }

    public function setCity(?string $city): self
    {
        $this->city = $city;

        return $this;
    }

    public function getRegion(): ?string
    {
        return $this->region;
    }

    public function setRegion(?string $region): self
    {
        $this->region = $region;

        return $this;
    }
 
    public function getZipCode(): ?string
    {
        return $this->zipCode;
    }
 
    public function setZipCode(?string $zipCode): self
    {
        $this->zipCode = $zipCode;

        return $this;
    }

    public function getCountry(): ?string
    {
        return $this->country;
    }

    public function setCountry(?string $country): self
    {
        $this->country = $country;

        return $this;
    }
}