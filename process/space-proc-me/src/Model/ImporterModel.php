<?php

namespace App\Model;

use Symfony\Component\Serializer\Attribute\SerializedName;

class ImporterModel
{
	#[SerializedName('ImporterCode')]
	private ?string $importerCode = "";
	#[SerializedName('CorporateName')]
	private ?string $corporateName = "";
	#[SerializedName('ImporterName')]
	private ?string $importerName = "";
	#[SerializedName('Address')]
	private ?string $address = "";
	#[SerializedName('City')]
	private ?string $city = "";
	#[SerializedName('ManagementCountry')]
	private ?string $managementCountry = "";
	#[SerializedName('Country')]
	private ?string $country = "";
	#[SerializedName('Subsidiary')]
	private ?string $subsidiary = "";
	#[SerializedName('SubsidiaryName')]
	private ?string $subsidiaryName = "";

	public function getImporterCode(): ?string
	{
		return $this->importerCode;
	}

	public function getCorporateName(): ?string
	{
		return $this->corporateName;
	}

	public function getImporterName(): ?string
	{
		return $this->importerName;
	}

	public function getAddress(): ?string
	{
		return $this->address;
	}

	public function getCity(): ?string
	{
		return $this->city;
	}

	public function getManagementCountry(): ?string
	{
		return $this->managementCountry;
	}

	public function getCountry(): ?string
	{
		return $this->country;
	}

	public function getSubsidiary(): ?string
	{
		return $this->subsidiary;
	}

	public function getSubsidiaryName(): ?string
	{
		return $this->subsidiaryName;
	}

	public function setImporterCode(?string $importerCode): self
	{
		$this->importerCode = $importerCode;
		return $this;
	}

	public function setCorporateName(?string $corporateName): self
	{
		$this->corporateName = $corporateName;
		return $this;
	}

	public function setImporterName(?string $importerName): self
	{
		$this->importerName = $importerName;
		return $this;
	}

	public function setAddress(?string $address): self
	{
		$this->address = $address;
		return $this;
	}

	public function setCity(?string $city): self
	{
		$this->city = $city;
		return $this;
	}

	public function setManagementCountry(?string $managementCountry): self
	{
		$this->managementCountry = $managementCountry;
		return $this;
	}

	public function setCountry(?string $country): self
	{
		$this->country = $country;
		return $this;
	}

	public function setSubsidiary(?string $subsidiary): self
	{
		$this->subsidiary = $subsidiary;
		return $this;
	}

	public function setSubsidiaryName(?string $subsidiaryName): self
	{
		$this->subsidiaryName = $subsidiaryName;
		return $this;
	}
}