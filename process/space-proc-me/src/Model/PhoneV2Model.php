<?php

namespace App\Model;

use Symfony\Component\Serializer\Attribute\SerializedName;

class PhoneV2Model
{
    #[SerializedName('PhoneAPV')]
    private ?string $phoneApv = "";
    #[SerializedName('PhoneNumber')]
    private ?string $phoneNumber = "";
    #[SerializedName('PhonePR')]
    private ?string $phonePr = "";
    #[SerializedName('PhoneVN')]
    private ?string $phoneVn = "";
    #[SerializedName('PhoneVO')]
    private ?string $phoneVo = "";

    public function getPhoneNumber(): ?string
    {
        return $this->phoneNumber;
    }

    public function setPhoneNumber(?string $phoneNumber): self
    {
        $this->phoneNumber = $phoneNumber;

        return $this;
    }

    public function getPhoneApv(): ?string
    {
        return $this->phoneApv;
    }

    public function setPhoneApv(?string $phoneApv): self
    {
        $this->phoneApv = $phoneApv;

        return $this;
    }

    public function getPhonePr(): ?string
    {
        return $this->phonePr;
    }
 
    public function setPhonePr(?string $phonePr): self
    {
        $this->phonePr = $phonePr;

        return $this;
    }

    public function getPhoneVn(): ?string
    {
        return $this->phoneVn;
    }

    public function setPhoneVn(?string $phoneVn): self
    {
        $this->phoneVn = $phoneVn;

        return $this;
    }

    public function getPhoneVo(): ?string
    {
        return $this->phoneVo;
    }

    public function setPhoneVo(?string $phoneVo): self
    {
        $this->phoneVo = $phoneVo;

        return $this;
    }
}