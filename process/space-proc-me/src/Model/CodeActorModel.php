<?php

namespace App\Model;

use Symfony\Component\Serializer\Attribute\SerializedName;

class CodeActorModel
{
	#[SerializedName('CodeActorAddressPR')]
	private ?string $codeActorAddressPr = "";
	#[SerializedName('CodeActorAddressRA')]
	private ?string $codeActorAddressRa = "";
	#[SerializedName('CodeActorAddressVN')]
	private ?string $codeActorAddressVn = "";
	#[SerializedName('CodeActorAddressVO')]
	private ?string $codeActorAddressVo = "";
	#[SerializedName('CodeActorCC_AG')]
	private ?string $codeActorCcAg = "";
	#[SerializedName('CodeActorCC_PR')]
	private ?string $codeActorCcPr = "";
	#[SerializedName('CodeActorCC_RA')]
	private ?string $codeActorCcRa = "";
	#[SerializedName('CodeActorCC_VN')]
	private ?string $codeActorCcVn = "";
	#[SerializedName('CodeActorCC_VO')]
	private ?string $codeActorCcVo = "";
	#[SerializedName('CodeActorSearch')]
	private ?string $codeActorSearch = "";

	public function getCodeActorAddressPr(): ?string
	{
		return $this->codeActorAddressPr;
	}

	public function getCodeActorAddressRa(): ?string
	{
		return $this->codeActorAddressRa;
	}

	public function getCodeActorAddressVn(): ?string
	{
		return $this->codeActorAddressVn;
	}

	public function getCodeActorAddressVo(): ?string
	{
		return $this->codeActorAddressVo;
	}

	public function getCodeActorCcAg(): ?string
	{
		return $this->codeActorCcAg;
	}

	public function getCodeActorCcPr(): ?string
	{
		return $this->codeActorCcPr;
	}

	public function getCodeActorCcRa(): ?string
	{
		return $this->codeActorCcRa;
	}

	public function getCodeActorCcVn(): ?string
	{
		return $this->codeActorCcVn;
	}

	public function getCodeActorCcVo(): ?string
	{
		return $this->codeActorCcVo;
	}

	public function getCodeActorSearch(): ?string
	{
		return $this->codeActorSearch;
	}

	public function setCodeActorAddressPr(?string $codeActorAddressPr): self
	{
		$this->codeActorAddressPr = $codeActorAddressPr;
		return $this;
	}

	public function setCodeActorAddressRa(?string $codeActorAddressRa): self
	{
		$this->codeActorAddressRa = $codeActorAddressRa;
		return $this;
	}

	public function setCodeActorAddressVn(?string $codeActorAddressVn): self
	{
		$this->codeActorAddressVn = $codeActorAddressVn;
		return $this;
	}

	public function setCodeActorAddressVo(?string $codeActorAddressVo): self
	{
		$this->codeActorAddressVo = $codeActorAddressVo;
		return $this;
	}

	public function setCodeActorCcAg(?string $codeActorCcAg): self
	{
		$this->codeActorCcAg = $codeActorCcAg;
		return $this;
	}

	public function setCodeActorCcPr(?string $codeActorCcPr): self
	{
		$this->codeActorCcPr = $codeActorCcPr;
		return $this;
	}

	public function setCodeActorCcRa(?string $codeActorCcRa): self
	{
		$this->codeActorCcRa = $codeActorCcRa;
		return $this;
	}

	public function setCodeActorCcVn(?string $codeActorCcVn): self
	{
		$this->codeActorCcVn = $codeActorCcVn;
		return $this;
	}

	public function setCodeActorCcVo(?string $codeActorCcVo): self
	{
		$this->codeActorCcVo = $codeActorCcVo;
		return $this;
	}

	public function setCodeActorSearch(?string $codeActorSearch): self
	{
		$this->codeActorSearch = $codeActorSearch;
		return $this;
	}
}