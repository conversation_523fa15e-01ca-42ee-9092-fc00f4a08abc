<?php

namespace App\Model;

use Symfony\Component\Serializer\Attribute\SerializedName;

class PdvImporterModel
{
    #[SerializedName('PDVCode')]
	private ?string $pdvCode = "";
    #[SerializedName('PDVName')]
	private ?string $pdvName = "";
    #[SerializedName('PDVContact')]
	private ?string $pdvContact = "";

	public function getPdvCode(): ?string
	{
		return $this->pdvCode;
	}

	public function getPdvName(): ?string
	{
		return $this->pdvName;
	}

	public function getPdvContact(): ?string
	{
		return $this->pdvContact;
	}

	public function setPdvCode(?string $pdvCode): self
	{
		$this->pdvCode = $pdvCode;
		return $this;
	}

	public function setPdvName(?string $pdvName): self
	{
		$this->pdvName = $pdvName;
		return $this;
	}

	public function setPdvContact(?string $pdvContact): self
	{
		$this->pdvContact = $pdvContact;
		return $this;
	}
}