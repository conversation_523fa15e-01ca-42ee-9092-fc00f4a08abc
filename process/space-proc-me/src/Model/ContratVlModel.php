<?php

namespace App\Model;

use Symfony\Component\Serializer\Attribute\SerializedName;

class ContratVlModel
{
    #[SerializedName('CodeActorAddressVL')]
	private ?string $codeActorAddressVl = "";
    #[SerializedName('CodeActorCC_VL')]
	private ?string $codeActorCcVl = "";
    #[SerializedName('CodeRegionVL')]
	private ?string $codeRegionVl = "";
    #[SerializedName('EmailVL')]
	private ?string $emailVl = "";
    #[SerializedName('PhoneVL')]
	private ?string $phoneVl = "";
    #[SerializedName('IsPrincipalVL')]
	private ?bool $isPrincipalVl = NULL;

	public function getCodeActorAddressVl(): ?string
	{
		return $this->codeActorAddressVl;
	}

	public function getCodeActorCcVl(): ?string
	{
		return $this->codeActorCcVl;
	}

	public function getCodeRegionVl(): ?string
	{
		return $this->codeRegionVl;
	}

	public function getEmailVl(): ?string
	{
		return $this->emailVl;
	}

	public function getPhoneVl(): ?string
	{
		return $this->phoneVl;
	}

	public function getIsPrincipalVl(): ?bool
	{
		return $this->isPrincipalVl;
	}

	public function setCodeActorAddressVl(?string $codeActorAddressVl): self
	{
		$this->codeActorAddressVl = $codeActorAddressVl;
		return $this;
	}

	public function setCodeActorCcVl(?string $codeActorCcVl): self
	{
		$this->codeActorCcVl = $codeActorCcVl;
		return $this;
	}

	public function setCodeRegionVl(?string $codeRegionVl): self
	{
		$this->codeRegionVl = $codeRegionVl;
		return $this;
	}

	public function setEmailVl(?string $emailVl): self
	{
		$this->emailVl = $emailVl;
		return $this;
	}

	public function setPhoneVl(?string $phoneVl): self
	{
		$this->phoneVl = $phoneVl;
		return $this;
	}

	public function setIsPrincipalVl(?bool $isPrincipalVl): self
	{
		$this->isPrincipalVl = $isPrincipalVl;
		return $this;
	}
}
