<?php

namespace App\Model;

use Symfony\Component\Serializer\Attribute\SerializedName;

class GroupModel
{
	#[SerializedName('GroupId')]
	private ?string $groupId = "";
	#[SerializedName('IsLeader')]
	private ?bool $isLeader = null;
	#[SerializedName('SubGroupId')]
	private ?string $subGroupId = "";
	#[SerializedName('SubGrouplabel')]
	private ?string $subGrouplabel = "";

	public function getGroupId(): ?string
	{
		return $this->groupId;
	}

	public function getSubGroupId(): ?string
	{
		return $this->subGroupId;
	}

	public function getIsLeader(): ?bool
	{
		return $this->isLeader;
	}

	public function getSubGrouplabel(): ?string
	{
		return $this->subGrouplabel;
	}

	public function setIsLeader(?bool $isLeader): self
	{
		$this->isLeader = $isLeader;
		return $this;
	}

	public function setGroupId(?string $groupId): self
	{
		$this->groupId = $groupId;
		return $this;
	}

	public function setSubGroupId(?string $subGroupId): self
	{
		$this->subGroupId = $subGroupId;
		return $this;
	}

	public function setSubGrouplabel(?string $subGrouplabel): self
	{
		$this->subGrouplabel = $subGrouplabel;
		return $this;
	}
}