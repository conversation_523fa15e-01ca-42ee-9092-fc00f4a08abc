<?php

namespace App\Model;
use Symfony\Component\Serializer\Attribute\SerializedName;

class DealerV2Model
{
    #[SerializedName('site_geo')]
    private ?string $siteGeo = "";
    private ?string $id;
    private ?string $rrdi = "";
    private ?string $name = "";
    #[SerializedName('phones')]
    private PhoneV2Model $phone;
    #[SerializedName('emails')]
    private EmailV2Model $email;
    private AddressV2Model $address;
    private CoordinateV2Model $coordinates;
    private ?float $distance = 0;
    #[SerializedName('is_agent')]
    private ?bool $isAgent = NULL;
    #[SerializedName('is_agent_a_p')]
    private ?bool $isAgentAp = NULL;
    #[SerializedName('is_secondary')]
    private ?bool $isSecondary = NULL;
    #[SerializedName('is_succursale')]
    private ?bool $isSuccursale = NULL;
    private ?array $business;
    #[SerializedName('open_hours')]
    private ?array $openHours;
    private PrincipalModel $principal;
    #[SerializedName('url_pages')]
    private UrlPageV2Model $urlPages;
    #[SerializedName('websites')]
    private WebSiteV2Model $webSites;
    private ?string $culture = NULL;
    private ?DataModel $data;
    #[SerializedName('is_carac_rdvi')]
    private ?bool $isCaracRdvi = false;
    #[SerializedName('type_operateur')]
    private ?string $typeOperateur = "";
    #[SerializedName('url_prdv_ercs')]
    private ?string $urlPrdvErcs = "";
    private ?bool $jockey = NULL;
    private ?bool $o2x = false;

    private ?bool $preferred = true;

    public function __construct()
    {
        $this->urlPages = new UrlPageV2Model();
        $this->webSites = new WebSiteV2Model();
        $this->principal = new PrincipalModel();
    }

    public function getAddress(): AddressV2Model
    {
        return $this->address;
    }

    public function getCoordinates(): CoordinateV2Model
    {
        return $this->coordinates;
    }

    public function getCulture(): ?string
    {
        return $this->culture;
    }

    public function getEmail(): EmailV2Model
    {
        return $this->email;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function getPhone(): PhoneV2Model
    {
        return $this->phone;
    }

    public function getRrdi(): ?string
    {
        return $this->rrdi;
    }

    public function getSiteGeo(): ?string
    {
        return $this->siteGeo;
    }

    public function getUrlPages(): UrlPageV2Model
    {
        return $this->urlPages;
    }

    public function getWebSites(): WebSiteV2Model
    {
        return $this->webSites;
    }

    public function setAddress(AddressV2Model $address): self
    {
        $this->address = $address;

        return $this;
    }

    public function setCoordinates(CoordinateV2Model $coordinates): self
    {
        $this->coordinates = $coordinates;

        return $this;
    }

    public function setCulture(?string $culture): self
    {
        $this->culture = $culture;

        return $this;
    }

    public function setEmail(EmailV2Model $email): self
    {
        $this->email = $email;

        return $this;
    }

    public function setName(?string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function setPhone(PhoneV2Model $phone): self
    {
        $this->phone = $phone;

        return $this;
    }

    public function setRrdi(?string $rrdi): self
    {
        $this->rrdi = $rrdi;

        return $this;
    }

    public function setSiteGeo(?string $siteGeo): self
    {
        $this->siteGeo = $siteGeo;

        return $this;
    }

    public function setUrlPages(UrlPageV2Model $urlPages): self
    {
        $this->urlPages = $urlPages;

        return $this;
    }

    public function setWebSites(WebSiteV2Model $webSites): self
    {
        $this->webSites = $webSites;

        return $this;
    }

    public function getDistance(): ?float
    {
        return $this->distance;
    }

    public function setDistance(?float $distance): self
    {
        $this->distance = $distance;

        return $this;
    }

    public function getIsAgent(): ?bool
    {
        return $this->isAgent;
    }

    public function setIsAgent(?bool $isAgent): self
    {
        $this->isAgent = $isAgent;

        return $this;
    }

    public function getIsAgentAp(): ?bool
    {
        return $this->isAgentAp;
    }

    public function setIsAgentAp(?bool $isAgentAp): self
    {
        $this->isAgentAp = $isAgentAp;

        return $this;
    }

    public function getIsSecondary(): ?bool
    {
        return $this->isSecondary;
    }

    public function setIsSecondary(?bool $isSecondary): self
    {
        $this->isSecondary = $isSecondary;

        return $this;
    }

    public function getIsSuccursale(): ?bool
    {
        return $this->isSuccursale;
    }

    public function setIsSuccursale(?bool $isSuccursale): self
    {
        $this->isSuccursale = $isSuccursale;

        return $this;
    }
    /**
     * @return array<BusinessModel>
     */
    public function getBusiness(): ?array
    {
        return $this->business;
    }

    public function setBusiness(?array $business): self
    {
        $this->business = $business;

        return $this;
    }
    /**
     * @return array<OpenHourV2Model>
     */
    public function getOpenHours(): ?array
    {
        return $this->openHours;
    }

    public function setOpenHours(?array $openHours): self
    {
        $this->openHours = $openHours;

        return $this;
    }

    public function getPrincipal(): PrincipalModel
    {
        return $this->principal;
    }

    public function setPrincipal(PrincipalModel $principal): self
    {
        $this->principal = $principal;

        return $this;
    }

    public function getIsCaracRdvi(): ?bool
    {
        return $this->isCaracRdvi;
    }

    public function setIsCaracRdvi(?bool $isCaracRdvi): self
    {
        $this->isCaracRdvi = $isCaracRdvi;

        return $this;
    }

    public function getTypeOperateur(): ?string
    {
        return $this->typeOperateur;
    }
 
    public function setTypeOperateur(?string $typeOperateur): self
    {
        $this->typeOperateur = $typeOperateur;

        return $this;
    }
 
    public function getUrlPrdvErcs(): ?string
    {
        return $this->urlPrdvErcs;
    }

    public function setUrlPrdvErcs(?string $urlPrdvErcs): self
    {
        $this->urlPrdvErcs = $urlPrdvErcs;

        return $this;
    }
 
    public function getJockey(): ?bool
    {
        return $this->jockey;
    }

    public function setJockey(?bool $jockey): self
    {
        $this->jockey = $jockey;

        return $this;
    }

    public function getO2x(): ?bool
    {
        return $this->o2x;
    }

    public function setO2x(?bool $o2x): self
    {
        $this->o2x = $o2x;

        return $this;
    }

    public function getData(): ?DataModel
    {
        return $this->data;
    }
 
    public function setData(?DataModel $data): self
    {
        $this->data = $data;

        return $this;
    }
 
    public function getId(): ?string
    {
        return $this->id;
    }
 
    public function setId(?string $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getPreferred(): ?bool
    {
        return $this->preferred;
    }

    public function setPreferred(?bool $preferred): self
    {
        $this->preferred = $preferred;

        return $this;
    }
}