<?php

namespace App\Model;
use Symfony\Component\Serializer\Attribute\SerializedName;

class AdrLivVnListModel
{
    #[SerializedName('ActorAdrCode')]
	private ?string $actorAdrCode;
    #[SerializedName('City')]
	private ?string $city;
    #[SerializedName('CommercialName')]
	private ?string $commercialName;
    #[SerializedName('Line1')]
	private ?string $line1;
    #[SerializedName('Line2')]
	private ?string $line2;
    #[SerializedName('isDefault')]
	private ?bool $isDefault;
	#[SerializedName('Type')]
	private ?String $type;

	public function getActorAdrCode(): ?string
	{
		return $this->actorAdrCode;
	}

	public function getCity(): ?string
	{
		return $this->city;
	}

	public function getCommercialName(): ?string
	{
		return $this->commercialName;
	}

	public function getLine1(): ?string
	{
		return $this->line1;
	}

	public function getLine2(): ?string
	{
		return $this->line2;
	}

	public function getIsDefault(): ?bool
	{
		return $this->isDefault;
	}

	public function getType(): ?string
	{
		return $this->type;
	}

	public function setType(?string $type): self
	{
		$this->type = $type;
		return $this;
	}

	public function setActorAdrCode(?string $actorAdrCode): self
	{
		$this->actorAdrCode = $actorAdrCode;
		return $this;
	}

	public function setCity(?string $city): self
	{
		$this->city = $city;
		return $this;
	}

	public function setCommercialName(?string $commercialName): self
	{
		$this->commercialName = $commercialName;
		return $this;
	}

	public function setLine1(?string $line1): self
	{
		$this->line1 = $line1;
		return $this;
	}

	public function setLine2(?string $line2): self
	{
		$this->line2 = $line2;
		return $this;
	}

	public function setIsDefault(?bool $isDefault): self
	{
		$this->isDefault = $isDefault;
		return $this;
	}
}
