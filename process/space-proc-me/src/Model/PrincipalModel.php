<?php

namespace App\Model;

use Symfony\Component\Serializer\Attribute\SerializedName;

class PrincipalModel {
    
    #[SerializedName('IsPrincipalAG')]
    private ?bool $isPrincipalAg = NULL;
    #[SerializedName('IsPrincipalPR')]
    private ?bool $isPrincipalPr = NULL;
    #[SerializedName('IsPrincipalRA')]
    private ?bool $isPrincipalRa = NULL;
    #[SerializedName('IsPrincipalVN')]
    private ?bool $isPrincipalVn = NULL;
    #[SerializedName('IsPrincipalVO')]
    private ?bool $isPrincipalVo = NULL;

    public function getIsPrincipalAg(): ?bool
    {
        return $this->isPrincipalAg;
    }

    public function setIsPrincipalAg(?bool $isPrincipalAg): self
    {
        $this->isPrincipalAg = $isPrincipalAg;

        return $this;
    }

    public function getIsPrincipalPr(): ?bool
    {
        return $this->isPrincipalPr;
    }

    public function setIsPrincipalPr(?bool $isPrincipalPr): self
    {
        $this->isPrincipalPr = $isPrincipalPr;

        return $this;
    }

    public function getIsPrincipalRa(): ?bool
    {
        return $this->isPrincipalRa;
    }
 
    public function setIsPrincipalRa(?bool $isPrincipalRa): self
    {
        $this->isPrincipalRa = $isPrincipalRa;

        return $this;
    }

    public function getIsPrincipalVn(): ?bool
    {
        return $this->isPrincipalVn;
    }

    public function setIsPrincipalVn(?bool $isPrincipalVn): self
    {
        $this->isPrincipalVn = $isPrincipalVn;

        return $this;
    }

    public function getIsPrincipalVo(): ?bool
    {
        return $this->isPrincipalVo;
    }

    public function setIsPrincipalVo(?bool $isPrincipalVo): self
    {
        $this->isPrincipalVo = $isPrincipalVo;

        return $this;
    }
}