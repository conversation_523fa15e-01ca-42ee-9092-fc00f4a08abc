<?php

namespace App\Model;

use Symfony\Component\Serializer\Attribute\SerializedName;

class OpenHourV2Model
{
    #[SerializedName('Label')]
    private ?string $label = "";
    #[SerializedName('Type')]
    private ?string $type = "";

    public function getLabel(): ?string
    {
        return $this->label;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setLabel(?string $label): self
    {
        $this->label = $label;

        return $this;
    }

    public function setType(?string $type): self
    {
        $this->type = $type;

        return $this;
    }
}