<?php

namespace App\Model;

use Symfony\Component\Validator\Constraints as Assert;

class ConsentArbitrationModel
{
    public function __construct(
        #[Assert\NotBlank(message: 'User ID must not be blank.', groups: ['consentArbitration'])]
        private string $userId,

        #[Assert\Length(exactly: 17, exactMessage: 'VIN must be exactly 17 characters.', groups: ['consentArbitration'])]
        #[Assert\NotBlank(message: 'VIN must not be blank.', groups: ['consentArbitration'])]
        private string $vin,

        private ?bool $consent,

        private ?string $brand,
        
        #[Assert\Length(exactly: 2, exactMessage: 'Country must be exactly 2 characters.', groups: ['consentArbitration'])]
        private string $country,
        
        #[Assert\Choice(
            choices: ['WEB', 'APP', "SPACEWEB"],
            message: 'Source must be either WEB, SPACEWEB or APP.',
            groups: ['consentArbitration']
        )]
        #[Assert\AtLeastOneOf(
            constraints: [
                new Assert\Blank(),
                new Assert\Choice(choices: ['WEB', 'APP', "SPACEWEB"])
            ],
            groups: ['consentArbitration'],
            message: 'Source field should validate one of the following conditions:'
        )]
        private ?string $source,

        private ?string $warrantyDocumentLink
    ) {}

    public function getUserId(): string
    {
        return $this->userId;
    }

    public function setUserId(string $userId): void
    {
        $this->userId = $userId;
    }

    public function getVin(): string
    {
        return $this->vin;
    }

    public function setVin(string $vin): void
    {
        $this->vin = $vin;
    }

    public function getConsent(): ?bool
    {
        return $this->consent;
    }

    public function setConsent(?bool $consent): void
    {
        $this->consent = $consent;
    }

    public function getBrand(): ?string
    {
        return $this->brand;
    }

    public function setBrand(?string $brand): void
    {
        $this->brand = $brand;
    }

    public function getCountry(): string
    {
        return $this->country;
    }

    public function setCountry(string $country): void
    {
        $this->country = $country;
    }

    public function getSource(): ?string
    {
        return $this->source;
    }

    public function setSource(?string $source): void
    {
        $this->source = $source;
    }

    public function getWarrantyDocumentLink(): ?string
    {
        return $this->warrantyDocumentLink;
    }

    public function setWarrantyDocumentLink(?string $warrantyDocumentLink): void
    {
        $this->warrantyDocumentLink = $warrantyDocumentLink;
    }
}