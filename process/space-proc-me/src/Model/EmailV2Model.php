<?php

namespace App\Model;
use Symfony\Component\Serializer\Attribute\SerializedName;

class EmailV2Model
{
    #[SerializedName('Email')]
    private ?string $email = "";
    #[SerializedName('EmailAPV')]
    private ?string $emailApv = "";
    #[SerializedName('EmailAgent')]
    private ?string $emailAgent = "";
    #[SerializedName('EmailGER')]
    private ?string $emailGer = "";
    #[SerializedName('EmailGRC')]
    private ?string $emailGrc = "";
    #[SerializedName('EmailPR')]
    private ?string $emailPr = "";
    #[SerializedName('EmailSales')]
    private ?string $emailSales = "";
    #[SerializedName('EmailVO')]
    private ?string $emailVo = "";

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function getEmailAgent(): ?string
    {
        return $this->emailAgent;
    }

    public function getEmailGer(): ?string
    {
        return $this->emailGer;
    }

    public function getEmailGrc(): ?string
    {
        return $this->emailGrc;
    }

    public function getEmailPr(): ?string
    {
        return $this->emailPr;
    }

    public function getEmailSales(): ?string
    {
        return $this->emailSales;
    }

    public function getEmailVo(): ?string
    {
        return $this->emailVo;
    }

    public function setEmail(?string $email): self
    {
        $this->email = $email;

        return $this;
    }

    public function setEmailAgent(?string $emailAgent): self
    {
        $this->emailAgent = $emailAgent;

        return $this;
    }

    public function setEmailGer(?string $emailGer): self
    {
        $this->emailGer = $emailGer;

        return $this;
    }

    public function setEmailGrc(?string $emailGrc): self
    {
        $this->emailGrc = $emailGrc;

        return $this;
    }

    public function setEmailPr(?string $emailPr): self
    {
        $this->emailPr = $emailPr;

        return $this;
    }

    public function setEmailSales(?string $emailSales): self
    {
        $this->emailSales = $emailSales;

        return $this;
    }

    public function setEmailVo(?string $emailVo): self
    {
        $this->emailVo = $emailVo;

        return $this;
    }

    public function getEmailApv(): ?string
    {
        return $this->emailApv;
    }

    public function setEmailApv(?string $emailApv)
    {
        $this->emailApv = $emailApv;

        return $this;
    }
}