<?php

namespace App\Model;

use Symfony\Component\Serializer\Attribute\SerializedName;

class CodeRegionModel
{
	#[SerializedName('CodeRegionAG')]
	private ?string $codeRegionAg = "";
	#[SerializedName('CodeRegionPR')]
	private ?string $codeRegionPr = "";
	#[SerializedName('CodeRegionRA')]
	private ?string $codeRegionRa = "";
	#[SerializedName('CodeRegionVN')]
	private ?string $codeRegionVn = "";
	#[SerializedName('CodeRegionVO')]
	private ?string $codeRegionVo = "";

	public function getCodeRegionAg(): ?string
	{
		return $this->codeRegionAg;
	}

	public function getCodeRegionPr(): ?string
	{
		return $this->codeRegionPr;
	}

	public function getCodeRegionRa(): ?string
	{
		return $this->codeRegionRa;
	}

	public function getCodeRegionVn(): ?string
	{
		return $this->codeRegionVn;
	}

	public function getCodeRegionVo(): ?string
	{
		return $this->codeRegionVo;
	}

	public function setCodeRegionAg(?string $codeRegionAg): self
	{
		$this->codeRegionAg = $codeRegionAg;
		return $this;
	}

	public function setCodeRegionPr(?string $codeRegionPr): self
	{
		$this->codeRegionPr = $codeRegionPr;
		return $this;
	}

	public function setCodeRegionRa(?string $codeRegionRa): self
	{
		$this->codeRegionRa = $codeRegionRa;
		return $this;
	}

	public function setCodeRegionVn(?string $codeRegionVn): self
	{
		$this->codeRegionVn = $codeRegionVn;
		return $this;
	}

	public function setCodeRegionVo(?string $codeRegionVo): self
	{
		$this->codeRegionVo = $codeRegionVo;
		return $this;
	}
}