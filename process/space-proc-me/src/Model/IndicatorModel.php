<?php

namespace App\Model;

use Symfony\Component\Serializer\Attribute\SerializedName;

class IndicatorModel
{
    #[SerializedName('Code')]
	private ?string $Code = "";
	private ?string $label = "";

	public function getCode(): ?string
	{
		return $this->Code;
	}

	public function getLabel(): ?string
	{
		return $this->label;
	}

	public function setCode(?string $Code): self
	{
		$this->Code = $Code;
		return $this;
	}

	public function setLabel(?string $label): self
	{
		$this->label = $label;
		return $this;
	}
}