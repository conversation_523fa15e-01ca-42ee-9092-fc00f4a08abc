<?php

namespace App\Model;
use Symfony\Component\Serializer\Attribute\SerializedName;

class DataModel
{
    #[SerializedName('BenefitList')]
	private ?array $benefitList = [];
    #[SerializedName('CodesActors')]
	private ?CodeActorModel $codesActors;
    #[SerializedName('CodesRegions')]
	private ?CodeRegionModel $codesRegions;
    #[SerializedName('FaxNumber')]
	private ?string $faxNumber = "";
    #[SerializedName('Group')]
	private ?GroupModel $group;
    #[SerializedName('Indicator')]
	private ?IndicatorModel $indicator;
    #[SerializedName('WelcomeMessage')]
	private ?string $welcomeMessage = "";
    #[SerializedName('Importer')]
	private ?ImporterModel $importer;
    #[SerializedName('PDVImporter')]
	private ?PdvImporterModel $pdvImporter;
    #[SerializedName('NumSiret')]
	private ?string $numSiret = "";
    #[SerializedName('LegalStatus')]
	private ?string $legalStatus = "";
    #[SerializedName('Capital')]
	private ?string $capital = "";
    #[SerializedName('CommercialRegister')]
	private ?string $commercialRegister = "";
    #[SerializedName('IntracommunityTVA')]
	private ?string $intracommunityTva = "";
    #[SerializedName('Brand')]
	private ?string $brand = "";
    #[SerializedName('ParentSiteGeo')]
	private ?string $parentSiteGeo = "";
    #[SerializedName('RaisonSocial')]
	private ?string $raisonSocial = "";
    #[SerializedName('RCSNumber')]
	private ?string $rcsNumber = "";
    #[SerializedName('GmCodeList')]
	private ?array $gmCodeList = [];
    #[SerializedName('LienVoList')]
	private ?array $lienVoList = [];
    #[SerializedName('bqCaptive')]
	private ?string $bqCaptive = "";
    #[SerializedName('carac_rdvi')]
	private ?string $caracRdvi = "";
    #[SerializedName('FtcCodeList')]
	private ?array $ftcCodeList = [];
	/** @var AdrLivVnListModel[]|null */
    #[SerializedName('AdrLivVNList')]
	private ?array $adrLivVnList;
    #[SerializedName('ContratVl')]
	private ?ContratVlModel $contratVl;

	public function __construct()
    {
        $this->codesActors = new CodeActorModel();
        $this->codesRegions = new CodeRegionModel();
        $this->group = new GroupModel();
        $this->indicator = new IndicatorModel();
        $this->importer = new ImporterModel();
        $this->pdvImporter = new PdvImporterModel();
        $this->contratVl = new ContratVlModel();
		
    }

	public function getBenefitList(): ?array
	{
		return $this->benefitList;
	}

	public function getCodesActors(): ?CodeActorModel
	{
		return $this->codesActors;
	}

	public function getCodesRegions(): ?CodeRegionModel
	{
		return $this->codesRegions;
	}

	public function getFaxNumber(): ?string
	{
		return $this->faxNumber;
	}

	public function getGroup(): ?GroupModel
	{
		return $this->group;
	}

	public function getIndicator(): ?IndicatorModel
	{
		return $this->indicator;
	}

	public function getWelcomeMessage(): ?string
	{
		return $this->welcomeMessage;
	}

	public function getImporter(): ?ImporterModel
	{
		return $this->importer;
	}

	public function getPdvImporter(): ?PdvImporterModel
	{
		return $this->pdvImporter;
	}

	public function getNumSiret(): ?string
	{
		return $this->numSiret;
	}

	public function getLegalStatus(): ?string
	{
		return $this->legalStatus;
	}

	public function getCapital(): ?string
	{
		return $this->capital;
	}

	public function getCommercialRegister(): ?string
	{
		return $this->commercialRegister;
	}

	public function getIntracommunityTva(): ?string
	{
		return $this->intracommunityTva;
	}

	public function getBrand(): ?string
	{
		return $this->brand;
	}

	public function getParentSiteGeo(): ?string
	{
		return $this->parentSiteGeo;
	}

	public function getRaisonSocial(): ?string
	{
		return $this->raisonSocial;
	}

	public function getRcsNumber(): ?string
	{
		return $this->rcsNumber;
	}

	public function getGmCodeList(): ?array
	{
		return $this->gmCodeList;
	}

	public function getLienVoList(): ?array
	{
		return $this->lienVoList;
	}

	public function getBqCaptive(): ?string
	{
		return $this->bqCaptive;
	}

	public function getCaracRdvi(): ?string
	{
		return $this->caracRdvi;
	}

	public function getFtcCodeList(): ?array
	{
		return $this->ftcCodeList;
	}

	/**
	 * @return AdrLivVnListModel[]|null
	 */
	public function getAdrLivVnList(): ?array
	{
		return $this->adrLivVnList;
	}

	public function getContratVl(): ?ContratVlModel
	{
		return $this->contratVl;
	}

	public function setBenefitList(?array $benefitList): self
	{
		$this->benefitList = $benefitList;
		return $this;
	}

	public function setCodesActors(?CodeActorModel $codesActors): self
	{
		$this->codesActors = $codesActors;
		return $this;
	}

	public function setCodesRegions(?CodeRegionModel $codesRegions): self
	{
		$this->codesRegions = $codesRegions;
		return $this;
	}

	public function setFaxNumber(?string $faxNumber): self
	{
		$this->faxNumber = $faxNumber;
		return $this;
	}

	public function setGroup(?GroupModel $group): self
	{
		$this->group = $group;
		return $this;
	}

	public function setIndicator(?IndicatorModel $indicator): self
	{
		$this->indicator = $indicator;
		return $this;
	}

	public function setWelcomeMessage(?string $welcomeMessage): self
	{
		$this->welcomeMessage = $welcomeMessage;
		return $this;
	}

	public function setImporter(?ImporterModel $importer): self
	{
		$this->importer = $importer;
		return $this;
	}

	public function setPdvImporter(?PdvImporterModel $pdvImporter): self
	{
		$this->pdvImporter = $pdvImporter;
		return $this;
	}

	public function setNumSiret(?string $numSiret): self
	{
		$this->numSiret = $numSiret;
		return $this;
	}

	public function setLegalStatus(?string $legalStatus): self
	{
		$this->legalStatus = $legalStatus;
		return $this;
	}

	public function setCapital(?string $capital): self
	{
		$this->capital = $capital;
		return $this;
	}

	public function setCommercialRegister(?string $commercialRegister): self
	{
		$this->commercialRegister = $commercialRegister;
		return $this;
	}

	public function setIntracommunityTva(?string $intracommunityTva): self
	{
		$this->intracommunityTva = $intracommunityTva;
		return $this;
	}

	public function setBrand(?string $brand): self
	{
		$this->brand = $brand;
		return $this;
	}

	public function setParentSiteGeo(?string $parentSiteGeo): self
	{
		$this->parentSiteGeo = $parentSiteGeo;
		return $this;
	}

	public function setRaisonSocial(?string $raisonSocial): self
	{
		$this->raisonSocial = $raisonSocial;
		return $this;
	}

	public function setRcsNumber(?string $rcsNumber): self
	{
		$this->rcsNumber = $rcsNumber;
		return $this;
	}

	public function setGmCodeList(?array $gmCodeList): self
	{
		$this->gmCodeList = $gmCodeList;
		return $this;
	}

	public function setLienVoList(?array $lienVoList): self
	{
		$this->lienVoList = $lienVoList;
		return $this;
	}

	public function setBqCaptive(?string $bqCaptive): self
	{
		$this->bqCaptive = $bqCaptive;
		return $this;
	}

	public function setCaracRdvi(?string $caracRdvi): self
	{
		$this->caracRdvi = $caracRdvi;
		return $this;
	}

	public function setFtcCodeList(?array $ftcCodeList): self
	{
		$this->ftcCodeList = $ftcCodeList;
		return $this;
	}

	/**
	 * @param AdrLivVnListModel[]|null $adrLivVnList
	 */
	public function setAdrLivVnList(?array $adrLivVnList): self
	{
		$this->adrLivVnList = $adrLivVnList;
		return $this;
	}

	public function setContratVl(?ContratVlModel $contratVl): self
	{
		$this->contratVl = $contratVl;
		return $this;
	}
}