<?php

namespace App\Manager;

use App\Trait\LoggerTrait;
use App\Service\UserService;
use Symfony\Component\HttpFoundation\Response;

class UserManager
{
    use LoggerTrait;
    public function __construct(
        private UserService $userService
    ) {
    }

    public function getUserByUserId(string $userId)
    {
        $this->logger->info('Getting user', ['userId' => $userId]);
        $userResponse = $this->userService->getUserByUserId($userId);
        $documents = [];
        if(Response::HTTP_OK == $userResponse->getCode()) {
            $user = json_decode($userResponse->getData(),true);
            $documents = $user['documents']? $user['documents'][0]: [];
        }
        return $documents?? [];
    }

    public function updateUserByUserDbId(string $userDbId, ?array $data)
    {
        $this->logger->info('Updating user', ['userDbId' => $userDbId]);
        $update = [];
        foreach ($data as $field => $value) {
            if($field == "civility" && $value !== null && $value !== '') {
                $update["profile.title"] = $value;
                continue;
            }
            if($value !== null && $value !== '') {
                $update["profile.$field"] = $value;
            }
        }

        $userResponse = $this->userService->updateUserByUserDbId($userDbId, $update);
        return $userResponse?? [];
    }

    public function createUser(?array $data)
    {
        $this->logger->info('Creating user', ['data' => $data]);
        $userData = [
            'userDbId' => $data['customerId'] ?? '',
            'userId' => $data['idp_Gigya_id'] ?? '',
            'userPsaId' => null,
            'profile' => [
                'title' => $data['civility'] ?? '',
                'email' => $data['email'] ?? '',
                'firstName' => $data['firstName'] ?? '',
                'lastName' => $data['lastName'] ?? '',
                'phone' => $data['phoneNumber'] ?? '',
                'address1' => $data['geopoint'] ?? '',
                'address2' => $data['street'] ?? '',
                'city' => $data['town'] ?? '',
                'zip' => $data['postCode'] ?? '',
                'country' => $data['countryCode'] ?? '',
            ]
        ];
        return $this->userService->createUser($userData);
    }
}