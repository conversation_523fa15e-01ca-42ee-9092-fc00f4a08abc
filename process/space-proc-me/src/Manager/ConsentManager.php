<?php

namespace App\Manager;

use App\Helper\ErrorResponse;
use App\Helper\ResponseArrayFormat;
use App\Helper\SuccessResponse;
use App\Service\ConsentService;
use Symfony\Component\HttpFoundation\Response;

/**
 * User Manager.
 */
class ConsentManager
{

    public function __construct(private ConsentService $consentService)
    {
        $this->consentService = $consentService;
    }

    /**
     * Get Accounts Info
     */
    public function getConsentsInfo(string $userId = null): ResponseArrayFormat
    {
        $response = $this->consentService->getConsentsInfo($userId);
        $responseCode = $response->getCode() ?? '';
        if (Response::HTTP_OK == $responseCode) {
            $responseData = $response->getData()['success'];
            return new SuccessResponse($responseData);
        }

        $errorData = $response->getData();
        return $this->handleErrorResponse($errorData->getData(), $errorData->getCode());
    }

    /**
     * Set Accounts Info
     */
    public function putConsentsInfo(string $userId, array $payload): ResponseArrayFormat
    {
        $response = $this->consentService->putConsentsInfo($userId, $payload);
        $responseCode = $response->getCode() ?? '';
        
        if (Response::HTTP_OK == $responseCode) {
            $responseData = $response->getData()['success'];
            return new SuccessResponse($responseData);
        }

        $errorData = $response->getData();
        return $this->handleErrorResponse($errorData->getData(), $errorData->getCode());
    }

    public function handleErrorResponse(array $responseErrorData, string $responseCode): ErrorResponse 
    {
        $errorDetails = $responseErrorData['error']['errors'] ?? [];
        $error = new ErrorResponse($responseErrorData['error']['message'] ?? 'unauthorized', $responseCode);
        $error->setErrors($errorDetails);
        return $error;
    }
}