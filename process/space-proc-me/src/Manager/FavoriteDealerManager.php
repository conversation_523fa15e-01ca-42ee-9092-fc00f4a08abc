<?php

namespace App\Manager;

use App\Helper\BrandHelper;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\Serializer;
use Symfony\Component\Serializer\Normalizer\ObjectNormalizer;

use App\Helper\ErrorResponse;
use App\Helper\ResponseArrayFormat;
use App\Helper\MarketHelper;
use App\Helper\SuccessResponse;
use App\Service\FavoriteDealerService;
use App\Trait\LoggerTrait;
use App\Service\MongoAtlasQueryService;
use App\Transformer\XpResponseTransformer;
use App\Transformer\XfEmeaResponseTransformer;
use App\Transformer\xfEmeaParameterRequestTransformer;

class FavoriteDealerManager
{
    use LoggerTrait;

    const COLLECTION = 'userData';

    private const COLLECTION_SETTINGS = 'settings';
    private Serializer $serializer;


    public function __construct(
        private ValidatorInterface $validator,
        private MongoAtlasQueryService $mongoAtlasQueryService,
        private FavoriteDealerService $favoriteDealerService,
        private XpResponseTransformer $xpResponseTransformer,
        private MongoAtlasQueryService $mongoService
    ) {
        $this->serializer = new Serializer([new ObjectNormalizer()]);
    }

    public function saveFavoriteDealer(array $params): ResponseArrayFormat
    {
        $brand = $params['brand'];
        if (BrandHelper::isXP($brand)) {
            return $this->saveFavoriteDealerXP($params);
        }
        return $this->saveFavoriteDealerXF($params);
    }

    private function saveFavoriteDealerXP(array $params): ResponseArrayFormat
    {
        $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Start saving XP favorite dealer in database', [
            'params' => $params,
        ]);
        $brand = strtolower($params['brand']);
        $brandParameters = [
            'country' => $params['country'],
            'idSiteGeo' => $params['siteGeo'],
            'language' => $params['language']
        ];
        $update = [
            'preferredDealer.' . $brand => $brandParameters
        ];
        $filter = [
            'userId' => $params['userId']
        ];

        $response = $this->mongoAtlasQueryService->updateOne(self::COLLECTION, $filter, $update);
        $responseContent = json_decode($response->getData(), true);
        $matchedCount = (int) ($responseContent['matchedCount'] ?? null);
        $modifiedCount = (int) ($responseContent['modifiedCount'] ?? null);
        if (Response::HTTP_OK == $response->getCode()) {
            if ($matchedCount && $modifiedCount) {
                $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' XP Favorite dealer is successfully added', [
                    'response' => $response->getData(),
                ]);
                return new SuccessResponse("Favourite dealer successfully added!", Response::HTTP_OK);
            } else {
                $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Cannot save XP Favorite dealer ', [
                    'response' => $response->getData(),
                ]);
                return new ErrorResponse("Cannot save favorite dealer in database", Response::HTTP_BAD_REQUEST);
            }
        }

        return new ErrorResponse($response->getData(), $response->getCode());
    }

    private function saveFavoriteDealerXF(array $params): ResponseArrayFormat
    {
        $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Start checking if vin exists for XF favorite dealer in database', [
            'params' => $params,
        ]);
        $siteGeo = $params['siteGeo'];
        $userId = $params['userId'];
        $vin = $params['vin'];
        $filterVinExists = [
            'userId' => $userId,
            'vehicle.vin' => $vin
        ];
        if (!$this->checkIfVinExistsInGarage($filterVinExists)) {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Faild to find the vin for XF favorite dealer in database', [
                'params' => $params,
            ]);
            return new ErrorResponse("The VIN is not part of the user's garage", Response::HTTP_FORBIDDEN);
        }
        $brandParameters = [
            'country' => $params['country'],
            'idSiteGeo' => $siteGeo
        ];
        $update = [
            'vehicle.$.preferredDealer' => $brandParameters
        ];
        $filter = [
            'userId' => $userId,
            'vehicle.vin' => $vin
        ];

        $response = $this->mongoAtlasQueryService->updateOne(self::COLLECTION, $filter, $update, true);
        $responseContent = json_decode($response->getData(), true);
        $matchedCount = (int) ($responseContent['matchedCount'] ?? null);
        $modifiedCount = (int) ($responseContent['modifiedCount'] ?? null);
        if (Response::HTTP_OK == $response->getCode()) {
            if ($matchedCount && $modifiedCount) {
                $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' XF Favorite dealer is successfully added', [
                    'response' => $response->getData(),
                ]);
                return new SuccessResponse("Favourite dealer successfully added!", Response::HTTP_OK);
            } else {
                $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Cannot save XF Favorite dealer ', [
                    'response' => $response->getData(),
                ]);
                return new ErrorResponse("Cannot save favorite dealer in database", Response::HTTP_BAD_REQUEST);
            }
        }

        return new ErrorResponse($response->getData(), $response->getCode());
    }

    public function deleteFavoriteDealer(array $params)
    {
        $brand = $params['brand'];
        if (BrandHelper::isXP($brand)) {
            return $this->deleteFavoriteDealerXP($params);
        } elseif (BrandHelper::isXF($brand)) {
            return $this->deleteFavoriteDealerXF($params);
        }
    }

    private function deleteFavoriteDealerXP(array $params): ResponseArrayFormat
    {
        $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Deleteing XP favorite dealer from database', [
            'params' => $params,
        ]);
        $brand = strtolower($params['brand']);
        $update = [
            'preferredDealer.' . $brand => null
        ];
        $filter = [
            'userId' => $params['userId']
        ];

        $response = $this->mongoAtlasQueryService->updateOne(self::COLLECTION, $filter, $update);
        $responseContent = json_decode($response->getData(), true);
        $matchedCount = (int) ($responseContent['matchedCount'] ?? null);
        $modifiedCount = (int) ($responseContent['modifiedCount'] ?? null);
        if (Response::HTTP_OK == $response->getCode()) {
            if ($matchedCount && $modifiedCount) {
                $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' XP Favorite dealer is successfully deleted', [
                    'response' => $response->getData(),
                ]);
                return new SuccessResponse("successfully deleted!", Response::HTTP_OK);
            } else {
                $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Cannot delete XP Favorite dealer ', [
                    'response' => $response->getData(),
                ]);
                return new ErrorResponse("Cannot delete favorite dealer from database", Response::HTTP_BAD_REQUEST);
            }
        }

        return new ErrorResponse($response->getData(), $response->getCode());
    }

    private function deleteFavoriteDealerXF(array $params): ResponseArrayFormat
    {
        $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Start checking if vin exists for XF favorite dealer in database', [
            'params' => $params,
        ]);
        $userId = $params['userId'];
        $vin = $params['vin'];
        $filterVinExists = [
            'userId' => $userId,
            'vehicle.vin' => $vin
        ];
        if (!$this->checkIfVinExistsInGarage($filterVinExists)) {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Faild to find the vin for XF favorite dealer in database', [
                'params' => $params,
            ]);
            return new ErrorResponse("The VIN is not part of the user's garage", Response::HTTP_FORBIDDEN);
        }

        $update = [
            'vehicle.$.preferredDealer' => null
        ];
        $filter = [
            'userId' => $userId,
            'vehicle.vin' => $vin
        ];

        $response = $this->mongoAtlasQueryService->updateOne(self::COLLECTION, $filter, $update, true);
        $responseContent = json_decode($response->getData(), true);
        $matchedCount = (int) ($responseContent['matchedCount'] ?? null);
        $modifiedCount = (int) ($responseContent['modifiedCount'] ?? null);
        if (Response::HTTP_OK == $response->getCode()) {
            if ($matchedCount && $modifiedCount) {
                $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' XF Favorite dealer is successfully deleted', [
                    'response' => $response->getData(),
                ]);
                return new SuccessResponse("successfully deleted!", Response::HTTP_OK);
            } else {
                $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Cannot delete XF Favorite dealer ', [
                    'response' => $response->getData(),
                ]);
                return new ErrorResponse("Cannot delete favorite dealer from database", Response::HTTP_BAD_REQUEST);
            }
        }

        return new ErrorResponse($response->getData(), $response->getCode());
    }

    public function getFavoriteDealer(array $params): ResponseArrayFormat
    {
        $brand = $params['brand'];
        if (BrandHelper::isXP($brand)) {
            return $this->getFavoriteDealerXP($params);
        }
        return $this->getFavoriteDealerXF($params);
    }

    public function getFavoriteDealerXP(array $params): ResponseArrayFormat
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Getting XP favorite dealer from database', [
                'params' => $params,
            ]);
            $filter = [
                'userId' => $params['userId']
            ];

            $response = $this->mongoAtlasQueryService->find(self::COLLECTION, $filter);
            $responseContent = json_decode($response->getData(), true);
            $documents = $responseContent['documents'] ?? [];
            if (Response::HTTP_OK == $response->getCode()) {
                if (count($documents) > 0) {
                    $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' XP Favorite dealer is successfully fetched', [
                        'response' => $response->getData(),
                    ]);
                    $site_geo = $documents[0]['preferredDealer'][strtolower($params['brand'])] ?? null;
                    if ($site_geo === null) {
                        return new ErrorResponse("No favorite dealer defined for the user", Response::HTTP_FORBIDDEN);
                    }
                    $params['country'] = $site_geo['country'] ?? '';
                    $params['language'] = $site_geo['language'] ?? '';
                    $params['siteGeo'] = $site_geo['idSiteGeo'] ?? '';
                    $response = $this->favoriteDealerService->getXpDealerDetails($params);
                    $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Favorite dealer is successfully fetched', [
                        'response' => $response->getData(),
                    ]);
                    if (Response::HTTP_OK == $response->getCode()) {
                        $data = $response->getData()['success'] ?? [];
                        if (empty($data)) {
                            return new ErrorResponse("No favorite dealer defined for the user", Response::HTTP_FORBIDDEN);
                        }
                        $params['o2x'] = $this->getO2xSettings($params['brand'], $params['source']);
                        $response = $this->xpResponseTransformer->mapper($data, $params);
                        $dealerResponse = ["Dealer" => $response];
                        return new SuccessResponse($dealerResponse, Response::HTTP_OK);
                    }
                    $responseData = $response->getData();
                    $result = (isset($responseData['error']['message'])) ? $responseData['error']['message'] : $responseData;
                    return new ErrorResponse($result, code: $response->getCode());
                } else {
                    $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Cannot find XP Favorite dealer ', [
                        'response' => $response->getData(),
                    ]);
                    return new ErrorResponse("No favorite dealer defined for the user", Response::HTTP_FORBIDDEN);
                }
            }
            $responseData = $response->getData();
            $result = (isset($responseData['error']['message'])) ? $responseData['error']['message'] : $responseData;
            return new ErrorResponse($result, code: $response->getCode());
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error getting favorite dealer: ' . $e->getMessage(), [
                'params' => $params,
                'exception' => $e,
            ]);
            return new ErrorResponse("Error getting favorite dealer", Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function getFavoriteDealerXF(array $params): ResponseArrayFormat
    {
        try {

            $userId = $params['userId'];
            $vehicleId = $params['vin'];
            $pipeline =
                [
                    [
                        '$match' => ['userId' => $userId],
                    ],
                    [
                        '$unwind' => '$vehicle',
                    ],
                    [
                        '$match' => [
                            'vehicle.vin' => ['$eq' => $vehicleId],
                        ],
                    ],
                    [
                        '$group' => [
                            '_id' => 'vehicle',
                            'vehicle' => [
                                '$push' => '$vehicle',
                            ],
                        ],
                    ],
                ];

            $response = $this->mongoService->aggregate(self::COLLECTION, $pipeline);
            $response = json_decode($response->getData(), true);

            $data = isset($response['documents']) ? reset($response['documents']) : [];
            $vehicle = isset($data['vehicle']) ? reset($data['vehicle']) : null;

            if (empty($vehicle)) {
                $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Vehicle not found', [
                    'params' => $params,
                    'vehicleId' => $vehicleId,
                ]);
                return new ErrorResponse("No favorite dealer defined for the user", Response::HTTP_FORBIDDEN);
            }
            $this->logger->debug(__CLASS__ . '::' . __METHOD__ . ' Vehicle found', [
                'params' => $params,
                'vehicleId' => $vehicleId,
            ]);
            if (!isset($vehicle['preferredDealer'])) {
                $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' No favorite dealer defined for the user', [
                    'params' => $params,
                    'vehicleId' => $vehicleId,
                ]);
                return new ErrorResponse("No favorite dealer defined for the user", Response::HTTP_FORBIDDEN);
            }
            $country = $vehicle['preferredDealer']['country'] ?? "";
            $dealerId = $vehicle['preferredDealer']['idSiteGeo'] ?? "";
            $params['siteGeo'] = $dealerId;
            $params['country'] = $country;

            $response = $this->favoriteDealerService->getXfDealerDetails($params);
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Xf favorite dealer is successfully fetched', [
                'params' => $params,
                'response' => $response->getData(),
            ]);
            if ($response->getCode() !== 200) {
                $responseData = $response->getData();
                $result = (isset($responseData['error']['message'])) ? $responseData['error']['message'] : $responseData;
                return new ErrorResponse($result, code: $response->getCode());
            }
            $data = $response->getData()['success'] ?? [];
            if (empty($data)) {
                return new ErrorResponse('No favorite dealer defined for the user', Response::HTTP_FORBIDDEN);
            }
            $marketCode = MarketHelper::getMarket($params['country']);
            $brandCode = BrandHelper::getBrandCode($params['brand']);
            $params = xfEmeaParameterRequestTransformer::mapper(
                $params,
                $marketCode,
                $brandCode
            );
            $xfEmeaResponse = XfEmeaResponseTransformer::mapper(
                $data ?? [],
                $params
            );
            $dealerResponse = ["dealer" => $xfEmeaResponse->getSuccess()];

            return new SuccessResponse($xfEmeaResponse->getSuccess(), Response::HTTP_OK);
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error getting favorite dealer: ' . $e->getMessage(), [
                'params' => $params,
                'exception' => $e,
            ]);
            return new ErrorResponse("Error getting favorite dealer", Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    private function getO2xSettings(string $brand, $source = "APP"): array
    {
        try {
            $filter = [
                'brand' => $brand,
                'source' => $source,
                'culture' => '',
                '$or' => [
                    ["settingsData.o2x.code" => "o2x"],
                    ["settingsData.config.code" => "o2x"]
                ]
            ];

            $response = $this->mongoService->find(self::COLLECTION_SETTINGS, $filter);
            $result = json_decode($response->getData(), true);
            $settingsDatas = $result['documents'][0]['settingsData'] ?? [];
            if ('APP' == $source) {
                $data = current(array_filter($settingsDatas, function ($item) {
                    $code = $item['config']['code'] ?? '';
                    return $code == 'o2x';
                }));
                if ($data) {
                    $config = $data['config'] ?? [];
                    $result = [...$data, ...$config];
                    unset($result['config']);
                } else {
                    $result = $settingsDatas['o2x'] ?? [];
                }

                return $result ?? [];
            }
            return $settingsDatas['o2x'] ?? [];
        } catch (\Exception $e) {
            $this->logger->error(
                sprintf('%s: Error while getting o2x settings ', __METHOD__),
                [
                    'exception_code' => $e->getCode(),
                    'exception_message' => $e->getMessage(),
                ]
            );
            return [];
        }
    }

    private function checkIfVinExistsInGarage(array $filter): bool
    {
        $response = $this->mongoAtlasQueryService->find(self::COLLECTION, $filter);
        $responseContent = json_decode($response->getData(), true);
        $documents = $responseContent['documents'] ?? [];
        return count($documents) > 0 ? true : false;
    }
}
