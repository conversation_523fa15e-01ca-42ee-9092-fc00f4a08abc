<?php

namespace App\Manager;

use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Component\HttpFoundation\Response;

use App\Helper\ErrorResponse;
use App\Helper\ResponseArrayFormat;
use App\Helper\SuccessResponse;
use App\Trait\LoggerTrait;
use App\Trait\ValidationResponseTrait;
use App\Model\ConsentArbitrationModel;
use App\Service\ConsentArbitrationService;

class ConsentArbitrationManager
{
    use LoggerTrait;
    use ValidationResponseTrait;

    public function __construct(
        private ValidatorInterface $validator,
        private ConsentArbitrationService $consentArbitrationService
    ) {
    }

    public function saveConsentArbitration(ConsentArbitrationModel $consentArbitrationModel): ResponseArrayFormat
    {
        try {
            $this->logger->info('ConsentArbitrationManager::saveConsentArbitration', [
                'model' => $consentArbitrationModel,
            ]);
            $response = $this->consentArbitrationService->saveConsentArbitration($consentArbitrationModel);
            if (Response::HTTP_CREATED == $response->getCode())
            {
                $this->logger->info('ConsentArbitrationManager::saveConsentArbitration', [
                    'response' => $response->getData(),
                ]);
                return new SuccessResponse(['message' => "consent is successfully recorded"], 200);
            }
            return new ErrorResponse($response->getData(), $response->getCode());
        }
        catch (\Throwable $e) {
            $this->logger->error(__METHOD__ .' Catching Exception '.$e->getMessage(), [
                'model' => $consentArbitrationModel,
                'exception' => $e,
            ]);

            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }
}