<?php
namespace App\Manager;

use Exception;
use App\Helper\ErrorResponse;
use App\Helper\SuccessResponse;
use App\Service\ProfileService;
use App\Model\ProfileModel;
use App\Manager\UserManager;
use Symfony\Component\HttpFoundation\Response;
use App\Trait\LoggerTrait;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use App\Helper\ResponseArrayFormat;

/**
 * Manager of the user Account
 */
class ProfileManager {
    use LoggerTrait;

    /**
     * @param ValidatorInterface $validator
     * @param ProfileService $profileService
     */
    public function __construct(private ValidatorInterface $validator,
        private ProfileService $profileService,
        private UserManager $userManager)
    {}

    /**
     * Description: update profile user account
     * @param string $id
     * @param array $data
     * @return array
     */
    public function putUserData(string $id, ?array $data): ResponseArrayFormat
    {
        try {
            $this->logger->info(__CLASS__ . "::" . __METHOD__ . " Put User data");
            $userData = $this->userManager->getUserByUserId($id);
            if(empty($userData)) {
                return new ErrorResponse("customer not found", Response::HTTP_NOT_FOUND);
            }
            $toUpdate = $data;
            $toUpdate["postCode"] = $data["zipcode"];
            $toUpdate["geopoint"] = $data["address1"];
            $toUpdate["town"] = $data["city"];
            $toUpdate["countryCode"] = $data["country"];
            $toUpdate["phoneNumber"] = $data["phone"];
            $toUpdate["primaryEmail"] = $data["email"];
            $toUpdate["street"] = $data["address2"];
            $response = $this->profileService->putUserData($userData["userDbId"], $toUpdate);

            if (Response::HTTP_OK == $response->getCode()) {
                $respData = $this->mappingUserDataResponse($response->getData()['success']);
                $this->userManager->updateUserByUserDbId($userData["userDbId"], $data);
                return new SuccessResponse($respData, $response->getCode());
            }
            $this->logger->error("Error: Put Profile data for (" . json_encode($response) . ")");
            $message = $response->getData() ?? 'ErrorResponse in getting profile customer: '.$id;
            $code = $response->getCode() ?? Response::HTTP_INTERNAL_SERVER_ERROR;
            return new ErrorResponse($message, $code);
        } catch (Exception $e) {
            $this->logger->error("Error: Put Profile data for $id ".$e->getMessage());
            return new ErrorResponse($e->getMessage(), $e->getCode() ?: Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Description: get profile user account
     * @param string $id
     * @return array
     */
    public function getUserData(string $id): ResponseArrayFormat
    {
        try {
            $this->logger->info(__CLASS__ . "::" . __METHOD__ . " get User data");
            $userData = $this->userManager->getUserByUserId($id);
            if(empty($userData)) {
                return new ErrorResponse("customer not found", Response::HTTP_NOT_FOUND);
            }

            $response = $this->profileService->getUserData($userData["userDbId"]);

            if (Response::HTTP_OK == $response->getCode()) {
                $data = $this->mappingUserDataResponse($response->getData()['success']);
                #invalidate cache related to profile
                return new SuccessResponse($data, $response->getCode());
            }
            $this->logger->error("Error: get Profile data for $id (" . json_encode($response) . ")");
            $message = $response->getData() ?? 'ErrorResponse in getting profile customer: '.$id;
            $code = $response->getCode() ?? Response::HTTP_INTERNAL_SERVER_ERROR;
            return new ErrorResponse($message, $code);
        } catch (Exception $e) {
            $this->logger->error("Error: get Profile data for $id ".$e->getMessage());
            return new ErrorResponse($e->getMessage(), $e->getCode() ?: Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    private function mappingUserDataResponse(?array $data) {
        $profileData = new ProfileModel();
        $profileData->setFirstName($data["firstName"]?? "")
                ->setLastName($data["lastName"]?? "")
                ->setCivility($data["civility"]?? "")
                ->setAddress1($data["geopoint"]?? "")
                ->setCity($data["town"]?? "")
                ->setCountry($data["countryCode"]?? "")
                ->setZipcode($data["postCode"]?? "")
                ->setPhone($data["phoneNumber"]?? "")
                ->setAddress2($data["street"]?? "")
                ->setEmail($data["email"]?? $data["primaryEmail"]?? "");
        return $profileData->toArray();
    }
}