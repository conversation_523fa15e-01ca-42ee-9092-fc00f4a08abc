<?php

namespace App\Connector;

use App\Trait\LoggerTrait;
use App\Helper\WSResponse;

class SystemUserDBConnector
{
    use LoggerTrait;
    /**
     * @var CustomHttpClient
     */
    private $client;
    
    /**
     * @var string
     */
    private $url;

    /**
     * @param CustomHttpClient $client
     * @param string $url
     */
    public function __construct(CustomHttpClient $client, string $url)
    {
        $this->url = $url;
        $this->client = $client;
    }

    /**
     * @param string $method 
     * @param string $url 
     * @param ?array|array $options 
     * @return WSResponse
     */
    public function call(string $method, string $uri, ?array $options = [])
    {
        try {
            $this->logger->info("SysUserDBConnector::call " . $this->url . $uri);
            return $this->client->request($method, $this->url.$uri, $options);
        } catch (\Exception $e) {
            $this->logger->error("SysUserDBConnector::call : Exception " . $e->getMessage());
           return new WSResponse($e->getCode(), $e->getMessage());
        }
    }
}