<?php

namespace App\Connector;

use App\Helper\WSResponse;
use App\Trait\LoggerTrait;

class SysServiceAdvisorConnector
{
    use LoggerTrait;

    public function __construct(
        private CustomHttpClient $client,
        private string $url
    ) {
    }

    public function call(string $method, string $uri, ?array $options = []): WSResponse
    {
        try {
            $this->logger->info('SysServiceAdvisorConnector::call ' . $this->url . $uri);
            return $this->client->request(
                $method,
                $this->url . $uri,
                $options
            );
        } catch (\Exception $e) {
            $this->logger->error('SysServiceAdvisorConnector::call : Cached Exception' . $e->getMessage());
            return new WSResponse(
                $e->getCode(),
                $e->getMessage()
            );
        }
    }
}
