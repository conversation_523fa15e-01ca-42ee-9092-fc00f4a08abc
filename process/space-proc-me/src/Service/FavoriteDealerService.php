<?php

namespace App\Service;

use Symfony\Component\HttpFoundation\Response;

use App\Connector\SysAPDVConnector;
use App\Connector\SysServiceAdvisorConnector;
use App\Helper\BrandHelper;
use App\Helper\WSResponse;
use App\Helper\MarketHelper;
use App\Trait\LoggerTrait;

class FavoriteDealerService
{
    use LoggerTrait;

    public function __construct(
        private SysAPDVConnector $sysAPDVConnector,
        private SysServiceAdvisorConnector $sysServiceAdvisorConnector
    ) {
    }

    public function getXpDealerDetails(array $params)
    {
        $query = [
            'brand' => $params['brand'],
            'country' => $params['country'],
            'language' => $params['language'],
        ];
        if (empty($params['siteGeo'])) {
            return new WSResponse(Response::HTTP_NOT_FOUND, ['error' => 'No favorite dealer found']);
        }
        $response = $this->sysAPDVConnector->call('GET', '/v1/dealer/'.$params['siteGeo'], ['query' => $query]);
        return $response;
    }

    public function getXfDealerDetails(array $params)
    {
        $marketCode = MarketHelper::getMarket($params['country']);
        $brandCode = BrandHelper::getBrandId($params['brand']);
        $dealerIds = explode('|', $params['siteGeo']); 
        $query = [
            'brand' => $brandCode,
            'market' => $marketCode,
            'siteCode' => $dealerIds[0] ?? "",
            'sincom' => $dealerIds[1] ?? "",
        ];
        $response = $this->sysServiceAdvisorConnector->call('GET', '/v1/dealer/detail', ['query' => $query]);
        return $response;
    }
}