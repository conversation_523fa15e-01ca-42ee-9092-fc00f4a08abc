<?php

namespace App\Service;

use Exception;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\SerializerInterface;
use App\Helper\WSResponse;
use App\Trait\LoggerTrait;
use App\Model\ConsentArbitrationModel; 

/**
 * ConsentArbitration service
 */
class ConsentArbitrationService
{
    use LoggerTrait;

    public const COLLECTION = 'consentArbitration';
    public const USER_COLLECTION = 'userData';
    private ConsentArbitrationModel $consentArbitrationModel;

    public function __construct(
        private MongoAtlasQueryService $mongoService,
        private SerializerInterface $serializer
    ) {
    }

    /**
     * get user data infos from DB.
     */
    public function getUserData(string $userId): string
    {
        $userData = $this->mongoService->find(self::USER_COLLECTION, ['userId' => $userId]);
        $userData = json_decode($userData->getData(), true);
        return $userData['documents'][0]['_id'] ?? '';
    }

    public function saveConsentArbitration(ConsentArbitrationModel $consentArbitrationModel): WSResponse
    {
        // todo: commenting below as cognito introspection is not there.
        // $dbUserDataId = $this->getUserData($consentArbitrationModel->getUserId());
        // if (empty($dbUserDataId)) {
        //     return new WSResponse(Response::HTTP_BAD_REQUEST, 'User not found');
        // }
        $this->consentArbitrationModel = $consentArbitrationModel;
        $this->logger->info('ConsentArbitrationService::saveConsentArbitration, saving consent arbitration', [
            'model' => $consentArbitrationModel,
        ]);
        $fields = $this->getConsentArbitrationArray();
        $fields['createdAt'] = time();
        $fields['region'] = $this->getRegion($consentArbitrationModel->getCountry());
        return $this->mongoService->insertOne(self::COLLECTION, $fields);
    }

    public function getRegion(string $country): string
    {
        if (in_array($country, ['US', 'CA', 'MX']))
        {
            return 'NA';
        }
        return 'EMEA';
    }

    public function getConsentArbitrationArray(): array
    {
        $consentArbitration = json_decode($this->serializer->serialize($this->consentArbitrationModel, 'json'), true);
        return $consentArbitration;
    }
}