<?php

namespace App\Service;

use App\Helper\ErrorResponse;
use App\Trait\LoggerTrait;
use App\Helper\WSResponse;
use Symfony\Component\HttpFoundation\Response;
use Exception;
use Symfony\Component\HttpFoundation\Request;
use App\Connector\SystemUserDBConnector;

class ProfileService
{
    use LoggerTrait;

    public function __construct(private SystemUserDBConnector $systemUserDBConnector) {}

    public function putUserData(string $id, ?array $data): WSResponse
    {
        try {
            $this->logger->info(__CLASS__ . "::" . __METHOD__ . " Put User data");
            $options  = [
                    'headers' => [
                        'Content-Type' => 'application/json'
                    ],
                    'body' => json_encode($data)
                ];
            $response = $this->systemUserDBConnector->call(Request::METHOD_PUT, '/v1/user/'.$id, $options);
            $responseCode = $response->getCode() ?? '';
            $responseData = $response->getData();
            if (Response::HTTP_OK == $responseCode) {
                return new WSResponse(Response::HTTP_OK, $responseData);
            }
            $message = $responseData['error'] ?? $responseData;
            return new WSResponse($responseCode, $message["message"]);
        } catch (Exception $e) {
            return new WSResponse($e->getCode(), $e->getMessage());
        }
    }

    public function getUserData(string $id): WSResponse
    {
        try {
            $this->logger->info(__CLASS__ . "::" . __METHOD__ . " get User data");
            $options  = [
                    'headers' => [
                        'Content-Type' => 'application/json'
                    ]
                ];
            $response = $this->systemUserDBConnector->call(Request::METHOD_GET, '/v1/user/'.$id, $options);
            $responseCode = $response->getCode() ?? '';
            $responseData = $response->getData();
            if (Response::HTTP_OK == $responseCode) {
                return new WSResponse(Response::HTTP_OK, $responseData);
            }
            $message = $responseData['error'] ?? $responseData;
            return new WSResponse($responseCode, $message["message"]);
        } catch (Exception $e) {
            return new WSResponse($e->getCode(), $e->getMessage());
        }
    }
}