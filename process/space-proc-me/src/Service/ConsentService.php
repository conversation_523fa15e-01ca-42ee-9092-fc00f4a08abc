<?php

namespace App\Service;

use App\Connector\SystemIdpConnector;
use App\Helper\WSResponse;
use Symfony\Component\HttpFoundation\Response;

/**
 * Introspection service
 */
class ConsentService
{

    public function __construct(private SystemIdpConnector $systemIdpConnector) {}

    /**
     * Get Account Info data from Gigya API
     * 
     *
     * @return WSResponse
     */
    public function getConsentsInfo(string $userId): WSResponse
    {
        try {
            $response = $this->systemIdpConnector->call('GET', '/v1/accounts/info', ['headers' => ['userId' => $userId]]);

            $responseCode = $response->getCode() ?? '';
            if (Response::HTTP_OK == $responseCode) {
                $responseData = $response->getData();
                return new WSResponse(Response::HTTP_OK, $responseData);
            }

            return new WSResponse(Response::HTTP_BAD_REQUEST, $response);
        } catch (\Exception $e) {
            return new WSResponse($e->getCode(), $e->getMessage());
        }
    }

    /**
     * Set Account Info data to Gigya API.
     * 
     *
     * @return WSResponse
     */
    public function putConsentsInfo(string $userId, array $payload): WSResponse
    {
        try {

            $response = $this->systemIdpConnector->call(
                'POST',
                '/v1/accounts/info',
                [
                    'headers' => [
                        'userId'  => $userId,
                        'Content-Type' => 'application/json'
                    ],
                    'body' => json_encode($payload)
                ],
            );

            $responseCode = $response->getCode() ?? '';
            if (Response::HTTP_OK == $responseCode) {
                $responseData = $response->getData();
                return new WSResponse(Response::HTTP_OK, $responseData);
            }

            return new WSResponse(Response::HTTP_BAD_REQUEST, $response);
        } catch (\Exception $e) {
            return new WSResponse($e->getCode(), $e->getMessage());
        }
    }
}
