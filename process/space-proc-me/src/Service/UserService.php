<?php

namespace App\Service;

use App\Trait\LoggerTrait;
use App\Helper\WSResponse;
use App\Service\MongoAtlasQueryService;
class UserService
{
    use LoggerTrait;

    public const COLLECTION = 'userData';

    public function __construct(
        private MongoAtlasQueryService $mongoService
    ) {}

    private function getUserByUserIdFilter(?string $userId)
    {
        return
        [
            'userId' => $userId
        ];
    }

    private function updateByUserDbIdFilter(?string $userDbId)
    {
        return
        [
            'userDbId' => $userDbId
        ];
    }

    public function getUserByUserId(?string $userId): WSResponse
    {
        $this->logger->info('Getting user', ['userId' => $userId]);
        return $this->mongoService->find(self::COLLECTION, $this->getUserByUserIdFilter($userId));
    }

    public function updateUserByUserDbId(?string $userDbId, ?array $data): WSResponse
    {
        $this->logger->info('Updating user', ['userDbId' => $userDbId]);
        return $this->mongoService->updateOne(self::COLLECTION, $this->updateByUserDbIdFilter($userDbId), $data, true);
    }

    public function createUser(?array $data): WSResponse
    {
        $this->logger->info('Creating user', ['data' => $data]);
        return $this->mongoService->insertOne(self::COLLECTION, $data);
    }
}
