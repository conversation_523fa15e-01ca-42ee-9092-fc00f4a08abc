<?php

namespace App\Tests\Builder;

use App\Message\ContractUpdateMessage;
use App\Message\Subscription;

class ContractUpdateMessageBuilder
{
    private string $messageId;
    private string $userId;
    private array $objects;
    private string $source;

    public function setMessageId(string $messageId): self
    {
        $this->messageId = $messageId;

        return $this;
    }

    public function setUserId(string $userId): self
    {
        $this->userId = $userId;

        return $this;
    }

    public function setObjects(array $objects): self
    {
        $this->objects = $objects;

        return $this;
    }

    public function addObject(string $key, $object): self
    {
        $this->objects[$key] = $object;

        return $this;
    }

    public function addSubscription(Subscription $subscription): self
    {
        $this->objects['subscription'] = $subscription;

        return $this;
    }

    public function setSource(string $source): self
    {
        $this->source = $source;

        return $this;
    }

    public function build(): ContractUpdateMessage
    {
        return new ContractUpdateMessage($this->messageId, $this->userId, $this->objects, $this->source);
    }
}
