<?php

namespace App\Tests\DtoResponse;

use App\DtoResponse\DealerResponseDto;
use App\Model\DealerV2Model;
use PHPUnit\Framework\TestCase;

class DealerResponseDtoTest extends TestCase
{
    private DealerResponseDto $dealerResponseDto;
    
    protected function setUp(): void
    {
        $this->dealerResponseDto = new DealerResponseDto();
    }
    
    public function testGetSuccessReturnsEmptyArrayByDefault(): void
    {
        $this->assertEquals([], $this->dealerResponseDto->getSuccess());
    }
    
    public function testSetSuccess(): void
    {
        $dealers = [
            new DealerV2Model(),
            new DealerV2Model()
        ];
        
        $this->dealerResponseDto->setSuccess($dealers);
        
        $this->assertSame($dealers, $this->dealerResponseDto->getSuccess());
    }
    
    public function testAddDealer(): void
    {
        $dealer1 = new DealerV2Model();
        $dealer1->setName('Dealer 1');
        
        $dealer2 = new DealerV2Model();
        $dealer2->setName('Dealer 2');
        
        // Add first dealer
        $result1 = $this->dealerResponseDto->addDealer($dealer1);
        
        // Assert the method returns $this for chaining
        $this->assertSame($this->dealerResponseDto, $result1);
        
        // Assert the dealer was added
        $this->assertCount(1, $this->dealerResponseDto->getSuccess());
        $this->assertSame($dealer1, $this->dealerResponseDto->getSuccess()[0]);
        
        // Add second dealer
        $result2 = $this->dealerResponseDto->addDealer($dealer2);
        
        // Assert the method returns $this for chaining
        $this->assertSame($this->dealerResponseDto, $result2);
        
        // Assert both dealers are in the array
        $this->assertCount(2, $this->dealerResponseDto->getSuccess());
        $this->assertSame($dealer1, $this->dealerResponseDto->getSuccess()[0]);
        $this->assertSame($dealer2, $this->dealerResponseDto->getSuccess()[1]);
    }
    
    public function testFluentInterface(): void
    {
        $dealer1 = new DealerV2Model();
        $dealer2 = new DealerV2Model();
        
        // Test method chaining
        $result = $this->dealerResponseDto
            ->addDealer($dealer1)
            ->addDealer($dealer2);
        
        $this->assertSame($this->dealerResponseDto, $result);
        $this->assertCount(2, $this->dealerResponseDto->getSuccess());
    }
}
