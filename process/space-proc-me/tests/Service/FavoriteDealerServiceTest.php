<?php

namespace App\Tests\Service;

use App\Connector\SysAPDVConnector;
use App\Connector\SysServiceAdvisorConnector;
use App\Helper\WSResponse;
use App\Service\FavoriteDealerService;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;

class FavoriteDealerServiceTest extends TestCase
{
    private $sysAPDVConnector;
    private $sysServiceAdvisorConnector;
    private $favoriteDealerService;
    private $logger;

    protected function setUp(): void
    {
        $this->sysAPDVConnector = $this->createMock(SysAPDVConnector::class);
        $this->sysServiceAdvisorConnector = $this->createMock(SysServiceAdvisorConnector::class);

        $this->favoriteDealerService = new FavoriteDealerService(
            $this->sysAPDVConnector,
            $this->sysServiceAdvisorConnector
        );

        // Set logger using reflection since it's injected via a trait
        $this->logger = $this->createMock(LoggerInterface::class);
        $reflection = new \ReflectionClass($this->favoriteDealerService);
        $loggerProperty = $reflection->getProperty('logger');
        $loggerProperty->setAccessible(true);
        $loggerProperty->setValue($this->favoriteDealerService, $this->logger);
    }

    public function testGetXpDealerDetails(): void
    {
        // Test data
        $params = [
            'brand' => 'DS',
            'country' => 'FR',
            'language' => 'fr',
            'siteGeo' => 'DEALER123'
        ];

        // Expected query parameters
        $expectedQuery = [
            'query' => [
                'brand' => 'DS',
                'country' => 'FR',
                'language' => 'fr'
            ]
        ];

        // Expected response
        $expectedResponse = new WSResponse(Response::HTTP_OK, json_encode([
            'success' => [
                'dealer' => [
                    'id' => 'DEALER123',
                    'name' => 'Test Dealer',
                    'address' => '123 Test Street'
                ]
            ]
        ]));

        // Set up expectations for the connector
        $this->sysAPDVConnector->expects($this->once())
            ->method('call')
            ->with('GET', '/v1/dealer/DEALER123', $expectedQuery)
            ->willReturn($expectedResponse);

        // Call the method
        $response = $this->favoriteDealerService->getXpDealerDetails($params);

        // Assert the response
        $this->assertSame($expectedResponse, $response);
    }

    public function testGetXpDealerDetailsWithEmptySiteGeo(): void
    {
        // Test data with empty siteGeo
        $params = [
            'brand' => 'DS',
            'country' => 'FR',
            'language' => 'fr',
            'siteGeo' => ''
        ];

        // The connector should not be called
        $this->sysAPDVConnector->expects($this->never())
            ->method('call');

        // Call the method
        $response = $this->favoriteDealerService->getXpDealerDetails($params);

        // Assert the response
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_NOT_FOUND, $response->getCode());

        // The response data is already an array, no need to decode
        $responseData = $response->getData();
        $this->assertArrayHasKey('error', $responseData);
        $this->assertEquals('No favorite dealer found', $responseData['error']);
    }

    public function testGetXfDealerDetails(): void
    {
        // Test data
        $params = [
            'brand' => 'FI',
            'country' => 'IT',
            'siteGeo' => 'SITE123|SINCOM456'
        ];

        // Expected query parameters
        $expectedQuery = [
            'query' => [
                'brand' => 'FIAT',  // This should match what BrandHelper::getBrandId returns
                'market' => 'IT',   // This should match what MarketHelper::getMarket returns
                'siteCode' => 'SITE123',
                'sincom' => 'SINCOM456'
            ]
        ];

        // Expected response
        $expectedResponse = new WSResponse(Response::HTTP_OK, json_encode([
            'success' => [
                'dealer' => [
                    'id' => 'SITE123|SINCOM456',
                    'name' => 'Test Dealer XF',
                    'address' => '456 Test Street'
                ]
            ]
        ]));

        // Set up expectations for the connector
        $this->sysServiceAdvisorConnector->expects($this->once())
            ->method('call')
            ->with('GET', '/v1/dealer/detail', $this->anything())
            ->willReturn($expectedResponse);

        // Call the method
        $response = $this->favoriteDealerService->getXfDealerDetails($params);

        // Assert the response
        $this->assertSame($expectedResponse, $response);
    }

    public function testGetXfDealerDetailsWithSinglePartSiteGeo(): void
    {
        // Test data with siteGeo that doesn't contain a pipe
        $params = [
            'brand' => 'FI',
            'country' => 'IT',
            'siteGeo' => 'SITE123'
        ];

        // Expected query parameters
        $expectedQuery = [
            'query' => [
                'brand' => 'FIAT',  // This should match what BrandHelper::getBrandId returns
                'market' => 'IT',   // This should match what MarketHelper::getMarket returns
                'siteCode' => 'SITE123',
                'sincom' => ''
            ]
        ];

        // Expected response
        $expectedResponse = new WSResponse(Response::HTTP_OK, json_encode([
            'success' => [
                'dealer' => [
                    'id' => 'SITE123',
                    'name' => 'Test Dealer XF',
                    'address' => '456 Test Street'
                ]
            ]
        ]));

        // Set up expectations for the connector
        $this->sysServiceAdvisorConnector->expects($this->once())
            ->method('call')
            ->with('GET', '/v1/dealer/detail', $this->anything())
            ->willReturn($expectedResponse);

        // Call the method
        $response = $this->favoriteDealerService->getXfDealerDetails($params);

        // Assert the response
        $this->assertSame($expectedResponse, $response);
    }
}
