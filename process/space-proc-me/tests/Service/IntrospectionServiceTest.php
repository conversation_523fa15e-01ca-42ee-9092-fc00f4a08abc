<?php

namespace App\Tests\Service;

use App\Connector\SystemIdpConnector;
use App\Helper\WSResponse;
use App\Service\IntrospectionService;
use App\Service\MongoAtlasQueryService;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\SerializerInterface;

class IntrospectionServiceTest extends TestCase
{
    private IntrospectionService $introspectionService;

    private MockObject $mongoService;

    private MockObject $systemIdpConnector;

    private MockObject $logger;

    private MockObject $serializer;

    public function setUp(): void
    {
        $this->systemIdpConnector = $this->createMock(SystemIdpConnector::class);
        $this->serializer = $this->createMock(SerializerInterface::class);
        $this->mongoService = $this->createMock(MongoAtlasQueryService::class);
        $this->introspectionService = new IntrospectionService($this->mongoService, $this->serializer, $this->systemIdpConnector);

        $this->logger = $this->createMock(LoggerInterface::class);
        $this->logger->expects($this->any())
            ->method('info')
        ;
        $this->logger->expects($this->any())
            ->method('error')
        ;
        $this->introspectionService->setLogger($this->logger);
    }

    public function testIsUserExist(): void
    {
        $this->mongoService->expects($this->once())
            ->method('find')
            ->willReturn(new WSResponse(Response::HTTP_OK, '{"documents": [{"userId":"123"}]}'));
        $this->assertTrue($this->introspectionService->isUserExist('123'));
    }

    public function testReadUserData(): void
    {
        $userCvsId = 'AP-ACNT200000943498';

        $this->mongoService->expects($this->once())
            ->method('find')
            ->willReturn(new WSResponse(Response::HTTP_OK, '{"documents": [{"userId":"123"}]}'));

        $response = $this->introspectionService->readUserData($userCvsId);
        $this->assertInstanceOf(WSResponse::class, $response);
    }

    public function testCreateUser(): void
    {
        $this->mongoService->expects($this->once())
            ->method('insertOne')
            ->WillReturn(new WSResponse(Response::HTTP_OK, []));

        $userData = [
            'userPsaId' => [
                'AP' => 'userCvsId123'
            ]
        ];
        $response = $this->introspectionService->createUser($userData, 'userCvsId123', 'AP');
        $this->assertInstanceOf(WSResponse::class, $response);
    }

    public function testAddCvsToUser(): void
    {
        $this->serializer->expects($this->any())
            ->method('serialize')
            ->willReturn('{}');

        $this->mongoService->expects($this->once())
            ->method('updatePush')
            ->WillReturn(new WSResponse(Response::HTTP_OK, []));

        $userData = [
            'userId' => 'user123',
            'userPsaId' => [
                'AP' => 'userCvsId123'
            ]
        ];
        $response = $this->introspectionService->addCvsToUser($userData, 'userCvsId123', 'AP');
        $this->assertInstanceOf(WSResponse::class, $response);
    }

    public function testGetGigyaDataException(): void
    {
        $userCvsId = 'SP-NOT404FOUND';

        $this->systemIdpConnector->expects($this->once())
            ->method('call')
            ->willThrowException(new \Exception());

        $response = $this->introspectionService->getGigyaData($userCvsId, 'AP');
        $this->assertInstanceOf(WSResponse::class, $response);
    }

    public function testGetUserCvsIdException(): void
    {
        $token = '59aa43a6-a15f-47e4-a2df-d80218d50129';
        $brand = 'AP';
        $source = 'SPACEWEB';
        $device = $country = '';

        $this->systemIdpConnector->expects($this->once())
            ->method('call')
            ->willThrowException(new \Exception());

        $response = $this->introspectionService->getUserCvsId($token, $brand,$source,$device,$country);
        $this->assertInstanceOf(WSResponse::class, $response);
    }

    public function testGetUserCvsIdSuccess(): void
    {
        $token = '59aa43a6-a15f-47e4-a2df-d80218d50129';
        $brand = 'AP';
        $source = 'SPACEWEB';
        $device = 'iOS';
        $country = 'FR';

        $mockResponse = new WSResponse(Response::HTTP_OK, ['success' => ['cvsId' => 'user123']]);

        $this->systemIdpConnector->expects($this->once())
            ->method('call')
            ->willReturn($mockResponse);

        $response = $this->introspectionService->getUserCvsId($token, $brand, $source, $device, $country);
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals('user123', $response->getData());
    }

    public function testGetUserCvsIdFailure(): void
    {
        $token = '59aa43a6-a15f-47e4-a2df-d80218d50129';
        $brand = 'AP';
        $source = 'SPACEWEB';
        $device = 'iOS';
        $country = 'FR';

        $mockResponse = new WSResponse(Response::HTTP_OK, ['success' => []]);

        $this->systemIdpConnector->expects($this->once())
            ->method('call')
            ->willReturn($mockResponse);

        $response = $this->introspectionService->getUserCvsId($token, $brand, $source, $device, $country);
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getCode());
    }

    public function testGetUserCLPCvsIdSuccess(): void
    {
        $token = '59aa43a6-a15f-47e4-a2df-d80218d50129';
        $brand = 'AP';
        $source = 'SPACEWEB';
        $device = 'iOS';
        $country = 'FR';

        $mockResponse = new WSResponse(Response::HTTP_OK, ['success' => ['cvsId' => 'user123']]);

        $this->systemIdpConnector->expects($this->once())
            ->method('call')
            ->willReturn($mockResponse);

        $response = $this->introspectionService->getUserCLPCvsId($token, $brand, $source, $device, $country);
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals('user123', $response->getData());
    }

    public function testGetUserCLPCvsIdFailure(): void
    {
        $token = '59aa43a6-a15f-47e4-a2df-d80218d50129';
        $brand = 'AP';
        $source = 'SPACEWEB';
        $device = 'iOS';
        $country = 'FR';

        $mockResponse = new WSResponse(Response::HTTP_OK, ['success' => []]);

        $this->systemIdpConnector->expects($this->once())
            ->method('call')
            ->willReturn($mockResponse);

        $response = $this->introspectionService->getUserCLPCvsId($token, $brand, $source, $device, $country);
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getCode());
    }

    public function testGetUserCLPCvsIdException(): void
    {
        $token = '59aa43a6-a15f-47e4-a2df-d80218d50129';
        $brand = 'AP';
        $source = 'SPACEWEB';
        $device = $country = '';

        $this->systemIdpConnector->expects($this->once())
            ->method('call')
            ->willThrowException(new \Exception());

        $response = $this->introspectionService->getUserCLPCvsId($token, $brand,$source,$device,$country);
        $this->assertInstanceOf(WSResponse::class, $response);
    }

    public function testReadUserDataByUserDbId(): void
    {
        $userCvsId = 'AP-ACNT200000943498';

        $this->mongoService->expects($this->once())
            ->method('find')
            ->willReturn(new WSResponse(Response::HTTP_OK, '{"documents": [{"userId":"123"}]}'));

        $response = $this->introspectionService->readUserDataByUserDbId($userCvsId);
        $this->assertInstanceOf(WSResponse::class, $response);
    }

    public function testIsUserExistFalse(): void
    {
        $this->mongoService->expects($this->once())
            ->method('find')
            ->willReturn(new WSResponse(Response::HTTP_OK, '{"documents": []}'));
        $this->assertFalse($this->introspectionService->isUserExist('123'));
    }

    public function testGetGigyaDataSuccess(): void
    {
        $userCvsId = 'AP-ACNT200000943498';
        $brand = 'AP';

        $mockResponse = new WSResponse(Response::HTTP_OK, ['success' => ['data' => 'test']]);

        $this->systemIdpConnector->expects($this->once())
            ->method('call')
            ->willReturn($mockResponse);

        $response = $this->introspectionService->getGigyaData($userCvsId, $brand);
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
    }

    public function testGetAccessTokenSuccess(): void
    {
        $cvsCode = 'code123';
        $redirectUri = 'https://example.com/callback';
        $brand = 'AP';
        $country = 'FR';
        $source = 'SPACEWEB';

        $mockResponse = new WSResponse(Response::HTTP_OK, ['success' => ['access_token' => 'token123']]);

        $this->systemIdpConnector->expects($this->once())
            ->method('call')
            ->willReturn($mockResponse);

        $response = $this->introspectionService->getAccessToken($cvsCode, $redirectUri, $brand, $country, $source);
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals('token123', $response->getData());
    }

    public function testGetAccessTokenFailure(): void
    {
        $cvsCode = 'code123';
        $redirectUri = 'https://example.com/callback';
        $brand = 'AP';
        $country = 'FR';
        $source = 'SPACEWEB';

        $mockResponse = new WSResponse(Response::HTTP_OK, ['success' => []]);

        $this->systemIdpConnector->expects($this->once())
            ->method('call')
            ->willReturn($mockResponse);

        $response = $this->introspectionService->getAccessToken($cvsCode, $redirectUri, $brand, $country, $source);
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getCode());
    }

    public function testGetAccessTokenException(): void
    {
        $cvsCode = 'code123';
        $redirectUri = 'https://example.com/callback';
        $brand = 'AP';
        $country = 'FR';
        $source = 'SPACEWEB';

        $this->systemIdpConnector->expects($this->once())
            ->method('call')
            ->willThrowException(new \Exception('Error', 500));

        $response = $this->introspectionService->getAccessToken($cvsCode, $redirectUri, $brand, $country, $source);
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(500, $response->getCode());
    }

    public function testGetTokenCVSSuccess(): void
    {
        $code = 'code123';
        $redirectUri = 'https://example.com/callback';
        $brand = 'AP';
        $country = 'FR';
        $source = 'SPACEWEB';
        $idp = 'CVS';

        $mockResponse = new WSResponse(Response::HTTP_OK, ['success' => ['access_token' => 'token123']]);

        $this->systemIdpConnector->expects($this->once())
            ->method('call')
            ->willReturn($mockResponse);

        $response = $this->introspectionService->getToken($code, $redirectUri, $brand, $country, $source, $idp);
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
    }

    public function testGetTokenGIGYASuccess(): void
    {
        $code = 'code123';
        $redirectUri = 'https://example.com/callback';
        $brand = 'AP';
        $country = 'FR';
        $source = 'SPACEWEB';
        $idp = 'GIGYA';

        $mockResponse = new WSResponse(Response::HTTP_OK, ['success' => ['access_token' => 'token123']]);

        $this->systemIdpConnector->expects($this->once())
            ->method('call')
            ->willReturn($mockResponse);

        $response = $this->introspectionService->getToken($code, $redirectUri, $brand, $country, $source, $idp);
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
    }

    public function testGetTokenNoAccessToken(): void
    {
        $code = 'code123';
        $redirectUri = 'https://example.com/callback';
        $brand = 'AP';
        $country = 'FR';
        $source = 'SPACEWEB';
        $idp = 'CVS';

        $mockResponse = new WSResponse(Response::HTTP_OK, ['success' => ['data' => 'test']]);

        $this->systemIdpConnector->expects($this->once())
            ->method('call')
            ->willReturn($mockResponse);

        $response = $this->introspectionService->getToken($code, $redirectUri, $brand, $country, $source, $idp);
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $response->getCode());
    }

    public function testGetTokenNonOkResponse(): void
    {
        $code = 'code123';
        $redirectUri = 'https://example.com/callback';
        $brand = 'AP';
        $country = 'FR';
        $source = 'SPACEWEB';
        $idp = 'CVS';

        $mockResponse = new WSResponse(Response::HTTP_NOT_FOUND, []);

        $this->systemIdpConnector->expects($this->once())
            ->method('call')
            ->willReturn($mockResponse);

        $response = $this->introspectionService->getToken($code, $redirectUri, $brand, $country, $source, $idp);
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getCode());
    }

    public function testGetTokenException(): void
    {
        $code = 'code123';
        $redirectUri = 'https://example.com/callback';
        $brand = 'AP';
        $country = 'FR';
        $source = 'SPACEWEB';
        $idp = 'CVS';

        $this->systemIdpConnector->expects($this->once())
            ->method('call')
            ->willThrowException(new \Exception('Error', 500));

        $response = $this->introspectionService->getToken($code, $redirectUri, $brand, $country, $source, $idp);
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(500, $response->getCode());
    }

    public function testCreateUserUnauthorized(): void
    {
        $this->expectException(\Exception::class);
        $this->expectExceptionCode(Response::HTTP_BAD_REQUEST);

        $userData = [
            'userPsaId' => [
                'AP' => 'differentId'
            ]
        ];
        $this->introspectionService->createUser($userData, 'userCvsId123', 'AP');
    }

    public function testAddCvsToUserUnauthorized(): void
    {
        $this->expectException(\Exception::class);
        $this->expectExceptionCode(Response::HTTP_BAD_REQUEST);

        $userData = [
            'userId' => 'user123',
            'userPsaId' => [
                'AP' => 'differentId'
            ]
        ];
        $this->introspectionService->addCvsToUser($userData, 'userCvsId123', 'AP');
    }
}
