<?php

namespace App\Tests\Service;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\SerializerInterface;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use App\Helper\WSResponse;
use App\Helper\SuccessResponse;
use App\Service\MongoAtlasQueryService;
use App\Service\ConsentArbitrationService;
use App\Model\ConsentArbitrationModel;


class ConsentArbitrationServiceTest extends TestCase
{
    private MongoAtlasQueryService $mongoService;
    private SerializerInterface $serializer;
    private LoggerInterface $loggerMock;
    private ConsentArbitrationService $consentArbitrationService;
    private ConsentArbitrationModel $consentArbitrationModel;

    protected function setUp(): void
    {
        $this->mongoService = $this->createMock(MongoAtlasQueryService::class);
        $this->serializer = $this->createMock(SerializerInterface::class);
        $this->loggerMock = $this->createMock(LoggerInterface::class);
        $this->consentArbitrationService = new ConsentArbitrationService($this->mongoService, $this->serializer);
        $this->consentArbitrationModel = $this->createMock(ConsentArbitrationModel::class);
        $this->consentArbitrationService->setLogger($this->loggerMock);
    }

    public function testConsentArbitration(): void
    {   
        $this->mongoService->expects($this->once())
            ->method('insertOne')
            ->willReturn(new WSResponse(201, '{"insertedId":"6719fb9c36750eef1f265791"}'));
        $this->serializer->expects($this->once())
            ->method('serialize')
            ->willReturn('{"userId":"6719fb9c36750eef1f265791","vin":"12345678901234567","consent":true,"brand":"JE","country":"AT","source":"WEB"}');
        
        $response = $this->consentArbitrationService->saveConsentArbitration($this->consentArbitrationModel);
        $this->assertInstanceOf(WSResponse::class, $response);
    }
}