<?php

namespace App\Tests\Service;

use App\Helper\WSResponse;
use App\Service\MongoAtlasQueryService;
use App\Service\UserService;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;

class UserServiceTest extends TestCase
{
    private $mongoService;
    private $userService;
    private $logger;

    protected function setUp(): void
    {
        $this->mongoService = $this->createMock(MongoAtlasQueryService::class);
        $this->userService = new UserService($this->mongoService);
        
        // Set logger using reflection since it's injected via a trait
        $this->logger = $this->createMock(LoggerInterface::class);
        $reflection = new \ReflectionClass($this->userService);
        $loggerProperty = $reflection->getProperty('logger');
        $loggerProperty->setAccessible(true);
        $loggerProperty->setValue($this->userService, $this->logger);
    }

    public function testGetUserByUserId(): void
    {
        // Test data
        $userId = 'user123';
        $expectedFilter = ['userId' => $userId];
        
        // Mock the MongoDB response
        $expectedResponse = $this->createMock(WSResponse::class);
        
        // Set up expectations for the MongoDB service
        $this->mongoService->expects($this->once())
            ->method('find')
            ->with(UserService::COLLECTION, $expectedFilter)
            ->willReturn($expectedResponse);
        
        // Call the method
        $response = $this->userService->getUserByUserId($userId);
        
        // Assert the response
        $this->assertSame($expectedResponse, $response);
    }

    public function testUpdateUserByUserDbId(): void
    {
        // Test data
        $userDbId = 'db456';
        $updateData = [
            'profile.firstName' => 'John',
            'profile.lastName' => 'Doe'
        ];
        $expectedFilter = ['userDbId' => $userDbId];
        
        // Mock the MongoDB response
        $expectedResponse = $this->createMock(WSResponse::class);
        
        // Set up expectations for the MongoDB service
        $this->mongoService->expects($this->once())
            ->method('updateOne')
            ->with(UserService::COLLECTION, $expectedFilter, $updateData, true)
            ->willReturn($expectedResponse);
        
        // Call the method
        $response = $this->userService->updateUserByUserDbId($userDbId, $updateData);
        
        // Assert the response
        $this->assertSame($expectedResponse, $response);
    }

    public function testCreateUser(): void
    {
        // Test data
        $userData = [
            'userDbId' => 'db456',
            'userId' => 'user123',
            'profile' => [
                'firstName' => 'John',
                'lastName' => 'Doe'
            ]
        ];
        
        // Mock the MongoDB response
        $expectedResponse = $this->createMock(WSResponse::class);
        
        // Set up expectations for the MongoDB service
        $this->mongoService->expects($this->once())
            ->method('insertOne')
            ->with(UserService::COLLECTION, $userData)
            ->willReturn($expectedResponse);
        
        // Call the method
        $response = $this->userService->createUser($userData);
        
        // Assert the response
        $this->assertSame($expectedResponse, $response);
    }

    public function testGetUserByUserIdFilter(): void
    {
        // Test data
        $userId = 'user123';
        $expectedFilter = ['userId' => $userId];
        
        // Use reflection to access private method
        $reflection = new \ReflectionClass($this->userService);
        $method = $reflection->getMethod('getUserByUserIdFilter');
        $method->setAccessible(true);
        
        // Call the method
        $result = $method->invoke($this->userService, $userId);
        
        // Assert the result
        $this->assertEquals($expectedFilter, $result);
    }

    public function testUpdateByUserDbIdFilter(): void
    {
        // Test data
        $userDbId = 'db456';
        $expectedFilter = ['userDbId' => $userDbId];
        
        // Use reflection to access private method
        $reflection = new \ReflectionClass($this->userService);
        $method = $reflection->getMethod('updateByUserDbIdFilter');
        $method->setAccessible(true);
        
        // Call the method
        $result = $method->invoke($this->userService, $userDbId);
        
        // Assert the result
        $this->assertEquals($expectedFilter, $result);
    }
}
