<?php

namespace App\Tests\Service;

use App\Connector\SystemUserDBConnector;
use App\Helper\WSResponse;
use App\Service\ProfileService;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class ProfileServiceTest extends TestCase
{
    private $systemUserDBConnector;
    private $profileService;
    private $logger;

    protected function setUp(): void
    {
        $this->systemUserDBConnector = $this->createMock(SystemUserDBConnector::class);
        $this->profileService = new ProfileService($this->systemUserDBConnector);
        
        // Set logger using reflection since it's injected via a trait
        $this->logger = $this->createMock(LoggerInterface::class);
        $reflection = new \ReflectionClass($this->profileService);
        $loggerProperty = $reflection->getProperty('logger');
        $loggerProperty->setAccessible(true);
        $loggerProperty->setValue($this->profileService, $this->logger);
    }

    public function testGetUserDataSuccess(): void
    {
        // Test data
        $userId = 'user123';
        $responseData = [
            'success' => [
                'firstName' => 'John',
                'lastName' => 'Doe',
                'email' => '<EMAIL>'
            ]
        ];
        
        // Mock the connector response
        $connectorResponse = $this->createMock(WSResponse::class);
        $connectorResponse->method('getCode')->willReturn(Response::HTTP_OK);
        $connectorResponse->method('getData')->willReturn(json_encode($responseData));
        
        // Set up expectations for the connector
        $this->systemUserDBConnector->expects($this->once())
            ->method('call')
            ->with(
                Request::METHOD_GET,
                '/v1/user/' . $userId,
                [
                    'headers' => [
                        'Content-Type' => 'application/json'
                    ]
                ]
            )
            ->willReturn($connectorResponse);
        
        // Call the method
        $response = $this->profileService->getUserData($userId);
        
        // Assert the response
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals(json_encode($responseData), $response->getData());
    }

    public function testGetUserDataError(): void
    {
        // Test data
        $userId = 'user123';
        $errorData = [
            'error' => [
                'message' => 'User not found'
            ]
        ];
        
        // Mock the connector response
        $connectorResponse = $this->createMock(WSResponse::class);
        $connectorResponse->method('getCode')->willReturn(Response::HTTP_NOT_FOUND);
        $connectorResponse->method('getData')->willReturn($errorData);
        
        // Set up expectations for the connector
        $this->systemUserDBConnector->expects($this->once())
            ->method('call')
            ->with(
                Request::METHOD_GET,
                '/v1/user/' . $userId,
                [
                    'headers' => [
                        'Content-Type' => 'application/json'
                    ]
                ]
            )
            ->willReturn($connectorResponse);
        
        // Call the method
        $response = $this->profileService->getUserData($userId);
        
        // Assert the response
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_NOT_FOUND, $response->getCode());
        $this->assertEquals('User not found', $response->getData());
    }

    public function testGetUserDataException(): void
    {
        // Test data
        $userId = 'user123';
        $exceptionMessage = 'Connection error';
        $exceptionCode = 500;
        
        // Set up expectations for the connector to throw an exception
        $this->systemUserDBConnector->expects($this->once())
            ->method('call')
            ->with(
                Request::METHOD_GET,
                '/v1/user/' . $userId,
                [
                    'headers' => [
                        'Content-Type' => 'application/json'
                    ]
                ]
            )
            ->willThrowException(new \Exception($exceptionMessage, $exceptionCode));
        
        // Call the method
        $response = $this->profileService->getUserData($userId);
        
        // Assert the response
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals($exceptionCode, $response->getCode());
        $this->assertEquals($exceptionMessage, $response->getData());
    }

    public function testPutUserDataSuccess(): void
    {
        // Test data
        $userId = 'user123';
        $userData = [
            'firstName' => 'John',
            'lastName' => 'Doe',
            'email' => '<EMAIL>'
        ];
        $responseData = [
            'success' => [
                'statusCode' => 200,
                'statusReason' => 'OK'
            ]
        ];
        
        // Mock the connector response
        $connectorResponse = $this->createMock(WSResponse::class);
        $connectorResponse->method('getCode')->willReturn(Response::HTTP_OK);
        $connectorResponse->method('getData')->willReturn(json_encode($responseData));
        
        // Set up expectations for the connector
        $this->systemUserDBConnector->expects($this->once())
            ->method('call')
            ->with(
                Request::METHOD_PUT,
                '/v1/user/' . $userId,
                [
                    'headers' => [
                        'Content-Type' => 'application/json'
                    ],
                    'body' => json_encode($userData)
                ]
            )
            ->willReturn($connectorResponse);
        
        // Call the method
        $response = $this->profileService->putUserData($userId, $userData);
        
        // Assert the response
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals(json_encode($responseData), $response->getData());
    }

    public function testPutUserDataError(): void
    {
        // Test data
        $userId = 'user123';
        $userData = [
            'firstName' => 'John',
            'lastName' => 'Doe'
        ];
        $errorData = [
            'error' => [
                'message' => 'Invalid data'
            ]
        ];
        
        // Mock the connector response
        $connectorResponse = $this->createMock(WSResponse::class);
        $connectorResponse->method('getCode')->willReturn(Response::HTTP_BAD_REQUEST);
        $connectorResponse->method('getData')->willReturn($errorData);
        
        // Set up expectations for the connector
        $this->systemUserDBConnector->expects($this->once())
            ->method('call')
            ->with(
                Request::METHOD_PUT,
                '/v1/user/' . $userId,
                [
                    'headers' => [
                        'Content-Type' => 'application/json'
                    ],
                    'body' => json_encode($userData)
                ]
            )
            ->willReturn($connectorResponse);
        
        // Call the method
        $response = $this->profileService->putUserData($userId, $userData);
        
        // Assert the response
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getCode());
        $this->assertEquals('Invalid data', $response->getData());
    }

    public function testPutUserDataException(): void
    {
        // Test data
        $userId = 'user123';
        $userData = [
            'firstName' => 'John',
            'lastName' => 'Doe'
        ];
        $exceptionMessage = 'Connection error';
        $exceptionCode = 500;
        
        // Set up expectations for the connector to throw an exception
        $this->systemUserDBConnector->expects($this->once())
            ->method('call')
            ->with(
                Request::METHOD_PUT,
                '/v1/user/' . $userId,
                [
                    'headers' => [
                        'Content-Type' => 'application/json'
                    ],
                    'body' => json_encode($userData)
                ]
            )
            ->willThrowException(new \Exception($exceptionMessage, $exceptionCode));
        
        // Call the method
        $response = $this->profileService->putUserData($userId, $userData);
        
        // Assert the response
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals($exceptionCode, $response->getCode());
        $this->assertEquals($exceptionMessage, $response->getData());
    }
}
