<?php

namespace App\Tests\Service;

use App\Connector\SystemIdpConnector;
use App\Helper\WSResponse;
use App\Service\ConsentService;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;

class ConsentServiceTest extends TestCase
{
    private $systemIdpConnector;
    private $consentService;

    protected function setUp(): void
    {
        $this->systemIdpConnector = $this->createMock(SystemIdpConnector::class);
        $this->consentService = new ConsentService($this->systemIdpConnector);
    }

    public function testGetConsentsInfoSuccess(): void
    {
        // Test data
        $userId = 'user123';
        $responseData = [
            'success' => [
                'preferences' => [
                    'marketing' => [
                        'isConsentGranted' => true
                    ],
                    'profiling' => [
                        'isConsentGranted' => false
                    ]
                ]
            ]
        ];
        
        // Mock the connector response
        $connectorResponse = $this->createMock(WSResponse::class);
        $connectorResponse->method('getCode')->willReturn(Response::HTTP_OK);
        $connectorResponse->method('getData')->willReturn(json_encode($responseData));
        
        // Set up expectations for the connector
        $this->systemIdpConnector->expects($this->once())
            ->method('call')
            ->with('GET', '/v1/accounts/info', ['headers' => ['userId' => $userId]])
            ->willReturn($connectorResponse);
        
        // Call the method
        $response = $this->consentService->getConsentsInfo($userId);
        
        // Assert the response
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals(json_encode($responseData), $response->getData());
    }

    public function testGetConsentsInfoError(): void
    {
        // Test data
        $userId = 'user123';
        $errorResponse = $this->createMock(WSResponse::class);
        $errorResponse->method('getCode')->willReturn(Response::HTTP_BAD_REQUEST);
        
        // Mock the connector response
        $connectorResponse = $this->createMock(WSResponse::class);
        $connectorResponse->method('getCode')->willReturn(Response::HTTP_BAD_REQUEST);
        
        // Set up expectations for the connector
        $this->systemIdpConnector->expects($this->once())
            ->method('call')
            ->with('GET', '/v1/accounts/info', ['headers' => ['userId' => $userId]])
            ->willReturn($connectorResponse);
        
        // Call the method
        $response = $this->consentService->getConsentsInfo($userId);
        
        // Assert the response
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getCode());
    }

    public function testGetConsentsInfoException(): void
    {
        // Test data
        $userId = 'user123';
        $exceptionMessage = 'Connection error';
        $exceptionCode = 500;
        
        // Set up expectations for the connector to throw an exception
        $this->systemIdpConnector->expects($this->once())
            ->method('call')
            ->with('GET', '/v1/accounts/info', ['headers' => ['userId' => $userId]])
            ->willThrowException(new \Exception($exceptionMessage, $exceptionCode));
        
        // Call the method
        $response = $this->consentService->getConsentsInfo($userId);
        
        // Assert the response
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals($exceptionCode, $response->getCode());
        $this->assertEquals($exceptionMessage, $response->getData());
    }

    public function testPutConsentsInfoSuccess(): void
    {
        // Test data
        $userId = 'user123';
        $payload = [
            'preferences' => [
                'marketing' => [
                    'isConsentGranted' => true
                ],
                'profiling' => [
                    'isConsentGranted' => false
                ]
            ]
        ];
        $responseData = [
            'success' => [
                'statusCode' => 200,
                'statusReason' => 'OK',
                'time' => '2023-05-01T12:00:00Z'
            ]
        ];
        
        // Mock the connector response
        $connectorResponse = $this->createMock(WSResponse::class);
        $connectorResponse->method('getCode')->willReturn(Response::HTTP_OK);
        $connectorResponse->method('getData')->willReturn(json_encode($responseData));
        
        // Set up expectations for the connector
        $this->systemIdpConnector->expects($this->once())
            ->method('call')
            ->with(
                'POST',
                '/v1/accounts/info',
                [
                    'headers' => [
                        'userId' => $userId,
                        'Content-Type' => 'application/json'
                    ],
                    'body' => json_encode($payload)
                ]
            )
            ->willReturn($connectorResponse);
        
        // Call the method
        $response = $this->consentService->putConsentsInfo($userId, $payload);
        
        // Assert the response
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals(json_encode($responseData), $response->getData());
    }

    public function testPutConsentsInfoError(): void
    {
        // Test data
        $userId = 'user123';
        $payload = [
            'preferences' => [
                'marketing' => [
                    'isConsentGranted' => true
                ]
            ]
        ];
        $errorResponse = $this->createMock(WSResponse::class);
        $errorResponse->method('getCode')->willReturn(Response::HTTP_BAD_REQUEST);
        
        // Mock the connector response
        $connectorResponse = $this->createMock(WSResponse::class);
        $connectorResponse->method('getCode')->willReturn(Response::HTTP_BAD_REQUEST);
        
        // Set up expectations for the connector
        $this->systemIdpConnector->expects($this->once())
            ->method('call')
            ->with(
                'POST',
                '/v1/accounts/info',
                [
                    'headers' => [
                        'userId' => $userId,
                        'Content-Type' => 'application/json'
                    ],
                    'body' => json_encode($payload)
                ]
            )
            ->willReturn($connectorResponse);
        
        // Call the method
        $response = $this->consentService->putConsentsInfo($userId, $payload);
        
        // Assert the response
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getCode());
    }

    public function testPutConsentsInfoException(): void
    {
        // Test data
        $userId = 'user123';
        $payload = [
            'preferences' => [
                'marketing' => [
                    'isConsentGranted' => true
                ]
            ]
        ];
        $exceptionMessage = 'Connection error';
        $exceptionCode = 500;
        
        // Set up expectations for the connector to throw an exception
        $this->systemIdpConnector->expects($this->once())
            ->method('call')
            ->with(
                'POST',
                '/v1/accounts/info',
                [
                    'headers' => [
                        'userId' => $userId,
                        'Content-Type' => 'application/json'
                    ],
                    'body' => json_encode($payload)
                ]
            )
            ->willThrowException(new \Exception($exceptionMessage, $exceptionCode));
        
        // Call the method
        $response = $this->consentService->putConsentsInfo($userId, $payload);
        
        // Assert the response
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals($exceptionCode, $response->getCode());
        $this->assertEquals($exceptionMessage, $response->getData());
    }
}
