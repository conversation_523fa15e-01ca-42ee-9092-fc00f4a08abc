<?php

namespace App\Tests\Connector;

use App\Connector\InternalHttpClientService;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Contracts\HttpClient\ResponseInterface;
use Symfony\Contracts\HttpClient\ResponseStreamInterface;

class InternalHttpClientServiceTest extends TestCase
{
    private $httpClient;
    private $requestStack;
    private $request;
    private $internalHttpClientService;

    protected function setUp(): void
    {
        $this->httpClient = $this->createMock(HttpClientInterface::class);
        $this->requestStack = $this->createMock(RequestStack::class);
        $this->request = $this->createMock(Request::class);

        // Set up the request stack to return the request
        $this->requestStack->method('getCurrentRequest')->willReturn($this->request);

        $this->internalHttpClientService = new InternalHttpClientService($this->httpClient, $this->requestStack);
    }

    public function testRequest(): void
    {
        // Test data
        $method = 'GET';
        $url = 'https://api.example.com/v1/resource';
        $options = ['headers' => ['Content-Type' => 'application/json']];

        // Mock the response
        $expectedResponse = $this->createMock(ResponseInterface::class);

        // Set up expectations for the HTTP client
        $this->httpClient->expects($this->once())
            ->method('request')
            ->with($method, $url, $options)
            ->willReturn($expectedResponse);

        // Call the method
        $response = $this->internalHttpClientService->request($method, $url, $options);

        // Assert the response
        $this->assertSame($expectedResponse, $response);
    }

    public function testStream(): void
    {
        // Test data
        $responses = [$this->createMock(ResponseInterface::class)];
        $timeout = 30.0;

        // Mock the response stream
        $expectedStream = $this->createMock(ResponseStreamInterface::class);

        // Set up expectations for the HTTP client
        $this->httpClient->expects($this->once())
            ->method('stream')
            ->with($responses, $timeout)
            ->willReturn($expectedStream);

        // Call the method
        $stream = $this->internalHttpClientService->stream($responses, $timeout);

        // Assert the stream
        $this->assertSame($expectedStream, $stream);
    }

    public function testWithOptions(): void
    {
        // Test data
        $options = ['timeout' => 30, 'max_redirects' => 5];

        // Mock the HTTP client with options
        $clientWithOptions = $this->createMock(HttpClientInterface::class);

        // Set up expectations for the HTTP client
        $this->httpClient->expects($this->once())
            ->method('withOptions')
            ->with($options)
            ->willReturn($clientWithOptions);

        // Call the method
        $result = $this->internalHttpClientService->withOptions($options);

        // Assert the result
        $this->assertSame($this->internalHttpClientService, $result);
    }
}
