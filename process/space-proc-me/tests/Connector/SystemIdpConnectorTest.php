<?php

use App\Connector\CustomHttpClient;
use App\Connector\SystemIdpConnector;
use App\Helper\WSResponse;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;

class SystemIdpConnectorTest extends TestCase
{
    private $client;
    private $connector;

    public function setUp(): void
    {
        $this->client = $this->createMock(CustomHttpClient::class);
        $this->connector = new SystemIdpConnector($this->client, 'https://example.com');
        $logger = $this->createMock(LoggerInterface::class);
        $logger->expects($this->any())
            ->method('info')
            ;
        $logger->expects($this->any())
            ->method('error')
            ;
        $this->connector->setLogger($logger);
    }
    public function testCallReturnsSuccess()
    {
        $this->client->expects($this->once())
                       ->method('request')
                       ->willReturn(new WSResponse(200, ['userId' =>'123']));

        $response = $this->connector->call('GET', '/v1/users',[]);
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(200, $response->getCode());
        $this->assertEquals(['userId' =>'123'], $response->getData());
    }

    public function testCallThrowException()
    {
        
        $this->client->expects($this->once())
            ->method('request')
            ->willThrowException(new \Exception('API error', 500));

        $wsResponse = $this->connector->call('GET', '/v1/users', []);

        $this->assertInstanceOf(WSResponse::class, $wsResponse);
        $this->assertEquals(500, $wsResponse->getCode());
        $this->assertEquals('API error', $wsResponse->getData());
    }
}