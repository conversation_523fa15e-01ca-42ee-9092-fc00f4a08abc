<?php

namespace App\Tests\Connector;

use App\Connector\CustomHttpClient;
use App\Connector\SysServiceAdvisorConnector;
use App\Helper\WSResponse;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;

class SysServiceAdvisorConnectorTest extends TestCase
{
    private $client;
    private $connector;
    private $logger;
    private $url = 'https://api.example.com';

    protected function setUp(): void
    {
        $this->client = $this->createMock(CustomHttpClient::class);
        $this->connector = new SysServiceAdvisorConnector($this->client, $this->url);
        
        // Set logger using reflection since it's injected via a trait
        $this->logger = $this->createMock(LoggerInterface::class);
        $reflection = new \ReflectionClass($this->connector);
        $loggerProperty = $reflection->getProperty('logger');
        $loggerProperty->setAccessible(true);
        $loggerProperty->setValue($this->connector, $this->logger);
    }

    public function testCallSuccess(): void
    {
        // Test data
        $method = 'GET';
        $uri = '/v1/dealer/detail';
        $options = ['query' => ['brand' => 'FIAT', 'market' => 'IT']];
        $expectedUrl = $this->url . $uri;
        
        // Mock the client response
        $expectedResponse = $this->createMock(WSResponse::class);
        
        // Set up expectations for the client
        $this->client->expects($this->once())
            ->method('request')
            ->with($method, $expectedUrl, $options)
            ->willReturn($expectedResponse);
        
        // Call the method
        $response = $this->connector->call($method, $uri, $options);
        
        // Assert the response
        $this->assertSame($expectedResponse, $response);
    }

    public function testCallWithDefaultOptions(): void
    {
        // Test data
        $method = 'GET';
        $uri = '/v1/dealer/detail';
        $expectedUrl = $this->url . $uri;
        
        // Mock the client response
        $expectedResponse = $this->createMock(WSResponse::class);
        
        // Set up expectations for the client
        $this->client->expects($this->once())
            ->method('request')
            ->with($method, $expectedUrl, [])
            ->willReturn($expectedResponse);
        
        // Call the method
        $response = $this->connector->call($method, $uri);
        
        // Assert the response
        $this->assertSame($expectedResponse, $response);
    }

    public function testCallException(): void
    {
        // Test data
        $method = 'GET';
        $uri = '/v1/dealer/detail';
        $options = ['query' => ['brand' => 'FIAT', 'market' => 'IT']];
        $expectedUrl = $this->url . $uri;
        $exceptionMessage = 'Connection error';
        $exceptionCode = 500;
        
        // Set up expectations for the client to throw an exception
        $this->client->expects($this->once())
            ->method('request')
            ->with($method, $expectedUrl, $options)
            ->willThrowException(new \Exception($exceptionMessage, $exceptionCode));
        
        // Call the method
        $response = $this->connector->call($method, $uri, $options);
        
        // Assert the response
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals($exceptionCode, $response->getCode());
        $this->assertEquals($exceptionMessage, $response->getData());
    }
}
