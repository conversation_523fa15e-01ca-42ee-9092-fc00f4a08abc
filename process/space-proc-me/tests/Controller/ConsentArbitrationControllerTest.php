<?php

namespace App\Tests\Controller;

use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBag;
use Symfony\Component\HttpFoundation\HeaderBag;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use App\Controller\ConsentArbitrationController;
use App\Helper\SuccessResponse;
use App\Manager\ConsentArbitrationManager;

class ConsentArbitrationControllerTest extends WebTestCase
{
    private $validator;
    private $consentArbitrationManager;

    protected function setUp(): void
    {
        self::bootKernel();
        $container = static::getContainer();
        $this->validator = $container->get(ValidatorInterface::class);
        $this->consentArbitrationManager = $container->get(ConsentArbitrationManager::class);
    }

    public function testConsentArbitrationWithInvalidVin(): void
    {
        $queryParameters = [
            'brand' => 'CR',
            'country' => 'FR',
            'source' => 'WEB'
        ];
        $content = json_encode([
            'vin' => 'FCASWF000INT001340',
            'consent' => true
        ]);

        $controller = new ConsentArbitrationController();
        $controller->setContainer(static::getContainer());
        $request = Request::create('/v1/consents/arbitration', 'POST', [], [], [], [], $content);
        $request->query = new ParameterBag($queryParameters);
        $request->headers = new HeaderBag(['userId' => '6736320116974042a60293d16xasd2y2823']);
        $response = $controller->saveConsentArbitration($request, $this->validator, $this->consentArbitrationManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertSame(422, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
    }

    public function testConsentArbitrationWithInvalidQueryParameters(): void
    {
        $queryParameters = [
            'brand' => 'ABCD',
            'country' => 'ABCD',
            'source' => 'INVALID'
        ];
        $content = json_encode([
            'vin' => 'FCASWF000INT001340',
            'consent' => true
        ]);

        $controller = new ConsentArbitrationController();
        $controller->setContainer(static::getContainer());
        $request = Request::create('/v1/consents/arbitration', 'POST', [], [], [], [], $content);
        $request->query = new ParameterBag($queryParameters);
        $request->headers = new HeaderBag(['userId' => '6736320116974042a60293d16xasd2y2823']);
        $response = $controller->saveConsentArbitration($request, $this->validator, $this->consentArbitrationManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertSame(422, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
    }
    
    public function testConsentArbitration(): void
    {
        $queryParameters = [
            'brand' => 'CR',
            'country' => 'FR',
            'source' => 'WEB'
        ];
        $content = json_encode([
            'vin' => 'FCASWF000INT00134',
            'consent' => true
        ]);
        $consentArbitrationManager = $this->createMock(ConsentArbitrationManager::class);
        $controller = new ConsentArbitrationController();
        $controller->setContainer(static::getContainer());

        $request = Request::create('/v1/consents/arbitration', 'POST', [], [], [], [], $content);
        $request->query = new ParameterBag($queryParameters);
        $request->headers = new HeaderBag(['userId' => '6736320116974042a60293d16xasd2y2823']);
        $consentArbitrationManager->expects($this->once())
            ->method('saveConsentArbitration')
            ->willReturn(new SuccessResponse(['message' => 'consent is successfully recorded'], 201));
        $response = $controller->saveConsentArbitration($request, $this->validator, $consentArbitrationManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertSame(201, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('success', $responseData);
    }
}