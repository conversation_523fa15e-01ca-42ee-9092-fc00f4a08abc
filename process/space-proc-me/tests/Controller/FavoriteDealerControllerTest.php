<?php

namespace App\Tests\Controller;

use App\Controller\FavoriteDealerController;
use App\Helper\SuccessResponse;
use App\Manager\FavoriteDealerManager;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBag;
use Symfony\Component\HttpFoundation\HeaderBag;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class FavoriteDealerControllerTest extends KernelTestCase
{
    private $validator;
    private $favoriteDealerManager;

    protected function setUp(): void
    {
        self::bootKernel();
        $container = static::getContainer();
        $this->validator = $this->createMock(ValidatorInterface::class);
        $this->favoriteDealerManager = $this->createMock(FavoriteDealerManager::class);
    }

    public function testSaveFavoriteDealerWithMissingParameters(): void
    {
        // Create a controller with a mock validator that will fail validation
        $controller = new FavoriteDealerController($this->validator);
        $controller->setContainer(static::getContainer());

        // Create a request with missing parameters
        $request = Request::create('/v1/preferences/favorite-dealer', 'POST', [], [], [], [], json_encode(['site_geo' => '']));
        $request->query = new ParameterBag(['brand' => 'CR', 'country' => '', 'language' => '']);
        $request->headers = new HeaderBag(['userId' => '']);

        // Set up the validator to return validation errors
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList([
                $this->createMock(\Symfony\Component\Validator\ConstraintViolation::class)
            ]));

        $response = $controller->saveFavoriteDealer($request, $this->favoriteDealerManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
    }

    public function testSaveFavoriteDealerSuccess(): void
    {
        // Create a controller with a mock validator that will pass validation
        $controller = new FavoriteDealerController($this->validator);
        $controller->setContainer(static::getContainer());

        // Create a valid request
        $request = Request::create('/v1/preferences/favorite-dealer', 'POST', [], [], [], [], json_encode(['site_geo' => 'DEALER123']));
        $request->query = new ParameterBag(['brand' => 'CR', 'country' => 'FR', 'language' => 'fr']);
        $request->headers = new HeaderBag(['userId' => '12345']);

        // Set up the validator to pass validation
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        // Set up the manager to return a success response
        $this->favoriteDealerManager->expects($this->once())
            ->method('saveFavoriteDealer')
            ->willReturn(new SuccessResponse([]));

        $response = $controller->saveFavoriteDealer($request, $this->favoriteDealerManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('success', $responseData);
    }

    public function testGetFavoriteDealerWithMissingParameters(): void
    {
        // Create a controller with a mock validator that will fail validation
        $controller = new FavoriteDealerController($this->validator);
        $controller->setContainer(static::getContainer());

        // Create a request with missing parameters
        $request = Request::create('/v1/preferences/favorite-dealer', 'GET');
        $request->query = new ParameterBag(['brand' => 'CR', 'source' => '']);
        $request->headers = new HeaderBag(['userId' => '']);

        // Set up the validator to return validation errors
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList([
                $this->createMock(\Symfony\Component\Validator\ConstraintViolation::class)
            ]));

        $response = $controller->getFavoriteDealer($request, $this->favoriteDealerManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
    }

    public function testGetFavoriteDealerSuccess(): void
    {
        // Create a controller with a mock validator that will pass validation
        $controller = new FavoriteDealerController($this->validator);
        $controller->setContainer(static::getContainer());

        // Create a valid request
        $request = Request::create('/v1/preferences/favorite-dealer', 'GET');
        $request->query = new ParameterBag(['brand' => 'CR', 'source' => 'WEB']);
        $request->headers = new HeaderBag(['userId' => '12345']);

        // Set up the validator to pass validation
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        // Set up the manager to return a success response
        $this->favoriteDealerManager->expects($this->once())
            ->method('getFavoriteDealer')
            ->willReturn(new SuccessResponse(['dealer' => ['name' => 'Test Dealer']]));

        $response = $controller->getFavoriteDealer($request, $this->favoriteDealerManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('success', $responseData);
    }

    public function testDeleteFavoriteDealerWithMissingParameters(): void
    {
        // Create a controller with a mock validator that will fail validation
        $controller = new FavoriteDealerController($this->validator);
        $controller->setContainer(static::getContainer());

        // Create a request with missing parameters
        $request = Request::create('/v1/preferences/favorite-dealer', 'DELETE');
        $request->query = new ParameterBag(['brand' => 'CR', 'country' => '']);
        $request->headers = new HeaderBag(['userId' => '']);

        // Set up the validator to return validation errors
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList([
                $this->createMock(\Symfony\Component\Validator\ConstraintViolation::class)
            ]));

        $response = $controller->deleteFavoriteDealer($request, $this->favoriteDealerManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
    }

    public function testDeleteFavoriteDealerSuccess(): void
    {
        // Create a controller with a mock validator that will pass validation
        $controller = new FavoriteDealerController($this->validator);
        $controller->setContainer(static::getContainer());

        // Create a valid request
        $request = Request::create('/v1/preferences/favorite-dealer', 'DELETE');
        $request->query = new ParameterBag(['brand' => 'CR', 'country' => 'FR']);
        $request->headers = new HeaderBag(['userId' => '12345']);

        // Set up the validator to pass validation
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        // Set up the manager to return a success response
        $this->favoriteDealerManager->expects($this->once())
            ->method('deleteFavoriteDealer')
            ->willReturn(new SuccessResponse([]));

        $response = $controller->deleteFavoriteDealer($request, $this->favoriteDealerManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('success', $responseData);
    }
}
