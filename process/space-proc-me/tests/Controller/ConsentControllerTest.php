<?php

namespace App\Tests\Controller;

use App\Controller\ConsentController;
use App\Helper\SuccessResponse;
use App\Manager\ConsentManager;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBag;
use Symfony\Component\HttpFoundation\HeaderBag;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class ConsentControllerTest extends KernelTestCase
{
    private $validator;
    private $consentManager;

    protected function setUp(): void
    {
        self::bootKernel();
        $container = static::getContainer();
        $this->validator = $container->get(ValidatorInterface::class);
        $this->consentManager = $this->createMock(ConsentManager::class);
    }

    public function testGetConsentsInfoWithMissingUserId(): void
    {
        $controller = new ConsentController();
        $controller->setContainer(static::getContainer());

        $request = Request::create('/v1/consents', 'GET');
        $request->headers = new HeaderBag([]);

        $response = $controller->getConsentsInfo($this->validator, $this->consentManager, $request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
        $this->assertArrayHasKey('message', $responseData['error']);
        $this->assertArrayHasKey('errors', $responseData['error']);
        $this->assertArrayHasKey('userId', $responseData['error']['errors']);
    }

    public function testGetConsentsInfoSuccess(): void
    {
        $controller = new ConsentController();
        $controller->setContainer(static::getContainer());

        $request = Request::create('/v1/consents', 'GET');
        $request->headers = new HeaderBag(['userId' => '12345']);

        $expectedResponse = [
            'preferences' => [
                'marketing' => [
                    'isConsentGranted' => true
                ],
                'profiling' => [
                    'isConsentGranted' => false
                ],
                'thirdPartiesMarketing' => [
                    'isConsentGranted' => true
                ],
                'thirdPartiesProfiling' => [
                    'isConsentGranted' => false
                ]
            ]
        ];

        $this->consentManager->expects($this->once())
            ->method('getConsentsInfo')
            ->with('12345')
            ->willReturn(new SuccessResponse($expectedResponse));

        $response = $controller->getConsentsInfo($this->validator, $this->consentManager, $request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('success', $responseData);
        $this->assertEquals($expectedResponse, $responseData['success']);
    }

    public function testPutConsentsInfoWithInvalidPayload(): void
    {
        $controller = new ConsentController();
        $controller->setContainer(static::getContainer());

        $request = Request::create('/v1/consents', 'POST', [], [], [], [], json_encode([]));
        $request->headers = new HeaderBag(['userId' => '12345']);

        $response = $controller->putConsentsInfo($this->validator, $this->consentManager, $request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
    }

    public function testPutConsentsInfoSuccess(): void
    {
        $controller = new ConsentController();
        $controller->setContainer(static::getContainer());

        $payload = [
            'preferences' => [
                'marketing' => [
                    'isConsentGranted' => true
                ],
                'profiling' => [
                    'isConsentGranted' => false
                ],
                'thirdPartiesMarketing' => [
                    'isConsentGranted' => true
                ],
                'thirdPartiesProfiling' => [
                    'isConsentGranted' => false
                ]
            ]
        ];

        $request = Request::create('/v1/consents', 'POST', [], [], [], [], json_encode($payload));
        $request->headers = new HeaderBag(['userId' => '12345']);

        // Create a mock validator that will pass validation
        $mockValidator = $this->createMock(ValidatorInterface::class);
        $mockValidator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        $this->consentManager->expects($this->once())
            ->method('putConsentsInfo')
            ->with('12345', $payload)
            ->willReturn(new SuccessResponse(['status' => 'success']));

        $response = $controller->putConsentsInfo($mockValidator, $this->consentManager, $request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('success', $responseData);
        $this->assertEquals(['status' => 'success'], $responseData['success']);
    }
}
