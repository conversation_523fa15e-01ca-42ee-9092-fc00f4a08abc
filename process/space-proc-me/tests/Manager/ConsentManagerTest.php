<?php

namespace App\Tests\Manager;

use App\Helper\ErrorResponse;
use App\Helper\SuccessResponse;
use App\Helper\WSResponse;
use App\Manager\ConsentManager;
use App\Service\ConsentService;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;

class ConsentManagerTest extends TestCase
{
    private $consentService;
    private $consentManager;

    protected function setUp(): void
    {
        $this->consentService = $this->createMock(ConsentService::class);
        $this->consentManager = new ConsentManager($this->consentService);
    }

    public function testGetConsentsInfoSuccess(): void
    {
        // Test data
        $userId = 'user123';
        $responseData = [
            'success' => [
                'preferences' => [
                    'marketing' => [
                        'isConsentGranted' => true
                    ],
                    'profiling' => [
                        'isConsentGranted' => false
                    ],
                    'thirdPartiesMarketing' => [
                        'isConsentGranted' => true
                    ],
                    'thirdPartiesProfiling' => [
                        'isConsentGranted' => false
                    ]
                ]
            ]
        ];

        // Mock the service response
        $serviceResponse = $this->createMock(WSResponse::class);
        $serviceResponse->method('getCode')->willReturn(Response::HTTP_OK);
        $serviceResponse->method('getData')->willReturn($responseData);

        // Set up expectations for the service
        $this->consentService->expects($this->once())
            ->method('getConsentsInfo')
            ->with($userId)
            ->willReturn($serviceResponse);

        // Call the method
        $response = $this->consentManager->getConsentsInfo($userId);

        // Assert the response
        $this->assertInstanceOf(SuccessResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals($responseData['success'], $response->getData());
    }

    public function testGetConsentsInfoError(): void
    {
        // Test data
        $userId = 'user123';
        $errorData = [
            'error' => [
                'message' => 'User not found',
                'errors' => [
                    'userId' => 'Invalid user ID'
                ]
            ]
        ];

        // Mock the service response
        $serviceResponse = $this->createMock(WSResponse::class);
        $serviceResponse->method('getCode')->willReturn(Response::HTTP_NOT_FOUND);

        // Create a mock error response
        $errorResponse = $this->createMock(WSResponse::class);
        $errorResponse->method('getData')->willReturn($errorData);
        $errorResponse->method('getCode')->willReturn(Response::HTTP_NOT_FOUND);

        // Set up the service response to return the error response
        $serviceResponse->method('getData')->willReturn($errorResponse);

        // Set up expectations for the service
        $this->consentService->expects($this->once())
            ->method('getConsentsInfo')
            ->with($userId)
            ->willReturn($serviceResponse);

        // Call the method
        $response = $this->consentManager->getConsentsInfo($userId);

        // Assert the response
        $this->assertInstanceOf(ErrorResponse::class, $response);
        $responseArray = $response->toArray();
        $this->assertEquals(Response::HTTP_NOT_FOUND, $responseArray['code']);
        $this->assertEquals('User not found', $responseArray['content']['error']['message']);
        $this->assertEquals(['userId' => 'Invalid user ID'], $responseArray['content']['error']['errors']);
    }

    public function testPutConsentsInfoSuccess(): void
    {
        // Test data
        $userId = 'user123';
        $payload = [
            'preferences' => [
                'marketing' => [
                    'isConsentGranted' => true
                ],
                'profiling' => [
                    'isConsentGranted' => false
                ],
                'thirdPartiesMarketing' => [
                    'isConsentGranted' => true
                ],
                'thirdPartiesProfiling' => [
                    'isConsentGranted' => false
                ]
            ]
        ];
        $responseData = [
            'success' => [
                'statusCode' => 200,
                'statusReason' => 'OK',
                'time' => '2023-05-01T12:00:00Z'
            ]
        ];

        // Mock the service response
        $serviceResponse = $this->createMock(WSResponse::class);
        $serviceResponse->method('getCode')->willReturn(Response::HTTP_OK);
        $serviceResponse->method('getData')->willReturn($responseData);

        // Set up expectations for the service
        $this->consentService->expects($this->once())
            ->method('putConsentsInfo')
            ->with($userId, $payload)
            ->willReturn($serviceResponse);

        // Call the method
        $response = $this->consentManager->putConsentsInfo($userId, $payload);

        // Assert the response
        $this->assertInstanceOf(SuccessResponse::class, $response);
        $responseArray = $response->toArray();
        $this->assertEquals(Response::HTTP_OK, $responseArray['code']);
        $this->assertEquals($responseData['success'], $responseArray['content']['success']);
    }

    public function testPutConsentsInfoError(): void
    {
        // Test data
        $userId = 'user123';
        $payload = [
            'preferences' => [
                'marketing' => [
                    'isConsentGranted' => true
                ]
            ]
        ];
        $errorData = [
            'error' => [
                'message' => 'Invalid payload',
                'errors' => [
                    'preferences' => 'Missing required fields'
                ]
            ]
        ];

        // Mock the service response
        $serviceResponse = $this->createMock(WSResponse::class);
        $serviceResponse->method('getCode')->willReturn(Response::HTTP_BAD_REQUEST);

        // Create a mock error response
        $errorResponse = $this->createMock(WSResponse::class);
        $errorResponse->method('getData')->willReturn($errorData);
        $errorResponse->method('getCode')->willReturn(Response::HTTP_BAD_REQUEST);

        // Set up the service response to return the error response
        $serviceResponse->method('getData')->willReturn($errorResponse);

        // Set up expectations for the service
        $this->consentService->expects($this->once())
            ->method('putConsentsInfo')
            ->with($userId, $payload)
            ->willReturn($serviceResponse);

        // Call the method
        $response = $this->consentManager->putConsentsInfo($userId, $payload);

        // Assert the response
        $this->assertInstanceOf(ErrorResponse::class, $response);
        $responseArray = $response->toArray();
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $responseArray['code']);
        $this->assertEquals('Invalid payload', $responseArray['content']['error']['message']);
        $this->assertEquals(['preferences' => 'Missing required fields'], $responseArray['content']['error']['errors']);
    }

    public function testHandleErrorResponse(): void
    {
        // Test data
        $responseErrorData = [
            'error' => [
                'message' => 'Test error message',
                'errors' => [
                    'field1' => 'Error 1',
                    'field2' => 'Error 2'
                ]
            ]
        ];
        $responseCode = Response::HTTP_BAD_REQUEST;

        // Call the method
        $response = $this->consentManager->handleErrorResponse($responseErrorData, $responseCode);

        // Assert the response
        $this->assertInstanceOf(ErrorResponse::class, $response);
        $responseArray = $response->toArray();
        $this->assertEquals($responseCode, $responseArray['code']);
        $this->assertEquals('Test error message', $responseArray['content']['error']['message']);
        $this->assertEquals($responseErrorData['error']['errors'], $responseArray['content']['error']['errors']);
    }

    public function testHandleErrorResponseWithoutErrors(): void
    {
        // Test data
        $responseErrorData = [
            'error' => [
                'message' => 'Test error message'
            ]
        ];
        $responseCode = Response::HTTP_UNAUTHORIZED;

        // Call the method
        $response = $this->consentManager->handleErrorResponse($responseErrorData, $responseCode);

        // Assert the response
        $this->assertInstanceOf(ErrorResponse::class, $response);
        $responseArray = $response->toArray();
        $this->assertEquals($responseCode, $responseArray['code']);
        $this->assertEquals('Test error message', $responseArray['content']['error']['message']);
        // The errors key might be present but empty, so we don't check for its absence
    }

    public function testHandleErrorResponseWithoutMessage(): void
    {
        // Test data
        $responseErrorData = [
            'error' => [
                'errors' => [
                    'field1' => 'Error 1'
                ]
            ]
        ];
        $responseCode = Response::HTTP_UNAUTHORIZED;

        // Call the method
        $response = $this->consentManager->handleErrorResponse($responseErrorData, $responseCode);

        // Assert the response
        $this->assertInstanceOf(ErrorResponse::class, $response);
        $responseArray = $response->toArray();
        $this->assertEquals($responseCode, $responseArray['code']);
        $this->assertEquals('unauthorized', $responseArray['content']['error']['message']);
        $this->assertEquals($responseErrorData['error']['errors'], $responseArray['content']['error']['errors']);
    }
}
