<?php

namespace App\Tests\Manager;

use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use App\Helper\ErrorResponse;
use App\Helper\SuccessResponse;
use App\Helper\WSResponse;
use App\Manager\ConsentArbitrationManager;
use App\Model\ConsentArbitrationModel;
use App\Service\ConsentArbitrationService;

class ConsentArbitrationManagerTest extends TestCase
{
    public function testSaveConsentArbitration(): void
    {
        $consentArbitrationModel = new ConsentArbitrationModel(
            '032ff177af82467ea6ddfaecffe33ca1',
            '12345678901234567',
            true,
            'JE',
            'AT',
            'WEB',
            null
        );
        $consentArbitrationMockService = $this->createMock(ConsentArbitrationService::class);
        $consentArbitrationModel = $this->createMock(ConsentArbitrationModel::class);
        $validator = $this->createMock(ValidatorInterface::class);
        $logger = $this->createMock(LoggerInterface::class);
        $consentArbitrationMockService->expects($this->once())
            ->method('saveConsentArbitration')
            ->with($consentArbitrationModel)
            ->willReturn(new WSResponse(201, '{"insertedId":"6719fb9c36750eef1f265791"}'));
        
        
        $consentArbitrationManager = new ConsentArbitrationManager($validator, $consentArbitrationMockService);
        $consentArbitrationManager->setLogger($logger);
        $response = $consentArbitrationManager->saveConsentArbitration($consentArbitrationModel);

        $this->assertInstanceOf(SuccessResponse::class, $response);

    }
}