<?php

namespace App\Tests\Manager;

use App\Helper\ErrorResponse;
use App\Helper\SuccessResponse;
use App\Helper\WSResponse;
use App\Manager\ProfileManager;
use App\Manager\UserManager;
use App\Model\ProfileModel;
use App\Service\ProfileService;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class ProfileManagerTest extends TestCase
{
    private $validator;
    private $profileService;
    private $userManager;
    private $profileManager;
    private $logger;

    protected function setUp(): void
    {
        $this->validator = $this->createMock(ValidatorInterface::class);
        $this->profileService = $this->createMock(ProfileService::class);
        $this->userManager = $this->createMock(UserManager::class);

        $this->profileManager = new ProfileManager(
            $this->validator,
            $this->profileService,
            $this->userManager
        );

        // Set logger using reflection since it's injected via a trait
        $this->logger = $this->createMock(LoggerInterface::class);
        $reflection = new \ReflectionClass($this->profileManager);
        $loggerProperty = $reflection->getProperty('logger');
        $loggerProperty->setAccessible(true);
        $loggerProperty->setValue($this->profileManager, $this->logger);
    }

    public function testGetUserDataSuccess(): void
    {
        // Test data
        $userId = 'user123';
        $userDbId = 'db456';
        $userData = [
            'userDbId' => $userDbId,
            'userId' => $userId
        ];

        $serviceResponseData = [
            'success' => [
                'firstName' => 'John',
                'lastName' => 'Doe',
                'civility' => 'Mr',
                'geopoint' => '123 Main St',
                'town' => 'Paris',
                'countryCode' => 'FR',
                'postCode' => '75001',
                'phoneNumber' => '123456789',
                'street' => 'Apt 4B',
                'primaryEmail' => '<EMAIL>'
            ]
        ];

        // Expected mapped response
        $expectedMappedData = [
            'firstName' => 'John',
            'lastName' => 'Doe',
            'civility' => 'Mr',
            'address1' => '123 Main St',
            'city' => 'Paris',
            'country' => 'FR',
            'zipcode' => '75001',
            'phone' => '123456789',
            'address2' => 'Apt 4B',
            'email' => '<EMAIL>'
        ];

        // Set up expectations for the user manager
        $this->userManager->expects($this->once())
            ->method('getUserByUserId')
            ->with($userId)
            ->willReturn($userData);

        // Mock the service response
        $serviceResponse = $this->createMock(WSResponse::class);
        $serviceResponse->method('getCode')->willReturn(Response::HTTP_OK);
        $serviceResponse->method('getData')->willReturn($serviceResponseData);

        // Set up expectations for the profile service
        $this->profileService->expects($this->once())
            ->method('getUserData')
            ->with($userDbId)
            ->willReturn($serviceResponse);

        // Call the method
        $response = $this->profileManager->getUserData($userId);

        // Assert the response
        $this->assertInstanceOf(SuccessResponse::class, $response);
        $responseArray = $response->toArray();
        $this->assertEquals(Response::HTTP_OK, $responseArray['code']);
        $this->assertEquals(['success' => $expectedMappedData], $responseArray['content']);
    }

    public function testGetUserDataUserNotFound(): void
    {
        // Test data
        $userId = 'user123';

        // Set up expectations for the user manager - user not found
        $this->userManager->expects($this->once())
            ->method('getUserByUserId')
            ->with($userId)
            ->willReturn([]);

        // The profile service should not be called
        $this->profileService->expects($this->never())
            ->method('getUserData');

        // Call the method
        $response = $this->profileManager->getUserData($userId);

        // Assert the response
        $this->assertInstanceOf(ErrorResponse::class, $response);
        $responseArray = $response->toArray();
        $this->assertEquals(Response::HTTP_NOT_FOUND, $responseArray['code']);
        $this->assertEquals('customer not found', $responseArray['content']['error']['message']);
    }

    public function testGetUserDataServiceError(): void
    {
        // Test data
        $userId = 'user123';
        $userDbId = 'db456';
        $userData = [
            'userDbId' => $userDbId,
            'userId' => $userId
        ];

        // Set up expectations for the user manager
        $this->userManager->expects($this->once())
            ->method('getUserByUserId')
            ->with($userId)
            ->willReturn($userData);

        // Mock the service response with an error
        $serviceResponse = $this->createMock(WSResponse::class);
        $serviceResponse->method('getCode')->willReturn(Response::HTTP_INTERNAL_SERVER_ERROR);
        $serviceResponse->method('getData')->willReturn('Service error');

        // Set up expectations for the profile service
        $this->profileService->expects($this->once())
            ->method('getUserData')
            ->with($userDbId)
            ->willReturn($serviceResponse);

        // Call the method
        $response = $this->profileManager->getUserData($userId);

        // Assert the response
        $this->assertInstanceOf(ErrorResponse::class, $response);
        $responseArray = $response->toArray();
        $this->assertEquals(Response::HTTP_INTERNAL_SERVER_ERROR, $responseArray['code']);
        $this->assertEquals('Service error', $responseArray['content']['error']['message']);
    }

    public function testPutUserDataSuccess(): void
    {
        // Test data
        $userId = 'user123';
        $userDbId = 'db456';
        $userData = [
            'userDbId' => $userDbId,
            'userId' => $userId
        ];

        $inputData = [
            'firstName' => 'John',
            'lastName' => 'Doe',
            'civility' => 'Mr',
            'address1' => '123 Main St',
            'city' => 'Paris',
            'country' => 'FR',
            'zipcode' => '75001',
            'phone' => '123456789',
            'address2' => 'Apt 4B',
            'email' => '<EMAIL>'
        ];

        $expectedServiceData = [
            'firstName' => 'John',
            'lastName' => 'Doe',
            'civility' => 'Mr',
            'address1' => '123 Main St',
            'city' => 'Paris',
            'country' => 'FR',
            'zipcode' => '75001',
            'phone' => '123456789',
            'address2' => 'Apt 4B',
            'email' => '<EMAIL>',
            'postCode' => '75001',
            'geopoint' => '123 Main St',
            'town' => 'Paris',
            'countryCode' => 'FR',
            'phoneNumber' => '123456789',
            'primaryEmail' => '<EMAIL>',
            'street' => 'Apt 4B'
        ];

        $serviceResponseData = [
            'success' => [
                'firstName' => 'John',
                'lastName' => 'Doe',
                'civility' => 'Mr',
                'geopoint' => '123 Main St',
                'town' => 'Paris',
                'countryCode' => 'FR',
                'postCode' => '75001',
                'phoneNumber' => '123456789',
                'street' => 'Apt 4B',
                'primaryEmail' => '<EMAIL>'
            ]
        ];

        // Expected mapped response
        $expectedMappedData = [
            'firstName' => 'John',
            'lastName' => 'Doe',
            'civility' => 'Mr',
            'address1' => '123 Main St',
            'city' => 'Paris',
            'country' => 'FR',
            'zipcode' => '75001',
            'phone' => '123456789',
            'address2' => 'Apt 4B',
            'email' => '<EMAIL>'
        ];

        // Set up expectations for the user manager
        $this->userManager->expects($this->once())
            ->method('getUserByUserId')
            ->with($userId)
            ->willReturn($userData);

        // Mock the service response
        $serviceResponse = $this->createMock(WSResponse::class);
        $serviceResponse->method('getCode')->willReturn(Response::HTTP_OK);
        $serviceResponse->method('getData')->willReturn($serviceResponseData);

        // Set up expectations for the profile service
        $this->profileService->expects($this->once())
            ->method('putUserData')
            ->with($userDbId, $this->callback(function($data) use ($expectedServiceData) {
                // Check that all expected keys are present
                foreach ($expectedServiceData as $key => $value) {
                    if (!isset($data[$key]) || $data[$key] !== $value) {
                        return false;
                    }
                }
                return true;
            }))
            ->willReturn($serviceResponse);

        // Set up expectations for updating the user
        $this->userManager->expects($this->once())
            ->method('updateUserByUserDbId')
            ->with($userDbId, $inputData);

        // Call the method
        $response = $this->profileManager->putUserData($userId, $inputData);

        // Assert the response
        $this->assertInstanceOf(SuccessResponse::class, $response);
        $responseArray = $response->toArray();
        $this->assertEquals(Response::HTTP_OK, $responseArray['code']);
        $this->assertEquals(['success' => $expectedMappedData], $responseArray['content']);
    }

    public function testPutUserDataUserNotFound(): void
    {
        // Test data
        $userId = 'user123';
        $inputData = [
            'firstName' => 'John',
            'lastName' => 'Doe'
        ];

        // Set up expectations for the user manager - user not found
        $this->userManager->expects($this->once())
            ->method('getUserByUserId')
            ->with($userId)
            ->willReturn([]);

        // The profile service should not be called
        $this->profileService->expects($this->never())
            ->method('putUserData');

        // Call the method
        $response = $this->profileManager->putUserData($userId, $inputData);

        // Assert the response
        $this->assertInstanceOf(ErrorResponse::class, $response);
        $responseArray = $response->toArray();
        $this->assertEquals(Response::HTTP_NOT_FOUND, $responseArray['code']);
        $this->assertEquals('customer not found', $responseArray['content']['error']['message']);
    }

    public function testPutUserDataServiceError(): void
    {
        // Test data
        $userId = 'user123';
        $userDbId = 'db456';
        $userData = [
            'userDbId' => $userDbId,
            'userId' => $userId
        ];

        $inputData = [
            'firstName' => 'John',
            'lastName' => 'Doe',
            'address1' => '123 Main St',
            'city' => 'Paris',
            'country' => 'FR',
            'zipcode' => '75001',
            'phone' => '123456789',
            'address2' => 'Apt 4B',
            'email' => '<EMAIL>'
        ];

        // Set up expectations for the user manager
        $this->userManager->expects($this->once())
            ->method('getUserByUserId')
            ->with($userId)
            ->willReturn($userData);

        // Mock the service response with an error
        $serviceResponse = $this->createMock(WSResponse::class);
        $serviceResponse->method('getCode')->willReturn(Response::HTTP_INTERNAL_SERVER_ERROR);
        $serviceResponse->method('getData')->willReturn('Service error');

        // Set up expectations for the profile service
        $this->profileService->expects($this->once())
            ->method('putUserData')
            ->willReturn($serviceResponse);

        // The user should not be updated
        $this->userManager->expects($this->never())
            ->method('updateUserByUserDbId');

        // Call the method
        $response = $this->profileManager->putUserData($userId, $inputData);

        // Assert the response
        $this->assertInstanceOf(ErrorResponse::class, $response);
        $responseArray = $response->toArray();
        $this->assertEquals(Response::HTTP_INTERNAL_SERVER_ERROR, $responseArray['code']);
        $this->assertEquals('Service error', $responseArray['content']['error']['message']);
    }

    public function testMappingUserDataResponse(): void
    {
        // Test data
        $serviceData = [
            'firstName' => 'John',
            'lastName' => 'Doe',
            'civility' => 'Mr',
            'geopoint' => '123 Main St',
            'town' => 'Paris',
            'countryCode' => 'FR',
            'postCode' => '75001',
            'phoneNumber' => '123456789',
            'street' => 'Apt 4B',
            'primaryEmail' => '<EMAIL>'
        ];

        // Expected mapped data
        $expectedMappedData = [
            'firstName' => 'John',
            'lastName' => 'Doe',
            'civility' => 'Mr',
            'address1' => '123 Main St',
            'city' => 'Paris',
            'country' => 'FR',
            'zipcode' => '75001',
            'phone' => '123456789',
            'address2' => 'Apt 4B',
            'email' => '<EMAIL>'
        ];

        // Use reflection to access private method
        $reflection = new \ReflectionClass($this->profileManager);
        $method = $reflection->getMethod('mappingUserDataResponse');
        $method->setAccessible(true);

        // Call the method
        $result = $method->invoke($this->profileManager, $serviceData);

        // Assert the result
        $this->assertEquals($expectedMappedData, $result);
    }

    public function testMappingUserDataResponseWithEmail(): void
    {
        // Test data with email instead of primaryEmail
        $serviceData = [
            'firstName' => 'John',
            'lastName' => 'Doe',
            'email' => '<EMAIL>'
        ];

        // Expected mapped data
        $expectedMappedData = [
            'firstName' => 'John',
            'lastName' => 'Doe',
            'civility' => '',
            'address1' => '',
            'city' => '',
            'country' => '',
            'zipcode' => '',
            'phone' => '',
            'address2' => '',
            'email' => '<EMAIL>'
        ];

        // Use reflection to access private method
        $reflection = new \ReflectionClass($this->profileManager);
        $method = $reflection->getMethod('mappingUserDataResponse');
        $method->setAccessible(true);

        // Call the method
        $result = $method->invoke($this->profileManager, $serviceData);

        // Assert the result
        $this->assertEquals($expectedMappedData, $result);
    }

    public function testMappingUserDataResponseWithEmptyData(): void
    {
        // Test with empty data
        $serviceData = [];

        // Expected mapped data - all fields should be empty strings
        $expectedMappedData = [
            'firstName' => '',
            'lastName' => '',
            'civility' => '',
            'address1' => '',
            'city' => '',
            'country' => '',
            'zipcode' => '',
            'phone' => '',
            'address2' => '',
            'email' => ''
        ];

        // Use reflection to access private method
        $reflection = new \ReflectionClass($this->profileManager);
        $method = $reflection->getMethod('mappingUserDataResponse');
        $method->setAccessible(true);

        // Call the method
        $result = $method->invoke($this->profileManager, $serviceData);

        // Assert the result
        $this->assertEquals($expectedMappedData, $result);
    }
}
