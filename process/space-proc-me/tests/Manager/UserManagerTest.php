<?php

namespace App\Tests\Manager;

use App\Helper\WSResponse;
use App\Manager\UserManager;
use App\Service\UserService;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;

class UserManagerTest extends TestCase
{
    private $userService;
    private $userManager;
    private $logger;

    protected function setUp(): void
    {
        $this->userService = $this->createMock(UserService::class);
        $this->userManager = new UserManager($this->userService);
        
        // Set logger using reflection since it's injected via a trait
        $this->logger = $this->createMock(LoggerInterface::class);
        $reflection = new \ReflectionClass($this->userManager);
        $loggerProperty = $reflection->getProperty('logger');
        $loggerProperty->setAccessible(true);
        $loggerProperty->setValue($this->userManager, $this->logger);
    }

    public function testGetUserByUserIdSuccess(): void
    {
        // Test data
        $userId = 'user123';
        $userData = [
            'documents' => [
                [
                    'userDbId' => 'db456',
                    'userId' => $userId,
                    'profile' => [
                        'firstName' => 'John',
                        'lastName' => 'Doe'
                    ]
                ]
            ]
        ];
        
        // Mock the service response
        $serviceResponse = $this->createMock(WSResponse::class);
        $serviceResponse->method('getCode')->willReturn(Response::HTTP_OK);
        $serviceResponse->method('getData')->willReturn(json_encode($userData));
        
        // Set up expectations for the service
        $this->userService->expects($this->once())
            ->method('getUserByUserId')
            ->willReturn($serviceResponse);
        
        // Call the method
        $result = $this->userManager->getUserByUserId($userId);
        
        // Assert the result
        $this->assertEquals($userData['documents'][0], $result);
    }

    public function testGetUserByUserIdNoUser(): void
    {
        // Test data
        $userId = 'user123';
        $userData = [
            'documents' => []
        ];
        
        // Mock the service response
        $serviceResponse = $this->createMock(WSResponse::class);
        $serviceResponse->method('getCode')->willReturn(Response::HTTP_OK);
        $serviceResponse->method('getData')->willReturn(json_encode($userData));
        
        // Set up expectations for the service
        $this->userService->expects($this->once())
            ->method('getUserByUserId')
            ->willReturn($serviceResponse);
        
        // Call the method
        $result = $this->userManager->getUserByUserId($userId);
        
        // Assert the result
        $this->assertEquals([], $result);
    }

    public function testGetUserByUserIdError(): void
    {
        // Test data
        $userId = 'user123';
        
        // Mock the service response
        $serviceResponse = $this->createMock(WSResponse::class);
        $serviceResponse->method('getCode')->willReturn(Response::HTTP_INTERNAL_SERVER_ERROR);
        
        // Set up expectations for the service
        $this->userService->expects($this->once())
            ->method('getUserByUserId')
            ->willReturn($serviceResponse);
        
        // Call the method
        $result = $this->userManager->getUserByUserId($userId);
        
        // Assert the result
        $this->assertEquals([], $result);
    }

    public function testUpdateUserByUserDbId(): void
    {
        // Test data
        $userDbId = 'db456';
        $inputData = [
            'firstName' => 'John',
            'lastName' => 'Doe',
            'email' => '<EMAIL>',
            'civility' => 'Mr',
            'phone' => '123456789',
            'address1' => '123 Main St',
            'address2' => 'Apt 4B',
            'city' => 'Paris',
            'zip' => '75001',
            'country' => 'FR'
        ];
        
        $expectedUpdate = [
            'profile.firstName' => 'John',
            'profile.lastName' => 'Doe',
            'profile.email' => '<EMAIL>',
            'profile.title' => 'Mr',
            'profile.phone' => '123456789',
            'profile.address1' => '123 Main St',
            'profile.address2' => 'Apt 4B',
            'profile.city' => 'Paris',
            'profile.zip' => '75001',
            'profile.country' => 'FR'
        ];
        
        // Mock the service response
        $serviceResponse = $this->createMock(WSResponse::class);
        $serviceResponse->method('getCode')->willReturn(Response::HTTP_OK);
        
        // Set up expectations for the service
        $this->userService->expects($this->once())
            ->method('updateUserByUserDbId')
            ->with($userDbId, $this->callback(function($data) use ($expectedUpdate) {
                // Check that all expected keys are present
                foreach ($expectedUpdate as $key => $value) {
                    if (!isset($data[$key]) || $data[$key] !== $value) {
                        return false;
                    }
                }
                return true;
            }))
            ->willReturn($serviceResponse);
        
        // Call the method
        $result = $this->userManager->updateUserByUserDbId($userDbId, $inputData);
        
        // Assert the result
        $this->assertSame($serviceResponse, $result);
    }

    public function testUpdateUserByUserDbIdWithEmptyValues(): void
    {
        // Test data
        $userDbId = 'db456';
        $inputData = [
            'firstName' => 'John',
            'lastName' => 'Doe',
            'email' => '<EMAIL>',
            'civility' => '',  // Empty civility
            'phone' => '',     // Empty phone
            'address1' => null // Null address1
        ];
        
        $expectedUpdate = [
            'profile.firstName' => 'John',
            'profile.lastName' => 'Doe',
            'profile.email' => '<EMAIL>'
        ];
        
        // Mock the service response
        $serviceResponse = $this->createMock(WSResponse::class);
        $serviceResponse->method('getCode')->willReturn(Response::HTTP_OK);
        
        // Set up expectations for the service
        $this->userService->expects($this->once())
            ->method('updateUserByUserDbId')
            ->with($userDbId, $this->callback(function($data) use ($expectedUpdate) {
                // Check that all expected keys are present
                foreach ($expectedUpdate as $key => $value) {
                    if (!isset($data[$key]) || $data[$key] !== $value) {
                        return false;
                    }
                }
                // Check that empty/null values are not included
                if (isset($data['profile.title']) || isset($data['profile.phone']) || isset($data['profile.address1'])) {
                    return false;
                }
                return true;
            }))
            ->willReturn($serviceResponse);
        
        // Call the method
        $result = $this->userManager->updateUserByUserDbId($userDbId, $inputData);
        
        // Assert the result
        $this->assertSame($serviceResponse, $result);
    }

    public function testCreateUser(): void
    {
        // Test data
        $inputData = [
            'customerId' => 'cust123',
            'idp_Gigya_id' => 'gigya123',
            'civility' => 'Mr',
            'email' => '<EMAIL>',
            'firstName' => 'John',
            'lastName' => 'Doe',
            'phoneNumber' => '123456789',
            'geopoint' => '123 Main St',
            'street' => 'Apt 4B',
            'town' => 'Paris',
            'postCode' => '75001',
            'countryCode' => 'FR'
        ];
        
        $expectedUserData = [
            'userDbId' => 'cust123',
            'userId' => 'gigya123',
            'userPsaId' => null,
            'profile' => [
                'title' => 'Mr',
                'email' => '<EMAIL>',
                'firstName' => 'John',
                'lastName' => 'Doe',
                'phone' => '123456789',
                'address1' => '123 Main St',
                'address2' => 'Apt 4B',
                'city' => 'Paris',
                'zip' => '75001',
                'country' => 'FR'
            ]
        ];
        
        // Mock the service response
        $serviceResponse = $this->createMock(WSResponse::class);
        $serviceResponse->method('getCode')->willReturn(Response::HTTP_OK);
        
        // Set up expectations for the service
        $this->userService->expects($this->once())
            ->method('createUser')
            ->with($this->callback(function($data) use ($expectedUserData) {
                // Check that all expected keys are present
                return $data == $expectedUserData;
            }))
            ->willReturn($serviceResponse);
        
        // Call the method
        $result = $this->userManager->createUser($inputData);
        
        // Assert the result
        $this->assertSame($serviceResponse, $result);
    }

    public function testCreateUserWithMissingData(): void
    {
        // Test data with missing fields
        $inputData = [
            'customerId' => 'cust123',
            'idp_Gigya_id' => 'gigya123',
            'firstName' => 'John',
            'lastName' => 'Doe'
        ];
        
        $expectedUserData = [
            'userDbId' => 'cust123',
            'userId' => 'gigya123',
            'userPsaId' => null,
            'profile' => [
                'title' => '',
                'email' => '',
                'firstName' => 'John',
                'lastName' => 'Doe',
                'phone' => '',
                'address1' => '',
                'address2' => '',
                'city' => '',
                'zip' => '',
                'country' => ''
            ]
        ];
        
        // Mock the service response
        $serviceResponse = $this->createMock(WSResponse::class);
        $serviceResponse->method('getCode')->willReturn(Response::HTTP_OK);
        
        // Set up expectations for the service
        $this->userService->expects($this->once())
            ->method('createUser')
            ->with($this->callback(function($data) use ($expectedUserData) {
                // Check that all expected keys are present
                return $data == $expectedUserData;
            }))
            ->willReturn($serviceResponse);
        
        // Call the method
        $result = $this->userManager->createUser($inputData);
        
        // Assert the result
        $this->assertSame($serviceResponse, $result);
    }
}
