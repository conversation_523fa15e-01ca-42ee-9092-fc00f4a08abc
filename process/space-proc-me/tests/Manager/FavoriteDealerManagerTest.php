<?php

namespace App\Tests\Manager;

use App\Helper\ErrorResponse;
use App\Helper\SuccessResponse;
use App\Helper\WSResponse;
use App\Manager\FavoriteDealerManager;
use App\Service\FavoriteDealerService;
use App\Service\MongoAtlasQueryService;
use App\Transformer\XfEmeaResponseTransformer;
use App\Transformer\XpResponseTransformer;
use App\Transformer\xfEmeaParameterRequestTransformer;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\Normalizer\ObjectNormalizer;
use Symfony\Component\Serializer\Serializer;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class FavoriteDealerManagerTest extends TestCase
{
    private $validator;
    private $mongoAtlasQueryService;
    private $favoriteDealerService;
    private $xpResponseTransformer;
    private $mongoService;
    private $favoriteDealerManager;
    private $logger;

    protected function setUp(): void
    {
        $this->validator = $this->createMock(ValidatorInterface::class);
        $this->mongoAtlasQueryService = $this->createMock(MongoAtlasQueryService::class);
        $this->favoriteDealerService = $this->createMock(FavoriteDealerService::class);
        $this->xpResponseTransformer = $this->createMock(XpResponseTransformer::class);
        $this->mongoService = $this->createMock(MongoAtlasQueryService::class);

        $this->favoriteDealerManager = new FavoriteDealerManager(
            $this->validator,
            $this->mongoAtlasQueryService,
            $this->favoriteDealerService,
            $this->xpResponseTransformer,
            $this->mongoService
        );

        // Set logger using reflection since it's injected via a trait
        $this->logger = $this->createMock(LoggerInterface::class);
        $reflection = new \ReflectionClass($this->favoriteDealerManager);
        $loggerProperty = $reflection->getProperty('logger');
        $loggerProperty->setAccessible(true);
        $loggerProperty->setValue($this->favoriteDealerManager, $this->logger);
    }

    public function testSaveFavoriteDealerXP(): void
    {
        // Test data for XP brand
        $params = [
            'brand' => 'DS',
            'country' => 'FR',
            'language' => 'fr',
            'siteGeo' => 'DEALER123',
            'userId' => 'user123'
        ];

        // Mock the MongoDB response
        $mongoResponse = new WSResponse(Response::HTTP_OK, json_encode([
            'matchedCount' => 1,
            'modifiedCount' => 1
        ]));

        // Set up expectations for the MongoDB service
        $this->mongoAtlasQueryService->expects($this->once())
            ->method('updateOne')
            ->with(
                'userData',
                ['userId' => 'user123'],
                ['preferredDealer.ds' => [
                    'country' => 'FR',
                    'idSiteGeo' => 'DEALER123',
                    'language' => 'fr'
                ]]
            )
            ->willReturn($mongoResponse);

        // Call the method
        $response = $this->favoriteDealerManager->saveFavoriteDealer($params);

        // Assert the response
        $this->assertInstanceOf(SuccessResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
    }

    public function testSaveFavoriteDealerXPFailure(): void
    {
        // Test data for XP brand
        $params = [
            'brand' => 'DS',
            'country' => 'FR',
            'language' => 'fr',
            'siteGeo' => 'DEALER123',
            'userId' => 'user123'
        ];

        // Mock the MongoDB response with no matched documents
        $mongoResponse = new WSResponse(Response::HTTP_OK, json_encode([
            'matchedCount' => 0,
            'modifiedCount' => 0
        ]));

        // Set up expectations for the MongoDB service
        $this->mongoAtlasQueryService->expects($this->once())
            ->method('updateOne')
            ->willReturn($mongoResponse);

        // Call the method
        $response = $this->favoriteDealerManager->saveFavoriteDealer($params);

        // Assert the response
        $this->assertInstanceOf(ErrorResponse::class, $response);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getCode());
    }

    public function testSaveFavoriteDealerXF(): void
    {
        $this->markTestSkipped('Cannot mock static methods');
        // Test data for XF brand
        $params = [
            'brand' => 'FI',
            'country' => 'IT',
            'siteGeo' => 'DEALER456',
            'userId' => 'user123',
            'vin' => 'VIN123456789'
        ];

        // Mock the MongoDB response for checking if VIN exists
        $vinCheckResponse = new WSResponse(Response::HTTP_OK, json_encode([
            'documents' => [['userId' => 'user123', 'vehicle' => [['vin' => 'VIN123456789']]]]
        ]));

        // Mock the MongoDB response for updating the dealer
        $updateResponse = new WSResponse(Response::HTTP_OK, json_encode([
            'matchedCount' => 1,
            'modifiedCount' => 1
        ]));

        // Set up expectations for the MongoDB service
        $this->mongoAtlasQueryService->expects($this->exactly(2))
            ->method('find')
            ->willReturn($vinCheckResponse);

        $this->mongoAtlasQueryService->expects($this->once())
            ->method('updateOne')
            ->with(
                'userData',
                ['userId' => 'user123', 'vehicle.vin' => 'VIN123456789'],
                ['vehicle.$.preferredDealer' => [
                    'country' => 'IT',
                    'idSiteGeo' => 'DEALER456'
                ]],
                true
            )
            ->willReturn($updateResponse);

        // Call the method
        $response = $this->favoriteDealerManager->saveFavoriteDealer($params);

        // Assert the response
        $this->assertInstanceOf(SuccessResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
    }

    public function testSaveFavoriteDealerXFVinNotInGarage(): void
    {
        // Test data for XF brand
        $params = [
            'brand' => 'FI',
            'country' => 'IT',
            'siteGeo' => 'DEALER456',
            'userId' => 'user123',
            'vin' => 'VIN123456789'
        ];

        // Mock the MongoDB response for checking if VIN exists - empty result
        $vinCheckResponse = new WSResponse(Response::HTTP_OK, json_encode([
            'documents' => []
        ]));

        // Set up expectations for the MongoDB service
        $this->mongoAtlasQueryService->expects($this->once())
            ->method('find')
            ->willReturn($vinCheckResponse);

        // The updateOne method should not be called
        $this->mongoAtlasQueryService->expects($this->never())
            ->method('updateOne');

        // Call the method
        $response = $this->favoriteDealerManager->saveFavoriteDealer($params);

        // Assert the response
        $this->assertInstanceOf(ErrorResponse::class, $response);
        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getCode());
    }

    public function testDeleteFavoriteDealerXP(): void
    {
        // Test data for XP brand
        $params = [
            'brand' => 'DS',
            'country' => 'FR',
            'userId' => 'user123'
        ];

        // Mock the MongoDB response
        $mongoResponse = new WSResponse(Response::HTTP_OK, json_encode([
            'matchedCount' => 1,
            'modifiedCount' => 1
        ]));

        // Set up expectations for the MongoDB service
        $this->mongoAtlasQueryService->expects($this->once())
            ->method('updateOne')
            ->with(
                'userData',
                ['userId' => 'user123'],
                ['preferredDealer.ds' => null]
            )
            ->willReturn($mongoResponse);

        // Call the method
        $response = $this->favoriteDealerManager->deleteFavoriteDealer($params);

        // Assert the response
        $this->assertInstanceOf(SuccessResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
    }

    public function testDeleteFavoriteDealerXF(): void
    {
        $this->markTestSkipped('Cannot mock static methods');
        // Test data for XF brand
        $params = [
            'brand' => 'FI',
            'country' => 'IT',
            'userId' => 'user123',
            'vin' => 'VIN123456789'
        ];

        // Mock the MongoDB response for checking if VIN exists
        $vinCheckResponse = new WSResponse(Response::HTTP_OK, json_encode([
            'documents' => [['userId' => 'user123', 'vehicle' => [['vin' => 'VIN123456789']]]]
        ]));

        // Mock the MongoDB response for updating the dealer
        $updateResponse = new WSResponse(Response::HTTP_OK, json_encode([
            'matchedCount' => 1,
            'modifiedCount' => 1
        ]));

        // Set up expectations for the MongoDB service
        $this->mongoAtlasQueryService->expects($this->exactly(2))
            ->method('find')
            ->willReturn($vinCheckResponse);

        $this->mongoAtlasQueryService->expects($this->once())
            ->method('updateOne')
            ->with(
                'userData',
                ['userId' => 'user123', 'vehicle.vin' => 'VIN123456789'],
                ['vehicle.$.preferredDealer' => null],
                true
            )
            ->willReturn($updateResponse);

        // Call the method
        $response = $this->favoriteDealerManager->deleteFavoriteDealer($params);

        // Assert the response
        $this->assertInstanceOf(SuccessResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
    }

    public function testGetFavoriteDealerXP(): void
    {
        $this->markTestSkipped('Cannot mock static methods');
        // Test data for XP brand
        $params = [
            'brand' => 'DS',
            'country' => 'FR',
            'userId' => 'user123',
            'source' => 'WEB'
        ];

        // Mock the MongoDB response for finding the user
        $findResponse = new WSResponse(Response::HTTP_OK, json_encode([
            'documents' => [[
                'userId' => 'user123',
                'preferredDealer' => [
                    'ds' => [
                        'country' => 'FR',
                        'language' => 'fr',
                        'idSiteGeo' => 'DEALER123'
                    ]
                ]
            ]]
        ]));

        // Mock the dealer service response
        $dealerResponse = new WSResponse(Response::HTTP_OK, json_encode([
            'success' => [
                'dealer' => [
                    'id' => 'DEALER123',
                    'name' => 'Test Dealer',
                    'address' => '123 Test Street'
                ]
            ]
        ]));

        // Mock the settings response
        $settingsResponse = new WSResponse(Response::HTTP_OK, json_encode([
            'documents' => [[
                'settingsData' => [
                    'o2x' => [
                        'enabled' => true
                    ]
                ]
            ]]
        ]));

        // Set up expectations for the MongoDB service
        $this->mongoAtlasQueryService->expects($this->once())
            ->method('find')
            ->with('userData', ['userId' => 'user123'])
            ->willReturn($findResponse);

        // Set up expectations for the dealer service
        $this->favoriteDealerService->expects($this->once())
            ->method('getXpDealerDetails')
            ->willReturn($dealerResponse);

        // Set up expectations for the MongoDB service for settings
        $this->mongoService->expects($this->once())
            ->method('find')
            ->willReturn($settingsResponse);

        // Set up expectations for the transformer
        $mockDealerModel = new \App\Model\DealerV2Model();
        $mockDealerModel->setSiteGeo('DEALER123');
        $mockDealerModel->setName('Test Dealer');

        $this->xpResponseTransformer->expects($this->once())
            ->method('mapper')
            ->willReturn($mockDealerModel);

        // Call the method
        $response = $this->favoriteDealerManager->getFavoriteDealer($params);

        // Assert the response
        $this->assertInstanceOf(SuccessResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
    }

    public function testGetFavoriteDealerXPNoDealer(): void
    {
        // Test data for XP brand
        $params = [
            'brand' => 'DS',
            'country' => 'FR',
            'userId' => 'user123',
            'source' => 'WEB'
        ];

        // Mock the MongoDB response for finding the user - no preferred dealer
        $findResponse = new WSResponse(Response::HTTP_OK, json_encode([
            'documents' => [[
                'userId' => 'user123',
                'preferredDealer' => []
            ]]
        ]));

        // Set up expectations for the MongoDB service
        $this->mongoAtlasQueryService->expects($this->once())
            ->method('find')
            ->with('userData', ['userId' => 'user123'])
            ->willReturn($findResponse);

        // The dealer service should not be called
        $this->favoriteDealerService->expects($this->never())
            ->method('getXpDealerDetails');

        // Call the method
        $response = $this->favoriteDealerManager->getFavoriteDealer($params);

        // Assert the response
        $this->assertInstanceOf(ErrorResponse::class, $response);
        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getCode());
    }

    public function testGetFavoriteDealerXF(): void
    {
        // Test data for XF brand
        $params = [
            'brand' => 'FI',
            'country' => 'IT',
            'userId' => 'user123',
            'vin' => 'VIN123456789',
            'source' => 'WEB'
        ];

        // Mock the MongoDB response for aggregation
        $aggregateResponse = new WSResponse(Response::HTTP_OK, json_encode([
            'documents' => [[
                'vehicle' => [[
                    'vin' => 'VIN123456789',
                    'preferredDealer' => [
                        'country' => 'IT',
                        'idSiteGeo' => 'DEALER456'
                    ]
                ]]
            ]]
        ]));

        // Mock the dealer service response
        $dealerResponse = new WSResponse(Response::HTTP_OK, json_encode([
            'success' => [
                'dealer' => [
                    'id' => 'DEALER456',
                    'name' => 'Test Dealer XF',
                    'address' => '456 Test Street'
                ]
            ]
        ]));

        // Set up expectations for the MongoDB service
        $this->mongoService->expects($this->once())
            ->method('aggregate')
            ->willReturn($aggregateResponse);

        // Set up expectations for the dealer service
        $this->favoriteDealerService->expects($this->once())
            ->method('getXfDealerDetails')
            ->willReturn($dealerResponse);

        // Create a mock DealerResponseDto
        $mockDealerModel = new \App\Model\DealerV2Model();
        $mockDealerModel->setSiteGeo('DEALER456');
        $mockDealerModel->setName('Test Dealer XF');

        $mockDealerResponseDto = new \App\DtoResponse\DealerResponseDto();
        $mockDealerResponseDto->addDealer($mockDealerModel);

        // Skip this test as we can't easily mock static methods
        $this->markTestSkipped('Cannot mock static method XfEmeaResponseTransformer::mapper');

        // Call the method
        $response = $this->favoriteDealerManager->getFavoriteDealer($params);

        // Assert the response
        $this->assertInstanceOf(SuccessResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
    }

    public function testGetFavoriteDealerXFNoVehicle(): void
    {
        // Test data for XF brand
        $params = [
            'brand' => 'FI',
            'country' => 'IT',
            'userId' => 'user123',
            'vin' => 'VIN123456789',
            'source' => 'WEB'
        ];

        // Mock the MongoDB response for aggregation - no vehicle found
        $aggregateResponse = new WSResponse(Response::HTTP_OK, json_encode([
            'documents' => []
        ]));

        // Set up expectations for the MongoDB service
        $this->mongoService->expects($this->once())
            ->method('aggregate')
            ->willReturn($aggregateResponse);

        // The dealer service should not be called
        $this->favoriteDealerService->expects($this->never())
            ->method('getXfDealerDetails');

        // Call the method
        $response = $this->favoriteDealerManager->getFavoriteDealer($params);

        // Assert the response
        $this->assertInstanceOf(ErrorResponse::class, $response);
        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getCode());
    }

    public function testCheckIfVinExistsInGarage(): void
    {
        // Use reflection to access private method
        $reflection = new \ReflectionClass($this->favoriteDealerManager);
        $method = $reflection->getMethod('checkIfVinExistsInGarage');
        $method->setAccessible(true);

        // Test data
        $filter = [
            'userId' => 'user123',
            'vehicle.vin' => 'VIN123456789'
        ];

        // Mock the MongoDB response - VIN exists
        $findResponse = new WSResponse(Response::HTTP_OK, json_encode([
            'documents' => [[
                'userId' => 'user123',
                'vehicle' => [['vin' => 'VIN123456789']]
            ]]
        ]));

        // Set up expectations for the MongoDB service
        $this->mongoAtlasQueryService->expects($this->once())
            ->method('find')
            ->with('userData', $filter)
            ->willReturn($findResponse);

        // Call the method
        $result = $method->invoke($this->favoriteDealerManager, $filter);

        // Assert the result
        $this->assertTrue($result);
    }

    public function testCheckIfVinExistsInGarageNotFound(): void
    {
        // Use reflection to access private method
        $reflection = new \ReflectionClass($this->favoriteDealerManager);
        $method = $reflection->getMethod('checkIfVinExistsInGarage');
        $method->setAccessible(true);

        // Test data
        $filter = [
            'userId' => 'user123',
            'vehicle.vin' => 'VIN123456789'
        ];

        // Mock the MongoDB response - VIN does not exist
        $findResponse = new WSResponse(Response::HTTP_OK, json_encode([
            'documents' => []
        ]));

        // Set up expectations for the MongoDB service
        $this->mongoAtlasQueryService->expects($this->once())
            ->method('find')
            ->with('userData', $filter)
            ->willReturn($findResponse);

        // Call the method
        $result = $method->invoke($this->favoriteDealerManager, $filter);

        // Assert the result
        $this->assertFalse($result);
    }

    public function testGetO2xSettings(): void
    {
        // Use reflection to access private method
        $reflection = new \ReflectionClass($this->favoriteDealerManager);
        $method = $reflection->getMethod('getO2xSettings');
        $method->setAccessible(true);

        // Test data
        $brand = 'DS';
        $source = 'WEB';

        // Mock the MongoDB response
        $findResponse = new WSResponse(Response::HTTP_OK, json_encode([
            'documents' => [[
                'settingsData' => [
                    'o2x' => [
                        'enabled' => true,
                        'config' => [
                            'showMap' => true
                        ]
                    ]
                ]
            ]]
        ]));

        // Set up expectations for the MongoDB service
        $this->mongoService->expects($this->once())
            ->method('find')
            ->with('settings', [
                'brand' => $brand,
                'source' => $source,
                'culture' => '',
                '$or' => [
                    ["settingsData.o2x.code" => "o2x"],
                    ["settingsData.config.code" => "o2x"]
                ]
            ])
            ->willReturn($findResponse);

        // Call the method
        $result = $method->invoke($this->favoriteDealerManager, $brand, $source);

        // Assert the result
        $this->assertIsArray($result);
        $this->assertArrayHasKey('enabled', $result);
        $this->assertTrue($result['enabled']);
    }
}
