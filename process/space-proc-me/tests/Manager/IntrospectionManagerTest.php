<?php

namespace App\Tests\Manager;

use App\Helper\ErrorResponse;
use App\Helper\SuccessResponse;
use App\Helper\WSResponse;
use App\Manager\IntrospectionManager;
use App\Service\IntrospectionService;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\SerializerInterface;

class IntrospectionManagerTest extends TestCase
{
    private IntrospectionManager $introspectionManager;
    private MockObject $introspectionService;
    private MockObject $profileService;
    private MockObject $userManager;
    private MockObject $logger;
    private MockObject $serializer;

    public function setUp(): void
    {
        $this->serializer = $this->createMock(SerializerInterface::class);
        $this->introspectionService = $this->createMock(IntrospectionService::class);
        $this->profileService = $this->createMock(\App\Service\ProfileService::class);
        $this->userManager = $this->createMock(\App\Manager\UserManager::class);

        $this->introspectionManager = new IntrospectionManager(
            $this->introspectionService,
            $this->serializer,
            $this->profileService,
            $this->userManager
        );

        $this->logger = $this->createMock(LoggerInterface::class);
        $this->logger->expects($this->any())
            ->method('info')
        ;
        $this->logger->expects($this->any())
            ->method('error')
        ;
        $this->introspectionManager->setLogger($this->logger);
    }

    public function testGetGigyaIdWithReadCase(): void
    {
        $userCvsToken = '59aa43a6-a15f-47e4-a2df-d80218d50129';
        $brand = 'AP';
        $userId = '123';
        $source = 'SPACEWEB';
        $device = $country = '';

        $this->introspectionService->expects($this->once())
            ->method('getUserCvsId')
            ->with($userCvsToken)
            ->willReturn(new WSResponse(200, $userId));
        $this->introspectionService->expects($this->once())
            ->method('readUserData')
            ->with($userId)
            ->willReturn(new WSResponse(200, '{"documents" : [{"userId":"123"}]}'));

        $response = $this->introspectionManager->getGigyaId($userCvsToken, $brand, $source, $device, $country);

        $this->assertInstanceOf(SuccessResponse::class, $response);
        $this->assertIsArray($response->getData());
        $this->assertEquals(Response::HTTP_OK, $response->toArray()['code']);
        $this->assertEquals(['userId' => '123'], $response->toArray()['content']['success']);
    }

    public function testGetGigyaIdWithAddCvsToUserCase(): void
    {
        $userCvsToken = '59aa43a6-a15f-47e4-a2df-d80218d50129';
        $brand = 'AP';
        $userCvsId = '8ac7afddade74d40a9d2a46a0d38982d';
        $source = 'SPACEWEB';
        $device = $country = '';

        $this->introspectionService->expects($this->once())
            ->method('getUserCvsId')
            ->with($userCvsToken)
            ->willReturn(new WSResponse(200, $userCvsId));
        $this->introspectionService->expects($this->once())
            ->method('getGigyaData')
            ->with($userCvsId)
            ->willReturn(new WSResponse(200, ['success' => ['userId' => '123']]));
        $this->introspectionService->expects($this->once())
            ->method('isUserExist')
            ->willReturn(true);
        $this->introspectionService->expects($this->once())
            ->method('addCvsToUser')
            ->willReturn(new WSResponse(200, ''));

        $response = $this->introspectionManager->getGigyaId($userCvsToken, $brand, $source, $device, $country);

        $this->assertInstanceOf(SuccessResponse::class, $response);
        $this->assertIsArray($response->getData());
        $this->assertEquals(Response::HTTP_OK, $response->toArray()['code']);
        $this->assertEquals(['success' => ['userId' => '123']], $response->toArray()['content']);
    }

    public function testGetGigyaIdWithAddCvsToUserCaseUnauthorized(): void
    {
        $userCvsToken = '59aa43a6-a15f-47e4-a2df-d80218d50129';
        $brand = 'AP';
        $userCvsId = '8ac7afddade74d40a9d2a46a0d38982d';
        $source = 'SPACEWEB';
        $device = $country = '';

        $this->introspectionService->expects($this->once())
            ->method('getUserCvsId')
            ->with($userCvsToken)
            ->willReturn(new WSResponse(200, $userCvsId));
        $this->introspectionService->expects($this->once())
            ->method('getGigyaData')
            ->with($userCvsId)
            ->willReturn(new WSResponse(200, ['success' => ['userId' => '123']]));
        $this->introspectionService->expects($this->once())
            ->method('isUserExist')
            ->willReturn(true);
        $this->introspectionService->expects($this->once())
            ->method('addCvsToUser')
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, ''));

        $response = $this->introspectionManager->getGigyaId($userCvsToken, $brand, $source, $device, $country);

        $this->assertInstanceOf(ErrorResponse::class, $response);
        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $response->toArray()['code']);
        $this->assertEquals('unauthorized', $response->toArray()['content']['error']['message']);
    }

    public function testGetGigyaIdWithCreateUserCase(): void
    {
        $userCvsToken = '59aa43a6-a15f-47e4-a2df-d80218d50129';
        $brand = 'AP';
        $userCvsId = '8ac7afddade74d40a9d2a46a0d38982d';
        $source = 'SPACEWEB';
        $device = $country = '';

        $this->introspectionService->expects($this->once())
            ->method('getUserCvsId')
            ->with($userCvsToken)
            ->willReturn(new WSResponse(200, $userCvsId));
        $this->introspectionService->expects($this->once())
            ->method('getGigyaData')
            ->with($userCvsId)
            ->willReturn(new WSResponse(200, ['success' => ['userId' => '123']]));
        $this->introspectionService->expects($this->once())
            ->method('createUser')
            ->willReturn(new WSResponse(200, ''));

        $response = $this->introspectionManager->getGigyaId($userCvsToken, $brand, $source, $device, $country);

        $this->assertInstanceOf(SuccessResponse::class, $response);
        $this->assertIsArray($response->getData());
        $this->assertEquals(Response::HTTP_OK, $response->toArray()['code']);
        $this->assertEquals(['success' => ['userId' => '123']], $response->toArray()['content']);
    }

    public function testGetGigyaIdWitUnauthorized(): void
    {
        $userCvsToken = '59aa43a6-a15f-47e4-a2df-d80218d50129';
        $brand = 'AP';
        $userId = '123';
        $source = 'SPACEWEB';
        $device = $country = '';

        $this->introspectionService->expects($this->once())
            ->method('getUserCvsId')
            ->with($userCvsToken)
            ->willReturn(new WSResponse(200, $userId));
        $this->introspectionService->expects($this->once())
            ->method('getGigyaData')
            ->with($userId)
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, ''));

        $response = $this->introspectionManager->getGigyaId($userCvsToken, $brand, $source, $device, $country);

        $this->assertInstanceOf(ErrorResponse::class, $response);
        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $response->toArray()['code']);
        $this->assertEquals('unauthorized', $response->toArray()['content']['error']['message']);
    }

    public function testGetGigyaIdUnauthorized(): void
    {
        $userCvsToken = '59aa43a6-a15f-47e4-a2df-d80218d50129';
        $brand = 'AP';
        $source = 'SPACEWEB';
        $device = $country = '';

        $this->introspectionService->expects($this->once())
            ->method('getUserCvsId')
            ->with($userCvsToken)
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, ''));

        $response = $this->introspectionManager->getGigyaId($userCvsToken, $brand, $source, $device, $country);

        $this->assertInstanceOf(ErrorResponse::class, $response);
        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $response->toArray()['code']);
        $this->assertEquals('unauthorized', $response->toArray()['content']['error']['message']);
    }

    public function testGetGigyaIdException(): void
    {
        $this->introspectionService->expects($this->once())
            ->method('getUserCvsId')
            ->willThrowException(new \Exception('API exception'));

        $response = $this->introspectionManager->getGigyaId('123', 'AP', 'SPACEWEB', '', '');
        $this->assertInstanceOf(ErrorResponse::class, $response);
    }
}
