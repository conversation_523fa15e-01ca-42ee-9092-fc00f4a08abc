<?php

namespace App\Tests\Trait;

use App\Trait\ValidationResponseTrait;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Validation;

class ValidationResponseTraitTest extends TestCase
{
    use ValidationResponseTrait;

    public function testGetValidationMessages(): void
    {
        $userCvsToken = '';
        $brand = '';
        $validator = Validation::createValidator();
        $errors = $validator->validate(
            compact('userCvsToken', 'brand'),
            new Assert\Collection([
                'userCvsToken'     => new Assert\NotBlank(),
                'brand' => new Assert\NotBlank(),
            ])
        );

        $messages = $this->getValidationMessages($errors);
        $expected = [
            'userCvsToken' => 'This value should not be blank.',
            'brand' => 'This value should not be blank.',
        ];

        static::assertEquals($expected, $messages);
        static::assertIsArray($messages);
    }

    public function testGetValidationErrorResponse(): void
    {
        $userCvsToken = '';
        $brand = '';
        $expected['error'] = [
            'message' => 'validation_failed',
            'errors' => [
                'userCvsToken' => 'This value should not be blank.',
                'brand' => 'This value should not be blank.',
            ],
        ];
        $validator = Validation::createValidator();
        $errors = $validator->validate(
            compact('userCvsToken', 'brand'),
            new Assert\Collection([
                'userCvsToken'     => new Assert\NotBlank(),
                'brand' => new Assert\NotBlank(),
            ])
        );

        $messages = $this->getValidationMessages($errors);
        $response = $this->getValidationErrorResponse($messages)->toArray();
        static::assertIsArray($response);
        static::assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response['code']);
        static::assertEquals($expected, $response['content']);
    }

    public function testGetValidationNoErrors(): void
    {
        $userCvsToken = 'test-token';
        $brand = 'AP';
        $validator = Validation::createValidator();
        $errors = $validator->validate(
            compact('userCvsToken', 'brand'),
            new Assert\Collection([
                'userCvsToken'     => new Assert\NotBlank(),
                'brand' => new Assert\NotBlank(),
            ])
        );

        $messages = $this->getValidationMessages($errors);

        static::assertEquals([], $messages);
        static::assertIsArray($messages);
        static::assertCount(0, $messages);
    }
}
