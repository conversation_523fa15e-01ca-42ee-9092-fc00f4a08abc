<?php

namespace App\Tests\Helper;

use App\Helper\BrandHelper;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Yaml\Yaml;

class BrandHelperTest extends TestCase
{
    /**
     * @dataProvider xfBrandsProvider
     */
    public function testIsXf(?string $brand, bool $expected): void
    {
        $result = BrandHelper::isXf($brand);
        $this->assertSame($expected, $result);
    }

    public function xfBrandsProvider(): array
    {
        return [
            'null brand' => [null, false],
            'XF brand SP' => ['SP', true],
            'XF brand FT' => ['FT', true],
            'XF brand FO' => ['FO', true],
            'XF brand AH' => ['AH', true],
            'XF brand AR' => ['AR', true],
            'XF brand CY' => ['CY', true],
            'XF brand DG' => ['DG', true],
            'XF brand JE' => ['JE', true],
            'XF brand LA' => ['LA', true],
            'XF brand RM' => ['RM', true],
            'XF brand MA' => ['MA', true],
            'lowercase sp' => ['sp', true],
            'lowercase ft' => ['ft', true],
            'non-XF brand DS' => ['DS', false],
            'non-XF brand AC' => ['AC', false],
            'invalid brand XX' => ['XX', false],
        ];
    }

    /**
     * @dataProvider xpBrandsProvider
     */
    public function testIsXP(?string $brand, bool $expected): void
    {
        $result = BrandHelper::isXP($brand);
        $this->assertSame($expected, $result);
    }

    public function xpBrandsProvider(): array
    {
        return [
            'null brand' => [null, false],
            'XP brand DS' => ['DS', true],
            'XP brand AC' => ['AC', true],
            'XP brand AP' => ['AP', true],
            'XP brand OP' => ['OP', true],
            'XP brand VX' => ['VX', true],
            'lowercase ds' => ['ds', true],
            'lowercase ac' => ['ac', true],
            'non-XP brand SP' => ['SP', false],
            'non-XP brand FT' => ['FT', false],
            'invalid brand XX' => ['XX', false],
        ];
    }

    /**
     * @dataProvider brandCodeProvider
     */
    public function testGetBrandCode(?string $brand, ?string $expected): void
    {
        // Create a temporary YAML file with our test data
        $mockData = $this->getBrandCodeMockData();
        $tempFile = sys_get_temp_dir() . '/brandCode_test.yml';
        file_put_contents($tempFile, Yaml::dump($mockData));

        // Use reflection to replace the FILE_PATH constant
        $reflectionClass = new \ReflectionClass(BrandHelper::class);
        $reflectionConstant = $reflectionClass->getReflectionConstant('FILE_PATH');

        // We can't modify constants at runtime, so we'll use a different approach
        // Create a mock class that extends BrandHelper and overrides the constant
        $mockClassName = 'MockBrandHelper' . uniqid();
        $mockClassCode = "
            namespace App\\Helper;
            class {$mockClassName} extends BrandHelper {
                private const FILE_PATH = '{$tempFile}';
            }
        ";
        eval($mockClassCode);

        // Call the method on our mock class
        $mockClass = "\\App\\Helper\\{$mockClassName}";
        $result = $mockClass::getBrandCode($brand);

        // Assert the result
        $this->assertSame($expected, $result);
    }

    public function brandCodeProvider(): array
    {
        return [
            'valid brand 00' => ['00', 'FT'],
            'valid brand 33' => ['33', 'DS'],
            'valid brand 57' => ['57', 'JE'],
            'invalid brand XX' => ['XX', null],
            'null brand' => [null, null],
        ];
    }

    /**
     * @dataProvider brandIdProvider
     */
    public function testGetBrandId(?string $brand, ?string $expected): void
    {
        // Create a temporary YAML file with our test data
        $mockData = $this->getBrandCodeMockData();
        $tempFile = sys_get_temp_dir() . '/brandCode_test.yml';
        file_put_contents($tempFile, Yaml::dump($mockData));

        // Use reflection to replace the FILE_PATH constant
        $reflectionClass = new \ReflectionClass(BrandHelper::class);
        $reflectionConstant = $reflectionClass->getReflectionConstant('FILE_PATH');

        // We can't modify constants at runtime, so we'll use a different approach
        // Create a mock class that extends BrandHelper and overrides the constant
        $mockClassName = 'MockBrandHelper' . uniqid();
        $mockClassCode = "
            namespace App\\Helper;
            class {$mockClassName} extends BrandHelper {
                private const FILE_PATH = '{$tempFile}';
            }
        ";
        eval($mockClassCode);

        // Call the method on our mock class
        $mockClass = "\\App\\Helper\\{$mockClassName}";
        $result = $mockClass::getBrandId($brand);

        // Assert the result
        $this->assertSame($expected, $result);
    }

    public function brandIdProvider(): array
    {
        return [
            'valid brand FT' => ['FT', '00'],
            'valid brand DS' => ['DS', '33'],
            'valid brand JE' => ['JE', '57'],
            'invalid brand XX' => ['XX', null],
            'null brand' => [null, null],
        ];
    }

    /**
     * Get mock data for brand codes
     */
    private function getBrandCodeMockData(): array
    {
        return [
            '56' => 'DG',
            '57' => 'JE',
            '58' => 'RM',
            '66' => 'AH',
            '70' => 'LA',
            '73' => 'AC',
            '74' => 'AP',
            '77' => 'FO',
            '83' => 'AR',
            '92' => 'CY',
            '98' => 'MA',
            '00' => 'FT',
            '33' => 'DS',
            'FI' => 'FIAT'
        ];
    }
}