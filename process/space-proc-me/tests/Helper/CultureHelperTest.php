<?php

namespace App\Tests\Helper;

use App\Helper\CultureHelper;
use PHPUnit\Framework\TestCase;

class CultureHelperTest extends TestCase
{
    public function testGetCultureWithDefaultSeparator(): void
    {
        // Test with default separator '_'
        $this->assertEquals('fr_FR', CultureHelper::getCulture('FR', 'fr'));
        $this->assertEquals('en_US', CultureHelper::getCulture('US', 'en'));
        $this->assertEquals('it_IT', CultureHelper::getCulture('IT', 'it'));
    }
    
    public function testGetCultureWithCustomSeparator(): void
    {
        // Test with custom separator '-'
        $this->assertEquals('fr-FR', CultureHelper::getCulture('FR', 'fr', '-'));
        $this->assertEquals('en-US', CultureHelper::getCulture('US', 'en', '-'));
        $this->assertEquals('it-IT', CultureHelper::getCulture('IT', 'it', '-'));
        
        // Test with empty separator
        $this->assertEquals('frFR', CultureHelper::getCulture('FR', 'fr', ''));
        $this->assertEquals('enUS', CultureHelper::getCulture('US', 'en', ''));
    }
    
    public function testGetCultureWithEmptyStrings(): void
    {
        // Test with empty country
        $this->assertEquals('fr_', CultureHelper::getCulture('', 'fr'));
        
        // Test with empty language
        $this->assertEquals('_FR', CultureHelper::getCulture('FR', ''));
        
        // Test with both empty
        $this->assertEquals('_', CultureHelper::getCulture('', ''));
    }
}
