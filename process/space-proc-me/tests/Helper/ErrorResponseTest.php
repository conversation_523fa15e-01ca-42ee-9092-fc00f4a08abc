<?php

namespace App\Tests\Helper;

use App\Helper\ErrorResponse;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;

class ErrorResponseTest extends TestCase
{
    private string $message;
    private int $code;

    public function setup(): void
    {
        $this->message = 'error occured';
        $this->code = Response::HTTP_BAD_REQUEST;
    }

    public function testToArray(): void
    {
        $expectedResponse = [
            'code'    => $this->code,
            'content' => ['error' => ['message' => $this->message]],
        ];

        $response = new ErrorResponse($this->message, $this->code);
        $this->assertEquals($response->toArray(), $expectedResponse);
    }

    public function testWrongstatusCode(): void
    {
        $expectedResponse = [
           'code'    => Response::HTTP_BAD_REQUEST,
           'content' => ['error' => ['message' => $this->message]],
        ];
        $response = new ErrorResponse($this->message, 0);
        $this->assertEquals($response->toArray(), $expectedResponse);
    }

    public function testArrayMessage(): void
    {
        $errors = ['error'];
        $expectedResponse = [
           'code'    => $this->code,
           'content' => ['error' => ['message' => json_encode($errors)]],
        ];
        $response = new ErrorResponse($errors, $this->code);
        $this->assertEquals($response->toArray(), $expectedResponse);
    }

    public function testSetters(): void
    {
        $response = new ErrorResponse('error', Response::HTTP_NOT_FOUND);
        $response->setCode($this->code);
        $response->setMessage($this->message);
        $response->setErrors(['errors']);
        $expectedResponse = [
            'code'    => $this->code,
            'content' => ['error' => ['message' => $this->message, 'errors' => ['errors']]],
        ];

        $this->assertEquals($response->toArray(), $expectedResponse);
    }
}
