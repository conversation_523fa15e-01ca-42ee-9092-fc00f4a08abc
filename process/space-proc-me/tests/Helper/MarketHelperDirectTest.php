<?php

namespace App\Tests\Helper;

use App\Helper\MarketHelper;
use PHPUnit\Framework\TestCase;

class MarketHelperDirectTest extends TestCase
{
    /**
     * Test the getMarket method with a real file
     */
    public function testGetMarketWithRealFile(): void
    {
        // This test will use the real YAML file
        // It will only pass if the file exists and contains valid data
        $result = MarketHelper::getMarket('FR');
        
        // We can't assert the exact value since it depends on the file content
        // But we can assert that it's either an integer or null
        $this->assertTrue(is_int($result) || is_null($result));
    }
    
    /**
     * Test the getRegion method with a real file
     */
    public function testGetRegionWithRealFile(): void
    {
        // This test will use the real YAML file
        // It will only pass if the file exists and contains valid data
        $result = MarketHelper::getRegion('FR');
        
        // We can't assert the exact value since it depends on the file content
        // But we can assert that it's either a string or null
        $this->assertTrue(is_string($result) || is_null($result));
    }
    
    /**
     * Test the getMarketFile method
     */
    public function testGetMarketFile(): void
    {
        // This test will use the real YAML file
        $result = MarketHelper::getMarketFile();
        
        // We can't assert the exact content since it depends on the file
        // But we can assert that it's an array
        $this->assertIsArray($result);
    }
}
