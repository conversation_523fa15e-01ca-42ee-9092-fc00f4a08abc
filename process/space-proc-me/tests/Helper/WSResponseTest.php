<?php

namespace App\Tests\Helper;

use App\Helper\WSResponse;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;

class WSResponseTest extends TestCase
{
    public function testConstruct(): void
    {
        $data = 'test content';
        $code = Response::HTTP_NOT_FOUND;
        $response = new WSResponse($code, $data);
        $this->assertEquals($response->getCode(), $code);
        $this->assertEquals($response->getData(), $data);
    }

    public function testSetters(): void
    {
        $data = 'test content';
        $code = Response::HTTP_NOT_FOUND;
        $response = new WSResponse(Response::HTTP_OK, 'test');
        $response->setCode($code);
        $response->setData($data);
        $this->assertEquals($response->getCode(), $code);
        $this->assertEquals($response->getData(), $data);
    }
}