<?php

namespace App\Tests\Transformer;

use App\DtoRequest\XfEmeaParameterRequest;
use App\DtoResponse\DealerResponseDto;
use App\Model\DealerServiceModel;
use App\Model\DealerV2Model;
use App\Model\OpenHourV2Model;
use App\Transformer\XfEmeaResponseTransformer;
use PHPUnit\Framework\TestCase;

class XfEmeaResponseTransformerTest extends TestCase
{
    public function testMapperBasicFunctionality(): void
    {
        // Create a DealerResponseDto directly
        $dealerResponseDto = new DealerResponseDto();

        // Assert the result is a DealerResponseDto
        $this->assertInstanceOf(DealerResponseDto::class, $dealerResponseDto);
    }

    public function testMapper(): void
    {
        // Create test data
        $data = [
            [
                'SINCOM' => 'SINCOM123',
                'SITECODE' => 'SITE123',
                'COMPANYNAM' => 'Test Dealer',
                'TEL_1' => '*********',
                'EMAIL' => '<EMAIL>',
                'ADDRESS' => '123 Main St',
                'TOWN' => 'Test City',
                'NATION' => 'Test Country',
                'ZIPCODE' => '12345',
                'PROVINCE' => 'Test Province',
                'YCOORD' => 51.5074,
                'XCOORD' => 0.1278,
                'WEBSITE' => 'https://example.com',
                'OPENING_HOURS_VN_1' => '0900',
                'CLOSING_HOURS_VN_1' => '1800',
                'OPENING_HOURS_VO_1' => '0930',
                'CLOSING_HOURS_VO_1' => '1830',
                'OPENING_HOURS_PR_1' => '1000',
                'CLOSING_HOURS_PR_1' => '1700',
                'OPENING_HOURS_APV_1' => '0800',
                'CLOSING_HOURS_APV_1' => '1900',
                'ACTIVITY_VN' => 'Y',
                'ACTIVITY_VO' => 'Y',
                'ACTIVITY_PR' => 'Y',
                'ACTIVITY_APV' => 'Y',
                'ACTIVITY' => [
                    [
                        'SERVICE' => 'VN',
                        'DATEWEEK' => 'Monday',
                        'MORNING_FROM' => '0900',
                        'MORNING_TO' => '1200',
                        'AFTERNOON_FROM' => '1400',
                        'AFTERNOON_TO' => '1800',
                        'DATE' => '1'
                    ],
                    [
                        'SERVICE' => 'VO',
                        'DATEWEEK' => 'Monday',
                        'MORNING_FROM' => '0930',
                        'MORNING_TO' => '1230',
                        'AFTERNOON_FROM' => '1430',
                        'AFTERNOON_TO' => '1830',
                        'DATE' => '1'
                    ]
                ],
                'SERVICES' => []
            ]
        ];

        // Create parameter request
        $params = new XfEmeaParameterRequest();
        $params->setBrand(0); // 0 corresponds to FT brand code
        $params->setBrandCode('FT');
        $params->setMarket(1); // 1 corresponds to GB market
        $params->setLanguage('en');
        $params->setLatitude('51.5074');
        $params->setLongitude('0.1278');
        $params->setRaduis('50');

        // Call the mapper method
        $result = XfEmeaResponseTransformer::mapper($data, $params);

        // Assert the result is a DealerResponseDto
        $this->assertInstanceOf(DealerResponseDto::class, $result);

        // Assert the result contains dealers
        $this->assertCount(1, $result->getSuccess());

        // Get the first dealer
        $dealer = $result->getSuccess()[0];

        // Assert dealer properties
        $this->assertEquals('SITE123|SINCOM123', $dealer->getSiteGeo());
        $this->assertEquals('Test Dealer', $dealer->getName());
        $this->assertEquals('*********', $dealer->getPhone()->getPhoneNumber());
        $this->assertEquals('<EMAIL>', $dealer->getEmail()->getEmail());
        $this->assertEquals('123 Main St', $dealer->getAddress()->getAddress1());
        $this->assertEquals('Test City', $dealer->getAddress()->getCity());
        $this->assertEquals('Test Country', $dealer->getAddress()->getCountry());
        $this->assertEquals('12345', $dealer->getAddress()->getZipCode());
        $this->assertEquals('Test Province', $dealer->getAddress()->getDepartment());
        $this->assertEquals('51.5074', $dealer->getCoordinates()->getLatitude());
        $this->assertEquals('0.1278', $dealer->getCoordinates()->getLongitude());
        $this->assertEquals('https://example.com', $dealer->getWebSites()->getPublic());

        // Assert business
        $business = $dealer->getBusiness();
        $this->assertCount(1, $business);

        // Assert opening hours
        $openHours = $dealer->getOpenHours();
        $this->assertIsArray($openHours);
        $this->assertGreaterThan(0, count($openHours));
    }

    public function testMapperWithEmptyData(): void
    {
        // Create parameter request
        $params = new XfEmeaParameterRequest();
        $params->setBrand(0); // 0 corresponds to FT brand code
        $params->setBrandCode('FT');
        $params->setMarket(1); // 1 corresponds to GB market
        $params->setLanguage('en');

        // Call the mapper method with empty data
        $result = XfEmeaResponseTransformer::mapper([], $params);

        // Assert the result is a DealerResponseDto
        $this->assertInstanceOf(DealerResponseDto::class, $result);

        // Assert the result contains no dealers
        $this->assertCount(0, $result->getSuccess());
    }

    public function testBuildResponse(): void
    {
        // Create parameter request
        $params = new XfEmeaParameterRequest();
        $params->setBrand(0); // 0 corresponds to FT brand code
        $params->setBrandCode('FT');
        $params->setMarket(1); // 1 corresponds to GB market
        $params->setLanguage('en');

        // Create test dealer data
        $dealers = [
            [
                'SINCOM' => 'SINCOM123',
                'SITECODE' => 'SITE123',
                'COMPANYNAM' => 'Test Dealer',
                'TEL_1' => '*********',
                'EMAIL' => '<EMAIL>',
                'ADDRESS' => '123 Main St',
                'TOWN' => 'Test City',
                'NATION' => 'Test Country',
                'ZIPCODE' => '12345',
                'PROVINCE' => 'Test Province',
                'YCOORD' => 51.5074,
                'XCOORD' => 0.1278,
                'WEBSITE' => 'https://example.com',
                'OPENING_HOURS_VN_1' => '0900',
                'CLOSING_HOURS_VN_1' => '1800',
                'ACTIVITY_VN' => 'Y',
                'ACTIVITY' => [
                    [
                        'SERVICE' => 'VN',
                        'DATEWEEK' => 'Monday',
                        'MORNING_FROM' => '0900',
                        'MORNING_TO' => '1200',
                        'AFTERNOON_FROM' => '1400',
                        'AFTERNOON_TO' => '1800',
                        'DATE' => '1'
                    ]
                ],
                'SERVICES' => []
            ]
        ];

        // Call the buildResponse method
        $result = XfEmeaResponseTransformer::buildResponse($params, $dealers);

        // Assert the result is a DealerResponseDto
        $this->assertInstanceOf(DealerResponseDto::class, $result);

        // Assert the result contains dealers
        $this->assertCount(1, $result->getSuccess());

        // Get the first dealer
        $dealer = $result->getSuccess()[0];

        // Assert dealer properties
        $this->assertEquals('SITE123|SINCOM123', $dealer->getSiteGeo());
        $this->assertEquals('Test Dealer', $dealer->getName());
        $this->assertEquals('*********', $dealer->getPhone()->getPhoneNumber());
    }

    public function testExtractServicesAndActivities(): void
    {
        // Use reflection to test private method
        $reflectionClass = new \ReflectionClass(XfEmeaResponseTransformer::class);
        $method = $reflectionClass->getMethod('extractServicesAndActivities');
        $method->setAccessible(true);

        // Create test data
        $sincomIds = [];
        $sincom = 'SINCOM123';
        $value = [
            'SINCOM' => 'SINCOM123',
            'DEALER_NAME' => 'Test Dealer',
            'ACTIVITY_VN' => 'Y',
            'ACTIVITY_VO' => 'Y',
            'ACTIVITY_PR' => 'N',
            'ACTIVITY_APV' => 'Y'
        ];

        // Call the method
        $method->invokeArgs(null, [&$sincomIds, $sincom, $value]);

        // Assert the result
        $this->assertArrayHasKey('SINCOM123', $sincomIds);
        $this->assertEquals('Test Dealer', $sincomIds['SINCOM123']['DEALER_NAME']);
        $this->assertEquals('Y', $sincomIds['SINCOM123']['ACTIVITY_VN']);
        $this->assertEquals('Y', $sincomIds['SINCOM123']['ACTIVITY_VO']);
        $this->assertEquals('N', $sincomIds['SINCOM123']['ACTIVITY_PR']);
        $this->assertEquals('Y', $sincomIds['SINCOM123']['ACTIVITY_APV']);
    }

    public function testGetFormatedTime(): void
    {
        // Use reflection to test private method
        $reflectionClass = new \ReflectionClass(XfEmeaResponseTransformer::class);
        $method = $reflectionClass->getMethod('getFormatedTime');
        $method->setAccessible(true);

        // Test valid time
        $this->assertEquals('09:00', $method->invoke(null, '0900'));
        $this->assertEquals('18:30', $method->invoke(null, '1830'));

        // Test invalid time
        $this->assertNull($method->invoke(null, ''));
        $this->assertNull($method->invoke(null, null));
        $this->assertNull($method->invoke(null, '123'));
        $this->assertNull($method->invoke(null, '12345'));
    }

    public function testIsServiceExist(): void
    {
        // Use reflection to test private method
        $reflectionClass = new \ReflectionClass(XfEmeaResponseTransformer::class);
        $method = $reflectionClass->getMethod('isServiceExist');
        $method->setAccessible(true);

        // Create test data
        $service1 = new DealerServiceModel();
        $service1->setCode('VN');
        $service2 = new DealerServiceModel();
        $service2->setCode('VO');
        $services = [$service1, $service2];

        // Test existing service
        $this->assertTrue($method->invoke(null, $services, 'VN'));
        $this->assertTrue($method->invoke(null, $services, 'VO'));

        // Test non-existing service
        $this->assertFalse($method->invoke(null, $services, 'PR'));
        $this->assertFalse($method->invoke(null, $services, ''));
    }
}
