<?php

namespace App\Tests\Transformer;

use App\DtoRequest\XfEmeaParameterRequest;
use App\Enum\DealerFilterEnum;
use App\Transformer\xfEmeaParameterRequestTransformer;
use PHPUnit\Framework\TestCase;

class xfEmeaParameterRequestTransformerTest extends TestCase
{
    public function testMapper(): void
    {
        // Create sample data
        $data = [
            'brand' => 'FT',
            'language' => 'fr',
            'latitude' => '48.8566',
            'longitude' => '2.3522',
            'resultmax' => '10'
        ];

        $codeMarket = 250;
        $brandCode = 1;

        // Call the mapper method
        $result = xfEmeaParameterRequestTransformer::mapper($data, $codeMarket, $brandCode);

        // Assert the result is a XfEmeaParameterRequest
        $this->assertInstanceOf(XfEmeaParameterRequest::class, $result);

        // Assert the properties are set correctly
        $this->assertEquals($brandCode, $result->getBrand());
        $this->assertEquals($data['brand'], $result->getBrandCode());
        $this->assertEquals($data['language'], $result->getLanguage());
        $this->assertEquals($codeMarket, $result->getMarket());
        $this->assertEquals($data['latitude'], $result->getLatitude());
        $this->assertEquals($data['longitude'], $result->getLongitude());
        $this->assertEquals($data['resultmax'], $result->getRaduis());
    }

    public function testMapperWithMissingValues(): void
    {
        // Create sample data with missing values
        $data = [
            // Missing 'brand'
            // Missing 'language'
            'latitude' => '48.8566',
            'longitude' => '2.3522',
            // Missing 'resultmax'
        ];

        $codeMarket = 250;
        $brandCode = 1;

        // Call the mapper method
        $result = xfEmeaParameterRequestTransformer::mapper($data, $codeMarket, $brandCode);

        // Assert the result is a XfEmeaParameterRequest
        $this->assertInstanceOf(XfEmeaParameterRequest::class, $result);

        // Assert the properties are set correctly
        $this->assertEquals($brandCode, $result->getBrand());
        $this->assertEquals('', $result->getBrandCode());
        $this->assertEquals('', $result->getLanguage());
        $this->assertEquals($codeMarket, $result->getMarket());
        $this->assertEquals($data['latitude'], $result->getLatitude());
        $this->assertEquals($data['longitude'], $result->getLongitude());
        $this->assertEquals(DealerFilterEnum::FILTER_RESULTMAX, $result->getRaduis());
    }

    public function testMapperWithNullValues(): void
    {
        // Call the mapper method with null values
        $result = xfEmeaParameterRequestTransformer::mapper([], 0, 0);

        // Assert the result is a XfEmeaParameterRequest
        $this->assertInstanceOf(XfEmeaParameterRequest::class, $result);

        // Assert the properties are set correctly
        $this->assertEquals(0, $result->getBrand());
        $this->assertEquals('', $result->getBrandCode());
        $this->assertEquals('', $result->getLanguage());
        $this->assertEquals(0, $result->getMarket());
        $this->assertEquals('', $result->getLatitude());
        $this->assertEquals('', $result->getLongitude());
        $this->assertEquals(DealerFilterEnum::FILTER_RESULTMAX, $result->getRaduis());
    }
}
