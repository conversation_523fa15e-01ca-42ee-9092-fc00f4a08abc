<?php

namespace App\Tests\Validator;

use App\Validator\CountryValidator;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Validator\Constraints\Country;
use Symfony\Component\Validator\Constraints\NotBlank;

class CountryValidatorTest extends TestCase
{
    public function testGetConstraints(): void
    {
        $constraints = CountryValidator::getConstraints();
        
        // Check that we have 2 constraints
        $this->assertCount(2, $constraints);
        
        // Check that the constraints are of the expected types
        $this->assertInstanceOf(NotBlank::class, $constraints[0]);
        $this->assertInstanceOf(Country::class, $constraints[1]);
    }
}
