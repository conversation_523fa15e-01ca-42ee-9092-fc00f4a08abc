<?php

namespace App\Tests\Validator;

use App\Validator\BrandValidator;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Validator\Constraints\Choice;
use Symfony\Component\Validator\Constraints\NotBlank;

class BrandValidatorTest extends TestCase
{
    public function testGetBrands(): void
    {
        $brands = BrandValidator::getBrands();
        
        // Check that we have the expected number of brands
        $this->assertCount(16, $brands);
        
        // Check that the brands array contains expected values
        $this->assertContains('AP', $brands);
        $this->assertContains('AC', $brands);
        $this->assertContains('DS', $brands);
        $this->assertContains('OP', $brands);
        $this->assertContains('VX', $brands);
        $this->assertContains('FT', $brands);
        $this->assertContains('FO', $brands);
        $this->assertContains('AH', $brands);
        $this->assertContains('AR', $brands);
        $this->assertContains('AL', $brands);
        $this->assertContains('CY', $brands);
        $this->assertContains('DG', $brands);
        $this->assertContains('JE', $brands);
        $this->assertContains('LA', $brands);
        $this->assertContains('RM', $brands);
        $this->assertContains('MA', $brands);
    }
    
    public function testGetConstraints(): void
    {
        $constraints = BrandValidator::getConstraints();
        
        // Check that we have 2 constraints
        $this->assertCount(2, $constraints);
        
        // Check that the constraints are of the expected types
        $this->assertInstanceOf(NotBlank::class, $constraints[0]);
        $this->assertInstanceOf(Choice::class, $constraints[1]);
        
        // Check the Choice constraint
        $this->assertEquals(BrandValidator::getBrands(), $constraints[1]->choices);
    }
}
