<?php

namespace App\Tests\Validator;

use App\Validator\VinValidator;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Type;

class VinValidatorTest extends TestCase
{
    public function testGetConstraints(): void
    {
        $constraints = VinValidator::getConstraints();
        
        // Check that we have 3 constraints
        $this->assertCount(3, $constraints);
        
        // Check that the constraints are of the expected types
        $this->assertInstanceOf(Type::class, $constraints[0]);
        $this->assertInstanceOf(NotBlank::class, $constraints[1]);
        $this->assertInstanceOf(Length::class, $constraints[2]);
        
        // Check the Type constraint
        $this->assertEquals('alnum', $constraints[0]->type);
        
        // Check the Length constraint
        $this->assertEquals(17, $constraints[2]->min);
        $this->assertEquals(17, $constraints[2]->max);
    }
}
