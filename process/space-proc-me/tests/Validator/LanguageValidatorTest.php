<?php

namespace App\Tests\Validator;

use App\Validator\LanguageValidator;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Validator\Constraints\Language;
use Symfony\Component\Validator\Constraints\NotBlank;

class LanguageValidatorTest extends TestCase
{
    public function testGetConstraintsForLanguage(): void
    {
        $constraints = LanguageValidator::getConstraintsForLanguage();
        
        // Check that we have 2 constraints
        $this->assertCount(2, $constraints);
        
        // Check that the constraints are of the expected types
        $this->assertInstanceOf(NotBlank::class, $constraints[0]);
        $this->assertInstanceOf(Language::class, $constraints[1]);
    }
}
