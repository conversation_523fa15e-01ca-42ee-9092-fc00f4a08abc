<?php

namespace App\Tests\Model;

use App\Model\WebSiteV2Model;
use PHPUnit\Framework\TestCase;

class WebSiteV2ModelTest extends TestCase
{
    private WebSiteV2Model $webSiteModel;
    
    protected function setUp(): void
    {
        $this->webSiteModel = new WebSiteV2Model();
    }
    
    public function testPrivateGetterAndSetter(): void
    {
        $private = 'https://private.example.com';
        $this->webSiteModel->setPrivate($private);
        $this->assertEquals($private, $this->webSiteModel->getPrivate());
    }
    
    public function testPublicGetterAndSetter(): void
    {
        $public = 'https://public.example.com';
        $this->webSiteModel->setPublic($public);
        $this->assertEquals($public, $this->webSiteModel->getPublic());
    }
    
    public function testNullValues(): void
    {
        // Test with null values
        $this->webSiteModel->setPrivate(null);
        $this->webSiteModel->setPublic(null);
        
        $this->assertNull($this->webSiteModel->getPrivate());
        $this->assertNull($this->webSiteModel->getPublic());
    }
    
    public function testEmptyStringValues(): void
    {
        // Test with empty string values
        $this->webSiteModel->setPrivate('');
        $this->webSiteModel->setPublic('');
        
        $this->assertEquals('', $this->webSiteModel->getPrivate());
        $this->assertEquals('', $this->webSiteModel->getPublic());
    }
}
