<?php

namespace App\Tests\Model;

use App\Model\GroupModel;
use PHPUnit\Framework\TestCase;

class GroupModelTest extends TestCase
{
    private GroupModel $groupModel;
    
    protected function setUp(): void
    {
        $this->groupModel = new GroupModel();
    }
    
    public function testGroupIdGetterAndSetter(): void
    {
        $groupId = 'GROUP001';
        $this->groupModel->setGroupId($groupId);
        $this->assertEquals($groupId, $this->groupModel->getGroupId());
    }
    
    public function testIsLeaderGetterAndSetter(): void
    {
        $isLeader = true;
        $this->groupModel->setIsLeader($isLeader);
        $this->assertEquals($isLeader, $this->groupModel->getIsLeader());
    }
    
    public function testSubGroupIdGetterAndSetter(): void
    {
        $subGroupId = 'SUBGROUP001';
        $this->groupModel->setSubGroupId($subGroupId);
        $this->assertEquals($subGroupId, $this->groupModel->getSubGroupId());
    }
    
    public function testSubGrouplabelGetterAndSetter(): void
    {
        $subGrouplabel = 'Subgroup Label';
        $this->groupModel->setSubGrouplabel($subGrouplabel);
        $this->assertEquals($subGrouplabel, $this->groupModel->getSubGrouplabel());
    }
    
    public function testNullValues(): void
    {
        // Test with null values
        $this->groupModel->setGroupId(null);
        $this->groupModel->setIsLeader(null);
        $this->groupModel->setSubGroupId(null);
        $this->groupModel->setSubGrouplabel(null);
        
        $this->assertNull($this->groupModel->getGroupId());
        $this->assertNull($this->groupModel->getIsLeader());
        $this->assertNull($this->groupModel->getSubGroupId());
        $this->assertNull($this->groupModel->getSubGrouplabel());
    }
    
    public function testEmptyStringValues(): void
    {
        // Test with empty string values
        $this->groupModel->setGroupId('');
        $this->groupModel->setSubGroupId('');
        $this->groupModel->setSubGrouplabel('');
        
        $this->assertEquals('', $this->groupModel->getGroupId());
        $this->assertEquals('', $this->groupModel->getSubGroupId());
        $this->assertEquals('', $this->groupModel->getSubGrouplabel());
    }
    
    public function testDefaultValues(): void
    {
        // Test default values
        $newModel = new GroupModel();
        
        $this->assertEquals('', $newModel->getGroupId());
        $this->assertNull($newModel->getIsLeader());
        $this->assertEquals('', $newModel->getSubGroupId());
        $this->assertEquals('', $newModel->getSubGrouplabel());
    }
}
