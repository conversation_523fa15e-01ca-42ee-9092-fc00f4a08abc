<?php

namespace App\Tests\Model;

use App\Model\BusinessModel;
use PHPUnit\Framework\TestCase;

class BusinessModelTest extends TestCase
{
    private BusinessModel $businessModel;
    
    protected function setUp(): void
    {
        $this->businessModel = new BusinessModel();
    }
    
    public function testCodeGetterAndSetter(): void
    {
        $code = 'BUS001';
        $this->businessModel->setCode($code);
        $this->assertEquals($code, $this->businessModel->getCode());
    }
    
    public function testLabelGetterAndSetter(): void
    {
        $label = 'Business Label';
        $this->businessModel->setLabel($label);
        $this->assertEquals($label, $this->businessModel->getLabel());
    }
    
    public function testTypeGetterAndSetter(): void
    {
        $type = 'Business Type';
        $this->businessModel->setType($type);
        $this->assertEquals($type, $this->businessModel->getType());
    }
    
    public function testNullValues(): void
    {
        // Test with null values
        $this->businessModel->setCode(null);
        $this->businessModel->setLabel(null);
        $this->businessModel->setType(null);
        
        $this->assertNull($this->businessModel->getCode());
        $this->assertNull($this->businessModel->getLabel());
        $this->assertNull($this->businessModel->getType());
    }
    
    public function testEmptyStringValues(): void
    {
        // Test with empty string values
        $this->businessModel->setCode('');
        $this->businessModel->setLabel('');
        $this->businessModel->setType('');
        
        $this->assertEquals('', $this->businessModel->getCode());
        $this->assertEquals('', $this->businessModel->getLabel());
        $this->assertEquals('', $this->businessModel->getType());
    }
    
    public function testFluentInterface(): void
    {
        // Test fluent interface (method chaining)
        $result = $this->businessModel
            ->setCode('BUS001')
            ->setLabel('Business Label')
            ->setType('Business Type');
        
        $this->assertSame($this->businessModel, $result);
    }
}
