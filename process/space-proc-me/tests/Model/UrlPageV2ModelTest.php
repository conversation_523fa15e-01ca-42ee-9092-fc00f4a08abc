<?php

namespace App\Tests\Model;

use App\Model\UrlPageV2Model;
use PHPUnit\Framework\TestCase;

class UrlPageV2ModelTest extends TestCase
{
    private UrlPageV2Model $urlPageModel;
    
    protected function setUp(): void
    {
        $this->urlPageModel = new UrlPageV2Model();
    }
    
    public function testUrlApvFormGetterAndSetter(): void
    {
        $urlApvForm = 'https://example.com/apv-form';
        $this->urlPageModel->setUrlApvForm($urlApvForm);
        $this->assertEquals($urlApvForm, $this->urlPageModel->getUrlApvForm());
    }
    
    public function testUrlContactGetterAndSetter(): void
    {
        $urlContact = 'https://example.com/contact';
        $this->urlPageModel->setUrlContact($urlContact);
        $this->assertEquals($urlContact, $this->urlPageModel->getUrlContact());
    }
    
    public function testUrlNewCarStockGetterAndSetter(): void
    {
        $urlNewCarStock = 'https://example.com/new-cars';
        $this->urlPageModel->setUrlNewCarStock($urlNewCarStock);
        $this->assertEquals($urlNewCarStock, $this->urlPageModel->getUrlNewCarStock());
    }
    
    public function testUrlUsedCarStockGetterAndSetter(): void
    {
        $urlUsedCarStock = 'https://example.com/used-cars';
        $this->urlPageModel->setUrlUsedCarStock($urlUsedCarStock);
        $this->assertEquals($urlUsedCarStock, $this->urlPageModel->getUrlUsedCarStock());
    }
    
    public function testUrlUsefullInformationGetterAndSetter(): void
    {
        $urlUsefullInformation = 'https://example.com/info';
        $this->urlPageModel->setUrlUsefullInformation($urlUsefullInformation);
        $this->assertEquals($urlUsefullInformation, $this->urlPageModel->getUrlUsefullInformation());
    }
    
    public function testNullValues(): void
    {
        // Test with null values
        $this->urlPageModel->setUrlApvForm(null);
        $this->urlPageModel->setUrlContact(null);
        $this->urlPageModel->setUrlNewCarStock(null);
        $this->urlPageModel->setUrlUsedCarStock(null);
        $this->urlPageModel->setUrlUsefullInformation(null);
        
        $this->assertNull($this->urlPageModel->getUrlApvForm());
        $this->assertNull($this->urlPageModel->getUrlContact());
        $this->assertNull($this->urlPageModel->getUrlNewCarStock());
        $this->assertNull($this->urlPageModel->getUrlUsedCarStock());
        $this->assertNull($this->urlPageModel->getUrlUsefullInformation());
    }
    
    public function testEmptyStringValues(): void
    {
        // Test with empty string values
        $this->urlPageModel->setUrlApvForm('');
        $this->urlPageModel->setUrlContact('');
        $this->urlPageModel->setUrlNewCarStock('');
        $this->urlPageModel->setUrlUsedCarStock('');
        $this->urlPageModel->setUrlUsefullInformation('');
        
        $this->assertEquals('', $this->urlPageModel->getUrlApvForm());
        $this->assertEquals('', $this->urlPageModel->getUrlContact());
        $this->assertEquals('', $this->urlPageModel->getUrlNewCarStock());
        $this->assertEquals('', $this->urlPageModel->getUrlUsedCarStock());
        $this->assertEquals('', $this->urlPageModel->getUrlUsefullInformation());
    }
}
