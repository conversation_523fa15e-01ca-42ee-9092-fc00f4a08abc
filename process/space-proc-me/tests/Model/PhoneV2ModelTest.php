<?php

namespace App\Tests\Model;

use App\Model\PhoneV2Model;
use PHPUnit\Framework\TestCase;

class PhoneV2ModelTest extends TestCase
{
    private PhoneV2Model $phoneModel;
    
    protected function setUp(): void
    {
        $this->phoneModel = new PhoneV2Model();
    }
    
    public function testPhoneNumberGetterAndSetter(): void
    {
        $phoneNumber = '123456789';
        $this->phoneModel->setPhoneNumber($phoneNumber);
        $this->assertEquals($phoneNumber, $this->phoneModel->getPhoneNumber());
    }
    
    public function testPhoneApvGetterAndSetter(): void
    {
        $phoneApv = '987654321';
        $this->phoneModel->setPhoneApv($phoneApv);
        $this->assertEquals($phoneApv, $this->phoneModel->getPhoneApv());
    }
    
    public function testPhonePrGetterAndSetter(): void
    {
        $phonePr = '555123456';
        $this->phoneModel->setPhonePr($phonePr);
        $this->assertEquals($phonePr, $this->phoneModel->getPhonePr());
    }
    
    public function testPhoneVnGetterAndSetter(): void
    {
        $phoneVn = '555987654';
        $this->phoneModel->setPhoneVn($phoneVn);
        $this->assertEquals($phoneVn, $this->phoneModel->getPhoneVn());
    }
    
    public function testPhoneVoGetterAndSetter(): void
    {
        $phoneVo = '555111222';
        $this->phoneModel->setPhoneVo($phoneVo);
        $this->assertEquals($phoneVo, $this->phoneModel->getPhoneVo());
    }
    
    public function testNullValues(): void
    {
        // Test with null values
        $this->phoneModel->setPhoneNumber(null);
        $this->phoneModel->setPhoneApv(null);
        $this->phoneModel->setPhonePr(null);
        $this->phoneModel->setPhoneVn(null);
        $this->phoneModel->setPhoneVo(null);
        
        $this->assertNull($this->phoneModel->getPhoneNumber());
        $this->assertNull($this->phoneModel->getPhoneApv());
        $this->assertNull($this->phoneModel->getPhonePr());
        $this->assertNull($this->phoneModel->getPhoneVn());
        $this->assertNull($this->phoneModel->getPhoneVo());
    }
    
    public function testEmptyStringValues(): void
    {
        // Test with empty string values
        $this->phoneModel->setPhoneNumber('');
        $this->phoneModel->setPhoneApv('');
        $this->phoneModel->setPhonePr('');
        $this->phoneModel->setPhoneVn('');
        $this->phoneModel->setPhoneVo('');
        
        $this->assertEquals('', $this->phoneModel->getPhoneNumber());
        $this->assertEquals('', $this->phoneModel->getPhoneApv());
        $this->assertEquals('', $this->phoneModel->getPhonePr());
        $this->assertEquals('', $this->phoneModel->getPhoneVn());
        $this->assertEquals('', $this->phoneModel->getPhoneVo());
    }
}
