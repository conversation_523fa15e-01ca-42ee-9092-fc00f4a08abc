<?php

namespace App\Tests\Model;

use App\Model\PrincipalModel;
use PHPUnit\Framework\TestCase;

class PrincipalModelTest extends TestCase
{
    private PrincipalModel $principalModel;
    
    protected function setUp(): void
    {
        $this->principalModel = new PrincipalModel();
    }
    
    public function testIsPrincipalAgGetterAndSetter(): void
    {
        $isPrincipalAg = true;
        $this->principalModel->setIsPrincipalAg($isPrincipalAg);
        $this->assertEquals($isPrincipalAg, $this->principalModel->getIsPrincipalAg());
    }
    
    public function testIsPrincipalPrGetterAndSetter(): void
    {
        $isPrincipalPr = true;
        $this->principalModel->setIsPrincipalPr($isPrincipalPr);
        $this->assertEquals($isPrincipalPr, $this->principalModel->getIsPrincipalPr());
    }
    
    public function testIsPrincipalRaGetterAndSetter(): void
    {
        $isPrincipalRa = true;
        $this->principalModel->setIsPrincipalRa($isPrincipalRa);
        $this->assertEquals($isPrincipalRa, $this->principalModel->getIsPrincipalRa());
    }
    
    public function testIsPrincipalVnGetterAndSetter(): void
    {
        $isPrincipalVn = true;
        $this->principalModel->setIsPrincipalVn($isPrincipalVn);
        $this->assertEquals($isPrincipalVn, $this->principalModel->getIsPrincipalVn());
    }
    
    public function testIsPrincipalVoGetterAndSetter(): void
    {
        $isPrincipalVo = true;
        $this->principalModel->setIsPrincipalVo($isPrincipalVo);
        $this->assertEquals($isPrincipalVo, $this->principalModel->getIsPrincipalVo());
    }
    
    public function testNullValues(): void
    {
        // Test with null values
        $this->principalModel->setIsPrincipalAg(null);
        $this->principalModel->setIsPrincipalPr(null);
        $this->principalModel->setIsPrincipalRa(null);
        $this->principalModel->setIsPrincipalVn(null);
        $this->principalModel->setIsPrincipalVo(null);
        
        $this->assertNull($this->principalModel->getIsPrincipalAg());
        $this->assertNull($this->principalModel->getIsPrincipalPr());
        $this->assertNull($this->principalModel->getIsPrincipalRa());
        $this->assertNull($this->principalModel->getIsPrincipalVn());
        $this->assertNull($this->principalModel->getIsPrincipalVo());
    }
    
    public function testFalseValues(): void
    {
        // Test with false values
        $this->principalModel->setIsPrincipalAg(false);
        $this->principalModel->setIsPrincipalPr(false);
        $this->principalModel->setIsPrincipalRa(false);
        $this->principalModel->setIsPrincipalVn(false);
        $this->principalModel->setIsPrincipalVo(false);
        
        $this->assertFalse($this->principalModel->getIsPrincipalAg());
        $this->assertFalse($this->principalModel->getIsPrincipalPr());
        $this->assertFalse($this->principalModel->getIsPrincipalRa());
        $this->assertFalse($this->principalModel->getIsPrincipalVn());
        $this->assertFalse($this->principalModel->getIsPrincipalVo());
    }
}
