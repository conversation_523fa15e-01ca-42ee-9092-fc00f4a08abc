<?php

namespace App\Tests\Model;

use App\Model\IndicatorModel;
use PHPUnit\Framework\TestCase;

class IndicatorModelTest extends TestCase
{
    private IndicatorModel $indicatorModel;
    
    protected function setUp(): void
    {
        $this->indicatorModel = new IndicatorModel();
    }
    
    public function testCodeGetterAndSetter(): void
    {
        $code = 'CODE001';
        $this->indicatorModel->setCode($code);
        $this->assertEquals($code, $this->indicatorModel->getCode());
    }
    
    public function testLabelGetterAndSetter(): void
    {
        $label = 'Indicator Label';
        $this->indicatorModel->setLabel($label);
        $this->assertEquals($label, $this->indicatorModel->getLabel());
    }
    
    public function testNullValues(): void
    {
        // Test with null values
        $this->indicatorModel->setCode(null);
        $this->indicatorModel->setLabel(null);
        
        $this->assertNull($this->indicatorModel->getCode());
        $this->assertNull($this->indicatorModel->getLabel());
    }
    
    public function testEmptyStringValues(): void
    {
        // Test with empty string values
        $this->indicatorModel->setCode('');
        $this->indicatorModel->setLabel('');
        
        $this->assertEquals('', $this->indicatorModel->getCode());
        $this->assertEquals('', $this->indicatorModel->getLabel());
    }
    
    public function testDefaultValues(): void
    {
        // Test default values
        $newModel = new IndicatorModel();
        
        $this->assertEquals('', $newModel->getCode());
        $this->assertEquals('', $newModel->getLabel());
    }
}
