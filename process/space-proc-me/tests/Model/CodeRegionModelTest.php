<?php

namespace App\Tests\Model;

use App\Model\CodeRegionModel;
use PHPUnit\Framework\TestCase;

class CodeRegionModelTest extends TestCase
{
    private CodeRegionModel $codeRegionModel;
    
    protected function setUp(): void
    {
        $this->codeRegionModel = new CodeRegionModel();
    }
    
    public function testCodeRegionAgGetterAndSetter(): void
    {
        $codeRegionAg = 'REG001';
        $this->codeRegionModel->setCodeRegionAg($codeRegionAg);
        $this->assertEquals($codeRegionAg, $this->codeRegionModel->getCodeRegionAg());
    }
    
    public function testCodeRegionPrGetterAndSetter(): void
    {
        $codeRegionPr = 'REG002';
        $this->codeRegionModel->setCodeRegionPr($codeRegionPr);
        $this->assertEquals($codeRegionPr, $this->codeRegionModel->getCodeRegionPr());
    }
    
    public function testCodeRegionRaGetterAndSetter(): void
    {
        $codeRegionRa = 'REG003';
        $this->codeRegionModel->setCodeRegionRa($codeRegionRa);
        $this->assertEquals($codeRegionRa, $this->codeRegionModel->getCodeRegionRa());
    }
    
    public function testCodeRegionVnGetterAndSetter(): void
    {
        $codeRegionVn = 'REG004';
        $this->codeRegionModel->setCodeRegionVn($codeRegionVn);
        $this->assertEquals($codeRegionVn, $this->codeRegionModel->getCodeRegionVn());
    }
    
    public function testCodeRegionVoGetterAndSetter(): void
    {
        $codeRegionVo = 'REG005';
        $this->codeRegionModel->setCodeRegionVo($codeRegionVo);
        $this->assertEquals($codeRegionVo, $this->codeRegionModel->getCodeRegionVo());
    }
    
    public function testNullValues(): void
    {
        // Test with null values
        $this->codeRegionModel->setCodeRegionAg(null);
        $this->codeRegionModel->setCodeRegionPr(null);
        $this->codeRegionModel->setCodeRegionRa(null);
        $this->codeRegionModel->setCodeRegionVn(null);
        $this->codeRegionModel->setCodeRegionVo(null);
        
        $this->assertNull($this->codeRegionModel->getCodeRegionAg());
        $this->assertNull($this->codeRegionModel->getCodeRegionPr());
        $this->assertNull($this->codeRegionModel->getCodeRegionRa());
        $this->assertNull($this->codeRegionModel->getCodeRegionVn());
        $this->assertNull($this->codeRegionModel->getCodeRegionVo());
    }
    
    public function testEmptyStringValues(): void
    {
        // Test with empty string values
        $this->codeRegionModel->setCodeRegionAg('');
        $this->codeRegionModel->setCodeRegionPr('');
        $this->codeRegionModel->setCodeRegionRa('');
        $this->codeRegionModel->setCodeRegionVn('');
        $this->codeRegionModel->setCodeRegionVo('');
        
        $this->assertEquals('', $this->codeRegionModel->getCodeRegionAg());
        $this->assertEquals('', $this->codeRegionModel->getCodeRegionPr());
        $this->assertEquals('', $this->codeRegionModel->getCodeRegionRa());
        $this->assertEquals('', $this->codeRegionModel->getCodeRegionVn());
        $this->assertEquals('', $this->codeRegionModel->getCodeRegionVo());
    }
    
    public function testDefaultValues(): void
    {
        // Test default values
        $newModel = new CodeRegionModel();
        
        $this->assertEquals('', $newModel->getCodeRegionAg());
        $this->assertEquals('', $newModel->getCodeRegionPr());
        $this->assertEquals('', $newModel->getCodeRegionRa());
        $this->assertEquals('', $newModel->getCodeRegionVn());
        $this->assertEquals('', $newModel->getCodeRegionVo());
    }
}
