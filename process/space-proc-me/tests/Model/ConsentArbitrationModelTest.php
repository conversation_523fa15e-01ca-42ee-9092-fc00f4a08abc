<?php

namespace App\Tests\Model;

use App\Model\ConsentArbitrationModel;
use PHPUnit\Framework\TestCase;

class ConsentArbitrationModelTest extends TestCase
{
    private $userId = 'user123';
    private $vin = 'ABCDEFGHIJKLMNOPQ'; // 17 characters
    private $consent = true;
    private $brand = 'DS';
    private $country = 'FR';
    private $source = 'WEB';
    private $warrantyDocumentLink = 'https://example.com/warranty';
    
    public function testConstructorAndGetters(): void
    {
        $model = new ConsentArbitrationModel(
            $this->userId,
            $this->vin,
            $this->consent,
            $this->brand,
            $this->country,
            $this->source,
            $this->warrantyDocumentLink
        );
        
        $this->assertEquals($this->userId, $model->getUserId());
        $this->assertEquals($this->vin, $model->getVin());
        $this->assertEquals($this->consent, $model->getConsent());
        $this->assertEquals($this->brand, $model->getBrand());
        $this->assertEquals($this->country, $model->getCountry());
        $this->assertEquals($this->source, $model->getSource());
        $this->assertEquals($this->warrantyDocumentLink, $model->getWarrantyDocumentLink());
    }
    
    public function testSetters(): void
    {
        $model = new ConsentArbitrationModel(
            $this->userId,
            $this->vin,
            $this->consent,
            $this->brand,
            $this->country,
            $this->source,
            $this->warrantyDocumentLink
        );
        
        $newUserId = 'newUser456';
        $newVin = 'QPONMLKJIHGFEDCBA'; // 17 characters
        $newConsent = false;
        $newBrand = 'CR';
        $newCountry = 'IT';
        $newSource = 'APP';
        $newWarrantyDocumentLink = 'https://example.com/new-warranty';
        
        $model->setUserId($newUserId);
        $model->setVin($newVin);
        $model->setConsent($newConsent);
        $model->setBrand($newBrand);
        $model->setCountry($newCountry);
        $model->setSource($newSource);
        $model->setWarrantyDocumentLink($newWarrantyDocumentLink);
        
        $this->assertEquals($newUserId, $model->getUserId());
        $this->assertEquals($newVin, $model->getVin());
        $this->assertEquals($newConsent, $model->getConsent());
        $this->assertEquals($newBrand, $model->getBrand());
        $this->assertEquals($newCountry, $model->getCountry());
        $this->assertEquals($newSource, $model->getSource());
        $this->assertEquals($newWarrantyDocumentLink, $model->getWarrantyDocumentLink());
    }
    
    public function testNullableFields(): void
    {
        $model = new ConsentArbitrationModel(
            $this->userId,
            $this->vin,
            null, // consent is nullable
            null, // brand is nullable
            $this->country,
            null, // source is nullable
            null  // warrantyDocumentLink is nullable
        );
        
        $this->assertNull($model->getConsent());
        $this->assertNull($model->getBrand());
        $this->assertNull($model->getSource());
        $this->assertNull($model->getWarrantyDocumentLink());
    }
    
    public function testConstructorWithSpacewebSource(): void
    {
        $model = new ConsentArbitrationModel(
            $this->userId,
            $this->vin,
            $this->consent,
            $this->brand,
            $this->country,
            'SPACEWEB', // Valid source option
            $this->warrantyDocumentLink
        );
        
        $this->assertEquals('SPACEWEB', $model->getSource());
    }
}
