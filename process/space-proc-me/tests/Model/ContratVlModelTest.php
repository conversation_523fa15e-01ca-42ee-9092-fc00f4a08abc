<?php

namespace App\Tests\Model;

use App\Model\ContratVlModel;
use PHPUnit\Framework\TestCase;

class ContratVlModelTest extends TestCase
{
    private ContratVlModel $contratVlModel;
    
    protected function setUp(): void
    {
        $this->contratVlModel = new ContratVlModel();
    }
    
    public function testCodeActorAddressVlGetterAndSetter(): void
    {
        $codeActorAddressVl = 'ADDR123';
        $this->contratVlModel->setCodeActorAddressVl($codeActorAddressVl);
        $this->assertEquals($codeActorAddressVl, $this->contratVlModel->getCodeActorAddressVl());
    }
    
    public function testCodeActorCcVlGetterAndSetter(): void
    {
        $codeActorCcVl = 'CC123';
        $this->contratVlModel->setCodeActorCcVl($codeActorCcVl);
        $this->assertEquals($codeActorCcVl, $this->contratVlModel->getCodeActorCcVl());
    }
    
    public function testCodeRegionVlGetterAndSetter(): void
    {
        $codeRegionVl = 'REG123';
        $this->contratVlModel->setCodeRegionVl($codeRegionVl);
        $this->assertEquals($codeRegionVl, $this->contratVlModel->getCodeRegionVl());
    }
    
    public function testEmailVlGetterAndSetter(): void
    {
        $emailVl = '<EMAIL>';
        $this->contratVlModel->setEmailVl($emailVl);
        $this->assertEquals($emailVl, $this->contratVlModel->getEmailVl());
    }
    
    public function testPhoneVlGetterAndSetter(): void
    {
        $phoneVl = '123456789';
        $this->contratVlModel->setPhoneVl($phoneVl);
        $this->assertEquals($phoneVl, $this->contratVlModel->getPhoneVl());
    }
    
    public function testIsPrincipalVlGetterAndSetter(): void
    {
        $isPrincipalVl = true;
        $this->contratVlModel->setIsPrincipalVl($isPrincipalVl);
        $this->assertEquals($isPrincipalVl, $this->contratVlModel->getIsPrincipalVl());
    }
    
    public function testNullValues(): void
    {
        // Test with null values
        $this->contratVlModel->setCodeActorAddressVl(null);
        $this->contratVlModel->setCodeActorCcVl(null);
        $this->contratVlModel->setCodeRegionVl(null);
        $this->contratVlModel->setEmailVl(null);
        $this->contratVlModel->setPhoneVl(null);
        $this->contratVlModel->setIsPrincipalVl(null);
        
        $this->assertNull($this->contratVlModel->getCodeActorAddressVl());
        $this->assertNull($this->contratVlModel->getCodeActorCcVl());
        $this->assertNull($this->contratVlModel->getCodeRegionVl());
        $this->assertNull($this->contratVlModel->getEmailVl());
        $this->assertNull($this->contratVlModel->getPhoneVl());
        $this->assertNull($this->contratVlModel->getIsPrincipalVl());
    }
    
    public function testEmptyStringValues(): void
    {
        // Test with empty string values
        $this->contratVlModel->setCodeActorAddressVl('');
        $this->contratVlModel->setCodeActorCcVl('');
        $this->contratVlModel->setCodeRegionVl('');
        $this->contratVlModel->setEmailVl('');
        $this->contratVlModel->setPhoneVl('');
        
        $this->assertEquals('', $this->contratVlModel->getCodeActorAddressVl());
        $this->assertEquals('', $this->contratVlModel->getCodeActorCcVl());
        $this->assertEquals('', $this->contratVlModel->getCodeRegionVl());
        $this->assertEquals('', $this->contratVlModel->getEmailVl());
        $this->assertEquals('', $this->contratVlModel->getPhoneVl());
    }
    
    public function testDefaultValues(): void
    {
        // Test default values
        $newModel = new ContratVlModel();
        
        $this->assertEquals('', $newModel->getCodeActorAddressVl());
        $this->assertEquals('', $newModel->getCodeActorCcVl());
        $this->assertEquals('', $newModel->getCodeRegionVl());
        $this->assertEquals('', $newModel->getEmailVl());
        $this->assertEquals('', $newModel->getPhoneVl());
        $this->assertNull($newModel->getIsPrincipalVl());
    }
}
