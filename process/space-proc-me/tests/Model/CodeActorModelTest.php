<?php

namespace App\Tests\Model;

use App\Model\CodeActorModel;
use PHPUnit\Framework\TestCase;

class CodeActorModelTest extends TestCase
{
    private CodeActorModel $codeActorModel;
    
    protected function setUp(): void
    {
        $this->codeActorModel = new CodeActorModel();
    }
    
    public function testCodeActorAddressPrGetterAndSetter(): void
    {
        $codeActorAddressPr = 'ADDR001';
        $this->codeActorModel->setCodeActorAddressPr($codeActorAddressPr);
        $this->assertEquals($codeActorAddressPr, $this->codeActorModel->getCodeActorAddressPr());
    }
    
    public function testCodeActorAddressRaGetterAndSetter(): void
    {
        $codeActorAddressRa = 'ADDR002';
        $this->codeActorModel->setCodeActorAddressRa($codeActorAddressRa);
        $this->assertEquals($codeActorAddressRa, $this->codeActorModel->getCodeActorAddressRa());
    }
    
    public function testCodeActorAddressVnGetterAndSetter(): void
    {
        $codeActorAddressVn = 'ADDR003';
        $this->codeActorModel->setCodeActorAddressVn($codeActorAddressVn);
        $this->assertEquals($codeActorAddressVn, $this->codeActorModel->getCodeActorAddressVn());
    }
    
    public function testCodeActorAddressVoGetterAndSetter(): void
    {
        $codeActorAddressVo = 'ADDR004';
        $this->codeActorModel->setCodeActorAddressVo($codeActorAddressVo);
        $this->assertEquals($codeActorAddressVo, $this->codeActorModel->getCodeActorAddressVo());
    }
    
    public function testCodeActorCcAgGetterAndSetter(): void
    {
        $codeActorCcAg = 'CC001';
        $this->codeActorModel->setCodeActorCcAg($codeActorCcAg);
        $this->assertEquals($codeActorCcAg, $this->codeActorModel->getCodeActorCcAg());
    }
    
    public function testCodeActorCcPrGetterAndSetter(): void
    {
        $codeActorCcPr = 'CC002';
        $this->codeActorModel->setCodeActorCcPr($codeActorCcPr);
        $this->assertEquals($codeActorCcPr, $this->codeActorModel->getCodeActorCcPr());
    }
    
    public function testCodeActorCcRaGetterAndSetter(): void
    {
        $codeActorCcRa = 'CC003';
        $this->codeActorModel->setCodeActorCcRa($codeActorCcRa);
        $this->assertEquals($codeActorCcRa, $this->codeActorModel->getCodeActorCcRa());
    }
    
    public function testCodeActorCcVnGetterAndSetter(): void
    {
        $codeActorCcVn = 'CC004';
        $this->codeActorModel->setCodeActorCcVn($codeActorCcVn);
        $this->assertEquals($codeActorCcVn, $this->codeActorModel->getCodeActorCcVn());
    }
    
    public function testCodeActorCcVoGetterAndSetter(): void
    {
        $codeActorCcVo = 'CC005';
        $this->codeActorModel->setCodeActorCcVo($codeActorCcVo);
        $this->assertEquals($codeActorCcVo, $this->codeActorModel->getCodeActorCcVo());
    }
    
    public function testCodeActorSearchGetterAndSetter(): void
    {
        $codeActorSearch = 'SEARCH001';
        $this->codeActorModel->setCodeActorSearch($codeActorSearch);
        $this->assertEquals($codeActorSearch, $this->codeActorModel->getCodeActorSearch());
    }
    
    public function testNullValues(): void
    {
        // Test with null values
        $this->codeActorModel->setCodeActorAddressPr(null);
        $this->codeActorModel->setCodeActorAddressRa(null);
        $this->codeActorModel->setCodeActorAddressVn(null);
        $this->codeActorModel->setCodeActorAddressVo(null);
        $this->codeActorModel->setCodeActorCcAg(null);
        $this->codeActorModel->setCodeActorCcPr(null);
        $this->codeActorModel->setCodeActorCcRa(null);
        $this->codeActorModel->setCodeActorCcVn(null);
        $this->codeActorModel->setCodeActorCcVo(null);
        $this->codeActorModel->setCodeActorSearch(null);
        
        $this->assertNull($this->codeActorModel->getCodeActorAddressPr());
        $this->assertNull($this->codeActorModel->getCodeActorAddressRa());
        $this->assertNull($this->codeActorModel->getCodeActorAddressVn());
        $this->assertNull($this->codeActorModel->getCodeActorAddressVo());
        $this->assertNull($this->codeActorModel->getCodeActorCcAg());
        $this->assertNull($this->codeActorModel->getCodeActorCcPr());
        $this->assertNull($this->codeActorModel->getCodeActorCcRa());
        $this->assertNull($this->codeActorModel->getCodeActorCcVn());
        $this->assertNull($this->codeActorModel->getCodeActorCcVo());
        $this->assertNull($this->codeActorModel->getCodeActorSearch());
    }
    
    public function testEmptyStringValues(): void
    {
        // Test with empty string values
        $this->codeActorModel->setCodeActorAddressPr('');
        $this->codeActorModel->setCodeActorAddressRa('');
        $this->codeActorModel->setCodeActorAddressVn('');
        $this->codeActorModel->setCodeActorAddressVo('');
        $this->codeActorModel->setCodeActorCcAg('');
        $this->codeActorModel->setCodeActorCcPr('');
        $this->codeActorModel->setCodeActorCcRa('');
        $this->codeActorModel->setCodeActorCcVn('');
        $this->codeActorModel->setCodeActorCcVo('');
        $this->codeActorModel->setCodeActorSearch('');
        
        $this->assertEquals('', $this->codeActorModel->getCodeActorAddressPr());
        $this->assertEquals('', $this->codeActorModel->getCodeActorAddressRa());
        $this->assertEquals('', $this->codeActorModel->getCodeActorAddressVn());
        $this->assertEquals('', $this->codeActorModel->getCodeActorAddressVo());
        $this->assertEquals('', $this->codeActorModel->getCodeActorCcAg());
        $this->assertEquals('', $this->codeActorModel->getCodeActorCcPr());
        $this->assertEquals('', $this->codeActorModel->getCodeActorCcRa());
        $this->assertEquals('', $this->codeActorModel->getCodeActorCcVn());
        $this->assertEquals('', $this->codeActorModel->getCodeActorCcVo());
        $this->assertEquals('', $this->codeActorModel->getCodeActorSearch());
    }
    
    public function testDefaultValues(): void
    {
        // Test default values
        $newModel = new CodeActorModel();
        
        $this->assertEquals('', $newModel->getCodeActorAddressPr());
        $this->assertEquals('', $newModel->getCodeActorAddressRa());
        $this->assertEquals('', $newModel->getCodeActorAddressVn());
        $this->assertEquals('', $newModel->getCodeActorAddressVo());
        $this->assertEquals('', $newModel->getCodeActorCcAg());
        $this->assertEquals('', $newModel->getCodeActorCcPr());
        $this->assertEquals('', $newModel->getCodeActorCcRa());
        $this->assertEquals('', $newModel->getCodeActorCcVn());
        $this->assertEquals('', $newModel->getCodeActorCcVo());
        $this->assertEquals('', $newModel->getCodeActorSearch());
    }
}
