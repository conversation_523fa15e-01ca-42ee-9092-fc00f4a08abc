<?php

namespace App\Tests\Model;

use App\Model\AddressV2Model;
use PHPUnit\Framework\TestCase;

class AddressV2ModelTest extends TestCase
{
    private AddressV2Model $addressModel;
    
    protected function setUp(): void
    {
        $this->addressModel = new AddressV2Model();
    }
    
    public function testAddress1GetterAndSetter(): void
    {
        $address1 = '123 Main St';
        $this->addressModel->setAddress1($address1);
        $this->assertEquals($address1, $this->addressModel->getAddress1());
    }
    
    public function testAddress2GetterAndSetter(): void
    {
        $address2 = 'Apt 4B';
        $this->addressModel->setAddress2($address2);
        $this->assertEquals($address2, $this->addressModel->getAddress2());
    }
    
    public function testAddress3GetterAndSetter(): void
    {
        $address3 = 'Building C';
        $this->addressModel->setAddress3($address3);
        $this->assertEquals($address3, $this->addressModel->getAddress3());
    }
    
    public function testDepartmentGetterAndSetter(): void
    {
        $department = 'Sales';
        $this->addressModel->setDepartment($department);
        $this->assertEquals($department, $this->addressModel->getDepartment());
    }
    
    public function testCityGetterAndSetter(): void
    {
        $city = 'Paris';
        $this->addressModel->setCity($city);
        $this->assertEquals($city, $this->addressModel->getCity());
    }
    
    public function testRegionGetterAndSetter(): void
    {
        $region = 'Ile-de-France';
        $this->addressModel->setRegion($region);
        $this->assertEquals($region, $this->addressModel->getRegion());
    }
    
    public function testZipCodeGetterAndSetter(): void
    {
        $zipCode = '75001';
        $this->addressModel->setZipCode($zipCode);
        $this->assertEquals($zipCode, $this->addressModel->getZipCode());
    }
    
    public function testCountryGetterAndSetter(): void
    {
        $country = 'France';
        $this->addressModel->setCountry($country);
        $this->assertEquals($country, $this->addressModel->getCountry());
    }
    
    public function testNullValues(): void
    {
        // Test with null values
        $this->addressModel->setAddress1(null);
        $this->addressModel->setAddress2(null);
        $this->addressModel->setAddress3(null);
        $this->addressModel->setDepartment(null);
        $this->addressModel->setCity(null);
        $this->addressModel->setRegion(null);
        $this->addressModel->setZipCode(null);
        $this->addressModel->setCountry(null);
        
        $this->assertNull($this->addressModel->getAddress1());
        $this->assertNull($this->addressModel->getAddress2());
        $this->assertNull($this->addressModel->getAddress3());
        $this->assertNull($this->addressModel->getDepartment());
        $this->assertNull($this->addressModel->getCity());
        $this->assertNull($this->addressModel->getRegion());
        $this->assertNull($this->addressModel->getZipCode());
        $this->assertNull($this->addressModel->getCountry());
    }
    
    public function testEmptyStringValues(): void
    {
        // Test with empty string values
        $this->addressModel->setAddress1('');
        $this->addressModel->setAddress2('');
        $this->addressModel->setAddress3('');
        $this->addressModel->setDepartment('');
        $this->addressModel->setCity('');
        $this->addressModel->setRegion('');
        $this->addressModel->setZipCode('');
        $this->addressModel->setCountry('');
        
        $this->assertEquals('', $this->addressModel->getAddress1());
        $this->assertEquals('', $this->addressModel->getAddress2());
        $this->assertEquals('', $this->addressModel->getAddress3());
        $this->assertEquals('', $this->addressModel->getDepartment());
        $this->assertEquals('', $this->addressModel->getCity());
        $this->assertEquals('', $this->addressModel->getRegion());
        $this->assertEquals('', $this->addressModel->getZipCode());
        $this->assertEquals('', $this->addressModel->getCountry());
    }
}
