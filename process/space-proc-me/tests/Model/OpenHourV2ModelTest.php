<?php

namespace App\Tests\Model;

use App\Model\OpenHourV2Model;
use PHPUnit\Framework\TestCase;

class OpenHourV2ModelTest extends TestCase
{
    private OpenHourV2Model $openHourModel;
    
    protected function setUp(): void
    {
        $this->openHourModel = new OpenHourV2Model();
    }
    
    public function testLabelGetterAndSetter(): void
    {
        $label = 'Monday: 9:00-18:00';
        $this->openHourModel->setLabel($label);
        $this->assertEquals($label, $this->openHourModel->getLabel());
    }
    
    public function testTypeGetterAndSetter(): void
    {
        $type = 'REGULAR';
        $this->openHourModel->setType($type);
        $this->assertEquals($type, $this->openHourModel->getType());
    }
    
    public function testNullValues(): void
    {
        // Test with null values
        $this->openHourModel->setLabel(null);
        $this->openHourModel->setType(null);
        
        $this->assertNull($this->openHourModel->getLabel());
        $this->assertNull($this->openHourModel->getType());
    }
    
    public function testEmptyStringValues(): void
    {
        // Test with empty string values
        $this->openHourModel->setLabel('');
        $this->openHourModel->setType('');
        
        $this->assertEquals('', $this->openHourModel->getLabel());
        $this->assertEquals('', $this->openHourModel->getType());
    }
    
    public function testDefaultValues(): void
    {
        // Test default values
        $newModel = new OpenHourV2Model();
        
        $this->assertEquals('', $newModel->getLabel());
        $this->assertEquals('', $newModel->getType());
    }
    
    public function testFluentInterface(): void
    {
        // Test fluent interface (method chaining)
        $result = $this->openHourModel
            ->setLabel('Monday: 9:00-18:00')
            ->setType('REGULAR');
        
        $this->assertSame($this->openHourModel, $result);
    }
}
