<?php

namespace App\Tests\Model;

use App\Model\ImporterModel;
use PHPUnit\Framework\TestCase;

class ImporterModelTest extends TestCase
{
    private ImporterModel $importerModel;
    
    protected function setUp(): void
    {
        $this->importerModel = new ImporterModel();
    }
    
    public function testImporterCodeGetterAndSetter(): void
    {
        $importerCode = 'IMP001';
        $this->importerModel->setImporterCode($importerCode);
        $this->assertEquals($importerCode, $this->importerModel->getImporterCode());
    }
    
    public function testCorporateNameGetterAndSetter(): void
    {
        $corporateName = 'Corporate Name';
        $this->importerModel->setCorporateName($corporateName);
        $this->assertEquals($corporateName, $this->importerModel->getCorporateName());
    }
    
    public function testImporterNameGetterAndSetter(): void
    {
        $importerName = 'Importer Name';
        $this->importerModel->setImporterName($importerName);
        $this->assertEquals($importerName, $this->importerModel->getImporterName());
    }
    
    public function testAddressGetterAndSetter(): void
    {
        $address = '123 Main St';
        $this->importerModel->setAddress($address);
        $this->assertEquals($address, $this->importerModel->getAddress());
    }
    
    public function testCityGetterAndSetter(): void
    {
        $city = 'Paris';
        $this->importerModel->setCity($city);
        $this->assertEquals($city, $this->importerModel->getCity());
    }
    
    public function testManagementCountryGetterAndSetter(): void
    {
        $managementCountry = 'FR';
        $this->importerModel->setManagementCountry($managementCountry);
        $this->assertEquals($managementCountry, $this->importerModel->getManagementCountry());
    }
    
    public function testCountryGetterAndSetter(): void
    {
        $country = 'France';
        $this->importerModel->setCountry($country);
        $this->assertEquals($country, $this->importerModel->getCountry());
    }
    
    public function testSubsidiaryGetterAndSetter(): void
    {
        $subsidiary = 'SUB001';
        $this->importerModel->setSubsidiary($subsidiary);
        $this->assertEquals($subsidiary, $this->importerModel->getSubsidiary());
    }
    
    public function testSubsidiaryNameGetterAndSetter(): void
    {
        $subsidiaryName = 'Subsidiary Name';
        $this->importerModel->setSubsidiaryName($subsidiaryName);
        $this->assertEquals($subsidiaryName, $this->importerModel->getSubsidiaryName());
    }
    
    public function testNullValues(): void
    {
        // Test with null values
        $this->importerModel->setImporterCode(null);
        $this->importerModel->setCorporateName(null);
        $this->importerModel->setImporterName(null);
        $this->importerModel->setAddress(null);
        $this->importerModel->setCity(null);
        $this->importerModel->setManagementCountry(null);
        $this->importerModel->setCountry(null);
        $this->importerModel->setSubsidiary(null);
        $this->importerModel->setSubsidiaryName(null);
        
        $this->assertNull($this->importerModel->getImporterCode());
        $this->assertNull($this->importerModel->getCorporateName());
        $this->assertNull($this->importerModel->getImporterName());
        $this->assertNull($this->importerModel->getAddress());
        $this->assertNull($this->importerModel->getCity());
        $this->assertNull($this->importerModel->getManagementCountry());
        $this->assertNull($this->importerModel->getCountry());
        $this->assertNull($this->importerModel->getSubsidiary());
        $this->assertNull($this->importerModel->getSubsidiaryName());
    }
    
    public function testEmptyStringValues(): void
    {
        // Test with empty string values
        $this->importerModel->setImporterCode('');
        $this->importerModel->setCorporateName('');
        $this->importerModel->setImporterName('');
        $this->importerModel->setAddress('');
        $this->importerModel->setCity('');
        $this->importerModel->setManagementCountry('');
        $this->importerModel->setCountry('');
        $this->importerModel->setSubsidiary('');
        $this->importerModel->setSubsidiaryName('');
        
        $this->assertEquals('', $this->importerModel->getImporterCode());
        $this->assertEquals('', $this->importerModel->getCorporateName());
        $this->assertEquals('', $this->importerModel->getImporterName());
        $this->assertEquals('', $this->importerModel->getAddress());
        $this->assertEquals('', $this->importerModel->getCity());
        $this->assertEquals('', $this->importerModel->getManagementCountry());
        $this->assertEquals('', $this->importerModel->getCountry());
        $this->assertEquals('', $this->importerModel->getSubsidiary());
        $this->assertEquals('', $this->importerModel->getSubsidiaryName());
    }
    
    public function testDefaultValues(): void
    {
        // Test default values
        $newModel = new ImporterModel();
        
        $this->assertEquals('', $newModel->getImporterCode());
        $this->assertEquals('', $newModel->getCorporateName());
        $this->assertEquals('', $newModel->getImporterName());
        $this->assertEquals('', $newModel->getAddress());
        $this->assertEquals('', $newModel->getCity());
        $this->assertEquals('', $newModel->getManagementCountry());
        $this->assertEquals('', $newModel->getCountry());
        $this->assertEquals('', $newModel->getSubsidiary());
        $this->assertEquals('', $newModel->getSubsidiaryName());
    }
    
    public function testFluentInterface(): void
    {
        // Test fluent interface (method chaining)
        $result = $this->importerModel
            ->setImporterCode('IMP001')
            ->setCorporateName('Corporate Name')
            ->setImporterName('Importer Name')
            ->setAddress('123 Main St')
            ->setCity('Paris')
            ->setManagementCountry('FR')
            ->setCountry('France')
            ->setSubsidiary('SUB001')
            ->setSubsidiaryName('Subsidiary Name');
        
        $this->assertSame($this->importerModel, $result);
    }
}
