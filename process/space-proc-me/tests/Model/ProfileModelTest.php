<?php

namespace App\Tests\Model;

use App\Model\ProfileModel;
use PHPUnit\Framework\TestCase;

class ProfileModelTest extends TestCase
{
    private $profileModel;

    protected function setUp(): void
    {
        $this->profileModel = new ProfileModel();
    }

    public function testGettersAndSetters(): void
    {
        // Test data
        $civility = 'Mr';
        $lastName = 'Doe';
        $firstName = 'John';
        $address1 = '123 Main St';
        $address2 = 'Apt 4B';
        $city = 'Paris';
        $country = 'FR';
        $zipcode = '75001';
        $phone = '123456789';
        $email = '<EMAIL>';
        
        // Set values using setters
        $this->profileModel->setCivility($civility)
            ->setLastName($lastName)
            ->setFirstName($firstName)
            ->setAddress1($address1)
            ->setAddress2($address2)
            ->setCity($city)
            ->setCountry($country)
            ->setZipcode($zipcode)
            ->setPhone($phone)
            ->setEmail($email);
        
        // Assert values using getters
        $this->assertEquals($civility, $this->profileModel->getCivility());
        $this->assertEquals($lastName, $this->profileModel->getLastName());
        $this->assertEquals($firstName, $this->profileModel->getFirstName());
        $this->assertEquals($address1, $this->profileModel->getAddress1());
        $this->assertEquals($address2, $this->profileModel->getAddress2());
        $this->assertEquals($city, $this->profileModel->getCity());
        $this->assertEquals($country, $this->profileModel->getCountry());
        $this->assertEquals($zipcode, $this->profileModel->getZipcode());
        $this->assertEquals($phone, $this->profileModel->getPhone());
        $this->assertEquals($email, $this->profileModel->getEmail());
    }

    public function testToArrayWithAllValues(): void
    {
        // Test data
        $civility = 'Mr';
        $lastName = 'Doe';
        $firstName = 'John';
        $address1 = '123 Main St';
        $address2 = 'Apt 4B';
        $city = 'Paris';
        $country = 'FR';
        $zipcode = '75001';
        $phone = '123456789';
        $email = '<EMAIL>';
        
        // Set values using setters
        $this->profileModel->setCivility($civility)
            ->setLastName($lastName)
            ->setFirstName($firstName)
            ->setAddress1($address1)
            ->setAddress2($address2)
            ->setCity($city)
            ->setCountry($country)
            ->setZipcode($zipcode)
            ->setPhone($phone)
            ->setEmail($email);
        
        // Expected array
        $expectedArray = [
            'civility' => $civility,
            'lastName' => $lastName,
            'firstName' => $firstName,
            'address1' => $address1,
            'address2' => $address2,
            'city' => $city,
            'country' => $country,
            'zipcode' => $zipcode,
            'phone' => $phone,
            'email' => $email
        ];
        
        // Call toArray method
        $result = $this->profileModel->toArray();
        
        // Assert the result
        $this->assertEquals($expectedArray, $result);
    }

    public function testToArrayWithSomeEmptyValues(): void
    {
        // Test data - some values are empty
        $lastName = 'Doe';
        $firstName = 'John';
        $email = '<EMAIL>';
        
        // Set only some values
        $this->profileModel->setLastName($lastName)
            ->setFirstName($firstName)
            ->setEmail($email);
        
        // Expected array - only non-empty values should be included
        $expectedArray = [
            'civility' => '',  // Default empty string values are included
            'lastName' => $lastName,
            'firstName' => $firstName,
            'address1' => '',
            'address2' => '',
            'city' => '',
            'country' => '',
            'zipcode' => '',
            'phone' => '',
            'email' => $email
        ];
        
        // Call toArray method
        $result = $this->profileModel->toArray();
        
        // Assert the result
        $this->assertEquals($expectedArray, $result);
    }

    public function testDefaultValues(): void
    {
        // A new ProfileModel should have empty string default values for most properties
        $this->assertEquals('', $this->profileModel->getCivility());
        $this->assertEquals('', $this->profileModel->getLastName());
        $this->assertEquals('', $this->profileModel->getFirstName());
        $this->assertEquals('', $this->profileModel->getAddress1());
        $this->assertEquals('', $this->profileModel->getAddress2());
        $this->assertEquals('', $this->profileModel->getCity());
        $this->assertEquals('', $this->profileModel->getCountry());
        $this->assertEquals('', $this->profileModel->getZipcode());
        $this->assertEquals('', $this->profileModel->getPhone());
        $this->assertEquals('', $this->profileModel->getEmail());
    }
}
