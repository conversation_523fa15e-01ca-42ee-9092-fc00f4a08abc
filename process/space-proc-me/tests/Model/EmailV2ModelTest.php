<?php

namespace App\Tests\Model;

use App\Model\EmailV2Model;
use PHPUnit\Framework\TestCase;

class EmailV2ModelTest extends TestCase
{
    private EmailV2Model $emailModel;
    
    protected function setUp(): void
    {
        $this->emailModel = new EmailV2Model();
    }
    
    public function testEmailGetterAndSetter(): void
    {
        $email = '<EMAIL>';
        $this->emailModel->setEmail($email);
        $this->assertEquals($email, $this->emailModel->getEmail());
    }
    
    public function testEmailApvGetterAndSetter(): void
    {
        $emailApv = '<EMAIL>';
        $this->emailModel->setEmailApv($emailApv);
        $this->assertEquals($emailApv, $this->emailModel->getEmailApv());
    }
    
    public function testEmailAgentGetterAndSetter(): void
    {
        $emailAgent = '<EMAIL>';
        $this->emailModel->setEmailAgent($emailAgent);
        $this->assertEquals($emailAgent, $this->emailModel->getEmailAgent());
    }
    
    public function testEmailGerGetterAndSetter(): void
    {
        $emailGer = '<EMAIL>';
        $this->emailModel->setEmailGer($emailGer);
        $this->assertEquals($emailGer, $this->emailModel->getEmailGer());
    }
    
    public function testEmailGrcGetterAndSetter(): void
    {
        $emailGrc = '<EMAIL>';
        $this->emailModel->setEmailGrc($emailGrc);
        $this->assertEquals($emailGrc, $this->emailModel->getEmailGrc());
    }
    
    public function testEmailPrGetterAndSetter(): void
    {
        $emailPr = '<EMAIL>';
        $this->emailModel->setEmailPr($emailPr);
        $this->assertEquals($emailPr, $this->emailModel->getEmailPr());
    }
    
    public function testEmailSalesGetterAndSetter(): void
    {
        $emailSales = '<EMAIL>';
        $this->emailModel->setEmailSales($emailSales);
        $this->assertEquals($emailSales, $this->emailModel->getEmailSales());
    }
    
    public function testEmailVoGetterAndSetter(): void
    {
        $emailVo = '<EMAIL>';
        $this->emailModel->setEmailVo($emailVo);
        $this->assertEquals($emailVo, $this->emailModel->getEmailVo());
    }
    
    public function testNullValues(): void
    {
        // Test with null values
        $this->emailModel->setEmail(null);
        $this->emailModel->setEmailApv(null);
        $this->emailModel->setEmailAgent(null);
        $this->emailModel->setEmailGer(null);
        $this->emailModel->setEmailGrc(null);
        $this->emailModel->setEmailPr(null);
        $this->emailModel->setEmailSales(null);
        $this->emailModel->setEmailVo(null);
        
        $this->assertNull($this->emailModel->getEmail());
        $this->assertNull($this->emailModel->getEmailApv());
        $this->assertNull($this->emailModel->getEmailAgent());
        $this->assertNull($this->emailModel->getEmailGer());
        $this->assertNull($this->emailModel->getEmailGrc());
        $this->assertNull($this->emailModel->getEmailPr());
        $this->assertNull($this->emailModel->getEmailSales());
        $this->assertNull($this->emailModel->getEmailVo());
    }
    
    public function testEmptyStringValues(): void
    {
        // Test with empty string values
        $this->emailModel->setEmail('');
        $this->emailModel->setEmailApv('');
        $this->emailModel->setEmailAgent('');
        $this->emailModel->setEmailGer('');
        $this->emailModel->setEmailGrc('');
        $this->emailModel->setEmailPr('');
        $this->emailModel->setEmailSales('');
        $this->emailModel->setEmailVo('');
        
        $this->assertEquals('', $this->emailModel->getEmail());
        $this->assertEquals('', $this->emailModel->getEmailApv());
        $this->assertEquals('', $this->emailModel->getEmailAgent());
        $this->assertEquals('', $this->emailModel->getEmailGer());
        $this->assertEquals('', $this->emailModel->getEmailGrc());
        $this->assertEquals('', $this->emailModel->getEmailPr());
        $this->assertEquals('', $this->emailModel->getEmailSales());
        $this->assertEquals('', $this->emailModel->getEmailVo());
    }
}
