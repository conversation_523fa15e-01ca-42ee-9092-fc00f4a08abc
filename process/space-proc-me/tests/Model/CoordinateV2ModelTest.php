<?php

namespace App\Tests\Model;

use App\Model\CoordinateV2Model;
use PHPUnit\Framework\TestCase;

class CoordinateV2ModelTest extends TestCase
{
    private CoordinateV2Model $coordinateModel;
    
    protected function setUp(): void
    {
        $this->coordinateModel = new CoordinateV2Model();
    }
    
    public function testLatitudeGetterAndSetter(): void
    {
        $latitude = 48.8566;
        $this->coordinateModel->setLatitude($latitude);
        $this->assertEquals($latitude, $this->coordinateModel->getLatitude());
    }
    
    public function testLongitudeGetterAndSetter(): void
    {
        $longitude = 2.3522;
        $this->coordinateModel->setLongitude($longitude);
        $this->assertEquals($longitude, $this->coordinateModel->getLongitude());
    }
    
    public function testAltitudeGetterAndSetter(): void
    {
        $altitude = 35.5;
        $this->coordinateModel->setAltitude($altitude);
        $this->assertEquals($altitude, $this->coordinateModel->getAltitude());
    }
    
    public function testNullValues(): void
    {
        // Test with null values
        $this->coordinateModel->setLatitude(null);
        $this->coordinateModel->setLongitude(null);
        $this->coordinateModel->setAltitude(null);
        
        $this->assertNull($this->coordinateModel->getLatitude());
        $this->assertNull($this->coordinateModel->getLongitude());
        $this->assertNull($this->coordinateModel->getAltitude());
    }
    
    public function testZeroValues(): void
    {
        // Test with zero values
        $this->coordinateModel->setLatitude(0.0);
        $this->coordinateModel->setLongitude(0.0);
        $this->coordinateModel->setAltitude(0.0);
        
        $this->assertEquals(0.0, $this->coordinateModel->getLatitude());
        $this->assertEquals(0.0, $this->coordinateModel->getLongitude());
        $this->assertEquals(0.0, $this->coordinateModel->getAltitude());
    }
    
    public function testNegativeValues(): void
    {
        // Test with negative values
        $this->coordinateModel->setLatitude(-48.8566);
        $this->coordinateModel->setLongitude(-2.3522);
        $this->coordinateModel->setAltitude(-35.5);
        
        $this->assertEquals(-48.8566, $this->coordinateModel->getLatitude());
        $this->assertEquals(-2.3522, $this->coordinateModel->getLongitude());
        $this->assertEquals(-35.5, $this->coordinateModel->getAltitude());
    }
}
