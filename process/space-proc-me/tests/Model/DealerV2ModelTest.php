<?php

namespace App\Tests\Model;

use App\Model\AddressV2Model;
use App\Model\CoordinateV2Model;
use App\Model\DataModel;
use App\Model\DealerV2Model;
use App\Model\EmailV2Model;
use App\Model\PhoneV2Model;
use App\Model\PrincipalModel;
use App\Model\UrlPageV2Model;
use App\Model\WebSiteV2Model;
use PHPUnit\Framework\TestCase;

class DealerV2ModelTest extends TestCase
{
    private DealerV2Model $dealerModel;

    protected function setUp(): void
    {
        $this->dealerModel = new DealerV2Model();
    }

    public function testConstructor(): void
    {
        // Test that the constructor initializes the required properties
        $this->assertInstanceOf(UrlPageV2Model::class, $this->dealerModel->getUrlPages());
        $this->assertInstanceOf(WebSiteV2Model::class, $this->dealerModel->getWebSites());
        $this->assertInstanceOf(PrincipalModel::class, $this->dealerModel->getPrincipal());
    }

    public function testSiteGeoGetterAndSetter(): void
    {
        $siteGeo = 'SITE123';
        $this->dealerModel->setSiteGeo($siteGeo);
        $this->assertEquals($siteGeo, $this->dealerModel->getSiteGeo());
    }

    public function testIdGetterAndSetter(): void
    {
        $id = 'DEALER123';
        $this->dealerModel->setId($id);
        $this->assertEquals($id, $this->dealerModel->getId());
    }

    public function testRrdiGetterAndSetter(): void
    {
        $rrdi = 'RRDI123';
        $this->dealerModel->setRrdi($rrdi);
        $this->assertEquals($rrdi, $this->dealerModel->getRrdi());
    }

    public function testNameGetterAndSetter(): void
    {
        $name = 'Dealer Name';
        $this->dealerModel->setName($name);
        $this->assertEquals($name, $this->dealerModel->getName());
    }

    public function testPhoneGetterAndSetter(): void
    {
        $phone = new PhoneV2Model();
        $phone->setPhoneNumber('123456789');

        $this->dealerModel->setPhone($phone);
        $this->assertSame($phone, $this->dealerModel->getPhone());
        $this->assertEquals('123456789', $this->dealerModel->getPhone()->getPhoneNumber());
    }

    public function testEmailGetterAndSetter(): void
    {
        $email = new EmailV2Model();
        $email->setEmail('<EMAIL>');

        $this->dealerModel->setEmail($email);
        $this->assertSame($email, $this->dealerModel->getEmail());
        $this->assertEquals('<EMAIL>', $this->dealerModel->getEmail()->getEmail());
    }

    public function testAddressGetterAndSetter(): void
    {
        $address = new AddressV2Model();
        $address->setAddress1('123 Main St');
        $address->setCity('Paris');

        $this->dealerModel->setAddress($address);
        $this->assertSame($address, $this->dealerModel->getAddress());
        $this->assertEquals('123 Main St', $this->dealerModel->getAddress()->getAddress1());
        $this->assertEquals('Paris', $this->dealerModel->getAddress()->getCity());
    }

    public function testCoordinatesGetterAndSetter(): void
    {
        $coordinates = new CoordinateV2Model();
        $coordinates->setLatitude(48.8566);
        $coordinates->setLongitude(2.3522);

        $this->dealerModel->setCoordinates($coordinates);
        $this->assertSame($coordinates, $this->dealerModel->getCoordinates());
        $this->assertEquals(48.8566, $this->dealerModel->getCoordinates()->getLatitude());
        $this->assertEquals(2.3522, $this->dealerModel->getCoordinates()->getLongitude());
    }

    public function testDistanceGetterAndSetter(): void
    {
        $distance = 10.5;
        $this->dealerModel->setDistance($distance);
        $this->assertEquals($distance, $this->dealerModel->getDistance());
    }

    public function testIsAgentGetterAndSetter(): void
    {
        $isAgent = true;
        $this->dealerModel->setIsAgent($isAgent);
        $this->assertEquals($isAgent, $this->dealerModel->getIsAgent());
    }

    public function testIsAgentApGetterAndSetter(): void
    {
        $isAgentAp = true;
        $this->dealerModel->setIsAgentAp($isAgentAp);
        $this->assertEquals($isAgentAp, $this->dealerModel->getIsAgentAp());
    }

    public function testIsSecondaryGetterAndSetter(): void
    {
        $isSecondary = true;
        $this->dealerModel->setIsSecondary($isSecondary);
        $this->assertEquals($isSecondary, $this->dealerModel->getIsSecondary());
    }

    public function testIsSuccursaleGetterAndSetter(): void
    {
        $isSuccursale = true;
        $this->dealerModel->setIsSuccursale($isSuccursale);
        $this->assertEquals($isSuccursale, $this->dealerModel->getIsSuccursale());
    }

    public function testBusinessGetterAndSetter(): void
    {
        $business = ['business1', 'business2'];
        $this->dealerModel->setBusiness($business);
        $this->assertEquals($business, $this->dealerModel->getBusiness());
    }

    public function testOpenHoursGetterAndSetter(): void
    {
        $openHours = ['monday' => '9-18', 'tuesday' => '9-18'];
        $this->dealerModel->setOpenHours($openHours);
        $this->assertEquals($openHours, $this->dealerModel->getOpenHours());
    }

    public function testPrincipalGetterAndSetter(): void
    {
        $principal = new PrincipalModel();
        $principal->setIsPrincipalVn(true);

        $this->dealerModel->setPrincipal($principal);
        $this->assertSame($principal, $this->dealerModel->getPrincipal());
        $this->assertTrue($this->dealerModel->getPrincipal()->getIsPrincipalVn());
    }

    public function testUrlPagesGetterAndSetter(): void
    {
        $urlPages = new UrlPageV2Model();
        $urlPages->setUrlContact('https://example.com/contact');

        $this->dealerModel->setUrlPages($urlPages);
        $this->assertSame($urlPages, $this->dealerModel->getUrlPages());
        $this->assertEquals('https://example.com/contact', $this->dealerModel->getUrlPages()->getUrlContact());
    }

    public function testWebSitesGetterAndSetter(): void
    {
        $webSites = new WebSiteV2Model();
        $webSites->setPublic('https://example.com');

        $this->dealerModel->setWebSites($webSites);
        $this->assertSame($webSites, $this->dealerModel->getWebSites());
        $this->assertEquals('https://example.com', $this->dealerModel->getWebSites()->getPublic());
    }

    public function testCultureGetterAndSetter(): void
    {
        $culture = 'fr_FR';
        $this->dealerModel->setCulture($culture);
        $this->assertEquals($culture, $this->dealerModel->getCulture());
    }

    public function testDataGetterAndSetter(): void
    {
        $data = new DataModel();
        $this->dealerModel->setData($data);
        $this->assertSame($data, $this->dealerModel->getData());
    }

    public function testIsCaracRdviGetterAndSetter(): void
    {
        $isCaracRdvi = true;
        $this->dealerModel->setIsCaracRdvi($isCaracRdvi);
        $this->assertEquals($isCaracRdvi, $this->dealerModel->getIsCaracRdvi());
    }

    public function testTypeOperateurGetterAndSetter(): void
    {
        $typeOperateur = 'TYPE1';
        $this->dealerModel->setTypeOperateur($typeOperateur);
        $this->assertEquals($typeOperateur, $this->dealerModel->getTypeOperateur());
    }

    public function testUrlPrdvErcsGetterAndSetter(): void
    {
        $urlPrdvErcs = 'https://example.com/prdv';
        $this->dealerModel->setUrlPrdvErcs($urlPrdvErcs);
        $this->assertEquals($urlPrdvErcs, $this->dealerModel->getUrlPrdvErcs());
    }

    public function testJockeyGetterAndSetter(): void
    {
        $jockey = true;
        $this->dealerModel->setJockey($jockey);
        $this->assertEquals($jockey, $this->dealerModel->getJockey());
    }

    public function testO2xGetterAndSetter(): void
    {
        $o2x = true;
        $this->dealerModel->setO2x($o2x);
        $this->assertEquals($o2x, $this->dealerModel->getO2x());
    }

    public function testPreferredGetterAndSetter(): void
    {
        $preferred = false;
        $this->dealerModel->setPreferred($preferred);
        $this->assertEquals($preferred, $this->dealerModel->getPreferred());
    }
}
