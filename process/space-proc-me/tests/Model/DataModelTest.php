<?php

namespace App\Tests\Model;

use App\Model\AdrLivVnListModel;
use App\Model\CodeActorModel;
use App\Model\CodeRegionModel;
use App\Model\ContratVlModel;
use App\Model\DataModel;
use App\Model\GroupModel;
use App\Model\ImporterModel;
use App\Model\IndicatorModel;
use App\Model\PdvImporterModel;
use PHPUnit\Framework\TestCase;

class DataModelTest extends TestCase
{
    private DataModel $dataModel;
    
    protected function setUp(): void
    {
        $this->dataModel = new DataModel();
    }
    
    public function testConstructor(): void
    {
        // Test that the constructor initializes the required properties
        $this->assertInstanceOf(CodeActorModel::class, $this->dataModel->getCodesActors());
        $this->assertInstanceOf(CodeRegionModel::class, $this->dataModel->getCodesRegions());
        $this->assertInstanceOf(GroupModel::class, $this->dataModel->getGroup());
        $this->assertInstanceOf(IndicatorModel::class, $this->dataModel->getIndicator());
        $this->assertInstanceOf(ImporterModel::class, $this->dataModel->getImporter());
        $this->assertInstanceOf(PdvImporterModel::class, $this->dataModel->getPdvImporter());
        $this->assertInstanceOf(ContratVlModel::class, $this->dataModel->getContratVl());
    }
    
    public function testBenefitListGetterAndSetter(): void
    {
        $benefitList = ['benefit1', 'benefit2'];
        $this->dataModel->setBenefitList($benefitList);
        $this->assertEquals($benefitList, $this->dataModel->getBenefitList());
    }
    
    public function testCodesActorsGetterAndSetter(): void
    {
        $codesActors = new CodeActorModel();
        $this->dataModel->setCodesActors($codesActors);
        $this->assertSame($codesActors, $this->dataModel->getCodesActors());
    }
    
    public function testCodesRegionsGetterAndSetter(): void
    {
        $codesRegions = new CodeRegionModel();
        $this->dataModel->setCodesRegions($codesRegions);
        $this->assertSame($codesRegions, $this->dataModel->getCodesRegions());
    }
    
    public function testFaxNumberGetterAndSetter(): void
    {
        $faxNumber = '*********';
        $this->dataModel->setFaxNumber($faxNumber);
        $this->assertEquals($faxNumber, $this->dataModel->getFaxNumber());
    }
    
    public function testGroupGetterAndSetter(): void
    {
        $group = new GroupModel();
        $this->dataModel->setGroup($group);
        $this->assertSame($group, $this->dataModel->getGroup());
    }
    
    public function testIndicatorGetterAndSetter(): void
    {
        $indicator = new IndicatorModel();
        $this->dataModel->setIndicator($indicator);
        $this->assertSame($indicator, $this->dataModel->getIndicator());
    }
    
    public function testWelcomeMessageGetterAndSetter(): void
    {
        $welcomeMessage = 'Welcome to our dealership!';
        $this->dataModel->setWelcomeMessage($welcomeMessage);
        $this->assertEquals($welcomeMessage, $this->dataModel->getWelcomeMessage());
    }
    
    public function testImporterGetterAndSetter(): void
    {
        $importer = new ImporterModel();
        $this->dataModel->setImporter($importer);
        $this->assertSame($importer, $this->dataModel->getImporter());
    }
    
    public function testPdvImporterGetterAndSetter(): void
    {
        $pdvImporter = new PdvImporterModel();
        $this->dataModel->setPdvImporter($pdvImporter);
        $this->assertSame($pdvImporter, $this->dataModel->getPdvImporter());
    }
    
    public function testNumSiretGetterAndSetter(): void
    {
        $numSiret = '*********01234';
        $this->dataModel->setNumSiret($numSiret);
        $this->assertEquals($numSiret, $this->dataModel->getNumSiret());
    }
    
    public function testLegalStatusGetterAndSetter(): void
    {
        $legalStatus = 'SAS';
        $this->dataModel->setLegalStatus($legalStatus);
        $this->assertEquals($legalStatus, $this->dataModel->getLegalStatus());
    }
    
    public function testCapitalGetterAndSetter(): void
    {
        $capital = '100000€';
        $this->dataModel->setCapital($capital);
        $this->assertEquals($capital, $this->dataModel->getCapital());
    }
    
    public function testCommercialRegisterGetterAndSetter(): void
    {
        $commercialRegister = 'Paris B *********';
        $this->dataModel->setCommercialRegister($commercialRegister);
        $this->assertEquals($commercialRegister, $this->dataModel->getCommercialRegister());
    }
    
    public function testIntracommunityTvaGetterAndSetter(): void
    {
        $intracommunityTva = 'FR*********01';
        $this->dataModel->setIntracommunityTva($intracommunityTva);
        $this->assertEquals($intracommunityTva, $this->dataModel->getIntracommunityTva());
    }
    
    public function testBrandGetterAndSetter(): void
    {
        $brand = 'FT';
        $this->dataModel->setBrand($brand);
        $this->assertEquals($brand, $this->dataModel->getBrand());
    }
    
    public function testParentSiteGeoGetterAndSetter(): void
    {
        $parentSiteGeo = 'SITE123';
        $this->dataModel->setParentSiteGeo($parentSiteGeo);
        $this->assertEquals($parentSiteGeo, $this->dataModel->getParentSiteGeo());
    }
    
    public function testRaisonSocialGetterAndSetter(): void
    {
        $raisonSocial = 'Dealer Company SAS';
        $this->dataModel->setRaisonSocial($raisonSocial);
        $this->assertEquals($raisonSocial, $this->dataModel->getRaisonSocial());
    }
    
    public function testRcsNumberGetterAndSetter(): void
    {
        $rcsNumber = '*********';
        $this->dataModel->setRcsNumber($rcsNumber);
        $this->assertEquals($rcsNumber, $this->dataModel->getRcsNumber());
    }
    
    public function testGmCodeListGetterAndSetter(): void
    {
        $gmCodeList = ['GM001', 'GM002'];
        $this->dataModel->setGmCodeList($gmCodeList);
        $this->assertEquals($gmCodeList, $this->dataModel->getGmCodeList());
    }
    
    public function testLienVoListGetterAndSetter(): void
    {
        $lienVoList = ['VO001', 'VO002'];
        $this->dataModel->setLienVoList($lienVoList);
        $this->assertEquals($lienVoList, $this->dataModel->getLienVoList());
    }
    
    public function testBqCaptiveGetterAndSetter(): void
    {
        $bqCaptive = 'BQ001';
        $this->dataModel->setBqCaptive($bqCaptive);
        $this->assertEquals($bqCaptive, $this->dataModel->getBqCaptive());
    }
    
    public function testCaracRdviGetterAndSetter(): void
    {
        $caracRdvi = 'RDVI001';
        $this->dataModel->setCaracRdvi($caracRdvi);
        $this->assertEquals($caracRdvi, $this->dataModel->getCaracRdvi());
    }
    
    public function testFtcCodeListGetterAndSetter(): void
    {
        $ftcCodeList = ['FTC001', 'FTC002'];
        $this->dataModel->setFtcCodeList($ftcCodeList);
        $this->assertEquals($ftcCodeList, $this->dataModel->getFtcCodeList());
    }
    
    public function testAdrLivVnListGetterAndSetter(): void
    {
        $adrLivVnList = [new AdrLivVnListModel()];
        $this->dataModel->setAdrLivVnList($adrLivVnList);
        $this->assertEquals($adrLivVnList, $this->dataModel->getAdrLivVnList());
    }
    
    public function testContratVlGetterAndSetter(): void
    {
        $contratVl = new ContratVlModel();
        $this->dataModel->setContratVl($contratVl);
        $this->assertSame($contratVl, $this->dataModel->getContratVl());
    }
    
    public function testNullValues(): void
    {
        // Test with null values for array properties
        $this->dataModel->setBenefitList(null);
        $this->dataModel->setGmCodeList(null);
        $this->dataModel->setLienVoList(null);
        $this->dataModel->setFtcCodeList(null);
        $this->dataModel->setAdrLivVnList(null);
        
        $this->assertNull($this->dataModel->getBenefitList());
        $this->assertNull($this->dataModel->getGmCodeList());
        $this->assertNull($this->dataModel->getLienVoList());
        $this->assertNull($this->dataModel->getFtcCodeList());
        $this->assertNull($this->dataModel->getAdrLivVnList());
    }
}
