<?php

namespace App\Tests\Model;

use App\Model\DealerServiceModel;
use PHPUnit\Framework\TestCase;

class DealerServiceModelTest extends TestCase
{
    private DealerServiceModel $dealerServiceModel;
    
    protected function setUp(): void
    {
        $this->dealerServiceModel = new DealerServiceModel();
    }
    
    public function testCodeGetterAndSetter(): void
    {
        $code = 'SERVICE001';
        $this->dealerServiceModel->setCode($code);
        $this->assertEquals($code, $this->dealerServiceModel->getCode());
    }
    
    public function testLabelGetterAndSetter(): void
    {
        $label = 'Service Label';
        $this->dealerServiceModel->setLabel($label);
        $this->assertEquals($label, $this->dealerServiceModel->getLabel());
    }
    
    public function testTypeGetterAndSetter(): void
    {
        $type = 'Service Type';
        $this->dealerServiceModel->setType($type);
        $this->assertEquals($type, $this->dealerServiceModel->getType());
    }
    
    public function testNullValues(): void
    {
        // Test with null values
        $this->dealerServiceModel->setCode(null);
        $this->dealerServiceModel->setLabel(null);
        $this->dealerServiceModel->setType(null);
        
        // getCode() returns string, so null becomes empty string or throws an error
        // Let's catch the error if it's thrown
        try {
            $this->dealerServiceModel->getCode();
            $this->fail('Expected an error when getting null code');
        } catch (\Throwable $e) {
            $this->assertTrue(true, 'Error thrown as expected');
        }
        
        $this->assertNull($this->dealerServiceModel->getLabel());
        $this->assertNull($this->dealerServiceModel->getType());
    }
    
    public function testEmptyStringValues(): void
    {
        // Test with empty string values
        $this->dealerServiceModel->setCode('');
        $this->dealerServiceModel->setLabel('');
        $this->dealerServiceModel->setType('');
        
        $this->assertEquals('', $this->dealerServiceModel->getCode());
        $this->assertEquals('', $this->dealerServiceModel->getLabel());
        $this->assertEquals('', $this->dealerServiceModel->getType());
    }
    
    public function testDefaultValues(): void
    {
        // Test default values
        $newModel = new DealerServiceModel();
        
        // getCode() might return null or empty string depending on implementation
        try {
            $code = $newModel->getCode();
            $this->assertNull($code);
        } catch (\Throwable $e) {
            $this->assertTrue(true, 'Error thrown as expected for default code');
        }
        
        $this->assertEquals('', $newModel->getLabel());
        $this->assertEquals('', $newModel->getType());
    }
}
