<?php

namespace App\Tests\Model;

use App\Model\AdrLivVnListModel;
use PHPUnit\Framework\TestCase;

class AdrLivVnListModelTest extends TestCase
{
    private AdrLivVnListModel $adrLivVnListModel;
    
    protected function setUp(): void
    {
        $this->adrLivVnListModel = new AdrLivVnListModel();
    }
    
    public function testActorAdrCodeGetterAndSetter(): void
    {
        $actorAdrCode = 'ADR123';
        $this->adrLivVnListModel->setActorAdrCode($actorAdrCode);
        $this->assertEquals($actorAdrCode, $this->adrLivVnListModel->getActorAdrCode());
    }
    
    public function testCityGetterAndSetter(): void
    {
        $city = 'Paris';
        $this->adrLivVnListModel->setCity($city);
        $this->assertEquals($city, $this->adrLivVnListModel->getCity());
    }
    
    public function testCommercialNameGetterAndSetter(): void
    {
        $commercialName = 'Dealer Name';
        $this->adrLivVnListModel->setCommercialName($commercialName);
        $this->assertEquals($commercialName, $this->adrLivVnListModel->getCommercialName());
    }
    
    public function testLine1GetterAndSetter(): void
    {
        $line1 = '123 Main St';
        $this->adrLivVnListModel->setLine1($line1);
        $this->assertEquals($line1, $this->adrLivVnListModel->getLine1());
    }
    
    public function testLine2GetterAndSetter(): void
    {
        $line2 = 'Apt 4B';
        $this->adrLivVnListModel->setLine2($line2);
        $this->assertEquals($line2, $this->adrLivVnListModel->getLine2());
    }
    
    public function testIsDefaultGetterAndSetter(): void
    {
        $isDefault = true;
        $this->adrLivVnListModel->setIsDefault($isDefault);
        $this->assertEquals($isDefault, $this->adrLivVnListModel->getIsDefault());
    }
    
    public function testTypeGetterAndSetter(): void
    {
        $type = 'TYPE1';
        $this->adrLivVnListModel->setType($type);
        $this->assertEquals($type, $this->adrLivVnListModel->getType());
    }
    
    public function testNullValues(): void
    {
        // Test with null values
        $this->adrLivVnListModel->setActorAdrCode(null);
        $this->adrLivVnListModel->setCity(null);
        $this->adrLivVnListModel->setCommercialName(null);
        $this->adrLivVnListModel->setLine1(null);
        $this->adrLivVnListModel->setLine2(null);
        $this->adrLivVnListModel->setIsDefault(null);
        $this->adrLivVnListModel->setType(null);
        
        $this->assertNull($this->adrLivVnListModel->getActorAdrCode());
        $this->assertNull($this->adrLivVnListModel->getCity());
        $this->assertNull($this->adrLivVnListModel->getCommercialName());
        $this->assertNull($this->adrLivVnListModel->getLine1());
        $this->assertNull($this->adrLivVnListModel->getLine2());
        $this->assertNull($this->adrLivVnListModel->getIsDefault());
        $this->assertNull($this->adrLivVnListModel->getType());
    }
    
    public function testEmptyStringValues(): void
    {
        // Test with empty string values
        $this->adrLivVnListModel->setActorAdrCode('');
        $this->adrLivVnListModel->setCity('');
        $this->adrLivVnListModel->setCommercialName('');
        $this->adrLivVnListModel->setLine1('');
        $this->adrLivVnListModel->setLine2('');
        $this->adrLivVnListModel->setType('');
        
        $this->assertEquals('', $this->adrLivVnListModel->getActorAdrCode());
        $this->assertEquals('', $this->adrLivVnListModel->getCity());
        $this->assertEquals('', $this->adrLivVnListModel->getCommercialName());
        $this->assertEquals('', $this->adrLivVnListModel->getLine1());
        $this->assertEquals('', $this->adrLivVnListModel->getLine2());
        $this->assertEquals('', $this->adrLivVnListModel->getType());
    }
}
