<?php

namespace App\Tests\Model;

use App\Model\PdvImporterModel;
use PHPUnit\Framework\TestCase;

class PdvImporterModelTest extends TestCase
{
    private PdvImporterModel $pdvImporterModel;
    
    protected function setUp(): void
    {
        $this->pdvImporterModel = new PdvImporterModel();
    }
    
    public function testPdvCodeGetterAndSetter(): void
    {
        $pdvCode = 'PDV001';
        $this->pdvImporterModel->setPdvCode($pdvCode);
        $this->assertEquals($pdvCode, $this->pdvImporterModel->getPdvCode());
    }
    
    public function testPdvNameGetterAndSetter(): void
    {
        $pdvName = 'PDV Name';
        $this->pdvImporterModel->setPdvName($pdvName);
        $this->assertEquals($pdvName, $this->pdvImporterModel->getPdvName());
    }
    
    public function testPdvContactGetterAndSetter(): void
    {
        $pdvContact = 'PDV Contact';
        $this->pdvImporterModel->setPdvContact($pdvContact);
        $this->assertEquals($pdvContact, $this->pdvImporterModel->getPdvContact());
    }
    
    public function testNullValues(): void
    {
        // Test with null values
        $this->pdvImporterModel->setPdvCode(null);
        $this->pdvImporterModel->setPdvName(null);
        $this->pdvImporterModel->setPdvContact(null);
        
        $this->assertNull($this->pdvImporterModel->getPdvCode());
        $this->assertNull($this->pdvImporterModel->getPdvName());
        $this->assertNull($this->pdvImporterModel->getPdvContact());
    }
    
    public function testEmptyStringValues(): void
    {
        // Test with empty string values
        $this->pdvImporterModel->setPdvCode('');
        $this->pdvImporterModel->setPdvName('');
        $this->pdvImporterModel->setPdvContact('');
        
        $this->assertEquals('', $this->pdvImporterModel->getPdvCode());
        $this->assertEquals('', $this->pdvImporterModel->getPdvName());
        $this->assertEquals('', $this->pdvImporterModel->getPdvContact());
    }
    
    public function testDefaultValues(): void
    {
        // Test default values
        $newModel = new PdvImporterModel();
        
        $this->assertEquals('', $newModel->getPdvCode());
        $this->assertEquals('', $newModel->getPdvName());
        $this->assertEquals('', $newModel->getPdvContact());
    }
    
    public function testFluentInterface(): void
    {
        // Test fluent interface (method chaining)
        $result = $this->pdvImporterModel
            ->setPdvCode('PDV001')
            ->setPdvName('PDV Name')
            ->setPdvContact('PDV Contact');
        
        $this->assertSame($this->pdvImporterModel, $result);
    }
}
