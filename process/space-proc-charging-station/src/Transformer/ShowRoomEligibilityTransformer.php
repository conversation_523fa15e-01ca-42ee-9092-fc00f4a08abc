<?php

namespace App\Transformer;

use App\Model\ShowRoomEligibility;
use App\Model\ShowRoomEligibilityList;

class ShowRoomEligibilityTransformer
{
    public static function mapIshowRoomData(array $data): ShowRoomEligibilityList
    {
        $ishowRoomEligibilities = new ShowRoomEligibilityList();
        foreach ($data as $item) {
            $iShowRoomEligibility = new ShowRoomEligibility();
            $iShowRoomEligibility
            ->setId($item['id'] ?? '')
            ->setConnectionOfferId($item['connectionOfferId'] ?? '')
            ->setEmail($item['email'] ?? '')
            ->setPhone($item['phone'] ?? '')
            ->setFirstName($item['firstName'] ?? '')
            ->setLastName($item['lastName'] ?? '')
            ->setCountryCode($item['countryCode'] ?? '')
            ->setLanguage($item['language'] ?? '')
            ->setStatus($item['status'] ?? '')
            ->setBenefitType($item['benefitType'] ?? '')
            ->setVin($item['vin'] ?? '')
            ->setFixedAmount($item['fixedAmount'] ?? '')
            ->setFixedAmount($item['userId'] ?? '');
            $ishowRoomEligibilities->addShowRoomEligibility($iShowRoomEligibility);
        }

        return $ishowRoomEligibilities;
    }
}
