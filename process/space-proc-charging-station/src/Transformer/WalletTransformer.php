<?php

namespace App\Transformer;

use App\Model\Wallet;

class WalletTransformer
{
    public static function mapWalletData(array $data): Wallet
    {
        $wallet = new Wallet();
        $wallet->setTotalSpentInUsd($data['totalSpentInUsd'] ?? 0);
        $wallet->setTotalSpentInCad($data['totalSpentInCad'] ?? 0);
        $wallet->setAccountBalance($data['accountBalance'] ?? '');
        if (isset($data['totalCredits'])) {
            $wallet->setTotalCredits($data['totalCredits']);
        }
        if (isset($data['walletBalance'])) {
            $wallet->setWalletBalance($data['walletBalance']);
        }

        return $wallet;
    }
}
