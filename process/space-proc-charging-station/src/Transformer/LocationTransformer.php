<?php

namespace App\Transformer;

use App\Model\AddressModel;
use App\Model\AvailabilityModel;
use App\Model\ChargeTypeAvailabilityModel;
use App\Model\ChargingCapacityModel;
use App\Model\ConnectorModel;
use App\Model\CoordinateModel;
use App\Model\LocationModel;
use App\Model\PoiModel;
use App\Model\PowerLevelModel;

class LocationTransformer
{
    public static function mapLocationData(array $dataArray): array
    {
        $locationModels = [];
        foreach ($dataArray as $data) {
            $location = new LocationModel();

            if (isset($data['addressDetail'])) {
                $address = new AddressModel();
                $address->setCountry($data['addressDetail']['country'] ?? '')
                    ->setCountryCode($data['addressDetail']['country'] ?? '')
                    ->setCountryCodeISO3($data['addressDetail']['countryCodeISO3'] ?? '')
                    ->setCountrySecondarySubdivision($data['addressDetail']['countrySecondarySubdivision'] ?? '')
                    ->setCountrySubdivision($data['addressDetail']['countrySubdivision'] ?? '')
                    ->setCountrySubdivisionName($data['addressDetail']['countrySubdivisionName'] ?? '')
                    ->setCountryTertiarySubdivision($data['addressDetail']['countryTertiarySubdivision'] ?? '')
                    ->setExtendedPostalCode($data['addressDetail']['extendedPostalCode'] ?? '')
                    ->setfreeformAddress(implode(', ', array_filter([
                        $data['addressDetail']['address'] ?? null,
                        $data['addressDetail']['city'] ?? null,
                        $data['addressDetail']['state'] ?? null,
                        $data['addressDetail']['postalCode'] ?? null,
                        $data['addressDetail']['country'] ?? null,
                    ])))
                    ->setLocalName($data['addressDetail']['localName'] ?? '')
                    ->setMunicipality($data['addressDetail']['city'] ?? '')
                    ->setPostalCode($data['addressDetail']['postalCode'] ?? '')
                    ->setStreetName($data['addressDetail']['address'] ?? '')
                    ->setStreetNumber($data['addressDetail']['streetNumber'] ?? '');

                $location->setAddress($address);
            }

            $location->setBrand($data['operator'] ?? '');

            if (isset($data['evses'])) {
                foreach ($data['evses'] as $evseData) {
                    if (isset($evseData['connectors'])) {
                        foreach ($evseData['connectors'] as $connectorData) {
                            $connector = new ConnectorModel();

                            $availability = new AvailabilityModel();
                            $connectorStatus = strtoupper($connectorData['status']);
                            $availability->setAvailable('AVAILABLE' === strtoupper($connectorStatus) ? 1 : 0)
                                ->setOccupied(0)
                                ->setOutOfService('OFFLINE' === strtoupper($connectorStatus) ? 1 : 0)
                                ->setReserved(0)
                                ->setUnknown('UNKONOWN' === strtoupper($connectorStatus) ? 1 : 0);
                            $connector->setAvailability($availability);

                            $connector->setCompatible(false);

                            $powerLevel = new PowerLevelModel();
                            $chargeTypeAvailability = new ChargeTypeAvailabilityModel();

                            $isFast = 'FAST' === strtoupper($connectorData['speed']);
                            $isRegular = 'REGULAR' === strtoupper($connectorData['speed']);

                            $chargeTypeAvailability->setFastCharge($isFast ? 1 : 0);
                            $chargeTypeAvailability->setRegularCharge($isRegular ? 1 : 0);
                            $chargeTypeAvailability->setSlowCharge(0);
                            $chargeTypeAvailability->setUnknown(!$isFast && !$isRegular ? 1 : 0);

                            $chargingCapacity = new ChargingCapacityModel();
                            $chargingCapacity->setChargingMode(!$isFast && !$isRegular ? 'UNKNOWN' : '');
                            $chargingCapacity->setPowerKw($connectorData['power'] ?? '');
                            $chargingCapacity->setType('');

                            $powerLevel = new PowerLevelModel();
                            $powerLevel->setChargeTypeAvailability($chargeTypeAvailability);
                            $powerLevel->addChargingCapacity($chargingCapacity);
                            $connector->setPowerLevel($powerLevel);

                            $connector->setTotal(1);
                            $connector->setType($connectorData['type'] ?? '');
                            $connector->setEvseId($evseData['evseId'] ?? '');
                            $connector->setConnectorId($connectorData['connectorId'] ?? '');
                            $connector->setPriceText($connectorData['priceText'] ?? '');
                            $connector->setSpeedShortCode($connectorData['speedShortCode'] ?? '');
                            $connector->setCapabilities($connectorData['capabilities'] ?? []);

                            $location->addConnector($connector);
                        }
                    }
                }
            }
            $location->setId($data['id'] ?? '')
                     ->setLocationId($data['locationId'] ?? '');

            $poi = new PoiModel();
            $poi->setAcceptablePayments(["ELECTRONIC_PURSE",
                                        "ELECTRONIC_TOLL_COLLECTION",
                                        "SERVICE_PROVIDER_PAYMENT_METHOD",
                                        "FUEL_CARD"]);
            $poi->setAccessType('');
            $poi->setName($data['operator'] ?? '');
            $poi->setTwentyFourSeven($data['twentyFourSeven'] ?? false);
            $poi->setPartnerIDs([]);
            $poi->setSpecialRestrictions([]);
            $location->setPoi($poi);

            if (isset($data['coordinates'])) {
                $coordinates = new CoordinateModel();
                $coordinates->setLat($data['coordinates']['latitude'] ?? null)
                    ->setLon($data['coordinates']['longitude'] ?? null);
                $location->setPosition($coordinates);
            }

            $locationModels[] = $location;
        }

        return $locationModels;
    }
}
