<?php

declare(strict_types=1);

namespace App\Exception;

use Symfony\Component\HttpFoundation\Response;

/**
 * Class MongoAtlasUrlNotProvidedException
 */
class MongoAtlasUrlNotProvidedException extends \InvalidArgumentException
{
    /**
     * Make a new exception.
     */
    public static function make(): self
    {
        return new self('MongoAtlas error!', Response::HTTP_NOT_FOUND);
    }
}
