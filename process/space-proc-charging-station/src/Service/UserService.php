<?php

namespace App\Service;

use App\Connector\F2mConnector;
use App\Helper\WSResponse;
use App\Trait\LoggerTrait;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Manager\ChargingStationManagementManager;

class UserService
{
    use LoggerTrait;

    public const COLLECTION = 'userData';

    public function __construct(
        private MongoAtlasQueryService $mongoService,
        private F2mConnector $connector,
    ) {}

    public function getUserByUserIdFilter(?string $userId)
    {
        return
            [
                [
                    '$match' => ['userId' => $userId],
                ],
            ];
    }

    public function getUserByUserIdAndVinFilter(?string $userId, ?string $vin)
    {
        return
            [
                [
                    '$match' => ['userId' => $userId, 'vehicle.vin' => $vin],
                ],
            ];
    }

    public function getUserByUserIdAndVin(?string $userId, ?string $vin): WSResponse
    {
        $this->logger->info('Getting user', ['userId' => $userId, 'vin' => $vin]);

        return $this->mongoService->aggregate(self::COLLECTION, $this->getUserByUserIdAndVinFilter($userId, $vin));
    }

    public function getUserByUserId(?string $userId): WSResponse
    {
        $this->logger->info('Getting user', ['userId' => $userId]);

        return $this->mongoService->aggregate(self::COLLECTION, $this->getUserByUserIdFilter($userId));
    }

    public function getUserData(string $userId): array
    {
        $userData = $this->mongoService->find(self::COLLECTION, ['userId' => $userId]);

        return json_decode($userData->getData(), true)['documents'][0] ?? [];
    }

    public function register(array $payload): WSResponse
    {
        $response = $this->connector->callF2mc(
            Request::METHOD_POST,
            'v1/user/enrolment',
            $payload
        );

        if (Response::HTTP_OK == $response->getCode()) {
            $responseContent = $response->getData();
            if (isset($responseContent['success']['accessToken'])) {
                return new WSResponse($response->getCode(), $responseContent);
            }

            return new WSResponse(Response::HTTP_NOT_FOUND, $responseContent);
        }

        return new WSResponse($response->getCode(), $response->getData());
    }

    public function getPaymentMethodUrl(string $token): WSResponse
    {
        $headers = [
            'accessToken' => $token ?? null,
        ];

        $options = [
            'headers' => $headers,
        ];
        $response = $this->connector->callF2mc(
            Request::METHOD_GET,
            'v1/user/payment-method-url',
            $options
        );

        if (Response::HTTP_OK == $response->getCode()) {
            $responseContent = $response->getData();

            return new WSResponse(Response::HTTP_NOT_FOUND, $responseContent['success']['url']);
        }

        return new WSResponse($response->getCode(), $response->getData());
    }

    public function registerF2mcUserInMongoDb(array $data, string $userId): WSResponse
    {
        return $this->mongoService->updateFirstDocument(self::COLLECTION, $data, ['userId' => $userId]);
    }


    public function updateF2mcUserInMongoDb(array $data, string $userId, string $vin)
    {
        $updateData = [
            "vehicle.$.chargingStation" => $data
        ];

        $this->mongoService->updateFirstDocument(
            self::COLLECTION,
            $updateData,
            ['userId' => $userId, 'vehicle.vin' => $vin]
        );
    }

    public function getAccountLinkUrl(string $token, string $providerShortCode): WSResponse
    {
        $uri = 'v1/user/account-link-url/' . $providerShortCode;

        $headers = [
            'accessToken' => $token,
            'Content-Type' => 'application/json'
        ];

        $options = [
            'headers' => $headers
        ];
        $wsResponse = $this->connector->callF2mc(
            Request::METHOD_GET,
            $uri,
            $options
        );
        if (Response::HTTP_CREATED == $wsResponse->getCode()) {
            $response = $wsResponse->getData();
            if (isset($response['data'])) {
                return new WSResponse($wsResponse->getCode(), $response['data']);
            }

            return new WSResponse(Response::HTTP_NOT_FOUND, $wsResponse->getData());
        }

        return new WSResponse($wsResponse->getCode(), $wsResponse->getData());
    }

    public function updateDBChargingStation(array $filter, array $data)
    {
        return $this->mongoService->updateOne(self::COLLECTION, $filter, $data, true);
    }

    public function getUserByProfileIdFilter(?string $profileId)
    {
        return
            [
                [
                    '$match' => ['f2mc.userId' => $profileId],
                ],
            ];
    }

    public function getUserByProfileId(?string $profileId): WSResponse
    {
        $this->logger->info('Getting user', ['f2mc.userId' => $profileId]);

        return $this->mongoService->aggregate(self::COLLECTION, $this->getUserByProfileIdFilter($profileId));
    }

    public function updateUserVehiclesFeatureCodeByStatus(string $userId, string $status)
    {
        try {
            // First, find the document to update
            $filter = [
                'userId' => $userId
            ];

            $response = $this->mongoService->findOne('userData', $filter);

            if (Response::HTTP_OK !== $response->getCode()) {
                $this->logger->error(__CLASS__ . '::' . __METHOD__ . " No user data found");
                return false;
            }

            $data = json_decode($response->getData(), true);
            $userDocument = $data['document'] ?? [];

            if (empty($userDocument)) {
                $this->logger->error(__CLASS__ . '::' . __METHOD__ . " Empty user document");
                return false;
            }

            // for each vehicle, check vehicly engine type to filter out electric vehicles
            $vehicles = $userDocument['vehicle'] ?? [];
            if ($vehicles) {
                foreach ($vehicles as $index => $vehicle) {
                    $engineType = $vehicle['type'] ?? '';
                    if (in_array($engineType, ['BEV', 'PHEV', 'HFCV'])) {
                        // update feature code status
                        foreach ($vehicle['featureCodes'] ?? [] as $featureIndex => $featureCode) {
                            if ($featureCode['code'] === ChargingStationManagementManager::CSM_FEATURE_CODE) {
                                $userDocument['vehicle'][$index]['featureCodes'][$featureIndex]['config']['enrolmentStatus'] = $status;
                            }
                        }
                    }
                }
            }

            $updateData = [
                "vehicle" => $userDocument['vehicle']
            ];

            $response = $this->mongoService->updateFirstDocument('userData', $updateData, ['userId' => $userId]);
            if (Response::HTTP_OK !== $response->getCode()) {
                $this->logger->error(__CLASS__ . '::' . __METHOD__ . " Error updating user document", [
                    'userId' => $userId,
                    'response' => $response->getData()
                ]);
                return false;
            }

            return true;
        } catch (\Throwable $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . " Exception while updating feature code status", [
                'userId' => $userId,
                'status' => $status,
                'exception' => $e->getMessage()
            ]);
            return false;
        }
    }

    public function updateUserVehicleId(string $userId, string $vehicleId, string $vin)
    {
        $filter = [
            'userId' => $userId
        ];
        $document = $this->mongoService->find('userData', $filter);
        $document = json_decode($document->getData(), true)['documents'][0] ?? [];
        foreach ($document['vehicle'] ?? [] as $k => $vehicleItem) {
            if ($vehicleItem['vin'] == $vin) {
                $document['vehicle'][$k]['chargingStation']['vehicleId'] = $vehicleId;
            }
        }
        $filter =  ['userId' => $document['userId']];
        return $this->mongoService->updateFirstDocument('userData', ['vehicle' => $document['vehicle']], $filter);
    }

    /**
     * Updates the feature code status for a specific vehicle by VIN
     *
     * @param string $userId The user ID
     * @param string $status The enrollment status to set
     * @param string $vin The vehicle identification number
     * @return mixed Returns the update result or false on failure
     */
    public function updateUserVehiclesFeatureCodeByStatusAndVin(string $userId, string $status, string $vin): bool
    {
        try {
            // First, find the document to update
            $filter = [
                'userId' => $userId,
                'vehicle.vin' => $vin
            ];

            $response = $this->mongoService->findOne('userData', $filter);

            if (Response::HTTP_OK !== $response->getCode()) {
                $this->logger->error(__CLASS__ . '::' . __METHOD__ . " No user data found");
                return false;
            }

            $data = json_decode($response->getData(), true);
            $userDocument = $data['document'] ?? [];

            if (empty($userDocument)) {
                $this->logger->error(__CLASS__ . '::' . __METHOD__ . " Empty user document");
                return false;
            }

            // Find vehicle index and update feature code directly
            foreach ($userDocument['vehicle'] ?? [] as $index => $vehicle) {
                if ($vehicle['vin'] === $vin) {
                    $vehicleIndex = $index;
                    $updatedFeatureCodes = $vehicle['featureCodes'] ?? [];

                    // Check if CHARGING_STATION_MANAGEMENT exists
                    foreach ($updatedFeatureCodes as $featureIndex => $featureCode) {
                        if ($featureCode['code'] === ChargingStationManagementManager::CSM_FEATURE_CODE) {
                            $updatedFeatureCodes[$featureIndex]['config']['enrolmentStatus'] = $status;
                            break;
                        }
                    }
                    break;
                }
            }

            if ($vehicleIndex === null || $updatedFeatureCodes === null) {
                $this->logger->error(__CLASS__ . '::' . __METHOD__ . " Vehicle not found");
                return false;
            }

            // Update only the featureCodes for the specific vehicle
            $updateData = [
                "vehicle.{$vehicleIndex}.featureCodes" => $updatedFeatureCodes
            ];

            $response = $this->mongoService->updateFirstDocument('userData', $updateData, ['userId' => $userId]);
            if (Response::HTTP_OK !== $response->getCode()) {
                $this->logger->error(__CLASS__ . '::' . __METHOD__ . " Error updating user document", [
                    'userId' => $userId,
                    'response' => $response->getData()
                ]);
                return false;
            }
            return true;
        } catch (\Throwable $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . " Exception while updating feature code status", [
                'userId' => $userId,
                'vin' => $vin,
                'status' => $status,
                'exception' => $e->getMessage()
            ]);
            return false;
        }
    }

    public function findUserVehicleByVin(string $userId, string $vin): WSResponse
    {
        try {
            $filter = [
                'userId' => $userId,
                'vehicle' => [
                    '$elemMatch' => [
                        'vin' => [
                            '$regex' => '^' . preg_quote($vin, '/') . '$',
                            '$options' => 'i'
                        ]
                    ]
                ]
            ];

            $projection = [
                'vehicle.$' => 1,
                'f2mc' => 1
            ];

            $result = $this->mongoService->findOne(self::COLLECTION, $filter, $projection);

            if (Response::HTTP_OK !== $result->getCode()) {
                $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error while fetching user vehicle by vin', [
                    'userId' => $userId,
                    'vin' => $vin
                ]);

                return new WSResponse(
                    $result->getCode(),
                    ['error' => ['message' => 'Error while fetching user vehicle data']]
                );
            }

            $data = json_decode($result->getData(), true);
            $success = false;
            $vehicle = null;

            if (isset($data['document']['vehicle']) && !empty($data['document']['vehicle'])) {
                $vehicle = $data['document']['vehicle'][0];
                $success = true;
            }
            if (isset($data['document']['f2mc']) && !empty($data['document']['f2mc'])) {
                $f2mc = $data['document']['f2mc'];
            }

            return new WSResponse(Response::HTTP_OK, ['success' => $success, 'vehicle' => $vehicle, 'f2mc' => $f2mc]);
        } catch (\Throwable $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error while fetching user vehicle by vin', [
                'userId' => $userId,
                'vin' => $vin,
                'exception' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    private function getUserByF2MUserIdFilter(string $f2mUserId, string $chargingSessionId): array
    {
        return [
            [
                '$match' => [
                    'f2mc.userId' => $f2mUserId,
                    'vehicle.chargingStation.session.id' => $chargingSessionId
                ]
            ]
        ];
    }

    public function getUserByF2MUserId(string $f2mUserId, string $chargingSessionId): WSResponse
    {
        $this->logger->info('Getting user', ['userId' => $f2mUserId, 'chargingSessionId' => $chargingSessionId]);
 
        return $this->mongoService->aggregate(self::COLLECTION, $this->getUserByF2MUserIdFilter($f2mUserId, $chargingSessionId));
    }

    public function updateUserRemoteCommandStatus(string $f2mUserId, string $chargingSessionId, string $status): WSResponse
    {
        $this->logger->info('Updating user remote command status', [
            'f2mUserId' => $f2mUserId,
            'chargingSessionId' => $chargingSessionId,
            'status' => $status
        ]);
 
        $filter = ['f2mc.userId' => $f2mUserId, 'vehicle.chargingStation.session.id' => $chargingSessionId];
 
        $fields = ['vehicle.$.chargingStation.session.remoteCommandStatus' => $status];
 
        return $this->mongoService->updateOne(self::COLLECTION, $filter, $fields);
    }

    private function getUserByOtherIdsFilter(string $f2mUserId, string $locationId, string $evseId, string $connectorId)
    {
        return [
            [
                '$match' => [
                    'f2mc.userId' => $f2mUserId,
                    'vehicle.chargingStation.session.id' => null,
                    'vehicle.chargingStation.session.locationId' => $locationId,
                    'vehicle.chargingStation.session.evseId' => $evseId,
                    'vehicle.chargingStation.session.connectorId' => $connectorId
                ]
            ]
        ];
    }

    public function getUserByOtherIds(string $f2mUserId, string $locationId, string $evseId, string $connectorId): WSResponse
    {
        $this->logger->info('Getting user', ['f2mUserId' => $f2mUserId, 'locationId' => $locationId, 'evseId' => $evseId, 'connectorId' => $connectorId]);
        
        return $this->mongoService->aggregate(self::COLLECTION, $this->getUserByOtherIdsFilter($f2mUserId, $locationId, $evseId, $connectorId));
    }

    public function updateUserRemoteCommandStatusUsingOtherIds(string $f2mUserId, string $locationId, string $evseId, string $connectorId, string $chargingSessionId, string $status): WSResponse
    {
        $this->logger->info('Updating user remote command status', [
            'f2mUserId' => $f2mUserId,
            'locationId' => $locationId,
            'evseId' => $evseId,
            'connectorId' => $connectorId,
            'chargingSessionId' => $chargingSessionId,
            'status' => $status
        ]);
        $filter = [
            'f2mc.userId' => $f2mUserId,
            'vehicle.chargingStation.session.id' => null,
            'vehicle.chargingStation.session.locationId' => $locationId,
            'vehicle.chargingStation.session.evseId' => $evseId,
            'vehicle.chargingStation.session.connectorId' => $connectorId
        ];
        $fields = [
            'vehicle.$.chargingStation.session.id' => $chargingSessionId,
            'vehicle.$.chargingStation.session.remoteCommandStatus' => $status
        ];
 
        return $this->mongoService->updateOne(self::COLLECTION, $filter, $fields);
    }
}