<?php

namespace App\Service;

use App\Connector\MongoAtlasApiConnector;
use App\Exception\MongoAtlasUrlNotProvidedException;
use App\Helper\WSResponse;
use App\Model\MongoQueryModel;
use App\Trait\LoggerTrait;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;

/**
 * This service helps to interact with mongoAtlas api.
 */
class MongoAtlasQueryService
{
    use LoggerTrait;

    private MongoQueryModel $query;

    public function __construct(
        private NormalizerInterface $normalizer,
        private MongoAtlasApiConnector $connector,
        private string $dataSource,
        string $database
    ) {
        $this->query = self::buildQuery($dataSource, $database);
    }

    /**
     * Build mongo query.
     */
    public static function buildQuery(string $dataSource, string $database): MongoQueryModel
    {
        return (new MongoQueryModel())
            ->setDataSource($dataSource)
            ->setDatabase($database);
    }

    /**
     * Perform find request throughout MongoAtlas Api.
     */
    public function find(string $collection, ?array $filter, ?int $limit = 1, ?array $projection = null): WSResponse
    {
        $this->query->setCollection($collection)
            ->setFilter($filter ?? [])
            ->setLimit($limit);

        //  Check if filter is provided and set it on the query
        if ($filter) {
            $this->query->setFilter($filter);
        }
        if ($projection !== null) {
            $this->query->setProjection($projection);
        }

        return $this->execute('find');
    }

    /**
     * Perform findOne request throughout MongoDB Atlas API.
     */
    public function findOne(string $collection, ?array $filter = null, ?array $projection = null): WSResponse
    {
        $this->query->setCollection($collection)
                    ->setFilter($filter ?? []);

        if ($projection !== null) {
            $this->query->setProjection($projection);
        }

        return $this->execute('findOne');
    }

    /**
     * Perform update single document request by adding new sub-document throughout MongoAtlas Api.
     */
    public function updatePush(string $collection, array $filter, array $fields, ?bool $upsert = false): WSResponse
    {
        $this->query->setCollection($collection)
                    ->setFilter($filter)
                    ->setUpdate(['$push' => $fields])
                    ->setUpsert($upsert ?? false);

        return $this->execute('updateOne');
    }

    /**
     * Perform insert single document request throughout MongoAtlas Api.
     */
    public function insertOne(string $collection, array $document): WSResponse
    {
        $this->query->setCollection($collection)
                    ->setDocument($document);

        return $this->execute('insertOne');
    }

    /**
     *  Perform insert multiple documents request throughout MongoAtlas Api.
     */
    public function insertMany(string $collection, array $documents): WSResponse
    {
        $this->query->setCollection($collection)
                    ->setDocuments($documents);

        return $this->execute('insertMany');
    }

    public function deleteFields(string $collection, array $filter, array $fields, ?bool $upsert = false): WSResponse
    {
        $this->query->setCollection($collection)
                    ->setFilter($filter)
                    ->setUpdate(['$unset' => $fields])
                    ->setUpsert($upsert ?? false);

        return $this->execute('updateOne');
    }

    public function removeFields(string $collection, array $filter, array $pull, ?bool $upsert = false): WSResponse
    {
        $this->query->setCollection($collection)
                    ->setFilter($filter)
                    ->setUpdate([
                        '$pull' => $pull
                    ])
                    ->setUpsert($upsert ?? false);

        return $this->execute('updateOne');
    }

    /**
     * Add an attribute to the first document in the collection.
     */
    public function updateFirstDocument(string $collection, array $data, $filters = []): WSResponse
    {
        $this->query->setCollection($collection)
            ->setFilter($filters) // Empty filter to target the first document
            ->setUpsert(true)
            ->setUpdate([
                '$set' => $data
            ]);

        return $this->execute('updateOne');
    }

    /**
     * Execute Mongo request.
     */
    private function execute(string $action): WSResponse
    {
        try {
            $this->logger->info(__METHOD__ . " Action [$action]");
            $endpoint = $this->connector->getEndpoint($action);
            $collection = $this->normalizer->normalize($this->query, 'array', ['groups' => ['default', $action]]);

            $response = $this->connector->call(Request::METHOD_POST, $endpoint, ['json' => $collection]);

            return new WSResponse($response->getStatusCode(), $response->getContent(false));
        } catch (\Exception $e) {
            $this->logger->error(__METHOD__ .' Catched Exception:'.$e->getMessage(), [
                'exception' => $e->getMessage(),
                'code' => $e->getCode(),
                'trace' => $e->getTraceAsString(),
                'collection' => $collection ?? [],
            ]);
            throw MongoAtlasUrlNotProvidedException::make();
        }
    }

    public function updateMany(string $collection, array $filter, array $fields, ?bool $upsert = false, ?array $arrayFilters = null): WSResponse
    {
        $this->query->setCollection($collection)
            ->setFilter($filter)
            ->setUpdate($fields)
            ->setUpsert($upsert ?? false);

        // Add arrayFilters to the query if provided
        if (null !== $arrayFilters) {
            $this->query->setArrayFilters($arrayFilters);
        }

        return $this->execute('updateMany');
    }

    /**
     * Perform aggregate request throughout MongoAtlas Api.
     */
    public function aggregate(string $collection, ?array $pipeline): WSResponse
    {
        $this->query->setCollection($collection)
            ->setPipeline($pipeline ?? []);

        return $this->execute('aggregate');
    }

    /**
     * Perform update single document request throughout MongoAtlas Api.
     */
    public function updateOne(
        string $collection,
        array $filter,
        array $fields,
        ?bool $upsert = false,
        ?array $options = []
    ): WSResponse {
        $this->query->setCollection($collection)
                    ->setFilter($filter)
                    ->setUpdate(['$set' => $fields])
                    ->setUpsert($upsert ?? false);

        return $this->execute('updateOne');
    }
}
