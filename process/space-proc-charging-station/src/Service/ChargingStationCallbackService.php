<?php

declare(strict_types=1);

namespace App\Service;

use App\Helper\ErrorResponse;
use App\Helper\IResponseArrayFormat;
use App\Helper\SuccessResponse;
use App\Trait\LoggerTrait;
use Symfony\Component\HttpFoundation\Response;

class ChargingStationCallbackService
{
    use LoggerTrait;

    public const USER_COLLECTION = 'userData';

    public function __construct(
        private MongoAtlasQueryService $mongoService,
        private SystemFirebaseClient $systemFirebaseClient
    ) {
    }

    private function getUserChargingSession(string $userId, ?string $internalId = null, ?string $sessionId = null): ?array
    {
        $idName = $internalId !== null ? 'internalId' : 'id';
        $idValue = $internalId ?? $sessionId;

        $pipeline = [
            [
                '$match' => [
                    'userId' => $userId
                ]
            ],
            [
                '$unwind' => '$vehicle'
            ],
            [
                '$match' => [
                    'vehicle.chargingStation.session' => [
                        '$elemMatch' => [
                            $idName => $idValue
                        ]
                    ]
                ]
            ],
            [
                '$project' => [
                    '_id' => 1,
                    'userId' => 1,
                    'session' => [
                        '$filter' => [
                            'input' => '$vehicle.chargingStation.session',
                            'as' => 'session',
                            'cond' => [
                                '$eq' => ['$$session.'.$idName, $idValue]
                            ]
                        ]
                    ]
                ]
            ],
            [
                '$unwind' => '$session'
            ],
            [
                '$project' => [
                    '_id' => 1,
                    'userId' => 1,
                    'session' => '$session'
                ]
            ]
        ];

        $result = $this->mongoService->aggregate(self::USER_COLLECTION, $pipeline);

        if ($result->getCode() !== Response::HTTP_OK) {
            $this->logger->error(__METHOD__.': Error while getting charging session by userId', [
                'userId' => $userId,
                'internalId' => $internalId,
                'sessionId' => $sessionId,
                'result' => $result->getData()
            ]);

            return null;
        }

        $data = json_decode($result->getData(), true);
        $documents = $data['documents'] ?? [];
        $document = !empty($documents) && isset($documents[0]['session']) ? $documents[0]['session'] : [];

        return $document;
    }

    public function getChargingSessionByUserIdAndInternalId(string $userId, string $internalId): ?array
    {
        return $this->getUserChargingSession($userId, $internalId);
    }

    public function getChargingSessionByUserIdAndSessionId(string $userId, string $sessionId): ?array
    {
        return $this->getUserChargingSession($userId, null, $sessionId);
    }

    private function updateUserChargingSession(
        string $userId,
        ?string $internalId,
        ?string $sessionId,
        array $chargingSession
    ): bool {
        $document = $this->getUserDocumentByChargingSession($userId, $sessionId, $internalId);
        if (empty($document)) {
            $this->logger->error(__METHOD__.': Charging session not found by userId and session identifier', [
                'userId' => $userId,
                'sessionId' => $sessionId,
                'internalId' => $internalId
            ]);

            return false;
        }

        $indexes = $this->getIndexesByChargingSession($document, $sessionId, $internalId);
        if (empty($indexes) || !isset($indexes['vehicleIndex']) || !isset($indexes['sessionsIndex'])) {
            $this->logger->error(__METHOD__.': Session object\'s indexes not found', [
                'userId' => $userId,
                'sessionId' => $sessionId,
                'internalId' => $internalId,
                'indexes' => $indexes
            ]);

            return false;
        }

        $filter = [
            'userId' => $userId
        ];

        $toUpdate = [
            'vehicle.'.$indexes['vehicleIndex'].'.chargingStation.session.'.$indexes['sessionsIndex'].'.remoteCommandStatus' => $chargingSession['remoteCommandStatus']
        ];

        $result = $this->mongoService->updateOne(self::USER_COLLECTION, $filter, $toUpdate);
        if ($result->getCode() !== Response::HTTP_OK) {
            $this->logger->error(__METHOD__.': Error while updating charging session', [
                'userId' => $userId,
                'internalId' => $internalId,
                'chargingSession' => $chargingSession,
                'toUpdate' => $toUpdate,
                'result' => $result->getData()
            ]);

            return false;
        }

        $response = json_decode($result->getData(), true);
        if ($response['modifiedCount'] !== 1) {
            $this->logger->error(__METHOD__.': Charging session not updated', [
                'userId' => $userId,
                'internalId' => $internalId,
                'chargingSession' => $chargingSession,
                'toUpdate' => $toUpdate,
                'response' => $response
            ]);

            return false;
        }

        return true;
    }

    public function updateChargingSessionByUserIdAndInternalId(
        string $userId,
        string $internalId,
        array $chargingSession
    ): bool {
        return $this->updateUserChargingSession($userId, $internalId, null, $chargingSession);
    }

    public function updateChargingSessionByUserIdAndSessionId(
        string $userId,
        string $sessionId,
        array $chargingSession
    ): bool {
        return $this->updateUserChargingSession($userId, null, $sessionId, $chargingSession);
    }

    public function sendNotification(string $userId, array $chargingSession): IResponseArrayFormat
    {
        try {
            $message = [
                'notification' => [
                    'title' => 'charging station update',
                    'body' => 'charging of vehicle has been updated'
                ],
                'content_available' => true,
                'data' => [
                    'provider' => 'space-mid',
                    'event' => [
                        'type' => 'chargingStationUpdate',
                        'details' => [
                            'session' => $chargingSession
                        ]
                    ]
                ]
            ];

            $this->systemFirebaseClient->sendNotification([$userId], $message);

            return new SuccessResponse();
        } catch (\Exception $e) {
            $this->logger->error(__METHOD__ .': Error while sending notification', [
                'userId' => $userId,
                'chargingSession' => $chargingSession,
                'exception' => $e->getMessage(),
                'code' => $e->getCode(),
                'trace' => $e->getTraceAsString(),
            ]);

            return new ErrorResponse(
                __METHOD__ .': Error while sending notification',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    public function getUserDocumentBySessionId(string $userId, string $sessionId): ?array
    {
        return $this->getUserDocumentByChargingSession($userId, $sessionId);
    }

    public function getUserDocumentByInternalId(string $userId, string $internalId): ?array
    {
        return $this->getUserDocumentByChargingSession($userId, null, $internalId);
    }

    private function getUserDocumentByChargingSession(
        string $userId,
        ?string $sessionId = null,
        ?string $internalId = null
    ): ?array {
        $idName = $internalId !== null ? 'internalId' : 'id';
        $idValue = $internalId ?? $sessionId;

        $filter = [
            'userId' => $userId,
            'vehicle.chargingStation.session.'.$idName => $idValue
        ];
        $result = $this->mongoService->find(self::USER_COLLECTION, $filter, 1);

        $data = json_decode($result->getData(), true);
        $documents = $data['documents'] ?? [];
        $document = !empty($documents) ? $documents[0] : [];

        return $document;
    }

    private function getIndexesByChargingSession(array $document, ?string $sessionId = null, ?string $internalId = null): array
    {
        $idName = $internalId !== null ? 'internalId' : 'id';
        $idValue = $internalId ?? $sessionId;

        if (!isset($document['vehicle']) || !is_array($document['vehicle'])) {
            return []; // Vehicle array not found or invalid
        }

        foreach ($document['vehicle'] as $vehicleIndex => &$vehicle) {
            if (isset($vehicle['chargingStation']['session']) && is_array($vehicle['chargingStation']['session'])) {
                foreach ($vehicle['chargingStation']['session'] as $sessionIndex => &$session) {
                    if (isset($session[$idName]) && $session[$idName] === $idValue) {
                        return [
                            'vehicleIndex' => $vehicleIndex,
                            'sessionsIndex' => $sessionIndex
                        ];
                    }
                }
            }
        }

        return []; // Session not found
    }
}
