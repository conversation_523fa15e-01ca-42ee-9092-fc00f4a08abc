<?php

namespace App\Service;

use App\Connector\F2mConnector;
use App\Helper\WSResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Contracts\HttpClient\HttpClientInterface;

/**
 * Vehicle Service
 */
class VehicleService
{
    public function __construct(
        private F2mConnector $connector,
        private HttpClientInterface $client
    ) {
    }

    /**
     * get vehicle list
     */
    public function getList(string $accesstoken): WSResponse
    {
        $uri = 'v1/user/vehicles';
        $headers = [
            'accesstoken' => $accesstoken,
            'accept' => 'application/json'
        ];

        $options = [
            'headers' => $headers,
        ];
        $response = $this->connector->callF2mc(
            Request::METHOD_GET,
            $uri,
            $options
        );

        return new WSResponse($response->getCode(), $response->getData());
    }

    /**
     * Add a new vehicle function
     */
    public function add(array $data): WSResponse
    {
        $payload = $data['params'];
        $accessToken = $data['accessToken'];

        $headers = [
            'accept' => 'application/json',
            'accesstoken' => $accessToken,
            'Content-Type' => 'application/json',
        ];

        $response = $this->connector->callF2mc(
            Request::METHOD_POST,
            "v1/user/vehicles",
            [
                'headers' => $headers,
                'json' => $payload,
            ]
        );

        return new WSResponse($response->getCode(), $response->getData());
    }

    /**
     * Update vehicle function
     */
    public function update(array $data): WSResponse
    {
        $vehicleId = $data['vehicleId'];
        $payload = $data['params'];
        $accessToken = $data['accessToken'];

        $headers = [
            'accept' => 'application/json',
            'accesstoken' => $accessToken,
            'Content-Type' => 'application/json',
        ];

        $response = $this->connector->callF2mc(
            Request::METHOD_PUT,
            "v1/user/vehicles/{$vehicleId}/default",
            [
                'headers' => $headers,
                'json' => $payload,
            ]
        );

        return new WSResponse($response->getCode(), $response->getData());
    }

    /**
     * Delete vehicle function
     */
    public function delete(array $data): WSResponse
    {
        $vehicleId = $data['vehicleId'];
        $accessToken = $data['accessToken'];

        $headers = [
            'accept' => 'application/json',
            'accesstoken' => $accessToken,
        ];

        $response = $this->connector->callF2mc(
            Request::METHOD_DELETE,
            "v1/user/vehicles/{$vehicleId}",
            [
                'headers' => $headers
            ]
        );

        return new WSResponse($response->getCode(), $response->getData());
    }
}
