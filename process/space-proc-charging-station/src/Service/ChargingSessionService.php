<?php

namespace App\Service;

use App\Connector\F2mConnector;
use App\Helper\WSResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\HttpClient\HttpClientInterface;

/**
 * Charging Session service.
 */
class ChargingSessionService
{
    public const USER_COLLECTION = 'userData';

    public function __construct(
        private F2mConnector $connector,
        private HttpClientInterface $client
    ) {
    }

    public function getHistory(array $params, ?string $token = null): WSResponse
    {
        $uri = 'v1/charging-sessions/history';

        $headers = [
            'accessToken' => $token ?? null,
            'Content-Type' => 'application/json',
        ];

        $options = [
            'headers' => $headers,
            'query' => $params,
        ];
        $wsResponse = $this->connector->callF2mc(
            Request::METHOD_GET,
            $uri,
            $options
        );
        if (Response::HTTP_CREATED == $wsResponse->getCode()) {
            $response = $wsResponse->getData();
            if (isset($response['data'])) {
                return new WSResponse($wsResponse->getCode(), $response['data']);
            }

            return new WSResponse(Response::HTTP_NOT_FOUND, $wsResponse->getData());
        }

        return new WSResponse($wsResponse->getCode(), $wsResponse->getData());
    }

    public function getIshowroomEligibility(string $token, $email): WSResponse
    {
        $uri = 'v1/user/ishowroom/eligibility';

        $headers = [
            'accessToken' => $token ?? null,
            'Content-Type' => 'application/json',
        ];

        $options = [
            'headers' => $headers,
            'json' => ['email' => $email],
        ];
        $wsResponse = $this->connector->callF2mc(
            Request::METHOD_POST,
            $uri,
            $options
        );
        if (Response::HTTP_CREATED == $wsResponse->getCode()) {
            $response = $wsResponse->getData();
            if (isset($response['data'])) {
                return new WSResponse($wsResponse->getCode(), $response['data']);
            }

            return new WSResponse(Response::HTTP_NOT_FOUND, $wsResponse->getData());
        }

        return new WSResponse($wsResponse->getCode(), $wsResponse->getData());
    }

    public function getWalletDetail(string $token): WSResponse
    {
        $uri = 'v1/user/wallet';

        $headers = [
            'accessToken' => $token,
            'Content-Type' => 'application/json',
        ];

        $options = [
            'headers' => $headers,
        ];
        $wsResponse = $this->connector->callF2mc(
            Request::METHOD_GET,
            $uri,
            $options
        );
        if (Response::HTTP_CREATED == $wsResponse->getCode()) {
            $response = $wsResponse->getData();
            if (isset($response['data'])) {
                return new WSResponse($wsResponse->getCode(), $response['data']);
            }

            return new WSResponse(Response::HTTP_NOT_FOUND, $wsResponse->getData());
        }

        return new WSResponse($wsResponse->getCode(), $wsResponse->getData());
    }

    public function stopSession(string $token, array $body): WSResponse
    {
        $uri = 'v1/charging-sessions/stop';

        $headers = [
            'accessToken' => $token,
            'Content-Type' => 'application/json',
        ];

        $options = [
            'headers' => $headers,
            'json' => $body,
        ];
        $wsResponse = $this->connector->callF2mc(
            Request::METHOD_POST,
            $uri,
            $options
        );
        if (Response::HTTP_CREATED == $wsResponse->getCode()) {
            $response = $wsResponse->getData();
            if (isset($response['data'])) {
                return new WSResponse($wsResponse->getCode(), $response['data']);
            }

            return new WSResponse(Response::HTTP_NOT_FOUND, $wsResponse->getData());
        }

        return new WSResponse($wsResponse->getCode(), $wsResponse->getData());
    }

    public function startSession(string $token, array $body): WSResponse
    {
        $uri = 'v1/charging-sessions/start';

        $headers = [
            'accessToken' => $token,
            'Content-Type' => 'application/json'
        ];

        $options = [
            'headers' => $headers,
            'json' => $body
        ];
        $wsResponse = $this->connector->callF2mc(
            Request::METHOD_POST,
            $uri,
            $options
        );
        if (Response::HTTP_CREATED == $wsResponse->getCode()) {
            $response = $wsResponse->getData();
            if (isset($response['data'])) {
                return new WSResponse($wsResponse->getCode(), $response['data']);
            }

            return new WSResponse(Response::HTTP_NOT_FOUND, $wsResponse->getData());
        }

        return new WSResponse($wsResponse->getCode(), $wsResponse->getData());
    }

    public function applyIshowroomCredit(string $token, string $id): WSResponse
    {
        $uri = 'v1/user/ishowroom/credit';
        $headers = [
            'accept' => 'application/json',
            'accesstoken' => $token,
            'Content-Type' => 'application/json'
        ];

        $options = [
            'json' => ['id' => $id],
            'headers' => $headers,
        ];
        $applyCreditResponse = $this->connector->callF2mc(
            Request::METHOD_POST,
            $uri,
            $options
        );

        return $applyCreditResponse;
    }

    public function getSession(string $sessionId, string $accessToken): WSResponse
    {
        $headers = [
            'accesstoken' => $accessToken
        ];
        $wsResponse = $this->connector->callF2mc(
            Request::METHOD_GET,
            "v1/charging/session/{$sessionId}",
            [
                'headers' => $headers
            ]
        );

        if (Response::HTTP_OK == $wsResponse->getCode()) {
            $response = $wsResponse->getData();
            if (isset($response['success'])) {
                return new WSResponse($wsResponse->getCode(), $response);
            }

            return new WSResponse(Response::HTTP_UNAUTHORIZED, $wsResponse->getData());
        }

        return new WSResponse($wsResponse->getCode(), $wsResponse->getData());
    }

    public function getCdrData(string $token, string $sessionId): WSResponse
    {
        $uri = "v1/charging/session/{$sessionId}/cdr";

        $headers = [
            'accessToken' => $token,
            'Content-Type' => 'application/json'
        ];

        $options = [
            'headers' => $headers
        ];
        $wsResponse = $this->connector->callF2mc(
            Request::METHOD_GET,
            $uri,
            $options
        );
        if (Response::HTTP_CREATED == $wsResponse->getCode()) {
            $response = $wsResponse->getData();
            if (isset($response['data'])) {
                return new WSResponse($wsResponse->getCode(), $response['data']);
            }

            return new WSResponse(Response::HTTP_NOT_FOUND, $wsResponse->getData());
        }

        return new WSResponse($wsResponse->getCode(), $wsResponse->getData());
    }

    public function getPaymentMethodUrl(string $accessToken): WSResponse
    {
        $headers = [
            'accesstoken' => $accessToken
        ];

        $wsResponse = $this->connector->callF2mc(
            Request::METHOD_GET,
            'v1/user/payment-method-url',
            [
                'headers' => $headers
            ]
        );

        if (Response::HTTP_OK == $wsResponse->getCode()) {
            $response = $wsResponse->getData();
            if (isset($response['success'])) {
                return new WSResponse($wsResponse->getCode(), $response['success']);
            }

            return new WSResponse(Response::HTTP_NOT_FOUND, $wsResponse->getData());
        }

        return new WSResponse($wsResponse->getCode(), $wsResponse->getData());
    }

    public function getPaymentHistory(string $accessToken): WSResponse
    {
        $headers = [
            'accept' => 'application/json',
            'accesstoken' => $accessToken,
            'Content-Type' => 'application/json'
        ];
        $wsResponse = $this->connector->callF2mc(
            Request::METHOD_GET,
            'v1/user/transactions',
            [
                'headers' => $headers
            ]
        );

        return $wsResponse;
    }
}
