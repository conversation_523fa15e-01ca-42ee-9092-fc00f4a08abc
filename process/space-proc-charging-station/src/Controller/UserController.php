<?php

namespace App\Controller;

use App\Manager\UserManager;
use App\Trait\ValidationResponseTrait;
use OpenApi\Attributes as OA;
use OpenApi\Attributes\JsonContent;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Validator\ValidatorInterface;

#[Route('v1/user', name: 'charging_station_')]
class UserController extends AbstractController
{
    use ValidationResponseTrait;

    #[OA\Tag(name: 'User')]
    #[Route('/account-link-url/{id}', name: 'user_account_link', methods: [Request::METHOD_GET])]
    #[OA\Parameter(
        name: 'userId',
        in: 'header',
        description: 'user Id',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Response(
        response: Response::HTTP_OK,
        description: 'Successful response',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'success', properties: [
                    new OA\Property(property: 'accountLinkingUrl', type: 'string'),
                    new OA\Property(property: 'accountLinkingRedirectUrl', type: 'string'),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: Response::HTTP_UNPROCESSABLE_ENTITY,
        description: 'Validation error',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', type: 'object')
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: Response::HTTP_UNAUTHORIZED,
        description: 'Invalid token',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message')
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: Response::HTTP_FORBIDDEN,
        description: 'Access denied due to invalid client credentials',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message')
                ]),
            ]
        )
    )]
    public function getAccountLinkUrl(
        string $id,
        Request $request,
        UserManager $userManager,
        ValidatorInterface $validator
    ): Response {
        $userId = $request->headers->get('userId', '');
        $errors = $validator->validate(
            compact('userId', 'id'),
            new Assert\Collection([
                'userId' => new Assert\NotBlank(),
                'id' => new Assert\NotBlank(),
            ])
        );
        $messages = $this->getValidationMessages($errors);
        if (!empty($messages)) {
            $response = $this->getValidationErrorResponse($messages)->getArrayFormat();

            return $this->json($response['content'], $response['code']);
        }
        $response = $userManager->getAccountLinkUrl($userId, $id)->getArrayFormat();
        $response = $this->removeNullValues($response);

        return $this->json($response['content'], $response['code']);
    }

    public function removeNullValues(array $array): array
    {
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $array[$key] = $this->removeNullValues($value);
            }
            if (is_null($array[$key])) {
                unset($array[$key]);
            }
        }
        return $array;
    }
}
