<?php

namespace App\Controller;

use App\Manager\ChargingLocationManager;
use App\Model\LocationModel;
use App\Trait\ValidationResponseTrait;
use Nelmio\ApiDocBundle\Annotation\Model;
use OpenApi\Attributes as OA;
use OpenApi\Attributes\JsonContent;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

#[Route('v1', name: 'charging_location_')]
class ChargingLocationController extends AbstractController
{
    use ValidationResponseTrait;

    #[OA\Parameter(
        name: 'sortBy',
        in: 'query',
        description: 'Order Direction (asc or desc)',
        schema: new OA\Schema(type: 'string', enum: ['ASC', 'DESC'])
    )]
    #[OA\Parameter(
        name: 'orderBy',
        in: 'query',
        description: 'Sort the results by a specific field',
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'offset',
        in: 'query',
        description: 'Offset of pagination',
        schema: new OA\Schema(type: 'integer', format: 'int32')
    )]
    #[OA\Parameter(
        name: 'limit',
        in: 'query',
        description: 'Limit of pagination',
        schema: new OA\Schema(type: 'integer', format: 'int32')
    )]
    #[OA\Parameter(
        name: 'fromDate',
        in: 'query',
        description: 'From Date (format : Y-m-d H:i:s)',
        schema: new OA\Schema(type: 'string', format: 'date')
    )]
    #[OA\Parameter(
        name: 'toDate',
        in: 'query',
        description: 'To Date (format : Y-m-d H:i:s)',
        schema: new OA\Schema(type: 'string', format: 'date')
    )]
    #[OA\Parameter(
        name: 'latitude',
        in: 'query',
        description: 'latitude',
        schema: new OA\Schema(type: 'number', format: 'float'),
        required: true
    )]
    #[OA\Parameter(
        name: 'longitude',
        in: 'query',
        description: 'longitude',
        schema: new OA\Schema(type: 'number', format: 'float'),
        required: true
    )]
    #[OA\Parameter(
        name: 'radius',
        in: 'query',
        description: 'radius',
        schema: new OA\Schema(type: 'integer', format: 'int32')
    )]
    #[OA\RequestBody(
        required: true,
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(
                    property: 'filters',
                    type: 'array',
                    items: new OA\Items(
                        type: 'object',
                        properties: [
                            new OA\Property(property: 'field', type: 'string'),
                            new OA\Property(property: 'operator', type: 'string'),
                            new OA\Property(property: 'value', type: 'string'),
                        ]
                    )
                ),
            ]
        )
    )]
    #[OA\Tag(name: 'Charging Locations')]
    #[OA\Response(
        response: 200,
        description: 'Successful response',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'success', properties: [
                    new OA\Property(property: 'chargeStations', ref: new Model(type: LocationModel::class)),
                    new OA\Property(property: 'count', type: 'integer'),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Invalid query parameters',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 422,
        description: 'Validation error',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 403,
        description: 'Access denied due to invalid client credentials',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                ]),
            ]
        )
    )]
    #[Route('/charging/locations', name: 'list', methods: ['POST'])]
    public function getLocations(Request $request, ChargingLocationManager $chargingSessionManager): JsonResponse
    {
        $sortBy = $request->query->get('sortBy');
        $orderBy = $request->query->get('orderBy');
        $offset = $request->query->get('offset');
        $limit = $request->query->get('limit');
        $fromDate = $request->query->get('fromDate');
        $toDate = $request->query->get('toDate');
        $latitude = $request->query->get('latitude') ?? null;
        $longitude = $request->query->get('longitude') ?? null;
        $radius = $request->query->get('radius') ?? null;

        $params = $request->toArray();
        if($offset === "") {
            $offset = 1;
        }
        if($limit === "") {
            $limit = PHP_INT_MAX;
        }

        $errors = $chargingSessionManager->validateChargingLocationData($params, $sortBy, $orderBy, $offset, $limit, $fromDate, $toDate, $latitude, $longitude, $radius);

        $messages = $this->getValidationMessages($errors);
        if (!empty($messages)) {
            $response = $this->getValidationErrorResponse($messages)->getArrayFormat();

            return $this->json($response['content'], $response['code']);
        }

        $response = $chargingSessionManager->getLocations($params, $sortBy, $orderBy, $offset, $limit, $fromDate, $toDate, $latitude, $longitude, $radius)->getArrayFormat();

        return $this->json($response['content'], $response['code']);
    }
}
