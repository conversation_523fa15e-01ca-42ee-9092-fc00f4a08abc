<?php

namespace App\Connector;

use App\Helper\WSResponse;
use App\Trait\LoggerTrait;

/**
 * The connector with the F2m API.
 */
class F2mConnector
{
    use LoggerTrait;

    public function __construct(
        private CustomHttpClient $client,
        private string $url,
    ) {
    }

    public function callF2mc(string $method, string $uri, mixed $options = []): WSResponse
    {
        try {
            $this->logger->info(__METHOD__.':: '.$uri.' options '.json_encode($options));

            return $this->client->request($method, $this->url.'/'.$uri, $options);
        } catch (\Exception $e) {
            $this->logger->error(__METHOD__.':: error occured while calling '.$uri.'error: '.$e->getMessage());

            return new WSResponse($e->getCode(), $e->getMessage());
        }
    }
}
