<?php

namespace App\Connector;

use App\Helper\WSResponse;
use App\Trait\LoggerTrait;

class SystemFirebaseConnector
{
    use LoggerTrait;

    public function __construct(
        private CustomHttpClient $client,
        private string $url)
    {
        $this->url = $url;
        $this->client = $client;
    }

    public function call(string $method, string $uri, array $options = []): WSResponse
    {
        try {
            $this->logger->info(__METHOD__.' '.$this->url.$uri);

            return $this->client->request($method, $this->url.$uri, $options);
        } catch (\Exception $e) {
            $this->logger->error(__METHOD__.' Cached Exception '.$e->getMessage(), [
                $method,
                'url' => $this->url.$uri,
                $options,
            ]);

            return new WSResponse($e->getCode(), $e->getMessage());
        }
    }
}
