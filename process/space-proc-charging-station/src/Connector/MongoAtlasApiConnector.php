<?php

namespace App\Connector;

use App\Trait\LoggerTrait;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Contracts\HttpClient\ResponseInterface;

/**
 * The connector with the AtlasMongoDB APi.
 */
class MongoAtlasApiConnector
{
    use LoggerTrait;

    public function __construct(
        private HttpClientInterface $mongoAtlasClient,
        private string $mongoApp,
    ) {
    }

    /**
     * call.
     */
    public function call(string $method, string $url, mixed $options = []): ResponseInterface
    {
        return $this->mongoAtlasClient->request($method, $url, $options);
    }

    /**
     * build mongoAtlas endpoint.
     */
    public function getEndpoint(string $action): string
    {
        return sprintf('/app/%s/endpoint/data/v1/action/%s', $this->mongoApp, $action);
    }
}
