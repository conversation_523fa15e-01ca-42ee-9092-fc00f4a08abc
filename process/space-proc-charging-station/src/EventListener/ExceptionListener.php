<?php

namespace App\EventListener;

use App\Helper\ErrorResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\ExceptionEvent;

class ExceptionListener
{
    public function onKernelException(ExceptionEvent $event)
    {
        $exception = $event->getThrowable();
        $code = $exception->getCode() ? $exception->getCode() : Response::HTTP_INTERNAL_SERVER_ERROR;
        $response = (new ErrorResponse($exception->getMessage(), $code))->getJsonFormat();
        // setup the Response object based on the caught exception
        $event->setResponse($response);
    }
}
