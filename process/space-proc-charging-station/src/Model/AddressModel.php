<?php

namespace App\Model;

class AddressModel
{
    private ?string $country;
    private ?string $countryCode;
    private ?string $countryCodeISO3;
    private ?string $countrySecondarySubdivision;
    private ?string $countrySubdivision;
    private ?string $countrySubdivisionName;
    private ?string $countryTertiarySubdivision;
    private ?string $extendedPostalCode;
    private ?string $freeformAddress;
    private ?string $localName;
    private ?string $municipality;
    private ?string $postalCode;
    private ?string $streetName;
    private ?string $streetNumber;

    public function getCountry(): ?string
    {
        return $this->country;
    }

    public function setCountry(?string $country): self
    {
        $this->country = $country;

        return $this;
    }

    public function getCountryCode(): ?string
    {
        return $this->countryCode;
    }

    public function setCountryCode(?string $countryCode): self
    {
        $this->countryCode = $countryCode;

        return $this;
    }

    public function getCountryCodeISO3(): ?string
    {
        return $this->countryCodeISO3;
    }

    public function setCountryCodeISO3(?string $countryCodeISO3): self
    {
        $this->countryCodeISO3 = $countryCodeISO3;

        return $this;
    }

    public function getCountrySecondarySubdivision(): ?string
    {
        return $this->countrySecondarySubdivision;
    }

    public function setCountrySecondarySubdivision(?string $countrySecondarySubdivision): self
    {
        $this->countrySecondarySubdivision = $countrySecondarySubdivision;

        return $this;
    }

    public function getCountrySubdivision(): ?string
    {
        return $this->countrySubdivision;
    }

    public function setCountrySubdivision(?string $countrySubdivision): self
    {
        $this->countrySubdivision = $countrySubdivision;

        return $this;
    }

    public function getCountrySubdivisionName(): ?string
    {
        return $this->countrySubdivisionName;
    }

    public function setCountrySubdivisionName(?string $countrySubdivisionName): self
    {
        $this->countrySubdivisionName = $countrySubdivisionName;

        return $this;
    }

    public function getCountryTertiarySubdivision(): ?string
    {
        return $this->countryTertiarySubdivision;
    }

    public function setCountryTertiarySubdivision(?string $countryTertiarySubdivision): self
    {
        $this->countryTertiarySubdivision = $countryTertiarySubdivision;

        return $this;
    }

    public function getExtendedPostalCode(): ?string
    {
        return $this->extendedPostalCode;
    }

    public function setExtendedPostalCode(?string $extendedPostalCode): self
    {
        $this->extendedPostalCode = $extendedPostalCode;

        return $this;
    }

    public function getFreeformAddress(): ?string
    {
        return $this->freeformAddress;
    }

    public function setFreeformAddress(?string $freeformAddress): self
    {
        $this->freeformAddress = $freeformAddress;

        return $this;
    }

    public function getLocalName(): ?string
    {
        return $this->localName;
    }

    public function setLocalName(?string $localName): self
    {
        $this->localName = $localName;

        return $this;
    }

    public function getMunicipality(): ?string
    {
        return $this->municipality;
    }

    public function setMunicipality(?string $municipality): self
    {
        $this->municipality = $municipality;

        return $this;
    }

    public function getPostalCode(): ?string
    {
        return $this->postalCode;
    }

    public function setPostalCode(?string $postalCode): self
    {
        $this->postalCode = $postalCode;

        return $this;
    }

    public function getStreetName(): ?string
    {
        return $this->streetName;
    }

    public function setStreetName(?string $streetName): self
    {
        $this->streetName = $streetName;

        return $this;
    }

    public function getStreetNumber(): ?string
    {
        return $this->streetNumber;
    }

    public function setStreetNumber(?string $streetNumber): self
    {
        $this->streetNumber = $streetNumber;

        return $this;
    }
}
