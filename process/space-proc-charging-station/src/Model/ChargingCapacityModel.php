<?php

namespace App\Model;

class ChargingCapacityModel
{
    private ?string $chargingMode;
    private ?float $powerKw;
    private ?string $type;

    public function getChargingMode(): ?string
    {
        return $this->chargingMode;
    }

    public function setChargingMode(?string $chargingMode): self
    {
        $this->chargingMode = $chargingMode;

        return $this;
    }

    public function getPowerKw(): ?float
    {
        return $this->powerKw;
    }

    public function setPowerKw(?float $powerKw): self
    {
        $this->powerKw = $powerKw;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(?string $type): self
    {
        $this->type = $type;

        return $this;
    }
}
