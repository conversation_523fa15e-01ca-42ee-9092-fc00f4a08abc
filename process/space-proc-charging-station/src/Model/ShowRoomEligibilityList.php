<?php

namespace App\Model;

use Symfony\Component\Serializer\Attribute\SerializedName;

class ShowRoomEligibilityList
{
    #[SerializedName('success')]
    private array $ishowRoomEligibilities;

    public function __construct()
    {
        $this->ishowRoomEligibilities = [];
    }

    /**
     * @return array<ShowRoomEligibility>
     */
    public function getIshowRoomEligibilities(): array
    {
        return $this->ishowRoomEligibilities;
    }

    /**
     * Set the value of ishowRoomEligibilities
     */
    public function setIshowRoomEligibilities(array $ishowRoomEligibilities): self
    {
        $this->ishowRoomEligibilities = $ishowRoomEligibilities;

        return $this;
    }

    public function addShowRoomEligibility(ShowRoomEligibility $showRoom): self
    {
        $this->ishowRoomEligibilities[] = $showRoom;

        return $this;
    }
}
