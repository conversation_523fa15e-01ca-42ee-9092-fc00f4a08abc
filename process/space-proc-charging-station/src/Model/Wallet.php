<?php

namespace App\Model;

class Wallet
{
    private ?string $totalCredits;
    private ?float $totalSpentInUsd;
    private ?float $totalSpentInCad;
    private ?string $accountBalance;
    private ?string $walletBalance;

    /**
     * Get the value of totalCredits.
     */
    public function getTotalCredits(): ?string
    {
        return $this->totalCredits;
    }

    /**
     * Set the value of totalCredits.
     */
    public function setTotalCredits(?string $totalCredits): self
    {
        $this->totalCredits = $totalCredits;

        return $this;
    }

    /**
     * Get the value of totalSpentInUsd.
     */
    public function getTotalSpentInUsd(): ?float
    {
        return $this->totalSpentInUsd;
    }

    /**
     * Set the value of totalSpentInUsd.
     */
    public function setTotalSpentInUsd(?float $totalSpentInUsd): self
    {
        $this->totalSpentInUsd = $totalSpentInUsd;

        return $this;
    }

    /**
     * Get the value of totalSpentInCad.
     */
    public function getTotalSpentInCad(): ?float
    {
        return $this->totalSpentInCad;
    }

    /**
     * Set the value of totalSpentInCad.
     */
    public function setTotalSpentInCad(?float $totalSpentInCad): self
    {
        $this->totalSpentInCad = $totalSpentInCad;

        return $this;
    }

    /**
     * Get the value of accountBalance.
     */
    public function getAccountBalance(): ?string
    {
        return $this->accountBalance;
    }

    /**
     * Set the value of accountBalance.
     */
    public function setAccountBalance(?string $accountBalance): self
    {
        $this->accountBalance = $accountBalance;

        return $this;
    }

    /**
     * Get the value of walletBalance.
     */
    public function getWalletBalance(): ?string
    {
        return $this->walletBalance;
    }

    /**
     * Set the value of walletBalance.
     */
    public function setWalletBalance(?string $walletBalance): self
    {
        $this->walletBalance = $walletBalance;

        return $this;
    }
}
