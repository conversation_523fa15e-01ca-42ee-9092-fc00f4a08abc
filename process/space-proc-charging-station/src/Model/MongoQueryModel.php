<?php

namespace App\Model;

use Symfony\Component\Serializer\Attribute\Groups;

/**
 * MongoDB query base model.
 */
class MongoQueryModel
{
    #[Groups(['default'])]
    private string $collection;

    #[Groups(['default'])]
    private string $database;

    #[Groups(['default'])]
    private string $dataSource;

    #[Groups(['findOne', 'find', 'updateOne', 'updateMany', 'deleteOne', 'deleteMany', 'replaceOne'])]
    private array $filter;

    #[Groups(['insertOne'])]
    private array $document;

    #[Groups(['updateOne', 'updateMany'])]
    private array $update;

    #[Groups(['updateOne'])]
    private array $options;

    #[Groups(['aggregate'])]
    private array $pipeline;

    #[Groups(['insertMany'])]
    private array $documents;

    #[Groups(['updateOne', 'updateMany'])]
    private bool $upsert;

    #[Groups(['findOne', 'find'])]
    private array $projection;

    #[Groups(['updateMany', 'insertMany'])]
    private ?array $arrayFilters = null; // New property

    #[Groups(['find'])]
    private ?int $limit = null;

    public function getCollection(): string
    {
        return $this->collection;
    }

    public function setCollection(string $collection): self
    {
        $this->collection = $collection;

        return $this;
    }

    public function getDatabase(): string
    {
        return $this->database;
    }

    public function setDatabase(string $database): self
    {
        $this->database = $database;

        return $this;
    }

    public function getDataSource(): string
    {
        return $this->dataSource;
    }

    public function setDataSource(string $dataSource): self
    {
        $this->dataSource = $dataSource;

        return $this;
    }

    public function getFilter(): array
    {
        return $this->filter;
    }

    public function setFilter(array $filter): self
    {
        $this->filter = $filter;

        return $this;
    }

    public function getDocument(): array
    {
        return $this->document;
    }

    public function setDocument(array $document): self
    {
        $this->document = $document;

        return $this;
    }

    public function getUpdate(): array
    {
        return $this->update;
    }

    public function setUpdate(array $update): self
    {
        $this->update = $update;

        return $this;
    }

    public function getPipeline(): array
    {
        return $this->pipeline;
    }

    public function setPipeline(array $pipeline): self
    {
        $this->pipeline = $pipeline;

        return $this;
    }

    public function getDocuments(): array
    {
        return $this->documents;
    }

    public function setDocuments(array $documents): self
    {
        $this->documents = $documents;

        return $this;
    }

    public function getUpsert(): bool
    {
        return $this->upsert;
    }

    public function setUpsert(bool $upsert): self
    {
        $this->upsert = $upsert;

        return $this;
    }

    public function getArrayFilters(): ?array
    {
        return $this->arrayFilters;
    }

    public function setArrayFilters(?array $arrayFilters): self
    {
        $this->arrayFilters = $arrayFilters;

        return $this;
    }

    public function getProjection(): array
    {
        return $this->projection;
    }

    public function setProjection(array $projection): self
    {
        $this->projection = $projection;

        return $this;
    }

    public function getOptions(): array
    {
        return $this->options;
    }

    public function setOptions(array $options): self
    {
        $this->options = $options;

        return $this;
    }

    public function getLimit(): ?int
    {
        return $this->limit;
    }

    public function setLimit(?int $limit): self
    {
        $this->limit = $limit;

        return $this;
    }
}
