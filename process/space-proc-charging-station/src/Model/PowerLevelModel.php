<?php

namespace App\Model;

class PowerLevelModel
{
    private ?ChargeTypeAvailabilityModel $chargeTypeAvailability;
    private ?array $chargingCapacities;

    public function getChargeTypeAvailability(): ?ChargeTypeAvailabilityModel
    {
        return $this->chargeTypeAvailability;
    }

    public function setChargeTypeAvailability(?ChargeTypeAvailabilityModel $chargeTypeAvailability): self
    {
        $this->chargeTypeAvailability = $chargeTypeAvailability;

        return $this;
    }

    public function getChargingCapacities(): ?array
    {
        return $this->chargingCapacities;
    }

    public function setChargingCapacities(?array $chargingCapacities): self
    {
        $this->chargingCapacities = $chargingCapacities;

        return $this;
    }

    public function addChargingCapacity(ChargingCapacityModel $chargingCapacity): void
    {
        if ($chargingCapacity instanceof ChargingCapacityModel) {
            $this->chargingCapacities[] = $chargingCapacity;
        }
    }
}
