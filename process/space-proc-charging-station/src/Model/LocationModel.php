<?php

namespace App\Model;

class LocationModel
{
    private AddressModel $address;
    private ?string $brand;
    private array $connectors;

    private ?string $id;
    private ?string $locationId;
    private PoiModel $poi;
    private CoordinateModel $position;

    public function getAddress(): AddressModel
    {
        return $this->address;
    }

    public function setAddress(AddressModel $address): self
    {
        $this->address = $address;

        return $this;
    }

    public function getBrand(): ?string
    {
        return $this->brand;
    }

    public function setBrand(?string $brand): self
    {
        $this->brand = $brand;

        return $this;
    }

    public function getConnectors(): array
    {
        return $this->connectors;
    }

    public function setConnectors(array $connectors): self
    {
        $this->connectors = $connectors;

        return $this;
    }

    public function addConnector(ConnectorModel $connector): void
    {
        if ($connector instanceof ConnectorModel) {
            $this->connectors[] = $connector;
        }
    }

    public function getId(): ?string
    {
        return $this->id;
    }

    public function setId(?string $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getLocationId(): ?string
    {
        return $this->locationId;
    }

    public function setLocationId(?string $locationId): self
    {
        $this->locationId = $locationId;

        return $this;
    }

    public function getPoi(): PoiModel
    {
        return $this->poi;
    }

    public function setPoi(PoiModel $poi): self
    {
        $this->poi = $poi;

        return $this;
    }

    public function getPosition(): CoordinateModel
    {
        return $this->position;
    }

    public function setPosition(CoordinateModel $position): self
    {
        $this->position = $position;

        return $this;
    }
}
