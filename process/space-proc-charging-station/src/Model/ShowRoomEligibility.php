<?php

namespace App\Model;

class ShowRoomEligibility
{
    private ?string $id;
    private ?string $email;
    private ?string $phone;
    private ?string $firstName;
    private ?string $lastName;
    private ?string $countryCode;
    private ?string $language;
    private ?string $status;
    private ?string $benefitType;
    private ?string $vin;
    private ?string $fixedAmount;
    private ?string $userId;
    private ?string $connectionOfferId;

    /**
     * Get the value of id.
     */
    public function getId(): ?string
    {
        return $this->id;
    }

    /**
     * Set the value of id.
     */
    public function setId(?string $id): self
    {
        $this->id = $id;

        return $this;
    }

    /**
     * Get the value of email.
     */
    public function getEmail(): ?string
    {
        return $this->email;
    }

    /**
     * Set the value of email.
     */
    public function setEmail(?string $email): self
    {
        $this->email = $email;

        return $this;
    }

    /**
     * Get the value of phone.
     */
    public function getPhone(): ?string
    {
        return $this->phone;
    }

    /**
     * Set the value of phone.
     */
    public function setPhone(?string $phone): self
    {
        $this->phone = $phone;

        return $this;
    }

    /**
     * Get the value of firstName.
     */
    public function getFirstName(): ?string
    {
        return $this->firstName;
    }

    /**
     * Set the value of firstName.
     */
    public function setFirstName(?string $firstName): self
    {
        $this->firstName = $firstName;

        return $this;
    }

    /**
     * Get the value of lastName.
     */
    public function getLastName(): ?string
    {
        return $this->lastName;
    }

    /**
     * Set the value of lastName.
     */
    public function setLastName(?string $lastName): self
    {
        $this->lastName = $lastName;

        return $this;
    }

    /**
     * Get the value of countryCode.
     */
    public function getCountryCode(): ?string
    {
        return $this->countryCode;
    }

    /**
     * Set the value of countryCode.
     */
    public function setCountryCode(?string $countryCode): self
    {
        $this->countryCode = $countryCode;

        return $this;
    }

    /**
     * Get the value of language.
     */
    public function getLanguage(): ?string
    {
        return $this->language;
    }

    /**
     * Set the value of language.
     */
    public function setLanguage(?string $language): self
    {
        $this->language = $language;

        return $this;
    }

    /**
     * Get the value of status.
     */
    public function getStatus(): ?string
    {
        return $this->status;
    }

    /**
     * Set the value of status.
     */
    public function setStatus(?string $status): self
    {
        $this->status = $status;

        return $this;
    }

    /**
     * Get the value of benefitType.
     */
    public function getBenefitType(): ?string
    {
        return $this->benefitType;
    }

    /**
     * Set the value of benefitType.
     */
    public function setBenefitType(?string $benefitType): self
    {
        $this->benefitType = $benefitType;

        return $this;
    }

    /**
     * Get the value of vin.
     */
    public function getVin(): ?string
    {
        return $this->vin;
    }

    /**
     * Set the value of vin.
     */
    public function setVin(?string $vin): self
    {
        $this->vin = $vin;

        return $this;
    }

    /**
     * Get the value of fixedAmount.
     */
    public function getFixedAmount(): ?string
    {
        return $this->fixedAmount;
    }

    /**
     * Set the value of fixedAmount.
     */
    public function setFixedAmount(?string $fixedAmount): self
    {
        $this->fixedAmount = $fixedAmount;

        return $this;
    }

    /**
     * Get the value of userId.
     */
    public function getUserId(): ?string
    {
        return $this->userId;
    }

    /**
     * Set the value of userId.
     */
    public function setUserId(?string $userId): self
    {
        $this->userId = $userId;

        return $this;
    }

    /**
     * Get the value of connectionOfferId
     */
    public function getConnectionOfferId(): ?string
    {
        return $this->connectionOfferId;
    }

    /**
     * Set the value of connectionOfferId
     */
    public function setConnectionOfferId(?string $connectionOfferId): self
    {
        $this->connectionOfferId = $connectionOfferId;

        return $this;
    }
}
