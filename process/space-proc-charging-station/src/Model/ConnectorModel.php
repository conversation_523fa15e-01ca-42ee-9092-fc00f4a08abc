<?php

namespace App\Model;

class ConnectorModel
{
    private ?AvailabilityModel $availability;
    private bool $compatible;
    private ?PowerLevelModel $powerLevel;
    private int $total;
    private ?string $type;
    private ?string $evseId;
    private ?string $connectorId;
    private ?string $priceText;
    private ?string $speedShortCode;
    private ?array $capabilities;

    public function getAvailability(): ?AvailabilityModel
    {
        return $this->availability;
    }

    public function setAvailability(?AvailabilityModel $availability): self
    {
        $this->availability = $availability;

        return $this;
    }

    public function getCompatible(): bool
    {
        return $this->compatible;
    }

    public function setCompatible(bool $compatible): self
    {
        $this->compatible = $compatible;

        return $this;
    }

    public function getPowerLevel(): ?PowerLevelModel
    {
        return $this->powerLevel;
    }

    public function setPowerLevel(?PowerLevelModel $powerLevel): self
    {
        $this->powerLevel = $powerLevel;

        return $this;
    }

    public function getTotal(): int
    {
        return $this->total;
    }

    public function setTotal(int $total): self
    {
        $this->total = $total;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(?string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getEvseId(): ?string
    {
        return $this->evseId;
    }

    public function setEvseId(?string $evseId): self
    {
        $this->evseId = $evseId;

        return $this;
    }

    public function getConnectorId(): ?string
    {
        return $this->connectorId;
    }

    public function setConnectorId(?string $connectorId): self
    {
        $this->connectorId = $connectorId;

        return $this;
    }

    public function getPriceText(): ?string
    {
        return $this->priceText;
    }

    public function setPriceText(?string $priceText): self
    {
        $this->priceText = $priceText;

        return $this;
    }

    public function getSpeedShortCode(): ?string
    {
        return $this->speedShortCode;
    }

    public function setSpeedShortCode(?string $speedShortCode): self
    {
        $this->speedShortCode = $speedShortCode;

        return $this;
    }

    public function getCapabilities(): ?array
    {
        return $this->capabilities;
    }

    public function setCapabilities(?array $capabilities): self
    {
        $this->capabilities = $capabilities;

        return $this;
    }
}
