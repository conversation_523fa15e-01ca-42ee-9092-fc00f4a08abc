<?php

namespace App\Model;

class CoordinateModel
{
    private ?float $lat;
    private ?float $lon;

    public function getLat(): ?float
    {
        return $this->lat;
    }

    public function setLat(?float $latitude): self
    {
        $this->lat = $latitude;

        return $this;
    }

    public function getLon(): ?float
    {
        return $this->lon;
    }

    public function setLon(?float $longitude): self
    {
        $this->lon = $longitude;

        return $this;
    }
}
