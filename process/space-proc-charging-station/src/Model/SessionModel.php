<?php

namespace App\Model;

class SessionModel
{
    private ?string $id;
    private ?string $fixedCommission;
    private ?string $commissionPercentage;
    private ?string $cost;
    private ?string $energy;
    private ?string $status;
    private ?string $startedAt;
    private ?string $chargingCompletedAt;
    private ?string $createdAt;
    private ?string $updatedAt;
    private ?string $connectonId;
    private ?string $timeZone;
    private ?string $currency;
    private ?int $totalTime;
    private ?string $unitTime;
    private ?string $priceText;
    private ?float $power;
    private ?float $deltaPower;
    private ?string $name;
    private ?string $locationId;
    private ?string $evseId;
    private ?string $type;
    private ?string $powerLevel;
    private AddressModel $address;
    private CoordinateModel $position;

    /**
     * Get the value of id
     *
     * @return ?string
     */
    public function getId(): ?string
    {
        return $this->id;
    }

    /**
     * Set the value of id
     *
     * @param ?string $id
     *
     * @return self
     */
    public function setId(?string $id): self
    {
        $this->id = $id;

        return $this;
    }

    /**
     * Get the value of fixedCommission
     *
     * @return ?string
     */
    public function getFixedCommission(): ?string
    {
        return $this->fixedCommission;
    }

    /**
     * Set the value of fixedCommission
     *
     * @param ?string $fixedCommission
     *
     * @return self
     */
    public function setFixedCommission(?string $fixedCommission): self
    {
        $this->fixedCommission = $fixedCommission;

        return $this;
    }

    /**
     * Get the value of commissionPercentage
     *
     * @return ?string
     */
    public function getCommissionPercentage(): ?string
    {
        return $this->commissionPercentage;
    }

    /**
     * Set the value of commissionPercentage
     *
     * @param ?string $commissionPercentage
     *
     * @return self
     */
    public function setCommissionPercentage(?string $commissionPercentage): self
    {
        $this->commissionPercentage = $commissionPercentage;

        return $this;
    }

    /**
     * Get the value of cost
     *
     * @return ?string
     */
    public function getCost(): ?string
    {
        return $this->cost;
    }

    /**
     * Set the value of cost
     *
     * @param ?string $cost
     *
     * @return self
     */
    public function setCost(?string $cost): self
    {
        $this->cost = $cost;

        return $this;
    }

    /**
     * Get the value of energy
     *
     * @return ?string
     */
    public function getEnergy(): ?string
    {
        return $this->energy;
    }

    /**
     * Set the value of energy
     *
     * @param ?string $energy
     *
     * @return self
     */
    public function setEnergy(?string $energy): self
    {
        $this->energy = $energy;

        return $this;
    }

    /**
     * Get the value of status
     *
     * @return ?string
     */
    public function getStatus(): ?string
    {
        return $this->status;
    }

    /**
     * Set the value of status
     *
     * @param ?string $status
     *
     * @return self
     */
    public function setStatus(?string $status): self
    {
        $this->status = $status;

        return $this;
    }

    /**
     * Get the value of startedAt
     *
     * @return ?string
     */
    public function getStartedAt(): ?string
    {
        return $this->startedAt;
    }

    /**
     * Set the value of startedAt
     *
     * @param ?string $startedAt
     *
     * @return self
     */
    public function setStartedAt(?string $startedAt): self
    {
        $this->startedAt = $startedAt;

        return $this;
    }

    /**
     * Get the value of chargingCompletedAt
     *
     * @return ?string
     */
    public function getChargingCompletedAt(): ?string
    {
        return $this->chargingCompletedAt;
    }

    /**
     * Set the value of chargingCompletedAt
     *
     * @param ?string $chargingCompletedAt
     *
     * @return self
     */
    public function setChargingCompletedAt(?string $chargingCompletedAt): self
    {
        $this->chargingCompletedAt = $chargingCompletedAt;

        return $this;
    }

    /**
     * Get the value of createdAt
     *
     * @return ?string
     */
    public function getCreatedAt(): ?string
    {
        return $this->createdAt;
    }

    /**
     * Set the value of createdAt
     *
     * @param ?string $createdAt
     *
     * @return self
     */
    public function setCreatedAt(?string $createdAt): self
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    /**
     * Get the value of updatedAt
     *
     * @return ?string
     */
    public function getUpdatedAt(): ?string
    {
        return $this->updatedAt;
    }

    /**
     * Set the value of updatedAt
     *
     * @param ?string $updatedAt
     *
     * @return self
     */
    public function setUpdatedAt(?string $updatedAt): self
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    /**
     * Get the value of connectonId
     *
     * @return ?string
     */
    public function getConnectonId(): ?string
    {
        return $this->connectonId;
    }

    /**
     * Set the value of connectonId
     *
     * @param ?string $connectonId
     *
     * @return self
     */
    public function setConnectonId(?string $connectonId): self
    {
        $this->connectonId = $connectonId;

        return $this;
    }

    /**
     * Get the value of timeZone
     *
     * @return ?string
     */
    public function getTimeZone(): ?string
    {
        return $this->timeZone;
    }

    /**
     * Set the value of timeZone
     *
     * @param ?string $timeZone
     *
     * @return self
     */
    public function setTimeZone(?string $timeZone): self
    {
        $this->timeZone = $timeZone;

        return $this;
    }

    /**
     * Get the value of currency
     *
     * @return ?string
     */
    public function getCurrency(): ?string
    {
        return $this->currency;
    }

    /**
     * Set the value of currency
     *
     * @param ?string $currency
     *
     * @return self
     */
    public function setCurrency(?string $currency): self
    {
        $this->currency = $currency;

        return $this;
    }

    /**
     * Get the value of totalTime
     *
     * @return ?int
     */
    public function getTotalTime(): ?int
    {
        return $this->totalTime;
    }

    /**
     * Set the value of totalTime
     *
     * @param ?int $totalTime
     *
     * @return self
     */
    public function setTotalTime(?int $totalTime): self
    {
        $this->totalTime = $totalTime;

        return $this;
    }

    /**
     * Get the value of unitTime
     *
     * @return ?string
     */
    public function getUnitTime(): ?string
    {
        return $this->unitTime;
    }

    /**
     * Set the value of unitTime
     *
     * @param ?string $unitTime
     *
     * @return self
     */
    public function setUnitTime(?string $unitTime): self
    {
        $this->unitTime = $unitTime;

        return $this;
    }

    /**
     * Get the value of priceText
     *
     * @return ?string
     */
    public function getPriceText(): ?string
    {
        return $this->priceText;
    }

    /**
     * Set the value of priceText
     *
     * @param ?string $priceText
     *
     * @return self
     */
    public function setPriceText(?string $priceText): self
    {
        $this->priceText = $priceText;

        return $this;
    }

    /**
     * Get the value of power
     *
     * @return ?float
     */
    public function getPower(): ?float
    {
        return $this->power;
    }

    /**
     * Set the value of power
     *
     * @param ?float $power
     *
     * @return self
     */
    public function setPower(?float $power): self
    {
        $this->power = $power;

        return $this;
    }

    /**
     * Get the value of deltaPower
     *
     * @return ?float
     */
    public function getDeltaPower(): ?float
    {
        return $this->deltaPower;
    }

    /**
     * Set the value of deltaPower
     *
     * @param ?float $deltaPower
     *
     * @return self
     */
    public function setDeltaPower(?float $deltaPower): self
    {
        $this->deltaPower = $deltaPower;

        return $this;
    }

    /**
     * Get the value of name
     *
     * @return ?string
     */
    public function getName(): ?string
    {
        return $this->name;
    }

    /**
     * Set the value of name
     *
     * @param ?string $name
     *
     * @return self
     */
    public function setName(?string $name): self
    {
        $this->name = $name;

        return $this;
    }

    /**
     * Get the value of locationId
     *
     * @return ?string
     */
    public function getLocationId(): ?string
    {
        return $this->locationId;
    }

    /**
     * Set the value of locationId
     *
     * @param ?string $locationId
     *
     * @return self
     */
    public function setLocationId(?string $locationId): self
    {
        $this->locationId = $locationId;

        return $this;
    }

    /**
     * Get the value of evseId
     *
     * @return ?string
     */
    public function getEvseId(): ?string
    {
        return $this->evseId;
    }

    /**
     * Set the value of evseId
     *
     * @param ?string $evseId
     *
     * @return self
     */
    public function setEvseId(?string $evseId): self
    {
        $this->evseId = $evseId;

        return $this;
    }

    /**
     * Get the value of type
     *
     * @return ?string
     */
    public function getType(): ?string
    {
        return $this->type;
    }

    /**
     * Set the value of type
     *
     * @param ?string $type
     *
     * @return self
     */
    public function setType(?string $type): self
    {
        $this->type = $type;

        return $this;
    }

    /**
     * Get the value of powerLevel
     *
     * @return ?string
     */
    public function getPowerLevel(): ?string
    {
        return $this->powerLevel;
    }

    /**
     * Set the value of powerLevel
     *
     * @param ?string $powerLevel
     *
     * @return self
     */
    public function setPowerLevel(?string $powerLevel): self
    {
        $this->powerLevel = $powerLevel;

        return $this;
    }

    /**
     * Get the value of address
     *
     * @return AddressModel
     */
    public function getAddress(): AddressModel
    {
        return $this->address;
    }

    /**
     * Set the value of address
     *
     * @param AddressModel $address
     *
     * @return self
     */
    public function setAddress(AddressModel $address): self
    {
        $this->address = $address;

        return $this;
    }

    /**
     * Get the value of position
     *
     * @return CoordinateModel
     */
    public function getPosition(): CoordinateModel
    {
        return $this->position;
    }

    /**
     * Set the value of position
     *
     * @param CoordinateModel $position
     *
     * @return self
     */
    public function setPosition(CoordinateModel $position): self
    {
        $this->position = $position;

        return $this;
    }
}
