<?php

namespace App\Model;

class PoiModel
{
    private ?array $acceptablePayments;
    private ?string $accessType;
    private ?string $name;
    private bool $twentyFourSeven;
    private ?array $partnerIDs;
    private ?array $specialRestrictions;

    public function getAcceptablePayments(): ?array
    {
        return $this->acceptablePayments;
    }

    public function setAcceptablePayments(?array $acceptablePayments): self
    {
        $this->acceptablePayments = $acceptablePayments;

        return $this;
    }

    public function getAccessType(): ?string
    {
        return $this->accessType;
    }

    public function setAccessType(?string $accessType): self
    {
        $this->accessType = $accessType;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getTwentyFourSeven(): bool
    {
        return $this->twentyFourSeven;
    }

    public function setTwentyFourSeven(bool $twentyFourSeven): self
    {
        $this->twentyFourSeven = $twentyFourSeven;

        return $this;
    }

    public function getPartnerIDs(): ?array
    {
        return $this->partnerIDs;
    }

    public function setPartnerIDs(?array $partnerIDs): self
    {
        $this->partnerIDs = $partnerIDs;

        return $this;
    }

    public function getSpecialRestrictions(): ?array
    {
        return $this->specialRestrictions;
    }

    public function setSpecialRestrictions(?array $specialRestrictions): self
    {
        $this->specialRestrictions = $specialRestrictions;

        return $this;
    }
}
