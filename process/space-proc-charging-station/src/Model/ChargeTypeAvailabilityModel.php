<?php

namespace App\Model;

class ChargeTypeAvailabilityModel
{
    private int $fastCharge;
    private int $regularCharge;
    private int $slowCharge;
    private int $unknown;

    public function getFastCharge(): int
    {
        return $this->fastCharge;
    }

    public function setFastCharge(int $fastCharge): self
    {
        $this->fastCharge = $fastCharge;

        return $this;
    }

    public function getRegularCharge(): int
    {
        return $this->regularCharge;
    }

    public function setRegularCharge(int $regularCharge): self
    {
        $this->regularCharge = $regularCharge;

        return $this;
    }

    public function getSlowCharge(): int
    {
        return $this->slowCharge;
    }

    public function setSlowCharge(int $slowCharge): self
    {
        $this->slowCharge = $slowCharge;

        return $this;
    }

    public function getUnknown(): int
    {
        return $this->unknown;
    }

    public function setUnknown(int $unknown): self
    {
        $this->unknown = $unknown;

        return $this;
    }
}
