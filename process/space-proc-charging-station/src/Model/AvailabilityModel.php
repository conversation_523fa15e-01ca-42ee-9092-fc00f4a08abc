<?php

namespace App\Model;

class AvailabilityModel
{
    private int $available;
    private int $occupied;
    private int $outOfService;
    private int $reserved;
    private int $unknown;

    public function getAvailable(): int
    {
        return $this->available;
    }

    public function setAvailable(int $available): self
    {
        $this->available = $available;

        return $this;
    }

    public function getOccupied(): int
    {
        return $this->occupied;
    }

    public function setOccupied(int $occupied): self
    {
        $this->occupied = $occupied;

        return $this;
    }

    public function getOutOfService(): int
    {
        return $this->outOfService;
    }

    public function setOutOfService(int $outOfService): self
    {
        $this->outOfService = $outOfService;

        return $this;
    }

    public function getReserved(): int
    {
        return $this->reserved;
    }

    public function setReserved(int $reserved): self
    {
        $this->reserved = $reserved;

        return $this;
    }

    public function getUnknown(): int
    {
        return $this->unknown;
    }

    public function setUnknown(int $unknown): self
    {
        $this->unknown = $unknown;

        return $this;
    }
}
