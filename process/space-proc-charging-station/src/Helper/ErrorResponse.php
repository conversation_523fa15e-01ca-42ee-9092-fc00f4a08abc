<?php

namespace App\Helper;

use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

/**
 * Error response class.
 */
class ErrorResponse implements IResponseArrayFormat
{
    private int $code = Response::HTTP_INTERNAL_SERVER_ERROR;
    private ?string $message = null;
    private array $errors = [];
    private array $items = [];

    public function __construct(mixed $errors = [], ?int $code = Response::HTTP_BAD_REQUEST)
    {
        $this->code = $code;
        if (is_string($errors)) {
            $this->message = $errors;
        } else {
            $this->message = json_encode($errors);
        }
    }

    public function setMessage(?string $message): self
    {
        $this->message = $message;

        return $this;
    }

    public function setCode(int $code)
    {
        $this->code = $code;

        return $this;
    }

    public function setErrors($errors)
    {
        $this->errors = $errors;

        return $this;
    }

    public function getArrayFormat(): array
    {
        $errors = [];
        if ($this->message) {
            $errors['message'] = $this->message;
        }
        if ($this->errors) {
            $errors['errors'] = $this->errors;
        }

        if ($this->items) {
            $errors = array_merge($errors, $this->items);
        }

        return [
            'code' => $this->code ?: Response::HTTP_BAD_REQUEST,
            'content' => [
                'error' => $errors,
            ],
        ];
    }

    public function getJsonFormat(): JsonResponse
    {
        return new JsonResponse($this->getArrayFormat(), $this->code);
    }

    public function getCode(): int
    {
        return $this->code;
    }

    public function getMessage(): ?string
    {
        return $this->message;
    }

    public function getErrors(): array
    {
        return $this->errors;
    }

    /**
     * Get the value of items
     *
     * @return array
     */
    public function getItems(): array
    {
        return $this->items;
    }

    /**
     * Set the value of items
     *
     * @param array $items
     *
     * @return self
     */
    public function setItems(string $key, string $value): self
    {
        $this->items[$key] = $value;

        return $this;
    }
}
