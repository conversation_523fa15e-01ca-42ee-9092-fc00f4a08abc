<?php

namespace App\Helper;

use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

/**
 * Success response class.
 */
class SuccessResponse implements IResponseArrayFormat
{
    private $code = Response::HTTP_OK;
    private $data;

    public function getCode(): int
    {
        return $this->code;
    }

    public function setCode($code): self
    {
        $this->code = $code;

        return $this;
    }

    public function setData($data)
    {
        $this->data = $data;

        return $this;
    }

    public function getData()
    {
        return $this->data;
    }

    public function getArrayFormat(): array
    {
        return [
            'code' => $this->code,
            'content' => [
                'success' => $this->data,
            ],
        ];
    }

    public function getJsonFormat(): JsonResponse
    {
        return new JsonResponse($this->getArrayFormat(), $this->code);
    }
}
