<?php

namespace App\Manager;

use App\Helper\ErrorResponse;
use App\Helper\IResponseArrayFormat;
use App\Helper\SuccessLocationResponse;
use App\Service\ChargingLocationService;
use App\Trait\LoggerTrait;
use App\Transformer\LocationTransformer;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

/**
 * ChargingLocationManager.
 */
class ChargingLocationManager
{
    use LoggerTrait;

    public function __construct(
        private ChargingLocationService $service,
        private ValidatorInterface $validator,
        private float $radius,
    ) {
    }

    public function calculateDistance(?float $latitude, ?float $longitude, ?float $chargeStationLatitude, ?float $chargeStationLongitude): float
    {
        // Earth's radius in kilometers
        $earthRadius = 6371;

        // Convert latitude and longitude from degrees to radians
        $lat1 = deg2rad($latitude);
        $lon1 = deg2rad($longitude);
        $lat2 = deg2rad($chargeStationLatitude);
        $lon2 = deg2rad($chargeStationLongitude);

        // Haversine formula
        $latDelta = $lat2 - $lat1;
        $lonDelta = $lon2 - $lon1;

        $a = sin($latDelta/2) * sin($latDelta/2) +
            cos($lat1) * cos($lat2) *
            sin($lonDelta/2) * sin($lonDelta/2);
            
        $c = 2 * atan2(sqrt($a), sqrt(1-$a));
        $distance = $earthRadius * $c;

        return $distance;
    }

    public function filterChargingLocations(?float $latitude, ?float $longitude, ?float $radius, array $chargeStations): array
    {
        $filteredChargeStations = [];
        foreach ($chargeStations as $chargeStation) {
            $chargingStationLatitude = $chargeStation->getPosition()->getLat();
            $chargingStationLongitude = $chargeStation->getPosition()->getLon();
            $distance = $this->calculateDistance($latitude, $longitude, $chargingStationLatitude, $chargingStationLongitude);
            if ($distance <= $radius) {
                // Store both the charging station and its distance
                $filteredChargeStations[] = [
                    'station' => $chargeStation,
                    'distance' => $distance
                ];
            }
        }
        return $filteredChargeStations;
    }

    public function getLocations(?array $params, ?string $sortBy, ?string $orderBy, ?int $offset, ?int $limit, ?string $fromDate, ?string $toDate, ?float $latitude, ?float $longitude, ?float $radius): IResponseArrayFormat
    {
        try {
            $this->logger->info(__METHOD__.' for params ', $params);
            $response = $this->service->getLocations($params, $sortBy, $orderBy, $offset, $limit, $fromDate, $toDate);
            if ($radius === null) {
                $radius = $this->radius;
            }
            if (Response::HTTP_OK == $response->getCode()) {
                $responseData = $response->getData()['success'] ?? [];
                $chargeStations = LocationTransformer::mapLocationData($responseData['result']);
                $filteredChargeStations = $this->filterChargingLocations($latitude, $longitude, $radius, $chargeStations);
                
                // Sort charging stations by distance (closest first)
                usort($filteredChargeStations, function($a, $b) {
                    return $a['distance'] <=> $b['distance'];
                });
                
                // Get total count of filtered stations before pagination
                $totalCount = count($filteredChargeStations);
                // $locationData['totalCount'] = $totalCount;
                
                // Ensure offset is valid and adjust for 1-based indexing
                $safeOffset = $offset ?? 1; // Default to 1 for 1-based indexing
                
                // Convert to 0-based for internal calculations
                $zeroBasedOffset = max(0, $safeOffset - 1);
                
                // Make sure limit is valid
                $safeLimit = $limit ?? PHP_INT_MAX;
                // if ($safeLimit < 1) {
                //     $safeLimit = PHP_INT_MAX;
                // }
                
                // Calculate pagination properly
                $paginatedChargeStations = [];
                
                // Calculate total pages (for 1-based indexing)
                $totalPages = ceil($totalCount / $safeLimit);
                // $locationData['totalPages'] = $totalPages;
                
                // Only apply pagination if offset is valid
                if ($safeOffset <= $totalPages) {
                    // Calculate how many items should be on the current page
                    $remainingItems = $totalCount - ($zeroBasedOffset * $safeLimit);
                    
                    // For the last page, only return the remaining items
                    if ($zeroBasedOffset == $totalPages - 1 && $remainingItems < $safeLimit) {
                        $paginatedChargeStations = array_slice($filteredChargeStations, $zeroBasedOffset * $safeLimit, $remainingItems);
                    } else {
                        $paginatedChargeStations = array_slice($filteredChargeStations, $zeroBasedOffset * $safeLimit, $safeLimit);
                    }
                }
                
                // Extract just the station objects for the response
                $stationsOnly = array_map(function($item) {
                    return $item['station'];
                }, $paginatedChargeStations);
                
                $locationData['chargeStations'] = $stationsOnly;
                $locationData['count'] = count($stationsOnly);

                return (new SuccessLocationResponse())->setData($locationData);
            }
            $responseData = $response->getData();
            $result = $responseData['error']['message'] ?? "Error while getting locations";

            return (new ErrorResponse())->setMessage($result)->setCode($response->getCode());
        } catch (\Exception $e) {
            $this->logger->error('Catched Exception in '.__METHOD__.' '.$e->getMessage());

            return (new ErrorResponse())->setMessage($e->getMessage())->setCode($e->getCode());
        }
    }

    public function validateChargingLocationData(?array $data, ?string $sortBy, ?string $orderBy, ?int $offset, ?int $limit, ?string $fromDate, ?string $toDate, ?float $latitude, ?float $longitude, ?float $radius): ConstraintViolationListInterface
    {
        $constraints = new Assert\Collection([
            'sortBy' => new Assert\Optional(),
            'orderBy' => new Assert\Optional(),
            'offset' => new Assert\Optional([
                new Assert\Type('integer'),
                new Assert\GreaterThanOrEqual([
                    'value' => 1, 
                    'message' => 'Offset must be a positive integer greater than or equal to 1.'
                ]),
            ]),
            'limit' => new Assert\Optional([
                new Assert\Type('integer'),
                new Assert\GreaterThanOrEqual([
                    'value' => 1,
                    'message' => 'Limit must be a positive integer greater than or equal to 1.'
                ]),
            ]),
            'fromDate' => new Assert\Optional([
                new Assert\DateTime(['format' => 'Y-m-d H:i:s']),
            ]),
            'toDate' => new Assert\Optional([
                new Assert\DateTime(['format' => 'Y-m-d H:i:s']),
            ]),
            'latitude' => new Assert\Optional([
                new Assert\NotNull(),
                new Assert\Type('float'),
            ]),
            'longitude' => new Assert\Optional([
                new Assert\NotNull(),
                new Assert\Type('float'),
            ]),
            'radius' => new Assert\Optional([
                new Assert\Type('float'),
            ]),
            'filters' => new Assert\Optional(new Assert\All([
                'constraints' => new Assert\Collection([
                    'field' => [
                        new Assert\NotBlank(),
                        new Assert\Type('string'),
                    ],
                    'operator' => [
                        new Assert\NotBlank(),
                        new Assert\Type('string'),
                    ],
                    'value' => [
                        new Assert\NotNull(),
                    ],
                ]),
            ])),
        ]);

        $dataToValidate = [
            'sortBy' => $sortBy,
            'orderBy' => $orderBy,
            'offset' => $offset,
            'limit' => $limit,
            'fromDate' => $fromDate,
            'toDate' => $toDate,
            'latitude' => $latitude,
            'longitude' => $longitude,
            'radius' => $radius,
            'filters' => isset($data['filters']) ? $data['filters'] : [],
        ];

        return $this->validator->validate($dataToValidate, $constraints);
    }
}
