<?php

declare(strict_types=1);

namespace App\Manager;

use App\Helper\ErrorResponse;
use App\Helper\IResponseArrayFormat;
use App\Helper\SuccessResponse;
use App\Message\ChargingStationCallback;
use App\Service\ChargingStationCallbackService;
use App\Trait\LoggerTrait;
use Symfony\Component\HttpFoundation\Response;

class ChargingStationCallbackManager
{
    use LoggerTrait;

    public function __construct(
        private ChargingStationCallbackService $chargingStationCallbackService
    ) {
    }

    public function handleSQSCallback(ChargingStationCallback $message): IResponseArrayFormat
    {
        // Handle the message
        if ($message->getAction() == 'start') {
            // Update the charging session status
            $toUpdate = [];
            if ($message->getResult() == 'ACCEPTED') {
                $toUpdate['remoteCommandStatus'] = 'CHARGING_ACCEPTED';
            } elseif ($message->getResult() == 'REJECTED') {
                $toUpdate['remoteCommandStatus'] = 'CHARGING_REJECTED';
            }

            if (empty($toUpdate)) {
                $this->logger->error(__METHOD__ .': Invalid result on received payload', [
                    'userId' => $message->getUserId(),
                    'internalId' => $message->getInternalId(),
                    'action' => $message->getAction(),
                    'result' => $message->getResult(),
                ]);

                return new ErrorResponse(__METHOD__ .': Invalid result on received payload', Response::HTTP_BAD_REQUEST);
            }

            $result = $this->chargingStationCallbackService->updateChargingSessionByUserIdAndInternalId(
                $message->getUserId(),
                $message->getInternalId(),
                $toUpdate
            );
            if ($result == null) {
                return new ErrorResponse(__METHOD__ .': Error while updating charging session', Response::HTTP_INTERNAL_SERVER_ERROR);
            }

            // reload the charging session data
            $chargingSession = $this->chargingStationCallbackService->getChargingSessionByUserIdAndInternalId(
                $message->getUserId(),
                $message->getInternalId()
            );
            if ($chargingSession == null) {
                return new ErrorResponse(__METHOD__ .': Charging session not found by userId and internalId', Response::HTTP_NOT_FOUND);
            }
        } elseif ($message->getAction() == 'stop') {
            // Update the charging session status
            $toUpdate = [];
            if ($message->getResult() == 'ACCEPTED') {
                $toUpdate['remoteCommandStatus'] = 'PENDING_STOP';
            } elseif ($message->getResult() == 'REJECTED') {
                $toUpdate['remoteCommandStatus'] = 'CHARGING_STOP_REJECTED';
            }

            if (empty($toUpdate)) {
                $this->logger->error(__METHOD__ .': Invalid result on received payload', [
                    'userId' => $message->getUserId(),
                    'sessionId' => $message->getSessionId(),
                    'action' => $message->getAction(),
                    'result' => $message->getResult(),
                ]);

                return new ErrorResponse(__METHOD__ .': Invalid result on received payload', Response::HTTP_BAD_REQUEST);
            }

            $result = $this->chargingStationCallbackService->updateChargingSessionByUserIdAndSessionId(
                $message->getUserId(),
                $message->getSessionId(),
                $toUpdate
            );
            if ($result == null) {
                return new ErrorResponse(__METHOD__ .': Error while updating charging session', Response::HTTP_INTERNAL_SERVER_ERROR);
            }

            $chargingSession = $this->chargingStationCallbackService->getChargingSessionByUserIdAndSessionId(
                $message->getUserId(),
                $message->getSessionId()
            );
            if ($chargingSession == null) {
                return new ErrorResponse(__METHOD__ .': Charging session not found by userId and sessionId', Response::HTTP_NOT_FOUND);
            }
        } else {
            $this->logger->error(__METHOD__ .': Invalid action received', [
                'action' => $message->getAction(),
            ]);

            return new ErrorResponse(
                __METHOD__ .': Invalid action received ['. $message->getAction() .']',
                Response::HTTP_BAD_REQUEST
            );
        }

        // send the notification
        $response = $this->chargingStationCallbackService->sendNotification($message->getUserId(), $chargingSession);
        if ($response instanceof ErrorResponse) {
            return $response;
        }

        return (new SuccessResponse())
                ->setCode(Response::HTTP_OK)
                ->setData('Charging session updated successfully');
    }
}
