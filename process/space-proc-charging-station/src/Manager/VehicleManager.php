<?php

namespace App\Manager;

use App\Helper\ErrorResponse;
use App\Helper\IResponseArrayFormat;
use App\Helper\SuccessResponse;
use App\Service\VehicleService;
use App\Trait\LoggerTrait;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

/**
 * VehicleManager
 */
class VehicleManager
{
    use LoggerTrait;

    public function __construct(
        private VehicleService $service,
        private ValidatorInterface $validator
    ) {
    }

    public function validateVehicleData(array $data): ConstraintViolationListInterface
    {
        $constraints = new Assert\Collection([
            'vin' => new Assert\Optional([
                new Assert\Length(['min' => 17, 'max' => 17])
            ]),
            'make' => new Assert\Optional(),
            'model' => new Assert\Optional(),
            'vehicleId' => new Assert\Optional([
                new Assert\Regex(['pattern' => '/^\d+$/', 'message' => 'This value should be number.']),
                new Assert\NotNull()
            ]),
        ]);

        return $this->validator->validate($data, $constraints);
    }

    /**
     * add a new vehicle
     */
    public function add(array $data): IResponseArrayFormat
    {
        try {
            $this->logger->info(__METHOD__.": start calling f2mc add vehicle for accessToken: ", ["data" => $data]);

            $response = $this->service->add($data);

            if ($response->getCode() == Response::HTTP_OK || $response->getCode() == Response::HTTP_CREATED) {
                $result = $response->getData()['success'] ?? [];

                return (new SuccessResponse())->setData($result)->setCode($response->getCode());
            }

            $responseData = $response->getData()['error'] ?? [];
            $result = $responseData['message'] ?? 'Error while calling f2mc get vehicle details';

            return (new ErrorResponse())->setMessage($result)->setCode($response->getCode() ?: Response::HTTP_INTERNAL_SERVER_ERROR);
        } catch (\Exception $e) {
            $this->logger->error(__METHOD__.": Caught Exception while calling f2mc add vehicle details" . $e->getMessage());

            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }

    /**
     * get vehicle list for a user
     */
    public function getList(string $accessToken): IResponseArrayFormat
    {
        $this->logger->info(__CLASS__."::".__METHOD__. " with parameters : ", ['accessToken' => $accessToken]);
        try {
            $this->logger->info(__CLASS__."::".__METHOD__.": start calling f2mc get vehicle for accessToken: ". $accessToken);
            $response = $this->service->getList($accessToken);

            if ($response->getCode() == Response::HTTP_OK) {
                $result = $response->getData()['success'] ?? [];

                return (new SuccessResponse())->setData($result)->setCode($response->getCode());
            }

            $responseData = $response->getData()['error'] ?? [];
            $result = $responseData['message'] ?? 'Error while calling f2mc get vehicle details';

            return (new ErrorResponse())->setMessage($result)->setCode($response->getCode() ?: Response::HTTP_INTERNAL_SERVER_ERROR);
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__."::".__METHOD__.": Caught Exception while calling f2mc get vehicle details" . $e->getMessage());

            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }

    /**
     * update vehicle data
     */
    public function update(array $data): IResponseArrayFormat
    {
        try {
            $this->logger->info(__CLASS__."::".__METHOD__.": start calling f2mc update vehicle for accessToken: ", ["data" => $data]);

            $response = $this->service->update($data);

            if ($response->getCode() == Response::HTTP_OK) {
                $result = $response->getData()['success'] ?? [];

                return (new SuccessResponse())->setData($result)->setCode($response->getCode());
            }

            $responseData = $response->getData()['error'] ?? [];
            $result = $responseData['message'] ?? 'Error while calling f2mc get vehicle details';

            return (new ErrorResponse())->setMessage($result)->setCode($response->getCode() ?: Response::HTTP_INTERNAL_SERVER_ERROR);
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__."::".__METHOD__.": Caught Exception while calling f2mc update vehicle details" . $e->getMessage());

            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }

    /**
     * delete a vehicle
     */
    public function delete(array $data): IResponseArrayFormat
    {
        try {
            $this->logger->info(__CLASS__."::".__METHOD__.": start calling f2mc delete vehicle for accessToken: ", ["data" => $data]);

            $response = $this->service->delete($data);
            if ($response->getCode() == Response::HTTP_OK) {
                $this->logger->info("=> " . __METHOD__ . " => Success data : ", $data);
                $result = $response->getData()['success'] ?? [];

                return (new SuccessResponse())->setData($result)->setCode($response->getCode());
            }

            $responseData = $response->getData()['error'] ?? [];
            $result = $responseData['message'] ?? 'Error while deleting vehicle';

            return (new ErrorResponse())->setMessage($result)->setCode($response->getCode() ?: Response::HTTP_INTERNAL_SERVER_ERROR);
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__."::".__METHOD__.": Caught Exception while calling f2mc delete vehicle" . $e->getMessage());

            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }
}
