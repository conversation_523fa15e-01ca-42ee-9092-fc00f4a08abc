<?php

namespace App\MessageHandler;

use App\Manager\ChargingStationManagementManager;
use App\Message\F2mEnvelopeBody;
use App\Service\MongoAtlasQueryService;
use App\Service\SystemFirebaseClient;
use App\Service\UserService;
use App\Trait\ValidationResponseTrait;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Component\Validator\Validator\ValidatorInterface;

/**
 * Processor class to handle F2m messge.
 */
#[AsMessageHandler]
class F2mEnvelopeBodyHandler
{
    // use InParametersValidator;
    use ValidationResponseTrait;
    private const EVENT_PAYMENT_SUCCESS = 'payment.success';
    private const EVENT_ACCOUNT_SUCCESS = 'account-link.success';

    public function __construct(
        private ValidatorInterface $validator,
        private LoggerInterface $logger,
        private MongoAtlasQueryService $mongoAtlasQueryService,
        private UserService $userService,
        private SystemFirebaseClient $systemFirebaseClient,
    ) {
    }

    /**
     * Process method that handles F2m messages.
     */
    public function __invoke(F2mEnvelopeBody $envelopeBody): void
    {
        $event = $envelopeBody->getEvent();

        if (self::EVENT_PAYMENT_SUCCESS !== $event || self::EVENT_ACCOUNT_SUCCESS !== $event) {
            $this->logger->info(sprintf('F2mEnvelopeBodyHandler: Unsupported event type "%s". No action taken.', $event));

            return;
        }

        $this->handlePaymentSuccess($envelopeBody, $event);
    }

    /**
     * Handle payment success event.
     */
    private function handlePaymentSuccess(F2mEnvelopeBody $envelopeBody, string $event): void
    {
        $userId = $envelopeBody->getData()->getUserId();
        $vin = $envelopeBody->getData()->getVin();
        if (!$userId) {
            $this->logger->warning('F2mEnvelopeBodyHandler: Missing or invalid userId in payment.success event. Skipping update.');

            return;
        }

        $filter = [
            'userId' => $userId,
        ];

        $this->logger->info(sprintf(
            'F2mEnvelopeBodyHandler: Setting "chargingStation" to "active" for userId "%s".',
            $userId
        ));

        //$this->mongoAtlasQueryService->updateMany('userData', $filter, $update);
        $f2mcProfile = $this->getF2mcProfileByProfileId($userId);
        if ($f2mcProfile) {
            $f2mc = $f2mcProfile['f2mc'];
            $isPaymentMethod = $event == self::EVENT_PAYMENT_SUCCESS ? true : ($f2mc['isPaymentMethod'] ?? false);
            $isAccountLinked = $event == self::EVENT_ACCOUNT_SUCCESS ? true : ($f2mc['isAccountLinked'] ?? false);
            $userId = $f2mc['userId'];
            $enrolmentStatus = '';
            if ($isAccountLinked == true and $isPaymentMethod == true) {
                $enrolmentStatus = ChargingStationManagementManager::COMPLETE;
            } elseif ($isAccountLinked == true and $isPaymentMethod == false) {
                $enrolmentStatus = ChargingStationManagementManager::NO_PAYEMENT_METHOD;
            } elseif ($isAccountLinked == false and $isPaymentMethod == true) {
                $enrolmentStatus = ChargingStationManagementManager::NO_ACCOUNT_LINKING;
            } else {
                $enrolmentStatus = ChargingStationManagementManager::NO_PAYEMENT_METHOD;
            }
            $this->userService->updateUserVehiclesFeatureCodeByStatus($userId, $enrolmentStatus);
            $update = [
                'f2mc.isAccountLinked' => $isAccountLinked,
                'f2mc.isPaymentMethod' => $isPaymentMethod,
            ];
            $this->mongoAtlasQueryService->updateOne('userData', $filter, $update, true);
            $vehicle = $this->filterByVin($f2mcProfile['vehicle'], $vin);
            $message = [
                'notification' => [
                    'title' => 'Garage update',
                    'body' => 'Garage of vehicle has been updated'
                ],
                'content_available' => true,
                'data' => [
                    'provider' => 'space-mid',
                    'event' => [
                        'type' => 'garageUpdate',
                        'details' => [
                            'vehicle' => [
                                'id' => $vehicle['id'],
                                'vin' => $vin
                            ]
                        ]
                    ]
                ]
            ];
            $this->sendNotificationToUser($userId, $message);
        }
    }

    private function sendNotificationToUser($userId, $message)
    {
        $user = $this->userService->getUserByUserId($userId)->getData();
        $this->systemFirebaseClient->sendNotification([$user], $message);
    }

    private function getF2mcProfileByProfileId(string $userId): ?array
    {
        $user = $this->userService->getUserByProfileId($userId);
        $data = json_decode($user->getData(), true);

        return $data['documents'][0] ?? null;
    }

    private function filterByVin(array $data, string $vin): ?array {
        return current(array_filter($data, function($item) use ($vin) {
            return $vin == $item['vin'];
        }));
    }
}
