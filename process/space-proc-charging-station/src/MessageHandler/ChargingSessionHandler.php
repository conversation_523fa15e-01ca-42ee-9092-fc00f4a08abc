<?php

namespace App\MessageHandler;

use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Component\Messenger\Exception\UnrecoverableMessageHandlingException;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Component\Validator\Constraints as Assert;

use App\Trait\ValidationResponseTrait;
use Psr\Log\LoggerInterface;
use App\Message\ChargingSessionHandlerEnvelopeBody;
use App\Service\UserService;

#[AsMessageHandler]
class ChargingSessionHandler
{
    use ValidationResponseTrait;

    public function __construct(
        private ValidatorInterface $validator,
        private LoggerInterface $logger,
        private UserService $userService
    ) {
    }

    public function __invoke(ChargingSessionHandlerEnvelopeBody $envelopeBody)
    {
        $this->logger->info('Starting reading a new message', ['message' => $envelopeBody->getMessage()]);
        $errors = $this->validator->validate($envelopeBody);
        $errorMessages = $this->getValidationMessages($errors);
        if (!empty($errorMessages)) {
            $response = $this->getValidationErrorResponse($errorMessages)->getArrayFormat();
            throw new UnrecoverableMessageHandlingException(json_encode($response['content']));
        }
        $message = $envelopeBody->getMessage();
        $chargingSessionId = $message['id'] ?? '';
        $f2mUserId = $message['userId'] ?? '';
        $status = $message['status'] ?? '';
        $errors = $this->validator->validate(
            compact('chargingSessionId', 'f2mUserId', 'status'),
            new Assert\Collection([
                'chargingSessionId' => [
                    new Assert\NotBlank(),
                    new Assert\NotNull()
                ],
                'f2mUserId' => [
                    new Assert\NotBlank(),
                    new Assert\NotNull()
                ],
                'status' => [
                    new Assert\NotBlank(),
                    new Assert\NotNull()
                ]
            ])
        );
        $errorMessages = $this->getValidationMessages($errors);
        if (!empty($errorMessages)) {
            $response = $this->getValidationErrorResponse($errorMessages)->getArrayFormat();
            $this->logger->error('Validation error', ['message' => $response['content']]);
            throw new UnrecoverableMessageHandlingException(json_encode($response['content']));
        }
        $userResponse = $this->userService->getUserByF2MUserId($f2mUserId, $chargingSessionId);
        $user = json_decode($userResponse->getData());
        if ($userResponse->getCode() == Response::HTTP_OK and isset($user->documents) and count($user->documents) == 1) {
            $this->logger->info('User found', [
                'user' => $user->documents[0]
            ]);
            $remoteStatusUpdate = $this->userService->updateUserRemoteCommandStatus($f2mUserId, $chargingSessionId, $status);
            if ($remoteStatusUpdate->getCode() == Response::HTTP_OK) {
                $this->logger->info('Remote command status updated', [
                    'remoteCommandStatus' => $remoteStatusUpdate->getData()
                ]);
            } else {
                $this->logger->error('Failed to update remote command status', [
                    'remoteCommandStatus' => $remoteStatusUpdate->getData()
                ]);
            }
        } else {
            $this->logger->info('user not found with session id ' . $chargingSessionId);
            $locationId = $message['locationId'] ?? "";
            $evseId = $message["evseId"] ?? "";
            $connectorId = $message["connectorId"] ?? "";
            $errors = $this->validator->validate(
                compact('f2mUserId', 'locationId', 'evseId', 'connectorId'),
                new Assert\Collection([
                    'f2mUserId' => [
                        new Assert\NotBlank(),
                        new Assert\NotNull()
                    ],
                    'locationId' => [
                        new Assert\NotBlank(),
                        new Assert\NotNull()
                    ],
                    'evseId' => [
                        new Assert\NotBlank(),
                        new Assert\NotNull()
                    ],
                    'connectorId' => [
                        new Assert\NotBlank(),
                        new Assert\NotNull()
                    ]
                ])
            );
            $errorMessages = $this->getValidationMessages($errors);
            if (!empty($errorMessages)) {
                $response = $this->getValidationErrorResponse($errorMessages)->getArrayFormat();
                $this->logger->error('Validation error', ['message' => $response['content']]);
                throw new UnrecoverableMessageHandlingException(json_encode($response['content']));
            }
            $userResponse = $this->userService->getUserByOtherIds($f2mUserId, $locationId, $evseId, $connectorId);
            $user = json_decode($userResponse->getData());
            if ($userResponse->getCode() == Response::HTTP_OK and isset($user->documents) and count($user->documents) == 1) {
                $this->logger->info('User found', [
                    'user' => $user->documents[0]
                ]);
                $remoteStatusUpdate = $this->userService->updateUserRemoteCommandStatusUsingOtherIds($f2mUserId, $locationId, $evseId, $connectorId, $chargingSessionId, $status);
                if ($remoteStatusUpdate->getCode() == Response::HTTP_OK) {
                    $this->logger->info('Remote command status updated', [
                        'remoteCommandStatus' => $remoteStatusUpdate->getData()
                    ]);
                } else {
                    $this->logger->error('Failed to update remote command status', [
                        'remoteCommandStatus' => $remoteStatusUpdate->getData()
                    ]);
                }
            } else {
                $this->logger->info('user not found with ', [
                    'f2mUserId' => $f2mUserId,
                    'locationId' => $locationId,
                    'evseId' => $evseId,
                    'connectorId' => $connectorId,
                ]);
            }
        }
        $this->logger->info('Ending reading a new message');
    }
}