<?php

namespace App\MessageHandler;

use App\Helper\ErrorResponse;
use App\Manager\ChargingStationCallbackManager;
use App\Message\ChargingStationCallback;
use App\Trait\LoggerTrait;
use App\Trait\ValidationResponseTrait;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Component\Validator\Validator\ValidatorInterface;

#[AsMessageHandler]
class ChargingStationCallbackHandler
{
    use ValidationResponseTrait;
    use LoggerTrait;

    public function __construct(
        private ChargingStationCallbackManager $chargingStationCallbackManager,
        private ValidatorInterface $validator
    ) {
    }

    public function __invoke(ChargingStationCallback $message): mixed
    {
        $this->logger->info(__METHOD__ . ' Received space charging station callback message', ['message' => $message]);
        $errors = $this->validator->validate($message);
        if (count($errors) > 0) {
            $this->logger->error('Invalid message received', [
                'message' => $message,
                'errors' => $errors
            ]);

            // important! allow Messenger Component to retry the SQS message management
            throw new \Exception('Invalid message received', Response::HTTP_BAD_REQUEST);
        }

        $result = $this->chargingStationCallbackManager->handleSQSCallback($message);
        if ($result instanceof ErrorResponse) {
            $this->logger->error('Error while handling SQS callback', [
                'message' => $message,
            ]);
        }

        return $result;
    }
}
