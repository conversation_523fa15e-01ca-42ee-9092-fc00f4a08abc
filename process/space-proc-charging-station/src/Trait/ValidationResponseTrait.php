<?php

namespace App\Trait;

use App\Helper\ErrorResponse;
use App\Helper\IResponseArrayFormat;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\ConstraintViolationInterface;
use Symfony\Component\Validator\ConstraintViolationListInterface;

trait ValidationResponseTrait
{
    /**
     * Get the validation error messages.
     */
    protected static function getValidationMessages(ConstraintViolationListInterface $errors): array
    {
        $messages = [];

        if (0 === $errors->count()) {
            return $messages;
        }

        foreach ($errors as $error) {
            /** @var ConstraintViolationInterface $error */
            $name = str_replace(['[', ']'], '', $error->getPropertyPath());
            $messages[$name] = $error->getMessage();
        }

        return $messages;
    }

    protected static function getValidationErrorResponse(
        array $messages,
        int $statusCode = Response::HTTP_UNPROCESSABLE_ENTITY,
    ): IResponseArrayFormat {
        return (new ErrorResponse())->setMessage('validation_failed')->setErrors($messages)->setCode($statusCode);
    }
}
