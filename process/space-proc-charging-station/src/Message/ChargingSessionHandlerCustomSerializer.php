<?php

namespace App\Message;

use Symfony\Component\Messenger\Exception\UnrecoverableMessageHandlingException;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\Transport\Serialization\Serializer;
use Symfony\Component\Messenger\Exception\InvalidArgumentException;

use App\Trait\LoggerTrait;

class ChargingSessionHandlerCustomSerializer extends Serializer
{
    use LoggerTrait;

    public function decode(array $encodedEnvelope): Envelope
    {
        $type = '';
        $sessionData = null;
        $flags = JSON_INVALID_UTF8_IGNORE |
            JSON_UNESCAPED_UNICODE |
            JSON_UNESCAPED_SLASHES |
            JSON_THROW_ON_ERROR;
        try {
            $this->logger->info( __METHOD__ . ' Decoding space charging session message', ['encodedEnvelope' => $encodedEnvelope]);
            $type = $encodedEnvelope['headers']['type'];
            $decodedMessage = json_decode($encodedEnvelope['body'], false, $flags);
            return $this->seralizeEnvelope($type, $decodedMessage);
        } 
        catch (\JsonException $e) {
            $this->logger->error( __METHOD__ .' Json decoding exception for Space Charging Session message: encoded envelope body is not valid JSON:'   , [
                'exception' => $e->getMessage(),
                'type' => $type,
                'sessionData' => $encodedEnvelope['body']
            ]);
            return $this->getEnvelope($type,$encodedEnvelope['body']);
        }
        catch (\Exception $e) {
            $this->logger->error( __METHOD__ .'Json decoding exception for the Message field on Space Charging Session message: the field is not valid JSON: ', [
                'exception' => $e->getMessage(),
                'type' => $type,
                'sessionData' => $encodedEnvelope['body']
            ]);
            return $this->getEnvelope($type,$sessionData);
        }
    }

    private function seralizeEnvelope($type, $decodedMessage): Envelope
    {
        $decodedBody['type'] = $type;
        $decodedBody['message'] = $decodedMessage ?? [];
        // encoding all togheter just one time
        $encodedEnvelope['body'] = json_encode($decodedBody);
        // add rule to enable deserialization for this message
        $encodedEnvelope['headers']['type'] = ChargingSessionHandlerEnvelopeBody::class;
        $envelope = parent::decode($encodedEnvelope);

        return $envelope;
    }

    private function getEnvelope(
        string $type,
        ?array $message
    ): Envelope {
        return new Envelope(new ChargingSessionHandlerEnvelopeBody($type, $message));
    }
}