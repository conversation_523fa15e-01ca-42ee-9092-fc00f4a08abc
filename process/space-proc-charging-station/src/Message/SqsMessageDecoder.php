<?php

namespace App\Message;

use App\Trait\LoggerTrait;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\Transport\Serialization\Serializer;
use Throwable;

class SqsMessageDecoder extends Serializer
{
    use LoggerTrait;

    public function decode(array $encodedEnvelope): Envelope
    {
        try {
            $this->logger->info(__METHOD__.': Decoding SQS message', ['encodedEnvelope' => $encodedEnvelope]);

            if (empty($encodedEnvelope['headers']['type'])) {
                $encodedEnvelope['headers']['type'] = ChargingStationCallback::class;
            }
            $body = json_decode($encodedEnvelope['body'], true);
            $sqsMessageBody = $body['MessageBody'] ?? [];

            $sqsMessageBody['action'] = $encodedEnvelope['headers']['action'] ?? null;
            $sqsMessageBody['internalId'] = $encodedEnvelope['headers']['internalId'] ?? null;
            $sqsMessageBody['userId'] = $encodedEnvelope['headers']['userId'] ?? null;
            $sqsMessageBody['sessionId'] = $encodedEnvelope['headers']['sessionId'] ?? null;
            $encodedEnvelope['body'] = json_encode($sqsMessageBody);

            return parent::decode($encodedEnvelope);
        } catch (Throwable $e) {
            // Log error with message details
            $this->logger->error('Error processing SQS message', [
                'exception' => $e->getMessage(),
                'encodedEnvelope' => $encodedEnvelope,
            ]);

            // Let Messenger retry the message by rethrowing the exception
            throw $e;
        }
    }
}
