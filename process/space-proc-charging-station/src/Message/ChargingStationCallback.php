<?php

namespace App\Message;

use Symfony\Component\Validator\Constraints as Assert;

class ChargingStationCallback
{
    public function __construct(
        private ?string $internalId,

        #[Assert\NotBlank]
        private ?string $userId,

        private ?string $sessionId,

        #[Assert\NotBlank]
        private ?string $action,

        private ?string $result
    ) {
    }

    public function getInternalId(): ?string
    {
        return $this->internalId;
    }

    public function getUserId(): ?string
    {
        return $this->userId;
    }

    public function getAction(): ?string
    {
        return $this->action;
    }

    public function getSessionId(): ?string
    {
        return $this->sessionId;
    }

    public function getResult(): ?string
    {
        return $this->result;
    }
}
