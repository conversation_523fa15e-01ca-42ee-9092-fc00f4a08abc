<?php

namespace App\Message\Field;

use Symfony\Component\Validator\Constraints as Assert;

class DataField
{
    /**
     * Construct.
     */
    public function __construct(
        #[Assert\NotBlank]
        private ?string $userId,
        #[Assert\NotBlank]
        private ?string $vin
    ) {
    }

    public function getUserId(): ?string
    {
        return $this->userId;
    }

    public function getVin(): ?string
    {
        return $this->vin;
    }
}
