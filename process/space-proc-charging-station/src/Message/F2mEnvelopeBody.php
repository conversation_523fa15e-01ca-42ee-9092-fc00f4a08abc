<?php

namespace App\Message;

use App\Message\Field\DataField;
use Symfony\Component\Serializer\Annotation\SerializedName;
use Symfony\Component\Validator\Constraints as Assert;

class F2mEnvelopeBody
{
    public function __construct(
        #[Assert\NotBlank]
        #[SerializedName('Event')]
        private string $event,
        #[SerializedName('data')]
        #[Assert\NotBlank]
        #[Assert\Valid]
        private ?DataField $data,
    ) {
    }

    public function getEvent(): string
    {
        return $this->event;
    }

    public function getData(): ?DataField
    {
        return $this->data;
    }
}
