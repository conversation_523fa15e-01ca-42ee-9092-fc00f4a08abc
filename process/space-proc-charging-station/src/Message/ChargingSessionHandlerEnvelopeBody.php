<?php

namespace App\Message;

use Symfony\Component\Serializer\Annotation\SerializedName;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Context\ExecutionContextInterface;

class ChargingSessionHandlerEnvelopeBody
{
    public function __construct(
        #[Assert\NotBlank]
        private string $type,
        #[Assert\NotBlank]
        private ?array $message
    ) {
    }

    #[Assert\Callback]
    public function validateEnvelopeBody(ExecutionContextInterface $context): void
    {
        if ($this->getMessage() === null) {
            $context->buildViolation('The message should not be empty and it should be array.')
                ->atPath('message')
                ->addViolation();
        }

        if ($this->getType() == "" or !in_array($this->getType(), ["session","cdr"])) {
            $context->buildViolation("The type should not be empty and it should be session or cdr.")
                ->atPath('type')
                ->addViolation();
        }
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function getMessage(): ?array
    {
        return $this->message;
    }

    public function setMessage(?array $message): void
    {
        $this->message = $message;
    }

    public function setType(string $type): void
    {
        $this->type = $type;
    }
}