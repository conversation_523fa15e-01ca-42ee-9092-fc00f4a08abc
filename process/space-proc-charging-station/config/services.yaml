# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices.html#use-parameters-for-application-configuration
parameters:
    f2mc.url: "%env(SYS_F2MC_URL)%"
    mongo_db.app: "%env(MONGO_APP)%"
    mongo_db.database: "%env(MONGO_DATABASE)%"
    mongo_db.datasource: "%env(MONGO_DATASOURCE)%"
    mongo_db.url: "%env(MONGO_ATLAS_BASE_URL)%"

services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.

    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    App\:
        resource: '../src/'
        exclude:
            - '../src/DependencyInjection/'
            - '../src/Entity/'
            - '../src/Kernel.php'

    # add more service definitions when explicit configuration is needed
    # please note that last definitions always *replace* previous ones

    App\EventListener\ExceptionListener:
        tags:
        - { name: kernel.event_listener, event: kernel.exception }

    App\Connector\F2mConnector:
        arguments:
            $url: '%f2mc.url%'
    App\Connector\MongoAtlasApiConnector:
        $mongoApp: '%mongo_db.app%'
    
    App\Service\MongoAtlasQueryService:
        arguments:
            $database: '%mongo_db.database%'
            $dataSource: '%mongo_db.datasource%'
    
    App\Connector\SystemFirebaseConnector:
        arguments:
            $url: "%env(MS_SYS_FIREBASE_URL)%"

    Symfony\Component\Messenger\Transport\TransportInterface: 
        tags:   [messenger.charging_station_sqs_queue,
                messenger.charging_session_sqs_queue,
                messenger.space_charging_station_callback
                ]

    App\Manager\ChargingStationManagementManager:
        arguments:
            $spaceB2bUrl: "%env(SPACE_B2B_URL)%"

    App\Manager\UserManager:
        arguments:
            $accountLinkingRedirectUrl: "%env(ACCOUNT_LINKING_REDIRECT_URL)%"
    
    App\Manager\ChargingLocationManager:
        arguments:
            $radius: "%env(radius)%"
