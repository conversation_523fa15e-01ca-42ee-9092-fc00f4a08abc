framework:
    messenger:
        # reset services after consuming messages
        reset_on_message: true

        transports:
            charging_session_sqs_queue:
                dsn: 'sqs://sqs.%env(resolve:AWS_SQS_REGION)%.amazonaws.com'
                options:                
                    access_key: '%env(AWS_SQS_ACCESS_KEY)%'
                    secret_key: '%env(AWS_SQS_SECRET_KEY)%'
                    queue_name: '%env(AWS_CHARGING_SESSION_SQS_QUEUE_NAME)%'
                    debug: false
                retry_strategy:
                    max_retries: 0
                serializer: App\Message\ChargingSessionHandlerCustomSerializer

            charging_station_sqs_queue: 
                dsn: 'sqs://sqs.%env(resolve:AWS_SQS_REGION)%.amazonaws.com'
                options:                
                    access_key: '%env(AWS_SQS_ACCESS_KEY)%'
                    secret_key: '%env(AWS_SQS_SECRET_KEY)%'
                    queue_name: '%env(AWS_SQS_QUEUE_NAME)%'
                    debug: false
                #serializer: messenger.transport.symfony_serializer
                retry_strategy:
                    max_retries: 0

            space_charging_station_callback:
                dsn: 'sqs://sqs.%env(resolve:AWS_SQS_REGION)%.amazonaws.com'
                options:                
                    queue_name: '%env(AWS_SQS_QUEUE_NAME_CALLBACK)%'
                    access_key: '%env(AWS_SQS_ACCESS_KEY)%'
                    secret_key: '%env(AWS_SQS_SECRET_KEY)%'
                    debug: false
                #serializer: messenger.transport.symfony_serializer
                serializer: App\Message\SqsMessageDecoder                    

                retry_strategy:
                    max_retries: 3

            # failed: 'doctrine://default?queue_name=failed'

            #sync: 'sync://' # just for test
                # default configuration
                #retry_strategy:
                #    max_retries: 1

        routing:
            # Route your messages to the transports
            'App\Message\F2mEnvelopeBody': charging_station_sqs_queue
            'App\Message\ChargingStationCallback': space_charging_station_callback
            'App\Message\ChargingSessionHandlerEnvelopeBody': charging_session_sqs_queue
