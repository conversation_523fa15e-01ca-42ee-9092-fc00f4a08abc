###> symfony/framework-bundle ###
/_init/
/.env.local
/.env.local.php
/.env.*.local
/.env.integ*
/.env.preprod*
/config/secrets/prod/prod.decrypt.private.php
/public/bundles/
/var/
/vendor/
/_init/
/coverage/
/.vscode/

/.history/
###< symfony/framework-bundle ###
###> symfony/phpunit-bridge ###
.phpunit.result.cache
/phpunit.xml
###< symfony/phpunit-bridge ###
/coverage/
makefile
phpstan.neon 

###> friendsofphp/php-cs-fixer ###
bin/php-cs-fixer
/.php-cs-fixer.php
/.php-cs-fixer.cache
###< friendsofphp/php-cs-fixer ###

###> phpunit/phpunit ###
/phpunit.xml
.phpunit.result.cache
###< phpunit/phpunit ###

###> phpstan/phpstan ###
phpstan.neon
phpstan.dist.neon
bin/phpstan
###< phpstan/phpstan ###

src/Controller/TestController.php