<?php

namespace App\Tests\Service;

use App\Connector\MongoAtlasApiConnector;
use App\Exception\MongoAtlasUrlNotProvidedException;
use App\Helper\WSResponse;
use App\Model\MongoQueryModel;
use App\Service\MongoAtlasQueryService;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Symfony\Contracts\HttpClient\ResponseInterface;

class MongoAtlasQueryServiceTest extends TestCase
{
    private MongoAtlasQueryService $service;
    private MongoAtlasApiConnector $connector;
    private NormalizerInterface $normalizer;
    private LoggerInterface $logger;

    public function setUp(): void
    {
        parent::setUp();

        $this->connector = $this->createMock(MongoAtlasApiConnector::class);
        $this->logger = $this->createMock(LoggerInterface::class);
        $this->logger->expects($this->any())
            ->method('info')
        ;
        $this->logger->expects($this->any())
            ->method('error')
        ;
        $this->connector->setLogger($this->logger);

        $this->normalizer = $this->createMock(NormalizerInterface::class);

        $this->service = new MongoAtlasQueryService(
            $this->normalizer,
            $this->connector,
            'testDataSource',
            'testDatabase'
        );
        $this->service->setLogger($this->logger);
    }

    public function testBuildQuery(): void
    {
        $query = MongoAtlasQueryService::buildQuery('testDataSource', 'testDatabase');

        $this->assertInstanceOf(MongoQueryModel::class, $query);
        $this->assertEquals('testDataSource', $query->getDataSource());
        $this->assertEquals('testDatabase', $query->getDatabase());
    }

    public function testFind(): void
    {
        $this->connector
            ->expects($this->once())
            ->method('getEndpoint')
            ->with('find')
            ->willReturn('testEndpoint');

        $this->normalizer
            ->expects($this->once())
            ->method('normalize')
            ->with($this->isInstanceOf(MongoQueryModel::class), 'array', ['groups' => ['default', 'find']])
            ->willReturn([
                'dataSource' => 'testDataSource',
                'database' => 'testDatabase',
                'collection' => 'testCollection',
                'filter' => [
                    'field1' => 'value1',
                    'field2' => 'value2',
                ],
            ]);

        $mockResponse = $this->createMock(ResponseInterface::class);
        $mockResponse->method('getContent')->willReturn('testResponse');
        $mockResponse->method('getStatusCode')->willReturn(Response::HTTP_OK);

        $this->connector
            ->expects($this->once())
            ->method('call')
            ->with('POST', 'testEndpoint', ['json' => [
                'dataSource' => 'testDataSource',
                'database' => 'testDatabase',
                'collection' => 'testCollection',
                'filter' => [
                    'field1' => 'value1',
                    'field2' => 'value2',
                ],
            ]])
            ->willReturn($mockResponse);

        $response = $this->service->find('testCollection', [
            'field1' => 'value1',
            'field2' => 'value2',
        ], 1, ['field1' => 1, 'field2' => 1]);

        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals('testResponse', $response->getData());
    }

    public function testAggregateOKResponse(): void
    {
        $this->connector
            ->expects($this->once())
            ->method('getEndpoint')
            ->with('aggregate')
            ->willReturn('aggregate');

        $this->normalizer
            ->expects($this->once())
            ->method('normalize')
            ->with($this->isInstanceOf(MongoQueryModel::class), 'array', ['groups' => ['default', 'aggregate']])
            ->willReturn([
                'dataSource' => 'testDataSource',
                'database' => 'testDatabase',
                'collection' => 'testCollection',
                'filter' => [
                    'field1' => 'value1',
                    'field2' => 'value2',
                ],
            ]);

        $mockResponse = $this->createMock(ResponseInterface::class);
        $mockResponse->method('getContent')->willReturn('testResponse');
        $mockResponse->method('getStatusCode')->willReturn(Response::HTTP_OK);

        $this->connector
            ->expects($this->once())
            ->method('call')
            ->with('POST', 'aggregate', ['json' => [
                'dataSource' => 'testDataSource',
                'database' => 'testDatabase',
                'collection' => 'testCollection',
                'filter' => [
                    'field1' => 'value1',
                    'field2' => 'value2',
                ],
            ]])
            ->willReturn($mockResponse);

        $response = $this->service->aggregate('testCollection', [
            'field1' => 'value1',
            'field2' => 'value2',
        ]);

        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals('testResponse', $response->getData());
    }

    public function testUpdateMany(): void
    {
        $response = $this->createMock(ResponseInterface::class);
        $response->method('getStatusCode')->willReturn(Response::HTTP_OK);
        $response->method('getContent')->willReturn('Success response payload');
        $this->mockMethodExecute('updateMany', $response);

        $result = $this->service->updateMany('', [], [], true, ['key' => 'value']);
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertSame(Response::HTTP_OK, $result->getCode());
        $this->assertSame('Success response payload', $result->getData());
    }

    public function testUpdateOne(): void
    {
        $response = $this->createMock(ResponseInterface::class);
        $response->method('getStatusCode')->willReturn(Response::HTTP_OK);
        $response->method('getContent')->willReturn('Success response payload');
        $this->mockMethodExecute('updateOne', $response);

        $result = $this->service->updateOne('', [], [], true);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertSame(Response::HTTP_OK, $result->getCode());
        $this->assertSame('Success response payload', $result->getData());
    }

    public function testUpdatePush(): void
    {
        $response = $this->createMock(ResponseInterface::class);
        $response->method('getStatusCode')->willReturn(Response::HTTP_OK);
        $response->method('getContent')->willReturn('Success response payload');
        $this->mockMethodExecute('updateOne', $response);

        $result = $this->service->updatePush('', [], [], true);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertSame(Response::HTTP_OK, $result->getCode());
        $this->assertSame('Success response payload', $result->getData());
    }

    public function testInsertOne(): void
    {
        $response = $this->createMock(ResponseInterface::class);
        $response->method('getStatusCode')->willReturn(Response::HTTP_OK);
        $response->method('getContent')->willReturn('Success response payload');
        $this->mockMethodExecute('insertOne', $response);

        $result = $this->service->insertOne('', []);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertSame(Response::HTTP_OK, $result->getCode());
        $this->assertSame('Success response payload', $result->getData());
    }

    public function testInsertMany(): void
    {
        $response = $this->createMock(ResponseInterface::class);
        $response->method('getStatusCode')->willReturn(Response::HTTP_OK);
        $response->method('getContent')->willReturn('Success response payload');
        $this->mockMethodExecute('insertMany', $response);

        $result = $this->service->insertMany('', []);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertSame(Response::HTTP_OK, $result->getCode());
        $this->assertSame('Success response payload', $result->getData());
    }

    private function mockMethodExecute(string $action, ResponseInterface $response): void
    {
        $this->logger
            ->expects($this->once())
            ->method('info')
            ->with($this->stringContains('MongoAtlasQueryService::execute'));

        $this->connector
            ->expects($this->once())
            ->method('getEndpoint')
            ->with($action)
            ->willReturn('endpoint');

        $this->normalizer
            ->expects($this->once())
            ->method('normalize')
            ->willReturn([]);

        $this->connector
            ->expects($this->once())
            ->method('call')
            ->willReturn($response);
    }

    private function mockMethodExecuteWithException(\Exception $e): void
    {
        $this->logger
            ->expects($this->once())
            ->method('info')
            ->willThrowException($e);

        $this->logger
            ->expects($this->once())
            ->method('error')
            ->with($this->stringContains('MongoAtlasQueryService::execute Catched Exception'));
    }

    public function testFindOne(): void
    {
        $collection = 'testCollection';
        $filter = ['field1' => 'value1'];
        $projection = ['field1' => 1, 'field2' => 1];

        $response = $this->createMock(ResponseInterface::class);
        $response->method('getStatusCode')->willReturn(Response::HTTP_OK);
        $response->method('getContent')->willReturn(json_encode(['field1' => 'value1', 'field2' => 'value2']));
        $this->mockMethodExecute('findOne', $response);

        $response = $this->service->findOne($collection, $filter, $projection);

        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $data = json_decode($response->getData(), true);
        $this->assertEquals(['field1' => 'value1', 'field2' => 'value2'], $data);
    }

    public function testDeleteFields(): void
    {
        $collection = 'testCollection';
        $filter = ['field1' => 'value1'];
        $fields = ['field2' => ''];
        $upsert = true;

        $response = $this->createMock(ResponseInterface::class);
        $response->method('getStatusCode')->willReturn(Response::HTTP_OK);
        $response->method('getContent')->willReturn(json_encode(['acknowledged' => true, 'modifiedCount' => 1]));
        $this->mockMethodExecute('updateOne', $response);

        $response = $this->service->deleteFields($collection, $filter, $fields, $upsert);

        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $data = json_decode($response->getData(), true);
        $this->assertEquals(['acknowledged' => true, 'modifiedCount' => 1], $data);
    }

    public function testRemoveFields(): void
    {
        $collection = 'testCollection';
        $filter = ['field1' => 'value1'];
        $pull = ['field2' => 'value2'];
        $upsert = true;

        $response = $this->createMock(ResponseInterface::class);
        $response->method('getStatusCode')->willReturn(Response::HTTP_OK);
        $response->method('getContent')->willReturn(json_encode(['acknowledged' => true, 'modifiedCount' => 1]));
        $this->mockMethodExecute('updateOne', $response);

        $response = $this->service->removeFields($collection, $filter, $pull, $upsert);

        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $data = json_decode($response->getData(), true);
        $this->assertEquals(['acknowledged' => true, 'modifiedCount' => 1], $data);
    }

    public function testUpdateFirstDocument(): void
    {
        $collection = 'testCollection';
        $data = ['field1' => 'newValue'];
        $filters = ['field2' => 'value2'];

        $response = $this->createMock(ResponseInterface::class);
        $response->method('getStatusCode')->willReturn(Response::HTTP_OK);
        $response->method('getContent')->willReturn(json_encode(['acknowledged' => true, 'modifiedCount' => 1]));
        $this->mockMethodExecute('updateOne', $response);

        $response = $this->service->updateFirstDocument($collection, $data, $filters);

        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $data = json_decode($response->getData(), true);
        $this->assertEquals(['acknowledged' => true, 'modifiedCount' => 1], $data);
    }

    public function testExecuteThrowsException(): void
    {
        $this->expectException(MongoAtlasUrlNotProvidedException::class);
        $this->expectExceptionMessage('MongoAtlas error!');

        $this->connector
            ->expects($this->once())
            ->method('getEndpoint')
            ->with('updateOne')
            ->willThrowException(new \Exception('Test exception'));

        $this->service->deleteFields('testCollection', ['field1' => 'value1'], ['field2' => ''], true);
    }
}
