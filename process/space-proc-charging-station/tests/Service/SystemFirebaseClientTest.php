<?php

namespace App\Tests\Service;

use App\Connector\SystemFirebaseConnector;
use App\Helper\WSResponse;
use App\Service\SystemFirebaseClient;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class SystemFirebaseClientTest extends TestCase
{
    private $connector;
    private $logger;
    private $service;

    protected function setUp(): void
    {
        $this->connector = $this->createMock(SystemFirebaseConnector::class);
        $this->logger = $this->createMock(LoggerInterface::class);

        $this->service = new SystemFirebaseClient($this->connector);
        $this->service->setLogger($this->logger);
    }

    public function testSendNotification(): void
    {
        $users = [
            [
                'push' => [
                    [
                        'pushToken' => 'token123'
                    ]
                ]
            ]
        ];
        $message = [
            'title' => 'Test Notification',
            'body' => 'This is a test notification.'
        ];

        $mockResponse = new WSResponse(Response::HTTP_OK, json_encode(['success' => true]));

        $this->connector->expects($this->once())
            ->method('call')
            ->with(
                Request::METHOD_POST,
                '/v1/push/notification',
                [
                    'headers' => [
                        'pushToken' => 'token123',
                    ],
                    'json' => [
                        'message' => $message,
                    ],
                ]
            )
            ->willReturn($mockResponse);

        $this->logger->expects($this->exactly(3))
            ->method('info')
            ->withConsecutive(
                [$this->stringContains('Sending notification'), $this->arrayHasKey('uri')],
                [$this->stringContains('Sending notification'), $this->arrayHasKey('options')],
                [$this->stringContains('Notification sent status'), $this->arrayHasKey('response')]
            );

        $result = $this->service->sendNotification($users, $message);

        $this->assertTrue($result);
    }
}
