<?php

namespace App\Tests\Service;

use App\Connector\F2mConnector;
use App\Helper\WSResponse;
use App\Service\ChargingLocationService;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class ChargingLocationServiceTest extends TestCase
{
    private $f2mcApiConnector;
    private $chargingLocationService;

    protected function setUp(): void
    {
        $this->f2mcApiConnector = $this->createMock(F2mConnector::class);
        $this->chargingLocationService = new ChargingLocationService($this->f2mcApiConnector);
    }

    public function testGetLocationsCreatesSuccessfulResponse(): void
    {
        $payload = ['filters' => []];
        $sortBy = 'name';
        $orderBy = 'asc';
        $offset = 0;
        $limit = 10;
        $fromDate = '2025-01-01 00:00:00';
        $toDate = '2025-01-10 00:00:00';

        $this->f2mcApiConnector->expects($this->once())
            ->method('callF2mc')
            ->with(
                Request::METHOD_POST,
                'v1/charging/locations',
                [
                    'headers' => [
                        'Content-Type' => 'application/json',
                    ],
                    'query' => ['sortBy' => $sortBy, 'orderBy' => $orderBy, 'offset' => $offset, 'limit' => $limit, 'fromDate' => $fromDate, 'toDate' => $toDate],
                    'json' => $payload,
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_OK, ['success' => ['result' => [], 'count' => 0]]));

        $result = $this->chargingLocationService->getLocations($payload, $sortBy, $orderBy, $offset, $limit, $fromDate, $toDate);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
        $this->assertArrayHasKey('success', $result->getData());
        $this->assertEquals(['success' => ['result' => [], 'count' => 0]], $result->getData());
    }

    public function testGetLocationsNotFound(): void
    {
        $payload = ['filters' => []];

        $wsResponseMock = $this->createMock(WSResponse::class);
        $wsResponseMock->method('getCode')->willReturn(Response::HTTP_OK);
        $wsResponseMock->method('getData')->willReturn(['error' => 'Not Found']);

        $this->f2mcApiConnector->method('callF2mc')->willReturn($wsResponseMock);

        $result = $this->chargingLocationService->getLocations($payload, null, null, null, null, null, null);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_NOT_FOUND, $result->getCode());
    }

    public function testGetLocationsHandlesErrorResponse(): void
    {
        $payload = ['filters' => []];
        $sortBy = 'Invalid name';
        $orderBy = 'asc';
        $offset = 0;
        $limit = 10;
        $fromDate = '2025-01-01 00:00:00';
        $toDate = '2025-01-10 00:00:00';

        $this->f2mcApiConnector->expects($this->once())
            ->method('callF2mc')
            ->with(
                Request::METHOD_POST,
                'v1/charging/locations',
                [
                    'headers' => [
                        'Content-Type' => 'application/json',
                    ],
                    'query' => ['sortBy' => $sortBy, 'orderBy' => $orderBy, 'offset' => $offset, 'limit' => $limit, 'fromDate' => $fromDate, 'toDate' => $toDate],
                    'json' => $payload,
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, ['error' => 'Invalid query params']));

        $result = $this->chargingLocationService->getLocations($payload, $sortBy, $orderBy, $offset, $limit, $fromDate, $toDate);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->getCode());
        $this->assertEquals(['error' => 'Invalid query params'], $result->getData());
    }
}
