<?php

declare(strict_types=1);

namespace App\Tests\Service;

use App\Helper\ErrorResponse;
use App\Helper\SuccessResponse;
use App\Helper\WSResponse;
use App\Service\ChargingStationCallbackService;
use App\Service\MongoAtlasQueryService;
use App\Service\SystemFirebaseClient;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;

class ChargingStationCallbackServiceTest extends TestCase
{
    private ChargingStationCallbackService $service;
    private MongoAtlasQueryService $mongoService;
    private SystemFirebaseClient $systemFirebaseClient;
    private LoggerInterface $logger;

    public function setUp(): void
    {
        $this->mongoService = $this->createMock(MongoAtlasQueryService::class);
        $this->systemFirebaseClient = $this->createMock(SystemFirebaseClient::class);
        $this->logger = $this->createMock(LoggerInterface::class);

        $this->service = new ChargingStationCallbackService(
            $this->mongoService,
            $this->systemFirebaseClient
        );

        $this->service->setLogger($this->logger);
    }

    public function testGetChargingSessionByUserIdAndInternalId(): void
    {
        $userId = 'user123';
        $internalId = 'internal123';

        $expectedPipeline = [
            [
                '$match' => [
                    'userId' => $userId
                ]
            ],
            [
                '$unwind' => '$vehicle'
            ],
            [
                '$match' => [
                    'vehicle.chargingStation.session' => [
                        '$elemMatch' => [
                            'internalId' => $internalId
                        ]
                    ]
                ]
            ],
            [
                '$project' => [
                    '_id' => 1,
                    'userId' => 1,
                    'session' => [
                        '$filter' => [
                            'input' => '$vehicle.chargingStation.session',
                            'as' => 'session',
                            'cond' => [
                                '$eq' => ['$$session.internalId', $internalId]
                            ]
                        ]
                    ]
                ]
            ],
            [
                '$unwind' => '$session'
            ],
            [
                '$project' => [
                    '_id' => 1,
                    'userId' => 1,
                    'session' => '$session'
                ]
            ]
        ];

        $mockResponse = $this->createMock(WSResponse::class);
        $mockResponse->method('getCode')->willReturn(Response::HTTP_OK);
        $jsonResponse = '{"documents":[{"_id":"6682aacdde6612063cf457fe","userId":"100000000f0b4dce874859657ae00001","session":{"id":"session-005","internalId":"internal-005","provider":"F2MC","startDate":null,"updateDate":null,"remoteCommandStatus":"MYVALUE","locationId":null,"evseId":null,"connectorId":null}}]}';
        $mockResponse->method('getData')->willReturn($jsonResponse);

        $this->mongoService
            ->expects($this->once())
            ->method('aggregate')
            ->with(ChargingStationCallbackService::USER_COLLECTION, $expectedPipeline)
            ->willReturn($mockResponse);

        $result = $this->service->getChargingSessionByUserIdAndInternalId($userId, $internalId);

        $this->assertIsArray($result);
        $expectedResult = [
            "id" => "session-005",
            "internalId" => "internal-005",
            "provider" => "F2MC",
            "startDate" => null,
            "updateDate" => null,
            "remoteCommandStatus" => "MYVALUE",
            "locationId" => null,
            "evseId" => null,
            "connectorId" => null,
        ];
        $this->assertEquals($expectedResult, $result);
    }

    public function testGetChargingSessionByUserIdAndInternalIdReturnsEmpty(): void
    {
        $userId = 'user123';
        $internalId = 'internal123';

        $expectedPipeline = [
            [
                '$match' => [
                    'userId' => $userId
                ]
            ],
            [
                '$unwind' => '$vehicle'
            ],
            [
                '$match' => [
                    'vehicle.chargingStation.session' => [
                        '$elemMatch' => [
                            'internalId' => $internalId
                        ]
                    ]
                ]
            ],
            [
                '$project' => [
                    '_id' => 1,
                    'userId' => 1,
                    'session' => [
                        '$filter' => [
                            'input' => '$vehicle.chargingStation.session',
                            'as' => 'session',
                            'cond' => [
                                '$eq' => ['$$session.internalId', $internalId]
                            ]
                        ]
                    ]
                ]
            ],
            [
                '$unwind' => '$session'
            ],
            [
                '$project' => [
                    '_id' => 1,
                    'userId' => 1,
                    'session' => '$session'
                ]
            ]
        ];

        $mockResponse = $this->createMock(WSResponse::class);
        $mockResponse->method('getCode')->willReturn(Response::HTTP_NOT_FOUND);
        $mockResponse->method('getData')->willReturn(json_encode(['documents' => []]));

        $this->mongoService
            ->expects($this->once())
            ->method('aggregate')
            ->with(ChargingStationCallbackService::USER_COLLECTION, $expectedPipeline)
            ->willReturn($mockResponse);

        $result = $this->service->getChargingSessionByUserIdAndInternalId($userId, $internalId);

        $this->assertNull($result);
    }

    public function testGetChargingSessionByUserIdAndSessionId(): void
    {
        $userId = 'user123';
        $sessionId = 'session123';

        $expectedPipeline = [
            [
                '$match' => [
                    'userId' => $userId
                ]
            ],
            [
                '$unwind' => '$vehicle'
            ],
            [
                '$match' => [
                    'vehicle.chargingStation.session' => [
                        '$elemMatch' => [
                            'id' => $sessionId
                        ]
                    ]
                ]
            ],
            [
                '$project' => [
                    '_id' => 1,
                    'userId' => 1,
                    'session' => [
                        '$filter' => [
                            'input' => '$vehicle.chargingStation.session',
                            'as' => 'session',
                            'cond' => [
                                '$eq' => ['$$session.id', $sessionId]
                            ]
                        ]
                    ]
                ]
            ],
            [
                '$unwind' => '$session'
            ],
            [
                '$project' => [
                    '_id' => 1,
                    'userId' => 1,
                    'session' => '$session'
                ]
            ]
        ];

        $mockResponse = $this->createMock(WSResponse::class);
        $mockResponse->method('getCode')->willReturn(Response::HTTP_OK);
        $jsonResponse = '{"documents":[{"_id":"6682aacdde6612063cf457fe","userId":"100000000f0b4dce874859657ae00001","session":{"id":"session-005","internalId":"internal-005","provider":"F2MC","startDate":null,"updateDate":null,"remoteCommandStatus":"MYVALUE","locationId":null,"evseId":null,"connectorId":null}}]}';
        $mockResponse->method('getData')->willReturn($jsonResponse);

        $this->mongoService
            ->expects($this->once())
            ->method('aggregate')
            ->with(ChargingStationCallbackService::USER_COLLECTION, $expectedPipeline)
            ->willReturn($mockResponse);

        $result = $this->service->getChargingSessionByUserIdAndSessionId($userId, $sessionId);

        $this->assertIsArray($result);
        $expectedResult = [
            "id" => "session-005",
            "internalId" => "internal-005",
            "provider" => "F2MC",
            "startDate" => null,
            "updateDate" => null,
            "remoteCommandStatus" => "MYVALUE",
            "locationId" => null,
            "evseId" => null,
            "connectorId" => null,
        ];
        $this->assertEquals($expectedResult, $result);
    }

    public function testGetUserDocumentBySessionId(): void
    {
        $userId = 'user123';
        $sessionId = 'session-002';

        $filter = [
            'userId' => $userId,
            'vehicle.chargingStation.session.id' => $sessionId
        ];
        $mockResponse = $this->createMock(WSResponse::class);
        $mockResponse->method('getCode')->willReturn(Response::HTTP_OK);
        $mockResponse->method('getData')->willReturn($this->getJsonRealUserDocument());

        $this->mongoService
            ->expects($this->once())
            ->method('find')
            ->with(ChargingStationCallbackService::USER_COLLECTION, $filter, 1)
            ->willReturn($mockResponse);

        $result = $this->service->getUserDocumentBySessionId($userId, $sessionId);

        $this->assertIsArray($result);
        $this->assertEquals('100000000f0b4dce874859657ae00001', $result['userId']);
        $this->assertArrayHasKey('vehicle', $result);
        $this->assertIsArray($result['vehicle']);
        $this->assertArrayHasKey('chargingStation', $result['vehicle'][0]);
        $this->assertArrayHasKey('session', $result['vehicle'][0]['chargingStation']);
        $this->assertIsArray($result['vehicle'][0]['chargingStation']['session']);
        $this->assertEquals('session-002', $result['vehicle'][0]['chargingStation']['session'][1]['id']);
    }

    public function testGetUserDocumentByInternalId(): void
    {
        $userId = 'user123';
        $internalId = 'internal-002';

        $filter = [
            'userId' => $userId,
            'vehicle.chargingStation.session.internalId' => $internalId
        ];

        $mockResponse = $this->createMock(WSResponse::class);
        $mockResponse->method('getCode')->willReturn(Response::HTTP_OK);
        $mockResponse->method('getData')->willReturn($this->getJsonRealUserDocument());

        $this->mongoService
            ->expects($this->once())
            ->method('find')
            ->with(ChargingStationCallbackService::USER_COLLECTION, $filter, 1)
            ->willReturn($mockResponse);

        $result = $this->service->getUserDocumentByInternalId($userId, $internalId);

        $this->assertIsArray($result);
        $this->assertEquals('100000000f0b4dce874859657ae00001', $result['userId']);
        $this->assertArrayHasKey('vehicle', $result);
        $this->assertIsArray($result['vehicle']);
        $this->assertArrayHasKey('chargingStation', $result['vehicle'][0]);
        $this->assertArrayHasKey('session', $result['vehicle'][0]['chargingStation']);
        $this->assertIsArray($result['vehicle'][0]['chargingStation']['session']);
        $this->assertEquals('session-002', $result['vehicle'][0]['chargingStation']['session'][1]['id']);
    }

    public function testSendNotification(): void
    {
        $userId = 'user123';
        $sessionData = [
            'id' => 'session-005',
            'internalId' => 'internal-005',
        ];

        $message = [
            'notification' => [
                'title' => 'charging station update',
                'body' => 'charging of vehicle has been updated'
            ],
            'content_available' => true,
            'data' => [
                'provider' => 'space-mid',
                'event' => [
                    'type' => 'chargingStationUpdate',
                    'details' => [
                        'session' => $sessionData
                    ]
                ]
            ]
        ];

        $mockResponse = $this->createMock(SuccessResponse::class);
        $mockResponse->method('getCode')->willReturn(Response::HTTP_OK);
        $mockResponse->method('getData')->willReturn(json_encode(['success' => true]));

        $this->systemFirebaseClient
            ->expects($this->once())
            ->method('sendNotification')
            ->with([$userId], $message)
            ->willReturn($mockResponse);

        $response = $this->service->sendNotification($userId, $sessionData);

        $this->assertInstanceOf(SuccessResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
    }

    public function testSendNotificationThrowsException(): void
    {
        $userId = 'user123';
        $sessionData = [
            'id' => 'session-005',
            'internalId' => 'internal-005',
        ];

        $message = [
            'notification' => [
                'title' => 'charging station update',
                'body' => 'charging of vehicle has been updated'
            ],
            'content_available' => true,
            'data' => [
                'provider' => 'space-mid',
                'event' => [
                    'type' => 'chargingStationUpdate',
                    'details' => [
                        'session' => $sessionData
                    ]
                ]
            ]
        ];

        $this->systemFirebaseClient
            ->expects($this->once())
            ->method('sendNotification')
            ->with([$userId], $message)
            ->willThrowException(new \Exception('Test exception'));

        $result = $this->service->sendNotification($userId, $sessionData);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals(Response::HTTP_INTERNAL_SERVER_ERROR, $result->getCode());
        $this->assertStringContainsString('Error while sending notification', $result->getMessage());
    }

    private function getJsonRealUserDocument(): string
    {
        $jsonDocument = '{"documents":[{"_id":"6682aacdde6612063cf457fe","userId":"100000000f0b4dce874859657ae00001","vehicle":[{"id":"2ee1fe3b-0594-4662-b538-310dcd5b3df0","vin":"VR3UPHNKSKT101603","versionId ":"1PK9AFTMD5B0A010M0LGOCFY","label":"Updated Vehicle Label UPDATED","mileage":{"value":0,"date":**********},"vehicleOrder":{"mopId":"NjQwZjM3ODBjZTNlNDY3MGNmNTg4NTBDTEST1","orderFormId":"566777","trackingStatus":"PRODUCTION_START","orderFormStatus":"TO_BE_VALIDATED","isUpdated":true},"addStatus":"CREATION","brand":"OP","country":"GB","isOrder":false,"language":"en","versionId":"test","visual":"https://OP.image.com","chargingStation":{"session":[{"id":"session-001","internalId":"internal-001","provider":"F2MC","startDate":null,"updateDate":null,"remoteCommandStatus":"CHARGING_ACCEPTED","locationId":null,"evseId":null,"connectorId":null},{"id":"session-002","internalId":"internal-002","provider":"F2MC","startDate":null,"updateDate":null,"remoteCommandStatus":"CHARGING_ACCEPTED","locationId":null,"evseId":null,"connectorId":null}]},"Provider":null,"session":null,"vehicleId":null,"provider":null,"CHARGING_STATION_MANAGEMENT":{"status":"enable","value":"","config":{"partner":"f2mc","enrolmentStatus":"noPayementMethod"}},"licencePlate":"myPlate","nickName":"MypersonalNickName"},{"id":"a2cd1e14-a541-442b-b086-d540b94c264b","vin":"VR3UPHNKSKT101604","versionId ":"1PK9AFTMD5B0A010M0LGOCFY","label":"Tetiana","mileage":{"value":20000,"date":**********},"vehicleOrder":{"mopId":"NjQwZjM3ODBjZTNlNDY3MGNmNTg4NTBDTEST1","orderFormId":"566777","trackingStatus":"DELIVERED_CUSTOMER","orderFormStatus":"TO_BE_VALIDATED","isUpdated":true},"addStaus":"","versionId":"test","chargingStation":{"session":{"remoteCommandStatus":"COMPLETED","updateDate":**********,"id":"4747173e-ce0d-4143-b916-298f814d73b9"}},"chargingStation2":{"session":{"id":"4747173e-ce0d-4143-b916-298f814d73b9"}},"CHARGING_STATION_MANAGEMENT":{"status":"enable","value":"","config":{"partner":"f2mc","enrolmentStatus":"noPayementMethod"}},"licencePlate":"myPlate","nickName":"MypersonalNickName"},{"id":"1993042f-23cc-4a9a-aa5c-27b476eca0ac","vin":"VR3UPHNKSKT101605","label":"Updated Vehicle Label UPDATED","versionId":"test","brand":"OP","visual":"https://OP.image.com","language":"en","country":"GB","isOrder":false,"vehicleOrder":{"mopId":"NjQwZjM3ODBjZTNlNDY3MGNmNTg4NTBDTEST2","orderFormId":"566777","trackingStatus":"DELIVERED_CUSTOMER","isUpdated":true,"orderFormStatus":"TO_BE_VALIDATED"},"addStatus":"","CHARGING_STATION_MANAGEMENT":{"status":"enable","value":"","config":{"partner":"f2mc","enrolmentStatus":"noPayementMethod"}}},{"id":"dbffc883-9c48-41ed-b120-44f2865c7d76","vin":"VR3UPHNKSKT101605","label":"Order OPEL Vehicle","versionId":"test","brand":"OP","visual":"https://OP.image.com","language":"en","country":"GB","isOrder":true,"vehicleOrder":{"mopId":"NjQwZjM3ODBjZTNlNDY3MGNmNTg4NTBDTEST2","orderFormId":"566777","trackingStatus":"PRODUCTION_START","isUpdated":true,"orderFormStatus":"TO_BE_VALIDATED"},"CHARGING_STATION_MANAGEMENT":{"status":"enable","value":"","config":{"partner":"f2mc","enrolmentStatus":"noPayementMethod"}}},{"id":"1358e78c-2032-421c-8137-47736f3f98bf","vin":"VR3UPHNKSKT101606","label":"Order OPEL Vehicle","versionId":"test","brand":"OP","visual":"https://OP.image.com","language":"en","country":"GB","isOrder":false,"vehicleOrder":{"mopId":"NjQwZjM3ODBjZTNlNDY3MGNmNTg4NTBDTEST3","orderFormId":"566777","trackingStatus":"DELIVERED_CUSTOMER","isUpdated":true,"orderFormStatus":"TO_BE_VALIDATED"},"addStatus":"","CHARGING_STATION_MANAGEMENT":{"status":"enable","value":"","config":{"partner":"f2mc","enrolmentStatus":"noPayementMethod"}}},{"id":"01db48f5-102e-487a-897f-8a5ae18f5262","vin":"VR3UPHNKSKT101607","label":"Order OPEL Vehicle","versionId":"test","brand":"OP","visual":"https://OP.image.com","language":"en","country":"GB","isOrder":false,"vehicleOrder":{"mopId":"NjQwZjM3ODBjZTNlNDY3MGNmNTg4NTBDTEST4","orderFormId":"566777","trackingStatus":"DELIVERED_CUSTOMER","isUpdated":true,"orderFormStatus":"TO_BE_VALIDATED"},"addStatus":"","CHARGING_STATION_MANAGEMENT":{"status":"enable","value":"","config":{"partner":"f2mc","enrolmentStatus":"noPayementMethod"}},"chargingStation":{"session":[{"id":"session-003","internalId":"internal-003","provider":"F2MC","startDate":null,"updateDate":null,"remoteCommandStatus":"MY SESSION VALUE","locationId":null,"evseId":null,"connectorId":null},{"id":"session-004","internalId":"internal-004","provider":"F2MC","startDate":null,"updateDate":null,"remoteCommandStatus":"PENDING","locationId":null,"evseId":null,"connectorId":null},{"id":"session-005","internalId":"internal-005","provider":"F2MC","startDate":null,"updateDate":null,"remoteCommandStatus":"MYVALUE","locationId":null,"evseId":null,"connectorId":null}]}},{"id":"18b17cc4-2595-402c-b18f-031d919f6c18","vin":"VR3UPHNKSKT101608","label":"Order OPEL Vehicle","versionId":"test","brand":"OP","visual":"https://OP.image.com","language":"en","country":"GB","isOrder":false,"vehicleOrder":{"mopId":"NjQwZjM3ODBjZTNlNDY3MGNmNTg4NTBDTEST5","orderFormId":"566777","trackingStatus":"DELIVERED_CUSTOMER","isUpdated":true,"orderFormStatus":"TO_BE_VALIDATED"},"addStatus":"CREATION","CHARGING_STATION_MANAGEMENT":{"status":"enable","value":"","config":{"partner":"f2mc","enrolmentStatus":"noPayementMethod"}}},{"vin":"VR3UPHNKSKT101603","mileage":{"value":1,"date":**********},"nickName":"MypersonalNickName","licencePlate":"myPlate","CHARGING_STATION_MANAGEMENT":{"status":"enable","value":"","config":{"partner":"f2mc","enrolmentStatus":"noPayementMethod"}}},{"vin":"VR3UPHNKSKT101603","mileage":{"value":1,"date":**********},"nickName":"MypersonalNickName","licencePlate":"myPlate","CHARGING_STATION_MANAGEMENT":{"status":"enable","value":"","config":{"partner":"f2mc","enrolmentStatus":"noPayementMethod"}}},{"id":"9200d72c-a98f-408d-b075-ab5f3dda78a3","versionId":"","shortLabel":"Citroen Basalt","regTimeStamp":1603178811423,"year":2022,"vin":"J1234567891234567","modelDescription":"","type":"modelDescription","mileage":{"value":20000,"date":1741092985},"sdp":"SSDP","featureCode":[],"picture":"https://example.com/picture.jpg","brand":"JE","country":"FR","isOrder":true,"addStatus":"COMPLETE","lastUpdate":**********,"make":null,"market":null,"warrantyStartDate":null,"isO2x":false,"licencePlate":"myPlate","nickName":"MypersonalNickNameTest"},{"id":"7ea8a2dc-18d9-48b4-ac16-3454bfc26162","versionId":"","shortLabel":"Citroen Basalt","regTimeStamp":1603178811423,"year":2022,"vin":"J1234567891234567","modelDescription":"","type":"modelDescription","mileage":{"value":0,"date":**********},"sdp":"SSDP","featureCode":[],"picture":"https://example.com/picture.jpg","brand":"JE","country":"FR","isOrder":true,"addStatus":"COMPLETE","lastUpdate":**********,"make":null,"market":null,"warrantyStartDate":null,"isO2x":false}],"id":"8a78aa8f-630c-447c-b9b0-3e103b958a57","userPsaId":null,"test":"3","userData":null,"f2mc":{"accessToken":"B_Vg","userId":"7c91e083-2a64-44c8-b6a2-21d54160477d"},"chargingStation":null,"provider":null,"session":null,"vehicleId":null,"charingStation":null,"label":"Tetiana 2","f2m":null,"accessToken":null,"refreshToken":null,"f2":null,"testObj":null,"testOb":{},"test2":"3","test4":"3","userDbId":"ST-ceab83f7013abf316ad3802a6fa7f7db"}]}';

        return $jsonDocument;
    }

    public function testUpdateChargingSessionByUserIdAndInternalId(): void
    {
        $userId = 'user123';
        $internalId = 'internal123';
        $chargingSession = ['remoteCommandStatus' => 'CHARGING'];

        $document = [
            '_id' => 'documentId',
            'userId' => $userId,
            'vehicle' => [
                [
                    'chargingStation' => [
                        'session' => [
                            [
                                'internalId' => $internalId,
                                'status' => 'active'
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $filter = [
            'userId' => $userId,
            'vehicle.chargingStation.session.internalId' => $internalId
        ];

        $mockFindResponse = $this->createMock(WSResponse::class);
        $mockFindResponse->method('getCode')->willReturn(Response::HTTP_OK);
        $mockFindResponse->method('getData')->willReturn(json_encode(['documents' => [$document]]));

        $this->mongoService->expects($this->once())
            ->method('find')
            ->with(ChargingStationCallbackService::USER_COLLECTION, $filter, 1)
            ->willReturn($mockFindResponse);

        $filter = ['userId' => $userId];
        $toUpdate = [
            'vehicle.0.chargingStation.session.0.remoteCommandStatus' => $chargingSession['remoteCommandStatus']
        ];

        $mockUpdateResponse = $this->createMock(WSResponse::class);
        $mockUpdateResponse->method('getCode')->willReturn(Response::HTTP_OK);
        $mockUpdateResponse->method('getData')->willReturn(json_encode(['modifiedCount' => 1]));

        $this->mongoService->expects($this->once())
            ->method('updateOne')
            ->with(ChargingStationCallbackService::USER_COLLECTION, $filter, $toUpdate)
            ->willReturn($mockUpdateResponse);

        $result = $this->service->updateChargingSessionByUserIdAndInternalId($userId, $internalId, $chargingSession);

        $this->assertTrue($result);
    }

    public function testUpdateChargingSessionByUserIdAndInternalIdReturnsFalseWhenDocumentNotFound(): void
    {
        $userId = 'user123';
        $internalId = 'internal123';
        $chargingSession = ['remoteCommandStatus' => 'CHARGING'];

        $filter = [
            'userId' => $userId,
            'vehicle.chargingStation.session.internalId' => $internalId
        ];

        $mockFindResponse = $this->createMock(WSResponse::class);
        $mockFindResponse->method('getCode')->willReturn(Response::HTTP_NOT_FOUND);
        $mockFindResponse->method('getData')->willReturn(json_encode(['documents' => []]));

        $this->mongoService->expects($this->once())
            ->method('find')
            ->with(ChargingStationCallbackService::USER_COLLECTION, $filter, 1)
            ->willReturn($mockFindResponse);

        $result = $this->service->updateChargingSessionByUserIdAndInternalId($userId, $internalId, $chargingSession);

        $this->assertFalse($result);
    }

    public function testUpdateChargingSessionByUserIdAndInternalIdReturnsFalseWhenIndexesNotFound(): void
    {
        $userId = 'user123';
        $internalId = 'internal123';
        $chargingSession = ['remoteCommandStatus' => 'CHARGING'];

        $document = [
            '_id' => 'documentId',
            'userId' => $userId,
            'vehicle' => [
                [
                    'chargingStation' => [
                        'session' => [
                            [
                                'internalId' => 'internal456',
                                'status' => 'active'
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $filter = [
            'userId' => $userId,
            'vehicle.chargingStation.session.internalId' => $internalId
        ];

        $mockFindResponse = $this->createMock(WSResponse::class);
        $mockFindResponse->method('getCode')->willReturn(Response::HTTP_OK);
        $mockFindResponse->method('getData')->willReturn(json_encode(['documents' => [$document]]));

        $this->mongoService->expects($this->once())
            ->method('find')
            ->with(ChargingStationCallbackService::USER_COLLECTION, $filter, 1)
            ->willReturn($mockFindResponse);

        $result = $this->service->updateChargingSessionByUserIdAndInternalId($userId, $internalId, $chargingSession);

        $this->assertFalse($result);
    }

    public function testUpdateChargingSessionByUserIdAndInternalIdReturnsFalseWhenUpdateFails(): void
    {
        $userId = 'user123';
        $internalId = 'internal123';
        $chargingSession = ['remoteCommandStatus' => 'CHARGING'];

        $document = [
            '_id' => 'documentId',
            'userId' => $userId,
            'vehicle' => [
                [
                    'chargingStation' => [
                        'session' => [
                            [
                                'internalId' => $internalId,
                                'status' => 'active'
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $filter = [
            'userId' => $userId,
            'vehicle.chargingStation.session.internalId' => $internalId
        ];

        $mockFindResponse = $this->createMock(WSResponse::class);
        $mockFindResponse->method('getCode')->willReturn(Response::HTTP_OK);
        $mockFindResponse->method('getData')->willReturn(json_encode(['documents' => [$document]]));

        $this->mongoService->expects($this->once())
            ->method('find')
            ->with(ChargingStationCallbackService::USER_COLLECTION, $filter, 1)
            ->willReturn($mockFindResponse);

        $filter = ['userId' => $userId];
        $toUpdate = [
            'vehicle.0.chargingStation.session.0.remoteCommandStatus' => $chargingSession['remoteCommandStatus']
        ];

        $mockUpdateResponse = $this->createMock(WSResponse::class);
        $mockUpdateResponse->method('getCode')->willReturn(Response::HTTP_INTERNAL_SERVER_ERROR);
        $mockUpdateResponse->method('getData')->willReturn(json_encode(['modifiedCount' => 0]));

        $this->mongoService->expects($this->once())
            ->method('updateOne')
            ->with(ChargingStationCallbackService::USER_COLLECTION, $filter, $toUpdate)
            ->willReturn($mockUpdateResponse);

        $result = $this->service->updateChargingSessionByUserIdAndInternalId($userId, $internalId, $chargingSession);

        $this->assertFalse($result);
    }

    public function testUpdateChargingSessionByUserIdAndInternalIdReturnsFalseWhenModifiedCountIsNotOne(): void
    {
        $userId = 'user123';
        $internalId = 'internal123';

        $document = [
            '_id' => 'documentId',
            'userId' => $userId,
            'vehicle' => [
                [
                    'chargingStation' => [
                        'session' => [
                            [
                                'internalId' => $internalId,
                                'status' => 'active'
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $filter = [
            'userId' => $userId,
            'vehicle.chargingStation.session.internalId' => $internalId
        ];

        $mockFindResponse = $this->createMock(WSResponse::class);
        $mockFindResponse->method('getCode')->willReturn(Response::HTTP_OK);
        $mockFindResponse->method('getData')->willReturn(json_encode(['documents' => [$document]]));

        $this->mongoService->expects($this->once())
            ->method('find')
            ->with(ChargingStationCallbackService::USER_COLLECTION, $filter, 1)
            ->willReturn($mockFindResponse);

        $filter = ['userId' => $userId];
        $toUpdate = [
            'vehicle.0.chargingStation.session.0.remoteCommandStatus' => 'CHARGING'
        ];

        $mockUpdateResponse = $this->createMock(WSResponse::class);
        $mockUpdateResponse->method('getCode')->willReturn(Response::HTTP_OK);
        $mockUpdateResponse->method('getData')->willReturn(json_encode(['modifiedCount' => 0]));

        $this->mongoService->expects($this->once())
            ->method('updateOne')
            ->with(ChargingStationCallbackService::USER_COLLECTION, $filter, $toUpdate)
            ->willReturn($mockUpdateResponse);

        $result = $this->service->updateChargingSessionByUserIdAndInternalId($userId, $internalId, ['remoteCommandStatus' => 'CHARGING']);

        $this->assertFalse($result);
    }

    public function testUpdateChargingSessionByUserIdAndSessionId(): void
    {
        $userId = 'user123';
        $sessionId = 'session123';
        $chargingSession = ['remoteCommandStatus' => 'CHARGING'];

        $document = [
            '_id' => 'documentId',
            'userId' => $userId,
            'vehicle' => [
                [
                    'chargingStation' => [
                        'session' => [
                            [
                                'id' => $sessionId,
                                'status' => 'active'
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $filter = [
            'userId' => $userId,
            'vehicle.chargingStation.session.id' => $sessionId
        ];

        $mockFindResponse = $this->createMock(WSResponse::class);
        $mockFindResponse->method('getCode')->willReturn(Response::HTTP_OK);
        $mockFindResponse->method('getData')->willReturn(json_encode(['documents' => [$document]]));

        $this->mongoService->expects($this->once())
            ->method('find')
            ->with(ChargingStationCallbackService::USER_COLLECTION, $filter, 1)
            ->willReturn($mockFindResponse);

        $indexes = [
            'vehicleIndex' => 0,
            'sessionsIndex' => 0
        ];

        $filter = ['userId' => $userId];
        $toUpdate = [
            'vehicle.0.chargingStation.session.0.remoteCommandStatus' => $chargingSession['remoteCommandStatus']
        ];

        $mockUpdateResponse = $this->createMock(WSResponse::class);
        $mockUpdateResponse->method('getCode')->willReturn(Response::HTTP_OK);
        $mockUpdateResponse->method('getData')->willReturn(json_encode(['modifiedCount' => 1]));

        $this->mongoService->expects($this->once())
            ->method('updateOne')
            ->with(ChargingStationCallbackService::USER_COLLECTION, $filter, $toUpdate)
            ->willReturn($mockUpdateResponse);

        $result = $this->service->updateChargingSessionByUserIdAndSessionId($userId, $sessionId, $chargingSession);

        $this->assertTrue($result);
    }

    public function testUpdateChargingSessionByUserIdAndInternalIdReturnFalseWhenVehicleArrayNotFound(): void
    {
        $userId = 'user123';
        $internalId = 'internal123';
        $chargingSession = ['remoteCommandStatus' => 'CHARGING'];

        $document = [
            '_id' => 'documentId',
            'userId' => $userId,
        ];

        $filter = [
            'userId' => $userId,
            'vehicle.chargingStation.session.internalId' => $internalId
        ];

        $mockFindResponse = $this->createMock(WSResponse::class);
        $mockFindResponse->method('getCode')->willReturn(Response::HTTP_OK);
        $mockFindResponse->method('getData')->willReturn(json_encode(['documents' => [$document]]));

        $this->mongoService->expects($this->once())
            ->method('find')
            ->with(ChargingStationCallbackService::USER_COLLECTION, $filter, 1)
            ->willReturn($mockFindResponse);

        $filter = ['userId' => $userId];
        $toUpdate = [
            'vehicle.0.chargingStation.session.0.remoteCommandStatus' => $chargingSession['remoteCommandStatus']
        ];

        $this->mongoService->expects($this->never())
            ->method('updateOne')
            ->with(ChargingStationCallbackService::USER_COLLECTION, $filter, $toUpdate);

        $result = $this->service->updateChargingSessionByUserIdAndInternalId($userId, $internalId, $chargingSession);

        $this->assertFalse($result);
    }
}
