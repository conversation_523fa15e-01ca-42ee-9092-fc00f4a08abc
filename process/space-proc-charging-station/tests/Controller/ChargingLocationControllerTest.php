<?php

use App\Controller\ChargingLocationController;
use App\Helper\ErrorResponse;
use App\Helper\SuccessResponse;
use App\Manager\ChargingLocationManager;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class ChargingLocationControllerTest extends WebTestCase
{
    private $chargingLocationController;
    private $chargingLocationManager;

    protected function setUp(): void
    {
        $validator = $this->createMock(ValidatorInterface::class);
        $this->chargingLocationManager = $this->createMock(ChargingLocationManager::class);
        $this->chargingLocationController = new ChargingLocationController($validator);
        $this->chargingLocationController->setContainer(static::getContainer());
    }

    public function testGetLocationsReturnsErrorResponse(): void
    {
        $headers = ['Content-Type' => 'application/json'];
        $data = json_encode(['invalid_param' => 'value']);
        $request = Request::create('/charging/locations', Request::METHOD_POST, [], [], [], $headers, $data);
        $this->chargingLocationManager->method('validateChargingLocationData')
            ->willReturn($this->createMock(ConstraintViolationListInterface::class));

        $this->chargingLocationManager->method('getLocations')
            ->willReturn((new ErrorResponse())->setMessage('Invalid query params')->setCode(Response::HTTP_BAD_REQUEST));

        $response = $this->chargingLocationController->getLocations($request, $this->chargingLocationManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(400, $response->getStatusCode());
        $this->assertStringContainsString('Invalid query params', $response->getContent());
    }

    public function testGetLocationsReturnsSuccessfulResponse(): void
    {
        $headers = ['Content-Type' => 'application/json'];
        $data = json_encode(['valid_param' => 'value']);
        $request = Request::create('/charging/locations', Request::METHOD_POST, [], [], [], $headers, $data);

        $this->chargingLocationManager->method('validateChargingLocationData')
            ->willReturn($this->createMock(ConstraintViolationListInterface::class));

        $expectedResponse = [
            'result' => '{}',
            'count' => 0,
        ];

        $this->chargingLocationManager->method('getLocations')
            ->willReturn((new SuccessResponse())->setData($expectedResponse)->setCode(Response::HTTP_OK));

        $response = $this->chargingLocationController->getLocations($request, $this->chargingLocationManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('{"success":{"result":"{}","count":0}}', $response->getContent());
    }

    public function testGetLocationsReturnsValidationError(): void
    {
        $headers = ['Content-Type' => 'application/json'];
        $data = json_encode(['invalid_param' => 'value']);
        $request = Request::create('/charging/locations', Request::METHOD_POST, [], [], [], $headers, $data);

        $constraint = new ConstraintViolation(
            'This value should not be blank.',
            null,
            ['data'],
            null,
            'data',
            null
        );

        $this->chargingLocationManager->method('validateChargingLocationData')
            ->willReturn(new ConstraintViolationList([$constraint]));

        $response = $this->chargingLocationController->getLocations($request, $this->chargingLocationManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());
        $this->assertStringContainsString('validation_failed', $response->getContent());
    }
}
