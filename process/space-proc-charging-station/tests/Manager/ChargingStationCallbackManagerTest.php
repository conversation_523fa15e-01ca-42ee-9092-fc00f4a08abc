<?php

namespace App\Tests\Manager;

use App\Helper\ErrorResponse;
use App\Helper\SuccessResponse;
use App\Manager\ChargingStationCallbackManager;
use App\Message\ChargingStationCallback;
use App\Service\ChargingStationCallbackService;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;

class ChargingStationCallbackManagerTest extends TestCase
{
    private ChargingStationCallbackManager $manager;
    private ChargingStationCallbackService $chargingStationCallbackService;
    private LoggerInterface $logger;

    public function setUp(): void
    {
        $this->chargingStationCallbackService = $this->createMock(ChargingStationCallbackService::class);
        $this->logger = $this->createMock(LoggerInterface::class);

        $this->manager = new ChargingStationCallbackManager(
            $this->chargingStationCallbackService
        );
        $this->manager->setLogger($this->logger);
    }

    public function testHandleSQSCallbackStartAcceptedWithSuccess(): void
    {
        $message = new ChargingStationCallback(
            'internal123',
            'user123',
            'session123',
            'start',
            'ACCEPTED'
        );

        $this->chargingStationCallbackService->expects($this->once())
            ->method('updateChargingSessionByUserIdAndInternalId')
            ->with('user123', 'internal123', ['remoteCommandStatus' => 'CHARGING_ACCEPTED'])
            ->willReturn(true);

        $this->chargingStationCallbackService->expects($this->once())
            ->method('getChargingSessionByUserIdAndInternalId')
            ->with('user123', 'internal123')
            ->willReturn(['internalId' => 'internal123', 'remoteCommandStatus' => 'CHARGING_ACCEPTED']);

        $this->chargingStationCallbackService->expects($this->once())
            ->method('sendNotification')
            ->with('user123', ['internalId' => 'internal123', 'remoteCommandStatus' => 'CHARGING_ACCEPTED'])
            ->willReturn(new SuccessResponse());

        $response = $this->manager->handleSQSCallback($message);

        $this->assertInstanceOf(SuccessResponse::class, $response);
        $this->assertEquals('Charging session updated successfully', $response->getData());
    }

    public function testHandleSQSCallbackStopAcceptedWithSuccess(): void
    {
        $message = new ChargingStationCallback(
            'internal123',
            'user123',
            'session123',
            'stop',
            'ACCEPTED'
        );

        $this->chargingStationCallbackService->expects($this->once())
            ->method('updateChargingSessionByUserIdAndSessionId')
            ->with('user123', 'session123', ['remoteCommandStatus' => 'PENDING_STOP'])
            ->willReturn(true);

        $this->chargingStationCallbackService->expects($this->once())
            ->method('getChargingSessionByUserIdAndSessionId')
            ->with('user123', 'session123')
            ->willReturn(['sessionId' => 'session123', 'remoteCommandStatus' => 'PENDING_STOP']);

        $this->chargingStationCallbackService->expects($this->once())
            ->method('sendNotification')
            ->with('user123', ['sessionId' => 'session123', 'remoteCommandStatus' => 'PENDING_STOP'])
            ->willReturn(new SuccessResponse());

        $response = $this->manager->handleSQSCallback($message);

        $this->assertInstanceOf(SuccessResponse::class, $response);
        $this->assertEquals('Charging session updated successfully', $response->getData());
    }

    public function testHandleSQSCallbackStartInvalidResult()
    {
        $message = new ChargingStationCallback(
            'internal123',
            'user123',
            'session123',
            'start',
            'INVALID_RESULT'
        );

        $this->logger->expects($this->once())
            ->method('error')
            ->with($this->stringContains('Invalid result on received payload'));

        $response = $this->manager->handleSQSCallback($message);

        $this->assertInstanceOf(ErrorResponse::class, $response);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getCode());
    }

    public function testHandleSQSCallbackStopInvalidResult()
    {
        $message = new ChargingStationCallback(
            'internal123',
            'user123',
            'session123',
            'stop',
            'INVALID_RESULT'
        );

        $this->logger->expects($this->once())
            ->method('error')
            ->with($this->stringContains('Invalid result on received payload'));

        $response = $this->manager->handleSQSCallback($message);

        $this->assertInstanceOf(ErrorResponse::class, $response);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getCode());
    }

    public function testHandleSQSCallbackInvalidAction()
    {
        $message = new ChargingStationCallback(
            'internal123',
            'user123',
            null,
            'invalid_action',
            null
        );

        $this->logger->expects($this->once())
            ->method('error')
            ->with($this->stringContains('Invalid action received'));

        $response = $this->manager->handleSQSCallback($message);

        $this->assertInstanceOf(ErrorResponse::class, $response);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getCode());
    }
}
