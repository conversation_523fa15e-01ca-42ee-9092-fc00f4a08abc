<?php

use App\Connector\CustomHttpClient;
use App\Connector\F2mConnector;
use App\Helper\WSResponse;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;

class F2mConnectorTest extends TestCase
{
    private $client;
    private $connector;

    public function setUp(): void
    {
        $this->client = $this->createMock(CustomHttpClient::class);
        $this->connector = new F2mConnector($this->client, 'https://example.com');
        $logger = $this->createMock(LoggerInterface::class);
        $logger->expects($this->any())->method('info');
        $logger->expects($this->any())->method('error');
        $this->connector->setLogger($logger);
    }

    public function testCallReturnsSuccess(): void
    {
        $this->client->expects($this->once())
            ->method('request')
            ->willReturn(new WSResponse(200, ['result' => [], 'count' => 0]));

        $response = $this->connector->callF2mc('POST', '/charging/locations', ['headers' => ['token' => 'tokenValue']]);
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(200, $response->getCode());
        $this->assertEquals(['result' => [], 'count' => 0], $response->getData());
    }

    public function testCallThrowException(): void
    {
        $this->client->expects($this->once())
            ->method('request')
            ->willThrowException(new Exception('API error', 500));

        $wsResponse = $this->connector->callF2mc('POST', '/charging/locations', []);

        $this->assertInstanceOf(WSResponse::class, $wsResponse);
        $this->assertEquals(500, $wsResponse->getCode());
        $this->assertEquals('API error', $wsResponse->getData());
    }
}
