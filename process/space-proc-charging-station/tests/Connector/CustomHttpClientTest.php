<?php

namespace App\tests\Service;

use App\Connector\CustomHttpClient;
use App\Helper\WSResponse;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Contracts\HttpClient\ResponseInterface;

class CustomHttpClientTest extends TestCase
{
    public function testCallSuccessResponse(): void
    {
        $httpClient = $this->createMock(HttpClientInterface::class);

        $responseData = ['foo' => 'bar'];
        $response = $this->createMock(ResponseInterface::class);
        $response->method('getStatusCode')->willReturn(Response::HTTP_OK);
        $response->method('toArray')->willReturn($responseData);
        $httpClient->method('request')->willReturn($response);

        $customHttpClient = new CustomHttpClient($httpClient);
        $logger = $this->createMock(LoggerInterface::class);
        $logger->expects($this->any())->method('info');
        $logger->expects($this->any())->method('error');
        $customHttpClient->setLogger($logger);

        $method = 'GET';
        $url = 'http://example.com/api';
        $options = [];
        $expectedResponse = new WSResponse(Response::HTTP_OK, $responseData);
        $actualResponse = $customHttpClient->request($method, $url, $options);
        $this->assertEquals($expectedResponse, $actualResponse);
    }

    public function testCallExceptionResponse(): void
    {
        $httpClient = $this->createMock(HttpClientInterface::class);
        $httpClient->method('request')->willThrowException(new \Exception());

        $customHttpClient = new CustomHttpClient($httpClient);
        $logger = $this->createMock(LoggerInterface::class);
        $logger->expects($this->any())->method('info');
        $logger->expects($this->any())->method('error');
        $customHttpClient->setLogger($logger);

        $method = 'GET';
        $url = 'http://example.com/api';
        $options = [];

        $response = $customHttpClient->request($method, $url, $options);
        $this->assertInstanceOf(WSResponse::class, $response);
    }
}
