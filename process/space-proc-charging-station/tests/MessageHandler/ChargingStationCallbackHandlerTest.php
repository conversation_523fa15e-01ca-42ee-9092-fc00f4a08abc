<?php

namespace App\Tests\MessageHandler;

use App\Helper\ErrorResponse;
use App\Helper\SuccessResponse;
use App\Manager\ChargingStationCallbackManager;
use App\Message\ChargingStationCallback;
use App\MessageHandler\ChargingStationCallbackHandler;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class ChargingStationCallbackHandlerTest extends TestCase
{
    private $chargingStationCallbackManager;
    private $validator;
    private $logger;
    private $handler;

    protected function setUp(): void
    {
        $this->chargingStationCallbackManager = $this->createMock(ChargingStationCallbackManager::class);
        $this->validator = $this->createMock(ValidatorInterface::class);
        $this->logger = $this->createMock(LoggerInterface::class);

        $this->handler = new ChargingStationCallbackHandler(
            $this->chargingStationCallbackManager,
            $this->validator
        );
        $this->handler->setLogger($this->logger);
    }

    public function testInvokeWithValidMessage(): void
    {
        $message = $this->createMock(ChargingStationCallback::class);

        $this->validator->expects($this->once())
            ->method('validate')
            ->with($message)
            ->willReturn(new ConstraintViolationList());

        $handleSQSCallbackResponse = new SuccessResponse('success', Response::HTTP_OK);
        $this->chargingStationCallbackManager->expects($this->once())
            ->method('handleSQSCallback')
            ->with($message)
            ->willReturn($handleSQSCallbackResponse);

        $this->logger->expects($this->once())
            ->method('info')
            ->with($this->stringContains('Received space charging station callback message'));

        $result = ($this->handler)($message);

        $this->assertInstanceOf(SuccessResponse::class, $result);
    }

    public function testInvokeWithInvalidMessage(): void
    {
        $message = $this->createMock(ChargingStationCallback::class);

        $violation = $this->createMock(ConstraintViolation::class);
        $violations = new ConstraintViolationList([$violation]);

        $this->validator->expects($this->once())
            ->method('validate')
            ->with($message)
            ->willReturn($violations);

        $this->logger->expects($this->once())
            ->method('error')
            ->with($this->stringContains('Invalid message received'));

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Invalid message received');

        ($this->handler)($message);
    }

    public function testInvokeWithErrorResponse(): void
    {
        $message = $this->createMock(ChargingStationCallback::class);

        $this->validator->expects($this->once())
            ->method('validate')
            ->with($message)
            ->willReturn(new ConstraintViolationList());

        $errorResponse = $this->createMock(ErrorResponse::class);

        $this->chargingStationCallbackManager->expects($this->once())
            ->method('handleSQSCallback')
            ->with($message)
            ->willReturn($errorResponse);

        $this->logger->expects($this->once())
            ->method('error')
            ->with($this->stringContains('Error while handling SQS callback'));

        $result = ($this->handler)($message);

        $this->assertInstanceOf(ErrorResponse::class, $result);
    }
}
